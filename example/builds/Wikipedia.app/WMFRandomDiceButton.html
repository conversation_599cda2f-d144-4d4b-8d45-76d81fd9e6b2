<!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->
<!-- This file is generated by scripts/generate_dice_roll_html.rb. Don't try to edit directly -->
<!-- %1$@ should be replaced with the button background color -->
<!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->

<html>
<head>
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <style>
      body {
      	background-color: rgba(0, 0, 0, 0);
      }

      #container {
      	position: absolute;
      	left: 8px;
      	top: 8px;
      	height: 28px;
      	width: 28px;
      	padding: 0 !important;
      	perspective: 1000px;
      	-webkit-perspective: 1000px;
      }

      #dice {
      	cursor: pointer;
      	position: absolute;
      	transform-style: preserve-3d;
      	height: 100%;
      	width: 100%;
      	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
      }

      #dice > div {
      	backface-visibility: hidden;
      	height: 28px;
      	width: 28px;
      	position: absolute;
      	background: #FFFFFF;
      	border-radius: 4px;
      }

      #dice > div > span { /*die dot styling */
      	position: absolute;
      	background: #%1$@;
      	height: 6px;
      	width: 6px;
      	border-radius: 50%;
      	-webkit-transform: translate3d(-50%, -50%, 0);
      	backface-visibility: hidden;
      }

      .one {
      	-webkit-transform: rotateY(0deg) translateZ(11px);
      }

      .two {
      	-webkit-transform: rotateX(180deg) translateZ(11px);
      }

      .three {
      	-webkit-transform: rotateY(90deg) translateZ(11px);
      }

      .four {
      	-webkit-transform: rotateY(-90deg) translateZ(11px);
      }

      .five {
      	-webkit-transform: rotateX(90deg) translateZ(11px);
      }

      .six {
      	-webkit-transform: rotateX(-90deg) translateZ(11px);
      }

    	.one span, .three span:nth-child(2), .five span:nth-child(5) {
    		top: 50%;
    		left: 50%;
    	}

    	.two span:nth-child(1), .three span:nth-child(1), .four span:nth-child(1), .five span:nth-child(1), .six span:nth-child(1) {
    		top: 25%;
    		left: 25%;
    	}

    	.two span:nth-child(2), .three span:nth-child(3), .four span:nth-child(4), .five span:nth-child(4), .six span:nth-child(6) {
    		top: 75%;
    		left: 75%;
    	}

    	.four span:nth-child(2), .five span:nth-child(2), .six span:nth-child(2) {
    		top: 25%;
    		left: 75%;
    	}

    	.four span:nth-child(3), .five span:nth-child(3), .six span:nth-child(5)  {
    		top: 75%;
    		left: 25%;
    	}

    	.six span:nth-child(3) {
    		top: 50%;
    		left: 25%;
    	}   

    	.six span:nth-child(4) {
    		top: 50%;
    		left: 75%;
    	}

        #dice.rolled-1 {
            -webkit-transform: translateZ(1px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
        }

        #dice.roll-1-2-1 {
            -webkit-animation: roll-keyframes-1-2-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-2-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-2-2 {
            -webkit-animation: roll-keyframes-1-2-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-2-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-2-3 {
            -webkit-animation: roll-keyframes-1-2-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-2-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-2-4 {
            -webkit-animation: roll-keyframes-1-2-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-2-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-3-1 {
            -webkit-animation: roll-keyframes-1-3-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-3-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-3-2 {
            -webkit-animation: roll-keyframes-1-3-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-3-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-3-3 {
            -webkit-animation: roll-keyframes-1-3-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-3-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-3-4 {
            -webkit-animation: roll-keyframes-1-3-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-3-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-4-1 {
            -webkit-animation: roll-keyframes-1-4-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-4-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-4-2 {
            -webkit-animation: roll-keyframes-1-4-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-4-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-4-3 {
            -webkit-animation: roll-keyframes-1-4-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-4-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-4-4 {
            -webkit-animation: roll-keyframes-1-4-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-4-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-5-1 {
            -webkit-animation: roll-keyframes-1-5-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-5-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-5-2 {
            -webkit-animation: roll-keyframes-1-5-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-5-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-5-3 {
            -webkit-animation: roll-keyframes-1-5-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-5-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-5-4 {
            -webkit-animation: roll-keyframes-1-5-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-5-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-6-1 {
            -webkit-animation: roll-keyframes-1-6-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-6-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-6-2 {
            -webkit-animation: roll-keyframes-1-6-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-6-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-6-3 {
            -webkit-animation: roll-keyframes-1-6-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-6-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-1-6-4 {
            -webkit-animation: roll-keyframes-1-6-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-1-6-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
        }
        #dice.rolled-2 {
            -webkit-transform: translateZ(0px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
        }

        #dice.roll-2-1-1 {
            -webkit-animation: roll-keyframes-2-1-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-1-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-1-2 {
            -webkit-animation: roll-keyframes-2-1-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-1-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-1-3 {
            -webkit-animation: roll-keyframes-2-1-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-1-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-1-4 {
            -webkit-animation: roll-keyframes-2-1-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-1-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-3-1 {
            -webkit-animation: roll-keyframes-2-3-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-3-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-3-2 {
            -webkit-animation: roll-keyframes-2-3-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-3-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-3-3 {
            -webkit-animation: roll-keyframes-2-3-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-3-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-3-4 {
            -webkit-animation: roll-keyframes-2-3-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-3-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-4-1 {
            -webkit-animation: roll-keyframes-2-4-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-4-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-4-2 {
            -webkit-animation: roll-keyframes-2-4-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-4-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-4-3 {
            -webkit-animation: roll-keyframes-2-4-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-4-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-4-4 {
            -webkit-animation: roll-keyframes-2-4-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-4-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-5-1 {
            -webkit-animation: roll-keyframes-2-5-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-5-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-5-2 {
            -webkit-animation: roll-keyframes-2-5-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-5-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-5-3 {
            -webkit-animation: roll-keyframes-2-5-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-5-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-5-4 {
            -webkit-animation: roll-keyframes-2-5-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-5-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-6-1 {
            -webkit-animation: roll-keyframes-2-6-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-6-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-6-2 {
            -webkit-animation: roll-keyframes-2-6-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-6-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-6-3 {
            -webkit-animation: roll-keyframes-2-6-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-6-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-2-6-4 {
            -webkit-animation: roll-keyframes-2-6-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-2-6-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(180deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
        }
        #dice.rolled-3 {
            -webkit-transform: translateZ(1px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
        }

        #dice.roll-3-1-1 {
            -webkit-animation: roll-keyframes-3-1-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-1-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-1-2 {
            -webkit-animation: roll-keyframes-3-1-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-1-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-1-3 {
            -webkit-animation: roll-keyframes-3-1-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-1-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-1-4 {
            -webkit-animation: roll-keyframes-3-1-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-1-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-2-1 {
            -webkit-animation: roll-keyframes-3-2-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-2-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-2-2 {
            -webkit-animation: roll-keyframes-3-2-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-2-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-2-3 {
            -webkit-animation: roll-keyframes-3-2-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-2-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-2-4 {
            -webkit-animation: roll-keyframes-3-2-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-2-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-4-1 {
            -webkit-animation: roll-keyframes-3-4-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-4-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-4-2 {
            -webkit-animation: roll-keyframes-3-4-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-4-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-4-3 {
            -webkit-animation: roll-keyframes-3-4-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-4-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-4-4 {
            -webkit-animation: roll-keyframes-3-4-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-4-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-5-1 {
            -webkit-animation: roll-keyframes-3-5-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-5-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-5-2 {
            -webkit-animation: roll-keyframes-3-5-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-5-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-5-3 {
            -webkit-animation: roll-keyframes-3-5-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-5-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-5-4 {
            -webkit-animation: roll-keyframes-3-5-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-5-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-6-1 {
            -webkit-animation: roll-keyframes-3-6-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-6-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-6-2 {
            -webkit-animation: roll-keyframes-3-6-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-6-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-6-3 {
            -webkit-animation: roll-keyframes-3-6-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-6-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-3-6-4 {
            -webkit-animation: roll-keyframes-3-6-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-3-6-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(-90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
        }
        #dice.rolled-4 {
            -webkit-transform: translateZ(1px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
        }

        #dice.roll-4-1-1 {
            -webkit-animation: roll-keyframes-4-1-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-1-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-1-2 {
            -webkit-animation: roll-keyframes-4-1-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-1-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-1-3 {
            -webkit-animation: roll-keyframes-4-1-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-1-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-1-4 {
            -webkit-animation: roll-keyframes-4-1-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-1-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-2-1 {
            -webkit-animation: roll-keyframes-4-2-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-2-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-2-2 {
            -webkit-animation: roll-keyframes-4-2-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-2-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-2-3 {
            -webkit-animation: roll-keyframes-4-2-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-2-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-2-4 {
            -webkit-animation: roll-keyframes-4-2-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-2-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-3-1 {
            -webkit-animation: roll-keyframes-4-3-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-3-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-3-2 {
            -webkit-animation: roll-keyframes-4-3-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-3-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-3-3 {
            -webkit-animation: roll-keyframes-4-3-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-3-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-3-4 {
            -webkit-animation: roll-keyframes-4-3-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-3-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-5-1 {
            -webkit-animation: roll-keyframes-4-5-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-5-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-5-2 {
            -webkit-animation: roll-keyframes-4-5-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-5-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-5-3 {
            -webkit-animation: roll-keyframes-4-5-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-5-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-5-4 {
            -webkit-animation: roll-keyframes-4-5-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-5-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-6-1 {
            -webkit-animation: roll-keyframes-4-6-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-6-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-6-2 {
            -webkit-animation: roll-keyframes-4-6-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-6-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-6-3 {
            -webkit-animation: roll-keyframes-4-6-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-6-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-4-6-4 {
            -webkit-animation: roll-keyframes-4-6-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-4-6-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(0deg) rotateY(90deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
        }
        #dice.rolled-5 {
            -webkit-transform: translateZ(0px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
        }

        #dice.roll-5-1-1 {
            -webkit-animation: roll-keyframes-5-1-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-1-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-1-2 {
            -webkit-animation: roll-keyframes-5-1-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-1-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-1-3 {
            -webkit-animation: roll-keyframes-5-1-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-1-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-1-4 {
            -webkit-animation: roll-keyframes-5-1-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-1-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-2-1 {
            -webkit-animation: roll-keyframes-5-2-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-2-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-2-2 {
            -webkit-animation: roll-keyframes-5-2-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-2-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-2-3 {
            -webkit-animation: roll-keyframes-5-2-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-2-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-2-4 {
            -webkit-animation: roll-keyframes-5-2-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-2-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-3-1 {
            -webkit-animation: roll-keyframes-5-3-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-3-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-3-2 {
            -webkit-animation: roll-keyframes-5-3-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-3-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-3-3 {
            -webkit-animation: roll-keyframes-5-3-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-3-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-3-4 {
            -webkit-animation: roll-keyframes-5-3-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-3-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-4-1 {
            -webkit-animation: roll-keyframes-5-4-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-4-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-4-2 {
            -webkit-animation: roll-keyframes-5-4-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-4-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-4-3 {
            -webkit-animation: roll-keyframes-5-4-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-4-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-4-4 {
            -webkit-animation: roll-keyframes-5-4-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-4-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-6-1 {
            -webkit-animation: roll-keyframes-5-6-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-6-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-6-2 {
            -webkit-animation: roll-keyframes-5-6-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-6-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-6-3 {
            -webkit-animation: roll-keyframes-5-6-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-6-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-5-6-4 {
            -webkit-animation: roll-keyframes-5-6-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-5-6-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(-90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-270deg) rotateY(360deg) rotateZ(0deg);
            }
        }
        #dice.rolled-6 {
            -webkit-transform: translateZ(1px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
        }

        #dice.roll-6-1-1 {
            -webkit-animation: roll-keyframes-6-1-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-1-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-1-2 {
            -webkit-animation: roll-keyframes-6-1-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-1-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-1-3 {
            -webkit-animation: roll-keyframes-6-1-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-1-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-1-4 {
            -webkit-animation: roll-keyframes-6-1-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-1-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-2-1 {
            -webkit-animation: roll-keyframes-6-2-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-2-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-2-2 {
            -webkit-animation: roll-keyframes-6-2-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-2-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-2-3 {
            -webkit-animation: roll-keyframes-6-2-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-2-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-180deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-2-4 {
            -webkit-animation: roll-keyframes-6-2-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-2-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(540deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-3-1 {
            -webkit-animation: roll-keyframes-6-3-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-3-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-3-2 {
            -webkit-animation: roll-keyframes-6-3-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-3-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-3-3 {
            -webkit-animation: roll-keyframes-6-3-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-3-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-450deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-3-4 {
            -webkit-animation: roll-keyframes-6-3-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-3-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(270deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-4-1 {
            -webkit-animation: roll-keyframes-6-4-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-4-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-4-2 {
            -webkit-animation: roll-keyframes-6-4-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-4-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-4-3 {
            -webkit-animation: roll-keyframes-6-4-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-4-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(-270deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-4-4 {
            -webkit-animation: roll-keyframes-6-4-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-4-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-360deg) rotateY(450deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-5-1 {
            -webkit-animation: roll-keyframes-6-5-1 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-5-1 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(360deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-5-2 {
            -webkit-animation: roll-keyframes-6-5-2 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-5-2 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(270deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-5-3 {
            -webkit-animation: roll-keyframes-6-5-3 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-5-3 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(-360deg) rotateZ(0deg);
            }
        }

        #dice.roll-6-5-4 {
            -webkit-animation: roll-keyframes-6-5-4 1s 1 ease-in-out forwards;
        }

        @-webkit-keyframes roll-keyframes-6-5-4 {
            0% {
            	-webkit-transform: translateZ(-19px) rotateX(90deg) rotateY(0deg) rotateZ(0deg);
            }
            80% {
              -webkit-transform: translateZ(-500px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
            100% {
            	-webkit-transform: translateZ(-19px) rotateX(-450deg) rotateY(360deg) rotateZ(0deg);
            }
        }
  </style>
</head>
<body>
	<div id="container">
		<div id="dice" class="rolled-1">
			<div class="one">
				<span></span>
			</div>
			<div class="two">
				<span></span>
				<span></span>
			</div>
			<div class="three">
				<span></span>
				<span></span>
				<span></span>
			</div>
			<div class="four">
				<span></span>
				<span></span>
				<span></span>
				<span></span>
			</div>
			<div class="five">
				<span></span>
				<span></span>
				<span></span>
				<span></span>
				<span></span>
			</div>
			<div class="six">
				<span></span>
				<span></span>
				<span></span>
				<span></span>
				<span></span>
				<span></span>
			</div>
		</div>
	</div>
</body>
