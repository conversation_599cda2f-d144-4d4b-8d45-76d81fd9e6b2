<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Assets.car</key>
		<data>
		fQZ34tjaSYGFFeGEOI12IdN0CWU=
		</data>
		<key>Base.lproj/InfoPlist.strings</key>
		<data>
		YUvHXdacPYbeo67wT2HsAMx+bWU=
		</data>
		<key>Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<data>
		0MIPa0qiDjReRwwobTsnJ01z+l0=
		</data>
		<key>Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<data>
		Mj+UqIePeD3fZVvbBtO1DovMmmU=
		</data>
		<key>Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<data>
		aaUtExK8wxGkTdbkloZytUecYPA=
		</data>
		<key>Info.plist</key>
		<data>
		pZTq3JK+RiC2PRIvgl6AYkH0qG8=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YUvHXdacPYbeo67wT2HsAMx+bWU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			Y0coxoHCD3iyE5LOGK7GAdLSOdZfvIjm4KfzHvq9y7w=
			</data>
		</dict>
		<key>Base.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FcNK+/1g39O3Nm0XCZiM3Zge8KNGhsvHrVHG08NPPu4=
			</data>
		</dict>
		<key>Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sOic6ugSKWwGvBSX5ZyYEUE+pFecCWW0YXAo5CNbmCQ=
			</data>
		</dict>
		<key>Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ONjPdmODSX1OZ0ECOcmwXeSTBsYidjIV1LiIiTyL9uc=
			</data>
		</dict>
		<key>Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			N+OywOVev2f3fzqdaCRA/HHE2cfI+yWpACpt9U+coOE=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FcNK+/1g39O3Nm0XCZiM3Zge8KNGhsvHrVHG08NPPu4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
