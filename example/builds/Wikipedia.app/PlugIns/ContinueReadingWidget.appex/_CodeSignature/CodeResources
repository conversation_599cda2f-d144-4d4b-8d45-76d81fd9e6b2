<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Base.lproj/InfoPlist.strings</key>
		<data>
		cEfGGYjSufquKwjwGzLJzyNvyrM=
		</data>
		<key>Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<data>
		0MIPa0qiDjReRwwobTsnJ01z+l0=
		</data>
		<key>Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<data>
		bMg1hW+qXlrCQA8PBvq3MTLxFyw=
		</data>
		<key>Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<data>
		WIcNPfYQNR29DirP3NOTyzUN5jE=
		</data>
		<key>Info.plist</key>
		<data>
		IChb407IZdih+45dJirLHgK6FuI=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cEfGGYjSufquKwjwGzLJzyNvyrM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Base.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			N2neWCXPMgE/LJyV9ZHFRuNVMJMLFfhyT3QjeRqjsqI=
			</data>
		</dict>
		<key>Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sOic6ugSKWwGvBSX5ZyYEUE+pFecCWW0YXAo5CNbmCQ=
			</data>
		</dict>
		<key>Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BkIvmhPYE93ldvoOkddUjmGtjYdVQvziSGr9Vl3QcIQ=
			</data>
		</dict>
		<key>Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			8qwi2c3eeGtm/QFM26GpJQbdeBd6dS9oWKz9suC81G0=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			N2neWCXPMgE/LJyV9ZHFRuNVMJMLFfhyT3QjeRqjsqI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
