{"success": {"backgroundImageName": "NotificationBackgroundSuccess.png", "borderColor": "#005700", "borderHeight": 1, "buttonBackgroundImageName": "NotificationButtonBackground.png", "buttonTitleTextColor": "#FFFFFF", "buttonTitleShadowColor": "#67B759", "buttonTitleShadowOffsetX": 0, "buttonTitleShadowOffsetY": -1, "contentFontSize": 12, "contentTextColor": "#FFFFFF", "imageName": "NotificationBackgroundSuccessIcon.png", "shadowColor": "#67B759", "shadowOffsetX": 0, "shadowOffsetY": -1, "textColor": "#FFFFFF", "titleFontSize": 14, "backgroundColor": "#76CF67"}, "message": {"backgroundImageName": "NotificationBackgroundMessage.png", "borderColor": "#727C83", "borderHeight": 1, "buttonBackgroundImageName": "NotificationButtonBackground.png", "buttonTitleTextColor": "#727C83", "buttonTitleShadowColor": "#EBEEF1", "buttonTitleShadowOffsetX": 0, "buttonTitleShadowOffsetY": 1, "contentFontSize": 12, "contentTextColor": "#727C83", "imageName": "", "shadowColor": "#EBEEF1", "shadowOffsetX": 0, "shadowOffsetY": 1, "textColor": "#727C83", "titleFontSize": 14, "backgroundColor": "#D4DDDF"}, "warning": {"backgroundImageName": "NotificationBackgroundWarning.png", "borderColor": "#A28918", "borderHeight": 1, "buttonBackgroundImageName": "NotificationButtonBackground.png", "buttonTitleTextColor": "#484638", "buttonTitleShadowColor": "#E5D87C", "buttonTitleShadowOffsetX": 0, "buttonTitleShadowOffsetY": 1, "contentFontSize": 12, "contentTextColor": "#484638", "imageName": "NotificationBackgroundWarningIcon.png", "shadowColor": "#E5D87C", "shadowOffsetX": 0, "shadowOffsetY": 1, "textColor": "#484638", "titleFontSize": 14, "backgroundColor": "#DAC43C"}, "error": {"backgroundImageName": "NotificationBackgroundError.png", "borderColor": "#700000", "borderHeight": 1, "buttonBackgroundImageName": "NotificationButtonBackground.png", "buttonTitleTextColor": "#FFFFFF", "buttonTitleShadowColor": "#812929", "buttonTitleShadowOffsetX": 0, "buttonTitleShadowOffsetY": -1, "contentFontSize": 12, "contentTextColor": "#FFFFFF", "imageName": "NotificationBackgroundErrorIcon.png", "shadowColor": "#812929", "shadowOffsetX": 0, "shadowOffsetY": -1, "textColor": "#FFFFFF", "titleFontSize": 14, "backgroundColor": "#DD3B41"}}