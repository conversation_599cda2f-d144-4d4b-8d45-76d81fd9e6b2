<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AboutViewController.plist</key>
		<data>
		u3UgNXQeA40q+Rrqn9dibqjlGlc=
		</data>
		<key>AbuseFilterAlertView.nib</key>
		<data>
		v/HBY/n2WMvHCQGxGs8SD5ipFIg=
		</data>
		<key><EMAIL></key>
		<data>
		zy09gDELIyWF+sMzMBZDUFeSnQU=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		Vo60ZFelC/Eh4rDIO86XuGceBjI=
		</data>
		<key>ArticleAsLivingDocHeaderView.nib</key>
		<data>
		V5gKVoyun2rxDegp4GTz+NaEiKQ=
		</data>
		<key>ArticlePopoverViewController.nib</key>
		<data>
		1vWK/cAIZmB16Tx80hZdHm6o+xE=
		</data>
		<key>Assets.car</key>
		<data>
		4wM2uckNza+HCYx8i2MhgDbx7y0=
		</data>
		<key>BeKindInputAccessoryView.nib</key>
		<data>
		vK4HfHIqsJr+MdLKFKJpMuEO8eA=
		</data>
		<key>ContextualHighlightEditToolbarView.nib</key>
		<data>
		KK/aROkHkn+nCNnts7LC3+mkyHs=
		</data>
		<key>CreateNewReadingListButtonView.nib</key>
		<data>
		uMryeGeVX5+TXm7Nh8nIaE5ldQ4=
		</data>
		<key>CreateReadingListViewController.nib</key>
		<data>
		wBc8pfQH4c8sAZrtDshXynaJeCw=
		</data>
		<key>DebugReadingListsViewController.nib</key>
		<data>
		AoOyYkw23o9yZIyY+F5pYKLID1E=
		</data>
		<key>DefaultEditToolbarView.nib</key>
		<data>
		wmMNEKQOlv+l4LN1DTKhi1a97YI=
		</data>
		<key>DescriptionEditViewController.storyboardc/DescriptionEditViewController.nib</key>
		<data>
		hYmYxRI2gpSNwJ13uOUfhh9eUx8=
		</data>
		<key>DescriptionEditViewController.storyboardc/Info.plist</key>
		<data>
		uEEdwh14resvplhCz/eUecZWBEE=
		</data>
		<key>DescriptionEditViewController.storyboardc/a7r-jI-djU-view-GYS-km-eqa.nib</key>
		<data>
		pri0/Q5CQ/64OptNjhX4y9aciLo=
		</data>
		<key>DescriptionHelpViewController.nib</key>
		<data>
		TMfXmFPZU7JL9uO+GWfNw/7ChdQ=
		</data>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomeContainerViewController.nib</key>
		<data>
		dusyhlSKOjSVOJhJA7l5LfrqssI=
		</data>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomeContentsViewController.nib</key>
		<data>
		XcxWI4gAUgzt/JT5Eg869YaV+QE=
		</data>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomeImageViewController.nib</key>
		<data>
		3HTHyG1YJlZ6c7p/ZzmKZjYhaEo=
		</data>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomeInitialViewController.nib</key>
		<data>
		seRYLgT7orB6f1gpwO15h+P7dNU=
		</data>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomePageViewController.nib</key>
		<data>
		0aDCMwZnvJ0MoDW1nA2keq1HIDk=
		</data>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomePanelViewController.nib</key>
		<data>
		rRMsWJ7XXUYt9Tbhh6mBthknQD0=
		</data>
		<key>DescriptionWelcome.storyboardc/HdP-0o-ZZC-view-lzy-Eu-ayy.nib</key>
		<data>
		E/jpQ37gWeOUIlVREgS06hdhw7c=
		</data>
		<key>DescriptionWelcome.storyboardc/Info.plist</key>
		<data>
		lVTuSHIE2bRQZ3xNf0++GHIQ4KQ=
		</data>
		<key>DescriptionWelcome.storyboardc/ncK-V0-zLz-view-HVN-iR-kMH.nib</key>
		<data>
		2s/lKhNbqi8yBfc8Re+t3MffJzw=
		</data>
		<key>DescriptionWelcome.storyboardc/phf-K5-0TN-view-GFc-RE-fPO.nib</key>
		<data>
		PLJCgSFBAuhSLYJMpsCOtESgfIo=
		</data>
		<key>DescriptionWelcome.storyboardc/qLZ-5x-Kxd-view-Ev9-ZH-utG.nib</key>
		<data>
		PO1vzPPFVI4bMmK8+YqwwCLdjPk=
		</data>
		<key>DescriptionWelcome.storyboardc/sbE-3l-bqs-view-wCD-x0-Vga.nib</key>
		<data>
		8/1JdC2yyqy81lRO9gdlqFLcQhQ=
		</data>
		<key>DiffHeaderCompareItemView.nib</key>
		<data>
		MY2CD7TzJYfDltZJRWNLORZ+oWQ=
		</data>
		<key>DiffHeaderCompareView.nib</key>
		<data>
		l/9XxB6+VU+y3oBHhPegAQ+6u5Y=
		</data>
		<key>DiffHeaderEditorView.nib</key>
		<data>
		T6MerFu4A+ft5ACPuFdkHQir3S0=
		</data>
		<key>DiffHeaderExtendedView.nib</key>
		<data>
		wipWSAlC+dgFRigkyCl71dID2OU=
		</data>
		<key>DiffHeaderSummaryView.nib</key>
		<data>
		pOy3A8RwURAuWJgUFzObAr00vUk=
		</data>
		<key>DiffHeaderTitleView.nib</key>
		<data>
		odnoTAUgt9rLLJCcXDRb663sn68=
		</data>
		<key>DiffListChangeCell.nib</key>
		<data>
		xSPirE4tb2YAE7VmkDyRi2ZZxMs=
		</data>
		<key>DiffListContextCell.nib</key>
		<data>
		ahZFfW995MYeHD4/0oqdoBoeqkA=
		</data>
		<key>DiffListUneditedCell.nib</key>
		<data>
		2YRnEPcvUdEOVulBCX4Xre1ieFA=
		</data>
		<key>DiffResponse.json</key>
		<data>
		zKLnEqZcL93Mq+xtntI5/O7QKwo=
		</data>
		<key>DiffToolbarView.nib</key>
		<data>
		CEQIPgUnW8eC9zQfPGNFTeL54SM=
		</data>
		<key>EditLinkViewController.nib</key>
		<data>
		PmndTm+ve0JT+GvCFivq53zn6dI=
		</data>
		<key>EditPreviewInternalLinkViewController.nib</key>
		<data>
		D6VvVKczbPLGqNcTUsH+F9PsmY8=
		</data>
		<key>EditSaveViewController.storyboardc/EditSaveViewController.nib</key>
		<data>
		qE0j6XrfCudN687RfPGJ+7b1tLk=
		</data>
		<key>EditSaveViewController.storyboardc/Info.plist</key>
		<data>
		v0hvH3eI/TwfbfARaM6mNkecI50=
		</data>
		<key>EditSaveViewController.storyboardc/YCc-it-nqb-view-tP4-ul-Au7.nib</key>
		<data>
		EqYzfwVeEF8z8TYVVY1RQ+KBpvs=
		</data>
		<key>EditSummaryViewController.nib</key>
		<data>
		aooCm2dwksdCLRR9jvisOiNKLyQ=
		</data>
		<key>EmptyViewController.nib</key>
		<data>
		aVSGO52xmnCd+fcI6Btl2lou98M=
		</data>
		<key>EraseSavedArticlesView.nib</key>
		<data>
		Mct4rPq0DRULwrpcTrdXUhOeyt4=
		</data>
		<key>Featured Article Widget Preview Content.json</key>
		<data>
		DAj/wy/1IO+w7/21Q7CV791ON/w=
		</data>
		<key>FocusNavigationView.nib</key>
		<data>
		emxC5LMlVkg6S4XzVtuotzOPW4E=
		</data>
		<key>FontSizeSliderViewController.nib</key>
		<data>
		uT9W0ESLcg0ES1BOgqtC4yCmxXk=
		</data>
		<key>Frameworks/WMF.framework/Assets.car</key>
		<data>
		lrVR85MLgm7m2cfoO3ZX1nhusrE=
		</data>
		<key>Frameworks/WMF.framework/BatchEditToolbarViewController.nib</key>
		<data>
		qEQVXnu/PbcVBsC45KvxzMvddpc=
		</data>
		<key>Frameworks/WMF.framework/Cache.momd/Cache 2.mom</key>
		<data>
		BwrzDc6sbIB540F8FzgubJXfFxU=
		</data>
		<key>Frameworks/WMF.framework/Cache.momd/Cache 2.omo</key>
		<data>
		+x9ZY3M6oizhkkOYb+CEs3wljCM=
		</data>
		<key>Frameworks/WMF.framework/Cache.momd/Cache.mom</key>
		<data>
		LCHCTYxA99zvWxY6vdkfw+4qu70=
		</data>
		<key>Frameworks/WMF.framework/Cache.momd/VersionInfo.plist</key>
		<data>
		DfjrTcCKHa0HShR1ZZJUqrBGmxA=
		</data>
		<key>Frameworks/WMF.framework/CacheItemMappingModel.cdm</key>
		<data>
		UB8ah+PIrQmunJwnMaKujE5U/p8=
		</data>
		<key>Frameworks/WMF.framework/EventLogging.momd/EventLogging 2.mom</key>
		<data>
		bR+BaPoFKhYoYFuRFshr7cgzWog=
		</data>
		<key>Frameworks/WMF.framework/EventLogging.momd/EventLogging 2.omo</key>
		<data>
		8CBmDSwsp7Y2ScV/OF6LvDzDyP0=
		</data>
		<key>Frameworks/WMF.framework/EventLogging.momd/EventLogging.mom</key>
		<data>
		uIxjVcKsAz3JOpoD9QEbVAsh6jk=
		</data>
		<key>Frameworks/WMF.framework/EventLogging.momd/VersionInfo.plist</key>
		<data>
		xGngkN7IJNQwyx/9+9R2rnRh8Mc=
		</data>
		<key>Frameworks/WMF.framework/EventPlatformEvents.momd/EventPlatformEvents.mom</key>
		<data>
		ow++q0zkDc+6srZ19rMbi9HbJWU=
		</data>
		<key>Frameworks/WMF.framework/EventPlatformEvents.momd/VersionInfo.plist</key>
		<data>
		LYV3Gfa6Mt1HOSv6WIHhA0cL96w=
		</data>
		<key>Frameworks/WMF.framework/Info.plist</key>
		<data>
		VOzNXHcXIBdvgzBtO9REZLR4xbU=
		</data>
		<key>Frameworks/WMF.framework/MediaWikiAcceptLanguageMapping.json</key>
		<data>
		I1fL5KV8X+sUSAEhY6xCiBFM0wY=
		</data>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/RemoteNotifications 2.mom</key>
		<data>
		hCWyYbm9/syOf0SynOvqUcWVlFM=
		</data>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/RemoteNotifications 3.mom</key>
		<data>
		WBtEGSoq2psGUu8xIv8PbRAejsQ=
		</data>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/RemoteNotifications 3.omo</key>
		<data>
		/8V4RY6Yq1hW4YkAl0R52Ku/FcU=
		</data>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/RemoteNotifications.mom</key>
		<data>
		zo547tlzlBVbAwcKscHGrpMXPBk=
		</data>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/VersionInfo.plist</key>
		<data>
		2gHA6qJqXHqdpNkbRtNxgTylLAU=
		</data>
		<key>Frameworks/WMF.framework/WMF</key>
		<data>
		3+ND0O5gzESuQ6iMo5Z7qQ5SjIU=
		</data>
		<key>Frameworks/WMF.framework/WMFArticlePreviewViewController.nib</key>
		<data>
		qnIMZHn2vedo7X5lhtMPzCS36NY=
		</data>
		<key>Frameworks/WMF.framework/Wikipedia.momd/VersionInfo.plist</key>
		<data>
		zWW7INFXYeXWQJjF+15wpp/v+T0=
		</data>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 2.mom</key>
		<data>
		bhwlLIgGyy4yWn3jNEQKlUXqbtw=
		</data>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 3.mom</key>
		<data>
		LkF74faJ+9DQA0RfKlxkumn/cOw=
		</data>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 4.mom</key>
		<data>
		Ey0eSw02AEOHj6+S9KNaFTFMpCA=
		</data>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 5.mom</key>
		<data>
		ohi2ZpXgzADqiGjh4U0IQnVwV8w=
		</data>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 6.mom</key>
		<data>
		Y58fGNQo0Cvu6IVP1+TU8in7fyY=
		</data>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 6.omo</key>
		<data>
		pOTNSrhln7u4KgTRTw1BBfU4ATU=
		</data>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia.mom</key>
		<data>
		MFLjdwVXQ+uC7D7jpgokcva/k40=
		</data>
		<key>Frameworks/WMF.framework/_CodeSignature/CodeResources</key>
		<data>
		jKy8/vt55Whv8rmg2/kpPHVcs0Q=
		</data>
		<key>Frameworks/WMF.framework/af.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NgqWtMCsreGN8iQ6KEifge+im0c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/af.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			lTC62+vbXSY+ynXoSoqPCELacp4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qQJvsAYk4pWx849/dv3ZSkmt8vE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ar.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			KqrQgvr7Pgxnuwz3sKqr0GPNw4c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/as.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/dvPoKo0djMnr717It8vDUCKQyw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/as.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			mb7vdYKRd1H32GH6eizxOTTHeUE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/assets/WMF_Black.pdf</key>
		<data>
		ITOQyLmkW/SRtwyxmKcqU9cpkqM=
		</data>
		<key>Frameworks/WMF.framework/assets/WMF_White.pdf</key>
		<data>
		g2Fu0l/R7AiyQBUUnbG5oiSzQfo=
		</data>
		<key>Frameworks/WMF.framework/assets/about.html</key>
		<data>
		71zWFcemVc/3OYWPEXi8nW2EDZw=
		</data>
		<key>Frameworks/WMF.framework/assets/about.js</key>
		<data>
		DhWj1UbMXLWhWP5HpcLVfo+EX9k=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-black.css</key>
		<data>
		yWBqMDNS/L2JIhJ9HCwJtMlVu64=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-common.css</key>
		<data>
		RfyS6Qlb8CXGhi9YVK/MEYzkJ88=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-dark.css</key>
		<data>
		Im5Cn8OCi9HoVMuMLKPkBPnbNTo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-editTextSelection.js</key>
		<data>
		57SJGQ0rUytjTb7QjspAfMc5E2c=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-index.html</key>
		<data>
		PIKxMFhbX8bZ1SzP/Zgmj3TMdoE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-light.css</key>
		<data>
		E5Dcm9NyaIq2nZPU62aCZ+WcCOg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-range-determination-bundle.js</key>
		<data>
		bEIqv3WkeqhGEo1UrJ2XYH5yUHs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-sepia.css</key>
		<data>
		GoyX9pXKutwyLat8Pa/n7JrGRYg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-syntax-highlighting-off.css</key>
		<data>
		fdlouvr705+hL5KOqiZ/FBPcYPw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-aa.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ab.json</key>
		<data>
		zTdNrDs6uJyZc3zpHz14vikWaM8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ace.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ady.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-af.json</key>
		<data>
		I73819yTF0EsTodl2+BU1fk8l5U=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ak.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-als.json</key>
		<data>
		3Eq8agAgulLI+FzpQo6Qg/QuT9c=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-alt.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-am.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-an.json</key>
		<data>
		bVjz4g8A7VQUWucyorJRR9/MZtw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ang.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ar.json</key>
		<data>
		Nu2SDZPasYpmqDd/S8w0JrkyulM=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-arc.json</key>
		<data>
		7Z/E8vXk3lU+mhm9qREXVFdOluo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ary.json</key>
		<data>
		Hm4o2reN/0EN/oE+iZPbsCG7GB8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-arz.json</key>
		<data>
		ETOItgKU5jn3IkObRqgBrDbaBds=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-as.json</key>
		<data>
		3QXl57Xifsnae2lqSBB5ktGL+cU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ast.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-atj.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-av.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-avk.json</key>
		<data>
		XjfRqxSSQoj7IqGPpIjHnejIhrY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-awa.json</key>
		<data>
		2rU6riSVKuwjgsJ5yaXdrw7xb78=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ay.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-az.json</key>
		<data>
		645uMqly8LCqajqOxSVimxbcZ5U=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-azb.json</key>
		<data>
		ovjrsLMwx0BSxY3Xz3PevbLrjrM=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ba.json</key>
		<data>
		8S55J7143TiP7oJvH7O9StE+WCU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ban.json</key>
		<data>
		YeqWTi/CkNp4jJxVKxiFqvXkbaw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bar.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bat-smg.json</key>
		<data>
		t5VE9wUH1dyxrXvzcWhqKrTT1Ec=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bcl.json</key>
		<data>
		//KNMzpdmxhmpvJAt3abgeDUeBk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-be-tarask.json</key>
		<data>
		myW1b6JXHaRpL5gXOrrSshOfo3M=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-be-x-old.json</key>
		<data>
		myW1b6JXHaRpL5gXOrrSshOfo3M=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-be.json</key>
		<data>
		sCzKlvO5Xye5E9EPLuPwL/ORdAs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bg.json</key>
		<data>
		uymHUAI69elcWzKwTo0+hi1v834=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bh.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bi.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bjn.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bm.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bn.json</key>
		<data>
		i0aUZfAJBVFq5a1TLTQ2LgYPaJU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bpy.json</key>
		<data>
		oL9OpW4ktd4uj6YXFfS2ywCoBd4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-br.json</key>
		<data>
		BxUdTM+gXun24f6vhdr2MI52Oxk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bs.json</key>
		<data>
		f74giBGTGTZYJ3JZCh9YJbIouNI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bug.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bxr.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ca.json</key>
		<data>
		VgVChIyTpuhB98zZq2llKqajG3Q=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cbk-zam.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cdo.json</key>
		<data>
		LSxzQOjoQsxRPinjoDk+pSrFv3U=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ce.json</key>
		<data>
		r5i94V18AlgZwz37QQDrHLssLIo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ceb.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ch.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cho.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-chr.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-chy.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ckb.json</key>
		<data>
		9wWfZ0wdbrq93ir+WLR4xTdM8e4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-co.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cr.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-crh.json</key>
		<data>
		z+nv7HH2q/5yqwD7XR0tS13fTBI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cs.json</key>
		<data>
		X82QPrPFsgpAoM3EjWGBgT7sEEU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-csb.json</key>
		<data>
		rk8fjiWdwrcJFrsfy61yy5fu0NY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cu.json</key>
		<data>
		WW6z8LJYgexw3P/AQ6az3F6xXOw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cv.json</key>
		<data>
		az47y1lS0Zj7ytr2sWmcJ7pU4fM=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cy.json</key>
		<data>
		GVSY8tTzoIY+q4y6OxhRpFOD2JU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-da.json</key>
		<data>
		bK3GYBF1b2LH+4r+zj/Ak1+Iqak=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-de.json</key>
		<data>
		5Q8o5FqF9LXY58esZ224hdqnKIw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-din.json</key>
		<data>
		TxJLk2R+3OTbHCwUcC5o+Mi7778=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-diq.json</key>
		<data>
		pElMHdyuEEfk0cYn3yMZk2Mo8YY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-dsb.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-dty.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-dv.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-dz.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ee.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-el.json</key>
		<data>
		OdDNk4rBvppnWuG2nb/bbGnUwO8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-eml.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-en.json</key>
		<data>
		N/q725getNHsJDPKjIYZpSMTKPw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-eo.json</key>
		<data>
		cIjEZEUekSZIxmn4pBhC8roSKzs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-es.json</key>
		<data>
		f9x/OzPGMWF4sTmjUgv2Q7Dw60U=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-et.json</key>
		<data>
		zSjlayV4R/nawWImOWw7VDIhE2w=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-eu.json</key>
		<data>
		p8TLPmNrRXU2PQOuf4gD2HdfY5E=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ext.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fa.json</key>
		<data>
		CtLM1u1VWdUarDwcK77Qbvni8y4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ff.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fi.json</key>
		<data>
		rN31VzUGJGSRjZsda8YuujeJfZA=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fiu-vro.json</key>
		<data>
		zSjlayV4R/nawWImOWw7VDIhE2w=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fj.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fo.json</key>
		<data>
		ODPO3Y198DU/8c/IRomyr3KxJcA=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fr.json</key>
		<data>
		D/0hzopIpm6OaKBYWDuX0dXfDHM=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-frp.json</key>
		<data>
		h5W7fKvBGGsk19RAd5ZEQwxGWVQ=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-frr.json</key>
		<data>
		jcgfzd72vyJotfFs474u07jTvN8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fur.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fy.json</key>
		<data>
		+5R53P4UpjA/mmof+vQy6aYMX4Y=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ga.json</key>
		<data>
		zL+GGGJXIWXkBq6a6mRJfOReBWo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gag.json</key>
		<data>
		UX8j3MQCFzVzO4F43pwiwd4jvPo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gan.json</key>
		<data>
		uLdYqbmE887gxf3jd03eFxgbaRk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gcr.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gd.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gl.json</key>
		<data>
		eAdyhryJn2yvFoYCGD75oVaZqm4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-glk.json</key>
		<data>
		2I0ytfmpKhiQkRfV1KjCB1qUJbk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gn.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gom.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gor.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-got.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gu.json</key>
		<data>
		rk3h/e0mWrF0WafcBQU4n0Lg++k=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gv.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ha.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hak.json</key>
		<data>
		LSxzQOjoQsxRPinjoDk+pSrFv3U=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-haw.json</key>
		<data>
		z2j7vrbkjq/0FamFA6/vBGkuoeo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-he.json</key>
		<data>
		GKxPpBozsFZFrU3n62L8hXbR0jE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hi.json</key>
		<data>
		xSHabSXQduKYWggyMueGqFNXfHI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hif.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ho.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hr.json</key>
		<data>
		WxM/B82qxTKk0D4I0nq8Ozd47LI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hsb.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ht.json</key>
		<data>
		W6wiUL2C/OF3XhiN10Q/B53dgtA=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hu.json</key>
		<data>
		KgfPW76fXBDJ8ERTQ2XuTuQ+6Xs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hy.json</key>
		<data>
		Xwy8drse48WCKXkeEVVZ7IIY1Sw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hyw.json</key>
		<data>
		1jIf2r57zaggHvjqydDcAL9jIsE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hz.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ia.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-id.json</key>
		<data>
		mJeZ2uGXMzBFE9HzeIKaLJU3K6k=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ie.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ig.json</key>
		<data>
		HaB4JTLUDaePtNSOChKE+/CNF/Y=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ii.json</key>
		<data>
		TK6qC7C4OoAUbhRHoqUNY1EQXko=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ik.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ilo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-inh.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-io.json</key>
		<data>
		UFG/tKHbpxprDZQo9R/z0KhNiTs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-is.json</key>
		<data>
		iaZ/0Zc2pVTjGU+9QAAVecr9Lto=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-it.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-iu.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ja.json</key>
		<data>
		67KpTmrO0mIV88Bv0XmxIcx+La0=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-jam.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-jbo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-jv.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ka.json</key>
		<data>
		E8Plevlp14uYskWyBxaeRddn3Tk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kaa.json</key>
		<data>
		gkYWM+bGTpvfEtBJqHlFcfrFRPE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kab.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kbd.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kbp.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kg.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ki.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kj.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kk.json</key>
		<data>
		2uXmk402KTuHWbjI8Bnh/rXeRHk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kl.json</key>
		<data>
		c+hHVy7w6K8SL+ZeFQ5wTzdvYUE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-km.json</key>
		<data>
		BQr/NaMv/CEKOUlewLFYsTX2HG8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ko.json</key>
		<data>
		Hgw3Y1bQ1Q/WcZTZ8X+rQjK0Eb4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-koi.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kr.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-krc.json</key>
		<data>
		oAvMAtoZC1ZHVFXKioWCENnw5J8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ks.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ksh.json</key>
		<data>
		Y2GnKouTNgagU28R8YbDWI+IJ+8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ku.json</key>
		<data>
		UPXlULLzkKAmCPwlReCi11TNwLY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kv.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kw.json</key>
		<data>
		YxqdVFYUskQjY16n6WP5u4vbAWk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ky.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-la.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lad.json</key>
		<data>
		E5itV9piEf5V0jypjoBcWb4Ub6E=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lb.json</key>
		<data>
		ysFLwhSfIy7dzvTDVYWtekXL3tA=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lbe.json</key>
		<data>
		CafJ9WeY+fqTdzKD9wfzKckIyxw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lez.json</key>
		<data>
		TKrnr5aBytHVdPnuYUMhfBIwvYA=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lfn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lg.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-li.json</key>
		<data>
		j3cKztE56n0hsaSCOqJXjBuLBTY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lij.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lld.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lmo.json</key>
		<data>
		uWDBOwP8xN5BQM+5L4yVGO3lH1k=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ln.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lrc.json</key>
		<data>
		2I0ytfmpKhiQkRfV1KjCB1qUJbk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lt.json</key>
		<data>
		t5VE9wUH1dyxrXvzcWhqKrTT1Ec=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ltg.json</key>
		<data>
		tnQszZE4deaCTRxM0bgvhZPGgO0=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lv.json</key>
		<data>
		tnQszZE4deaCTRxM0bgvhZPGgO0=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mad.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mai.json</key>
		<data>
		2rU6riSVKuwjgsJ5yaXdrw7xb78=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-map-bms.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mdf.json</key>
		<data>
		9MOAPy5mCclinwBdbgOjWsFAJ/w=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mg.json</key>
		<data>
		1ltA4oo/jRBVZWrLPSGUoUbH/d8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mh.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mhr.json</key>
		<data>
		OYhkDGsdgY6N636pK7BRN/D9Jks=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mi.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-min.json</key>
		<data>
		WDeLpvOZ2JmjLEUzkh1kiZaTxdo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mk.json</key>
		<data>
		AqXAC4S4gZGAI+g8MmOjO241xI4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ml.json</key>
		<data>
		rtWqJgh6kKZEEa+7kFu+WYAaryA=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mn.json</key>
		<data>
		HTEdRAKkqtZlGBuJL3gllNEv83U=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mni.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mnw.json</key>
		<data>
		4B+Gtx7hsm8bAiZsjz8+3B+pGLI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mr.json</key>
		<data>
		vFnEVyZe+dBcB8lKLaiRGR3U1RY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mrj.json</key>
		<data>
		OYhkDGsdgY6N636pK7BRN/D9Jks=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ms.json</key>
		<data>
		v1PMWX9JALlJoWlfkyaQYVFi2es=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mt.json</key>
		<data>
		KCTicMbfhEH7WZF9gA6NtUTTVoM=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mus.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mwl.json</key>
		<data>
		AXKHYaepkZ3DXQbnvfyl4BLaDX4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-my.json</key>
		<data>
		uD6HK7O6846DEQc96BslRmbHArk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-myv.json</key>
		<data>
		9MOAPy5mCclinwBdbgOjWsFAJ/w=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mzn.json</key>
		<data>
		x3+T9NIxhzODeLfzTe3IqDPvJrs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-na.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nah.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nap.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nds-nl.json</key>
		<data>
		lik9vCHoc+d7jcdQIAaKEO9p85g=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nds.json</key>
		<data>
		89yutkpUKSuh6CZrGBnePOr+xf0=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ne.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-new.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ng.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nia.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nl.json</key>
		<data>
		j3cKztE56n0hsaSCOqJXjBuLBTY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nn.json</key>
		<data>
		jg15Z7E8Jh3u0IcjmnqCg2b+MFM=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-no.json</key>
		<data>
		Hg26oDqEIJPbzpnWIhY2wnOYJnY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nov.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nqo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nrm.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nso.json</key>
		<data>
		7wyifuMip1P0lUKZ4jAoSb4NM7A=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nv.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ny.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-oc.json</key>
		<data>
		Qe3JkBG/b1alTjyirShG0pce8/Q=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-olo.json</key>
		<data>
		j/KgeuCOTJQnNcjjkOhssVK+NsY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-om.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-or.json</key>
		<data>
		sAXpUs+9KHzmNCv19KlS9lkBg0s=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-os.json</key>
		<data>
		yxKU1ki4QKl39UbeJvD7woSBLl4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pa.json</key>
		<data>
		O4w0K/ICDKMM9O5T9v38/LX7QVw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pag.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pam.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pap.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pcd.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pdc.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pfl.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pi.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pih.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pl.json</key>
		<data>
		KJYKOZ9UsxL2O08irJQgvzOnBlQ=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pms.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pnb.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pnt.json</key>
		<data>
		OdDNk4rBvppnWuG2nb/bbGnUwO8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ps.json</key>
		<data>
		b6q5B8whAj9Wx3ouVzgEbdQ4ZhY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pt.json</key>
		<data>
		nL7j8vJt62TdsSdAq5FEGPQbIxY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-qu.json</key>
		<data>
		1SgIEcUxGmXv+fY6zgmLabdGAco=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rm.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rmy.json</key>
		<data>
		UdVASIU2ZazvweWbIuzB/Wd5zD4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ro.json</key>
		<data>
		t19UX480peG2vqy4+82v71N8HyU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-roa-rup.json</key>
		<data>
		UdVASIU2ZazvweWbIuzB/Wd5zD4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-roa-tara.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ru.json</key>
		<data>
		Zpaox6mE8OTx1UlFsoJNz9bWYu4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rue.json</key>
		<data>
		NqpW/xdm77NEGpqdOLY1xYTzVeo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rw.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sa.json</key>
		<data>
		pfRYnV7ni3PNpk/1YeBE7CcBGC0=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sah.json</key>
		<data>
		i1zAjvEK7YxphRTJnY4/NCJynxE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sat.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sc.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-scn.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sco.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sd.json</key>
		<data>
		4b+3ibDHxP55y8+9/tgOOJ3LgwU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-se.json</key>
		<data>
		PQ+06aK+RelI5snIZcoNUE6tUTY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sg.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sh.json</key>
		<data>
		2U4kScNenofrw+z3dKG9ngnzFH0=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-shn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-shy.json</key>
		<data>
		ulNRQwXUjUircDsbD/C/X+pG+ls=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-si.json</key>
		<data>
		g+T3NwgVm5YpgHU3JimQbbRbw7c=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-simple.json</key>
		<data>
		+f3hOUAYaapFcoCQZ+mP+6KExhY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sk.json</key>
		<data>
		hx6EWehveJB8tlWM3fG0lIEhPvQ=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-skr.json</key>
		<data>
		3K94MyIwY2LDWZNe7wNCV7zVuy8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sl.json</key>
		<data>
		YBt5kMhUFJU1OtHivBP19Cnb55w=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sm.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-smn.json</key>
		<data>
		viraM4aHzqqC5SCNA+3xUUvUGe8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-so.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sq.json</key>
		<data>
		UXj+CzmnKnfyT9DDIEZKCi+BVCE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sr.json</key>
		<data>
		pKByFsijvveOmbKQ8cPeL5HoHvo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-srn.json</key>
		<data>
		sdHhpVD9/vxmfK3rGZVAtTSlfpY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ss.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-st.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-stq.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-su.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sv.json</key>
		<data>
		rz3BEmoEVBBNFCCjwgK0jXBFHDk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sw.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-szl.json</key>
		<data>
		rk8fjiWdwrcJFrsfy61yy5fu0NY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-szy.json</key>
		<data>
		uLdYqbmE887gxf3jd03eFxgbaRk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ta.json</key>
		<data>
		KSK4xPJV93aqPvy5+jyeIfOpRNU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tay.json</key>
		<data>
		uLdYqbmE887gxf3jd03eFxgbaRk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tcy.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-te.json</key>
		<data>
		RXY4hEVPQCT7zwsfx9dmkJjN8/Y=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-test.json</key>
		<data>
		LBCEAJcLB7M/cf8e9pdsU8WRAHQ=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tet.json</key>
		<data>
		2PcccUm1pv5yriJKKmiXCF5NzNg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tg.json</key>
		<data>
		TqnFRMdWc2NTVXvKozWzGbM4PyA=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-th.json</key>
		<data>
		EbyBLBfZHJOTziTEbDOSVFI7TFw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ti.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tk.json</key>
		<data>
		P0moHuonmrE5Jcbt4Ajv5Pnyyzo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tl.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-to.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tpi.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tr.json</key>
		<data>
		rBhUDwu0oY2GsI2HzQEIsdgQsCI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-trv.json</key>
		<data>
		uLdYqbmE887gxf3jd03eFxgbaRk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ts.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tt.json</key>
		<data>
		bBFDxzyMLQJwAiQeAhdeVg4fuOQ=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tum.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tw.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ty.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tyv.json</key>
		<data>
		w8aHJvWEYJffDco2zsF6RtyNUCw=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-udm.json</key>
		<data>
		vWZsWTrRqZyJisfS2bx2XORmdWQ=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ug.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-uk.json</key>
		<data>
		ad5k7XewhRGvIXq4irq7vCfBZIE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ur.json</key>
		<data>
		+KuHCm5e4t1kA3PceSUvxTuqvQk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-uz.json</key>
		<data>
		ZmRA9HoV2ujOx6LAoUmqDvkDWZs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ve.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vec.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vep.json</key>
		<data>
		UnmhCVj0W13QbPb+T8rDaMkxeUU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vi.json</key>
		<data>
		ihOzW4uAo1lIg27UrvuHnq7vxMQ=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vls.json</key>
		<data>
		j3cKztE56n0hsaSCOqJXjBuLBTY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-wa.json</key>
		<data>
		Vgmil/wplQmLPzqR17bwC/8D7cg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-war.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-wo.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-wuu.json</key>
		<data>
		GqEd+AkVoXGD++gSCHua3dLxkB8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-xal.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-xh.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-xmf.json</key>
		<data>
		E8Plevlp14uYskWyBxaeRddn3Tk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-yi.json</key>
		<data>
		aieJg4xtYz6OLXXKooY1h3rBvJA=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-yo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-yue.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-za.json</key>
		<data>
		GqEd+AkVoXGD++gSCHua3dLxkB8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zea.json</key>
		<data>
		j3cKztE56n0hsaSCOqJXjBuLBTY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zh-classical.json</key>
		<data>
		UPX2pWi+K1y9YUi3cIYYYDoP6mg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zh-min-nan.json</key>
		<data>
		LSxzQOjoQsxRPinjoDk+pSrFv3U=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zh-yue.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zh.json</key>
		<data>
		zmQGGhnslUZ5PhG1gp4CwxG/sIY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zu.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/addon/search/match-highlighter.js</key>
		<data>
		db7UyaGOcfDu66HXWPjT0sRkzf8=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/addon/search/search.js</key>
		<data>
		1+N2k+iw6/ycioeLQ0tkLfhxMbM=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/addon/search/searchcursor.js</key>
		<data>
		2lLTyDTCtNSNIkMj79o/BSrU2P0=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/ext.CodeMirror.js</key>
		<data>
		xp7iuZ1u2hxm+nIUvti88YfiV64=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/ext.CodeMirror.less</key>
		<data>
		fPt3mXz+Xy7IPzKUJAisBl5c+eo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/AUTHORS</key>
		<data>
		wLt2jj7IOXBpbylzWNE749i/UA0=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/CHANGELOG.md</key>
		<data>
		O5AXgPIEPT2k2rMrVYHWHqXFKbI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/CONTRIBUTING.md</key>
		<data>
		C7SA9G2NrUCRlmfs4lz9eTW4utE=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/LICENSE</key>
		<data>
		xDV1SZkf1wkNCstoXuFc+yohV1A=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/README.md</key>
		<data>
		I0p/cE+0PBgzR/w8gUdWFQxj1d4=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/lib/codemirror.css</key>
		<data>
		Ha94ns+dc5z+jfKVUX3UvYRt7Do=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/lib/codemirror.js</key>
		<data>
		Big5HmGmq19KvV/djdnHWOaw6Eg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/clike/clike.js</key>
		<data>
		xSYzNNi4hcWhB6U6Ro91DKupL9w=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/css/css.js</key>
		<data>
		VcpTTW13lfgV/Wf3xSv//dZuq+0=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/htmlmixed/htmlmixed.js</key>
		<data>
		PFZaQqDUDoixzBOfuyCN+A7wGbs=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/javascript/javascript.js</key>
		<data>
		3V/dI6T1A7msqSE27zrFG1SoFMY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/php/php.js</key>
		<data>
		twWEOqBYF04ANt7T6232kNnDLmk=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/xml/xml.js</key>
		<data>
		exwQlxf+ySS9d/5QUaJIr/BBtkU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/black4.png</key>
		<data>
		6LWcrPHr5/iiIQICD6re4BS3eoI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/ext2.png</key>
		<data>
		WTBLMkNJZxwU74hBuReh6ISpmCU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/ext4.png</key>
		<data>
		JjK65QSBQEaHxSyA4t+rYSKa6LI=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/link4.png</key>
		<data>
		AHhyJkxuEsrrAE8tTvf2ldMW1dg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/template4.png</key>
		<data>
		O9DfX5K7WJr7XISYwGPy5Ln512s=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/template8.png</key>
		<data>
		UsnEPyGNoUJmR53NCz3KCK1a4vY=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/mediawiki.css</key>
		<data>
		7pdLT6GIJEHiIBoydKhZgtS59XA=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/mediawiki.js</key>
		<data>
		6XAvl2imycaWL4WzqVVqox+fmOc=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/tests/qunit/.eslintrc.json</key>
		<data>
		gtW8LR1D2sSdT43ZBWVctx1lTGQ=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/tests/qunit/CodeMirror.mediawiki.test.js</key>
		<data>
		+mXjHCzGj8kESvD40wHOScuZzag=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/.eslintrc.json</key>
		<data>
		ZxkL9lwGcwB4CU9nqrE9BTnuvzg=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirror.init.js</key>
		<data>
		feMC3iFeDXEVOGU0aBLX/x2ZEtU=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirror.init.less</key>
		<data>
		uRaEgOZsJxFiZ+dR85RWwwCXDIo=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirrorAction.js</key>
		<data>
		ks/WRynT6cHETzA8Eo+tkk8if/A=
		</data>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirrorTool.js</key>
		<data>
		XD9S6O6Jlal8lEfxg7hu85kZnVA=
		</data>
		<key>Frameworks/WMF.framework/assets/index.js</key>
		<data>
		bAZaK/cRnc9W/2W8DjjF+O6BJWg=
		</data>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/base.css</key>
		<data>
		f+IJpKp6T+Bm918XFcaf3eXpM+w=
		</data>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/build/PCSHTMLConverter.js</key>
		<data>
		8JqUvIK9vQ/QCTGTmiXBcq9gTd4=
		</data>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/build/Polyfill.js</key>
		<data>
		7oR4M7SIJ6LE0qU1Jk/EAtyZcsE=
		</data>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/index.html</key>
		<data>
		OD0Hit0EsuCs11KIKSsPsc7oQfY=
		</data>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/pcs.css</key>
		<data>
		KP1Czsch/i8JSJUqrLZF7pWeLXU=
		</data>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/pcs.js</key>
		<data>
		Cd3d/e/unrCen9u8nBKL0o76/dk=
		</data>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-base.css</key>
		<data>
		y33O0LA2r1CDjfbIx9TibXpsbX0=
		</data>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-black.css</key>
		<data>
		rRATxJXK7T1vaEgaDSIEoD5QoG0=
		</data>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-dark.css</key>
		<data>
		2CGLKgRqueoe739xYF6JfF/+XV0=
		</data>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-light.css</key>
		<data>
		Za1aK6VxDzHM87PMtwmN8ooSIpI=
		</data>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-sepia.css</key>
		<data>
		2ZoAvebOwLnUezirUF29mdhcVlo=
		</data>
		<key>Frameworks/WMF.framework/assets/styleoverrides.css</key>
		<data>
		IchKVQWtv6Rf/RFdTj+kyAD6YvQ=
		</data>
		<key>Frameworks/WMF.framework/ast.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KzwtKPqyYqRq3F7mJ+wlPrsPYtg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ast.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			reoI6cKlCz82wGoeiclfe8el2ls=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/av.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			psYVYd3a8fP+NC0UcfPdemgXH1k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/azb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ba.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Olpw7F/6UGlZY4ZOdSyhtbIbbSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ba.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bgn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bn.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iAfVYuY//PQvke+OHDVVOGgq1A8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rEGnkAUnJt6kW2c2HY1sdOuOdnM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/br.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			t8SEvn+hIANskJshPwp1ZMs1GOQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/br.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			iW6TIYGqi5XYcy59JwnurXoNAgA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mauNRGV+oi1iHQv0H9o45NarNIw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bs.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			YvGXfzJmd3h8hkvGq8q6B+J2F8A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B4NPx13/8jg5tu97pgk2e93UtdI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ca.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			g2H+x6irH412gsQeapcbdMgh1w4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ce.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7Bpe6ddVOignc2nmdpdlkoPi6QI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ce.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			9aaX97Gkd6r6nrgOeE/yy7dqKe0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ckb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+OXbdrD5erKkOD13XxGnniNwcZo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ckb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			Vx4HAHZyQeLzD/cJJG2Emo8+N4k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cnh.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YKWPs0dhQLPtiA+qXIdrEtSzKS4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cs.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			WFmKn74kzojjPNNssRsGtcT17nY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cy.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4mUupTBPTfjQFNFiaUQAtqkTZ58=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			ZE6tX4iQb4GyQQjc5Vc3DQt9fH0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LSJhRsfueY3InDljmcarRYHbZuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/da.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			6iObTBztpe1MSaWi/F9txq8knBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			V+zN3ki/CwoS5+G/mahqBVVX3EY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/de.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rbXURI9zqUBCSiHLn4XghkhaEMM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/diq.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oMUa4xrCUO06+AVJGyazkhB/Ka8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/el.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CQGx7L1yF72A/S/306aP5jTqit4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/el.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			9p6Nz6yNWwvo0wmrd0UtO9Xdwl8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3zbt3ioQMlm+kBqGgTCuWXu0VeE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/en.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/eo.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NSflcyD9VWl8mgmEzl8i+4WKXUg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/eo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			suQ3PNTdK7H34l2EIfwQWCiSaME=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JhQHH/jwWewoW59WE/p2BAVYs88=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/es.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			mgo1f4BDN4aXOPUZsiyC6laGvgI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/eu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WjER5muF6VBdALPn3H4E9IXCUYU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/eu.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			iSoGUVEmTv2GZMEu4OPH6dkjHbU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fa.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kUChSgM8c76e2SiRfP4hcbxWzVg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			L5z4OTJs5judmk81nYTZ5pWEfuw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aTm1hnMSjH6x16hUAULgAMqkQpw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			NOsD5EbXIXdnlXzO7lGIHOxSeVw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fil.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			psYVYd3a8fP+NC0UcfPdemgXH1k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fo.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			birWVLoe+a2KdrliU5Z1bE94AdA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8+1uvd4G+5XZDZXQ9+TpxTVLLv4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			YKiINf5l9TvTShj1Tf5Zj0xSlwI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ga.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bFmQrWDxOa31/32QUejXGgxUXgA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ga.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			LkF8p2zIIflXrVioeUGttUx64jU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/gl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pbS3Sx7HODsNR/ksCpnDerUabSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/gl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			7Eqt6W/dlrenJQHOpPIytzjE7Do=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/haw.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wVCO8wg258p9oPofZsRHZ2LtJFE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/haw.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			UVClAgKzIxHK04SCtZlv2brcLks=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/he.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1ECYIkESpppQ02SV41Jy8o2Zr0o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/he.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			LCHSI6tQ5eBFikKyXRHSiPgU8Tc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aAgC1Dh7Gq8duqWCKTECV2jdZfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			pEFzF8HVd6BhmhdrcrxckDJSEk0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iLr1Jg1uAarv/zYIX1ES0fEF53A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			NPqA0zW+OUUfRhPs1/86rnaAu5Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hrx.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hsb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TgzXPQTKAYlJcU3o/ZYlq8ricwY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hsb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			PfuxQgGDb3Zd3wIlgAw25KG4/0g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mVrD0Oz3CuHclVRcu38YjH4cWtA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hu.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			3iXZ729jFqtFwb4vp/TUDfi7kyw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hy.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QrvapCPKb8wHY8F9sGUIu1u9qPs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			3bjMnHOHedWg5RHshpY2yBhIPjo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/id.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Fj7cr40JQKBUgF7OBURjgCCayVc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/id.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			NKGJvSXnTS0ogYnJZ9w55Q0J0S8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/is.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pXg7JTgr9X4poGUFypbcHubaB2I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/is.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			GCcTZuy+f94gs0/6UWGR87qMGOw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mhmD0qdJ5TugJS6ay+xnL5jPOLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/it.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			awb9jsdWCEET2QlfRUw6IoBf6Bw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gMw/gaPR/PvLcyhWEZcnfbAaKcE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ja.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			BiH+Rkd1lGv2rkFfxbeV2qjDmHs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/jv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qHoDF4m2K/lqvJa56vvzk+2tXoE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/jv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			mOYNb555Ha8zj85F0paKMTYI0/4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ka.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AyiHHC15Vj7/cpEWe49dZvLgyKU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ka.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kab.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SIN5lyM6mcTUWREVZ8wb7e48gAs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kab.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			lZuu2bZ4nloQFJUi4MzjdHAd8bk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kcg.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+2O2KJf5Oha/66rL7f5ZPb1yGIo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kcg.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			hjHomhKxQ+uMbsQ//1ra19mBeew=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/km.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			o9WocPo25v4RBqxsrvPE72gRUv0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/km.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			RziQoMPOf5AwnmbUk42GlZfi7SY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kn.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3rtptLYd4xxFsOXJY2yL/ObTZGw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mohIHmFrj2ceKadP1Svgl9i04gk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ko.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			HbFmEQKxHwZPZtMaeJsThrR1n28=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/krc.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1S15vhdklQpd1K5Cx47UDy74sjs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/krc.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ksh.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ETQdsqOuXj8AZ53MWl6b91XQ0fk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ksh.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			U10TJYf+MzQ6f3seIsVb6uMKb5w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			z/njCjmFMiyk8d3WQm4BxVn0nd8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lt.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cGRKvh/OVXL8eyWj6eV2NENygOs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lt.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			DYUhyplK6gwGWSoBt6rhgLr+6Ag=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SuTmhKaEMq9fajCK2D4p6lwj+Fk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			RRjL8yy5e92igpykeNYPlGVbeKE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mai.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8wGp5azWnTKS/ckfNwswcOxyUAs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mai.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7hKOe6T/Rw/gcZqjAn/5nyf9qMc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			VnP28dpTdUH99mzW/OIrcob8zRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ml.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9SfEtQrqTMZzIS+t5o0uIvt9kRs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ml.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			7gqZKfLiTedZmDnlzDyMBTrrUOE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			V6OySj6fe+y9VY7yahvBqAGs6Kc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			+0dtON4ZHhZx3PeYhBAA3chWeww=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rQ+7Fm7ktwauhIe5r22TWoXjSHI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ms.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			opBofILjyKpCZ5VpVXKsG2AIVLE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/my.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eJxlSnYUD8BWYV75jxib78v75XA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/my.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			MwQuf/WeuhOVXXzlcNsa/OwnHrs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/31dhq3mgo77S5p3U87KpIRdvXk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			BwUbNyXRxR/xgucgd4AwxA9GEdc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ne.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ooZXlHBuBkQ1uRxluMDTtDUtImc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ne.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			SZ5yZtn4A7t1Y3zMC23OxZ7Nu7o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SVVBxbWbB6QbmsEDXh1VRxeW5Uc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			/CDjdR0oxsFVABulfyKndIyfYvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nqo.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FyWj29nNOYe/J3IhjtwtjI6cwOU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nqo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			1AgohmptMF7hspjq4TUN/7MDPJA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/oc.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kOIxj2CkgvljbwUpK+JmLFjCUlk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/oc.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			Tm2fTW9gen4nALsSlki4C/mEZvc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/om.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ljQ70J6AY7MoE9rDfC3nko1/e1A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/om.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/or.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rcu+U52j8xMAbs/OH/jWbDc/Okw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/or.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pa.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			b6FVDnvemh6iP+D6ZyIHQIRXwo8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5zhMLGkgS8GTvr/YZKFIWqkNEJ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			pGGfNg6I4qtj6PYGxxpNXkneccI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ps.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			u2gyHwCcZMkUpfutNHw6bB4gzHw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ps.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			Yv+BpKya0Hh4jOT6V2FsT94Y4EM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pt-br.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QBut+152m6nEvFM4q/yB2+KaxxE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pt-br.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			onhGrYGQ5yyoASHhpQQ2OgVDsNc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pt.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pUkXHYIeiTFa9ScCI6T2ggtSbR0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pt.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			gxFWu49e4dN6scMhUejVJHOiiLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BJ7FY8cffUK+KGA9ZnyQ3Aq0dTE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ro.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			StEaNT3ligHQOaRySd9b2PrUbc0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mFZ1LYGm4dCV77pVzNxCSs38HJg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ru.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			b3iGKZEtKL4GII27hSL3pJA063E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sa.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SB8NC3p1dzct8XzB1Rj1fxNtNjw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sah.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2fT7ooopSdlSfgGzwBfN7TUT2fc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sah.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			iE657j3rYf0OnNgnaWheazWIDZY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sco.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Zdu2OUtyseaURwmGUtTde7OUBrM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sco.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sd.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x4LcbTgZ4amlNfVtTqWLxRM+jRE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sd.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			g7uhG/TwBvDNQR181gu4pBsgoZg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NIcbazB2ExrNCpMh1sQEwiMvdIo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			lUqiYjog0qrHGeSjUTSJvqauh3Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lXXFErrT6xdu199MOBghF0JyJmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			j/tk6MF6T3aAmRTCrmH0daN4uHQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sq.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			J1JQhxwfdufp0Zzb94vumoIRzn0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sq.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			3CGVGRmrmq7hpbYWJlY3FuCn0uQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sr-EC.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KCuJpo3QCGW0wcEt7Ay30teZcLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sr-EC.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			UJ0R4bxjo3ig2odHFPLyspA1w5M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/su.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			L2b573b2WWMANLsB5Kqi2JBlzY0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/su.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			t9sk9vg4+XWUUOh+EaVkbtb3UDQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			r+d8Q4Pb6QtFiBJpc8mTHXp23GI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			saoR5rOqRA5LYNfvR4+0pNCD/PU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sw.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rqiO0Wp7rNr8tGXC54ZnaAXR/Hs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sw.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ta.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aZ+xw372aACMaBvlTzoCgUcXgQc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ta.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			hXjzpfoitA9lgediy+JBTxs3AHQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tcy.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gncNdKG9pvtzcMDEbsHghp034lU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tcy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/te.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FeRPl6Na6Rz4NIqeaAGEwZCz4cU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/te.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			9YYfG/v8qS8QpLolQMuSH4uY9kM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tg-cyrl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+5EnsJL8CJMJ97egblTYBMdJM8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tg.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			psYVYd3a8fP+NC0UcfPdemgXH1k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/th.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ULCxaXYKFvSn1anlx0edBc8py4k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/th.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			vmpNwKyAs3fJB1BPucc+Gw5YUBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XEvSKMmGBRE3eRVO3jeWN44lYtI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+W2hLwRHQgbZ+7LXdarH0phG8vo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			dHoPCW5NEnh43SvelZwNzRHkvqI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rn0VUzgVZPaM5jVPLxFgBiuVht0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/uk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			I5aIg9qrHOsl55RbdFCQELSPGKs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ur.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1aC+fqC6Hv3jBBTL3zkdPJwayY0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ur.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			i2yIE9gcBmMxS2YgZkzjnt9C3EA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/uz.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XlVwrDaqeLsZgDoeXYNuRe52egw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/uz.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			5UKZqGDt2rHvaOF02saD4/ybe28=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/vec.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RUuJGq2byrqztVpKUwHL1co6ncE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/vec.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			nGXyMxmK7cifDcrfH9xoEU2fPc4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ogrjRtLQXosb+LPVl9nHoJlcrb8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/vi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			MH/NpT0QsyyKcJFetq1IuJ+0c6c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-language-variants.json</key>
		<data>
		cSEZZ4BOU0GnJQdeBMvE/X8sjuQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-languages.json</key>
		<data>
		wliupIajUaWgDYYnBpzsIAgp2I0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/aa.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ab.json</key>
		<data>
		yejb+AEAeOxS+P2taMoF3uzA2Kg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ace.json</key>
		<data>
		yB1uowiysDgBnmXigEsd6bOLJPY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ady.json</key>
		<data>
		Fdxlv3JSsw2Jf/sphpeDJ8xGf1k=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/af.json</key>
		<data>
		xMbUBJNGdFQLEVHEGEoiTAcWxhw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ak.json</key>
		<data>
		OH6L2/YX68oZJJLItE4BK0x7m9Q=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/als.json</key>
		<data>
		xz174qz7xiOzMmZaHt2/WqjgGvU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/alt.json</key>
		<data>
		7vkbzayFHoK513afOVOyMJ0+k+I=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/am.json</key>
		<data>
		xc1QAPrjfCxm/zpRp/jvuzbnsps=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/an.json</key>
		<data>
		6dMhLNhskszKru3SdnJVPy6pyrI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ang.json</key>
		<data>
		9j2SchCIpTVHVS3TvFtq2JCVBEk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ar.json</key>
		<data>
		1V6Z0pBAN1/wy8GyCLp/3mLsuug=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/arc.json</key>
		<data>
		xj3jIpxVtqNqOIDv0KccVlQi2Tg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ary.json</key>
		<data>
		V0kn2M3jsNyqZhuuOQGgTuFD9/s=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/arz.json</key>
		<data>
		83guhWMWz6sPOJTAJTpeJ3XFhNo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/as.json</key>
		<data>
		D1hLFEqO1t2d2SZXFkOf+uUFwYQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ast.json</key>
		<data>
		Wzg9ZQSvOis7RthNzUp2kkgMFVY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/atj.json</key>
		<data>
		z0qQHYZXDcbHDkU9q2U0PAVgCq8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/av.json</key>
		<data>
		EwkvORfCF5kzW/z5YwwUSMsrDls=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/avk.json</key>
		<data>
		CIAocCTN5wpqJPE/pDAGDRZhDDc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/awa.json</key>
		<data>
		58Wtggb8i+fAppqwcR8zdaCCM78=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ay.json</key>
		<data>
		b2xLc4jCU1d92CUlvRUaL2hgkVk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/az.json</key>
		<data>
		R9jnHY86a6e1UpdzvSAp6Hum0XE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/azb.json</key>
		<data>
		62TF1zncBECT55c54I+a8XgoxQM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ba.json</key>
		<data>
		KRm3gVz1uzGMVdBNdVZ+tdzD1Dc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ban.json</key>
		<data>
		QFhDz3TO1w14FR7zB+CIvVpIfXU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bar.json</key>
		<data>
		aaXyU2xeg8RenCBbF0UzWZuaufA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bat-smg.json</key>
		<data>
		R/3gVvUjc/BUkZ7Fa5pmERTWcIQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bcl.json</key>
		<data>
		9qaqIx6KqYJDFctH08GYR3kKOQs=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/be-tarask.json</key>
		<data>
		F9s53s3k6ZitIxLzIUL5qRpxuGE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/be-x-old.json</key>
		<data>
		F9s53s3k6ZitIxLzIUL5qRpxuGE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/be.json</key>
		<data>
		1oPZCYGQ/glNr/JgkDpBwUKL5DM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bg.json</key>
		<data>
		jLDeQm6In1r0Y5aY3LHVInuUkcM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bh.json</key>
		<data>
		oLq+JeZoONcrfk8PSyFsa6CmJFA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bi.json</key>
		<data>
		gjtvtmFQdEgF+GyxNC24oZHhoJQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bjn.json</key>
		<data>
		ZIhY53Mgi9YZ7UarYFkWiGUtmHE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bm.json</key>
		<data>
		zb2YDmigWJC7eZtO/ejbiWwyTIk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bn.json</key>
		<data>
		aqmImTtMvE282xvriGAp8B95YPE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bo.json</key>
		<data>
		OSKH8vkaWS6jOuml69Aw8vdODss=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bpy.json</key>
		<data>
		x3T7pRsZHMGF/AcuP+oWNu6b+q0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/br.json</key>
		<data>
		QP8ta0lzYs6xJeN4PmseUAjuLlQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bs.json</key>
		<data>
		XR86JT1u1x4py9wU19831folUoQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bug.json</key>
		<data>
		mdQA1DIMOpPthy+QqVZZua1s988=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bxr.json</key>
		<data>
		EaZ51Wh+YoAv/ak4mVG2+8kzylU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ca.json</key>
		<data>
		xAJfH2wDfuPkeacdogIoQahK/5s=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cbk-zam.json</key>
		<data>
		GytT2pSO2uvtMU3D5XLSdDeDbR4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cdo.json</key>
		<data>
		1Xcs+gZBng8NooDgoj/dXADG8y4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ce.json</key>
		<data>
		M86S4zxnPcx0IPZUJKTmbNJM2AA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ceb.json</key>
		<data>
		w6i4jJkt+FM6B4VgR3Qrkpo0F7c=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ch.json</key>
		<data>
		qP0UXT3xTPcu1bCyPgJPCUChIWs=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cho.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/chr.json</key>
		<data>
		dut1YjBvxRzJdkua2qU6sYiz3o0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/chy.json</key>
		<data>
		bG98RiHH2lN/RzqA+AffYXdxrdQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ckb.json</key>
		<data>
		HV6eoMVIyaA0hsLHLjMVOlgaOKY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/co.json</key>
		<data>
		1Z6FYMc33Zyf42vkIYf6GVP7Jns=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cr.json</key>
		<data>
		uNZ2qGvqi+i2UbO4iCkufTiFtzw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/crh.json</key>
		<data>
		C5E/S553D3Acj/4J3mSHCUf8lmg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cs.json</key>
		<data>
		FT2SK2ghfSd4Awu01MD7kYEqml0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/csb.json</key>
		<data>
		a7DgFSVRotPLylQASYBnuHiHNY4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cu.json</key>
		<data>
		tC83IRC4PR6KGu2GZQxeV3Aw5H8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cv.json</key>
		<data>
		9yImRF6u9OkroIhieliBPWOXefA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cy.json</key>
		<data>
		AW5+MwM41M9SS9ktdKTsClEDFyo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/da.json</key>
		<data>
		NE0ZKVaxJVwtSHYtXoj3gozmy8E=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/de.json</key>
		<data>
		ji5UVwH1bqTN2Dwsp0geiTT7YC0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/din.json</key>
		<data>
		lsnvSWsIv9dYKn4zTSY51dr1tfQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/diq.json</key>
		<data>
		ossi6CdRHQhTWrO/PJVJpQ5HGfM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/dsb.json</key>
		<data>
		B6kuIa8AbSigb3ChMThvKSvcfJ8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/dty.json</key>
		<data>
		zJpghfLJO327vnnb4EMi0hCb86E=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/dv.json</key>
		<data>
		L6VvXwae5oxmTEz6sDklgFI6+18=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/dz.json</key>
		<data>
		P88ZEDgEIASeZ8q6lMcHprahrEU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ee.json</key>
		<data>
		kaciPDxqWMajCLKNEn1mNiDuX+8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/el.json</key>
		<data>
		shnPTJ3wmNivMh3wUGR7hqT4EBQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/eml.json</key>
		<data>
		4K/4WtgcOpDCyryk1k2whSjiJIQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/en.json</key>
		<data>
		veMs7tDzBzmHaGbwgWEuQJoQoPw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/eo.json</key>
		<data>
		Qf63fDfMLHIWVJZao21+sKsPrXY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/es.json</key>
		<data>
		BVb41r1MEYKuD8hMqhcN7JedKik=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/et.json</key>
		<data>
		xWsRGCSHELQDndDl5hQT/8COCSY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/eu.json</key>
		<data>
		RtXYGXcIfGzT5PGAzahgbEuZJWw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ext.json</key>
		<data>
		7vbC9TvhFdykw2urVHxtIBu17OA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fa.json</key>
		<data>
		WDSBRW6xu5cgJIGS1vPlqbqaW5o=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ff.json</key>
		<data>
		YkBXmV/LTquiyyJs8HV7men+TNM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fi.json</key>
		<data>
		wKuf9jm+lOlVfnTMP16ZeGz6xHM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fiu-vro.json</key>
		<data>
		Zs8VsyXBAyrE2Nnx+kERb72emzk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fj.json</key>
		<data>
		GG97wXs5NPIhe+G10CF4dap5yiI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fo.json</key>
		<data>
		LVFrMmMUXk5KnYrTPI+rG5eF2fw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fr.json</key>
		<data>
		r0967k4Mbb+ikc4X+KMWz17/tRA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/frp.json</key>
		<data>
		pGww0JRV0RtGmD3ktisu4n3e9m4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/frr.json</key>
		<data>
		/0Kj75r1UIdB3Cwxw3RDMU0CNRY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fur.json</key>
		<data>
		NHXC+/ZY2KfU0G/LEVkuRVrlxX8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fy.json</key>
		<data>
		HxLaW/Mc1doisrOVxa736NNzrh0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ga.json</key>
		<data>
		BBTMCEXVE49E1PDzrI5ue86VCF4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gag.json</key>
		<data>
		om25uA420qA57sfQ1FFRmuhpt5s=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gan.json</key>
		<data>
		ENJY6Wp38NmvT4xiUKNNwmkK+ls=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gcr.json</key>
		<data>
		pPFtYq8gdCiwwOf6/kJd+tayOmo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gd.json</key>
		<data>
		xEjSFJ7ee2rr5R7+z+5iOc9RVWo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gl.json</key>
		<data>
		/TQ0jMl1VC5r9Or2T9iKNzALmmk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/glk.json</key>
		<data>
		qAnqjVQpVWatKLdN0uwlZGSntRU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gn.json</key>
		<data>
		J28cmXngzLhqHvhPiCMtin6sG8M=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gom.json</key>
		<data>
		+DMPi1yUZwM0FN1D3lDxbtR/uiA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gor.json</key>
		<data>
		RuzP/UmZfF2SjgOESTnkqpYG82Y=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/got.json</key>
		<data>
		JM175XvFab2NZzxdJA4yJFEZK7w=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gu.json</key>
		<data>
		1fBUmuBsbIy2BYyocug5ETcGp6M=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gv.json</key>
		<data>
		83NjE0d2Ff5Iwid1HSWMw+kzv7w=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ha.json</key>
		<data>
		Es8OnxaK64AR3E3hn1UsvyCGVvo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hak.json</key>
		<data>
		9XSi8rxwJsHKn2D9IWApaiznl3A=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/haw.json</key>
		<data>
		T+6YHFgz4cbOoCQUYzJWACBLYwE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/he.json</key>
		<data>
		HCmnO2rTAB9NXYyVZNuuw4F7xiM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hi.json</key>
		<data>
		ZL/4g1KwIEMxqlaX94vhkRy21LY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hif.json</key>
		<data>
		bmwbDeaCBpY4O2tsrEUwgznI+94=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ho.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hr.json</key>
		<data>
		Xw3UPH615TA7TevNzfhUIYJ/q0w=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hsb.json</key>
		<data>
		hcHL8Jb9JvxrWUDPZjPFk+iFgU0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ht.json</key>
		<data>
		0UwN9A3XI9FXTrmG70tWRex30Vc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hu.json</key>
		<data>
		7oOAHjzFPLJDVMoa1qQDdkZcx8s=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hy.json</key>
		<data>
		FTL+R8OzOXWji1s9f0/I3O1MG68=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hyw.json</key>
		<data>
		ovcVrZHxHjMCfKA29c35Tu/6n2U=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hz.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ia.json</key>
		<data>
		j0s18b+P9mPs6u5rGou05l2xXsU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/id.json</key>
		<data>
		5SfizABTPrnPsyH6lksNv/Gowa4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ie.json</key>
		<data>
		PkMKf60f3r5xcoaMoi2rlqAt1uQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ig.json</key>
		<data>
		Rh39E0VDCmMZot6L/y1tdZ/TXJY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ii.json</key>
		<data>
		LyW+GTczPgI0+SEII8OX35AgbOA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ik.json</key>
		<data>
		vJFj2QInk1Iy3uxhPszYlRLjuXg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ilo.json</key>
		<data>
		E31gn6Lib95pHqIVUdd2hcuNtvI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/inh.json</key>
		<data>
		ZWLiR2mckJJD49NEkRXShmHEczA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/io.json</key>
		<data>
		Z0Wf5+kdKNTWHuvBsPzZ7XV7sP0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/is.json</key>
		<data>
		2w+FWskbhMtSO7B8dCavVmviDj8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/it.json</key>
		<data>
		r3osiysZsYFN8ehf/qnOLirxVqU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/iu.json</key>
		<data>
		B/V03DiOga7oN72MrWaEzqMQYuo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ja.json</key>
		<data>
		iyRSsm02C5gQWRBsHA8b2UoAFFw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/jam.json</key>
		<data>
		PBEf/iFEzjFtovurpf/y8nRH+zo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/jbo.json</key>
		<data>
		fyhglXFVtG1IUgBRH8qGl0S9q6k=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/jv.json</key>
		<data>
		8ocWdpjG+gWKkpxxn5k2r2IycSA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ka.json</key>
		<data>
		gBmzCS+WiIuXy/jYE7WUINKb4gE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kaa.json</key>
		<data>
		wthV/l6xXP6XRwHpFXbjxtWxvZ8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kab.json</key>
		<data>
		iloAfmN/xNquIdsnYecq9G1r158=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kbd.json</key>
		<data>
		Q5DuurSaIdCRK+tQkLAbe00pUoI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kbp.json</key>
		<data>
		TOdzfxeZh3qSoENnkctNyJ1SpCY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kg.json</key>
		<data>
		eBdyahPq5/4HI70gh1KjaqeDujY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ki.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kj.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kk.json</key>
		<data>
		dYF8wRc6qz26G65waY8rtL+/7cM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kl.json</key>
		<data>
		6o8L96Z+B+yN7IRN4C7S1ROAiOU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/km.json</key>
		<data>
		Jzww2r6KbrF4wLLZQtvbyPMKsFA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kn.json</key>
		<data>
		cM9avpzXvMrpxWiNUvUo0h+oLcc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ko.json</key>
		<data>
		L9/GKr8BdGuS/CNVsFpWWyHJ1Os=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/koi.json</key>
		<data>
		wYWvsZadz2qNiPTsXfiUtC6/78M=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kr.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/krc.json</key>
		<data>
		KkY+TPKOX9vrfA3iQ/MD+SjMRR4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ks.json</key>
		<data>
		B9LgznW7rlhLUUt+OlVyeM82+tY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ksh.json</key>
		<data>
		7+hita0TldD8uMhecNwhW7z95ic=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ku.json</key>
		<data>
		RNHufqv3HFTkqLnIR2VveSEo9wo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kv.json</key>
		<data>
		p8vZeD7U52V+gcQI7MrSIOITLIU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kw.json</key>
		<data>
		B/kooCjnUEDeON1f9Qd8MSN29yg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ky.json</key>
		<data>
		/QBWPd7cYtWT4USM5vlkqzimonU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/la.json</key>
		<data>
		/XebkRCBDlZGJ9iyRwyUW3RUupA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lad.json</key>
		<data>
		cTIx9Y7x1DlenBQFv9sRI7u+Lrw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lb.json</key>
		<data>
		HERzAFQCFIxlc5L2N0ghIWz9u08=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lbe.json</key>
		<data>
		Dd0fjo/rsBymit2NmhozTQbSy3w=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lez.json</key>
		<data>
		z45Y94WsEPl23N6CYAa127dKgNE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lfn.json</key>
		<data>
		BC/LUnSCFh8MnkYgQvXSQh2GO2U=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lg.json</key>
		<data>
		TDdd3YbEwWiohrAxkZ2X+x+BP+s=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/li.json</key>
		<data>
		XVogR5BcsWFlKH33QylUB5zxMg8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lij.json</key>
		<data>
		COw+zEKtqSrfgEp/SXGVFweWP3g=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lld.json</key>
		<data>
		H4iZz/qMAOC2n32EEmeDmQ0nOBE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lmo.json</key>
		<data>
		espLDO+WPYuKEv06xTomsWEVeP0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ln.json</key>
		<data>
		jekq5lYo6ysfCZnJ144Ky/AIU6c=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lo.json</key>
		<data>
		cZBM9Ux8W1cA2qIxGwaEvkBVBxI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lrc.json</key>
		<data>
		ly2eHns5eVkgp/hrxBPOK3B0oAo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lt.json</key>
		<data>
		uUYc+8EHi1xh3+I3gHYAXspOEZQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ltg.json</key>
		<data>
		mAejBonY8QHoyHZCQEq2MWDT51k=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lv.json</key>
		<data>
		tYgh85Mbt9+IhVyiTtmVI4mMOpw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mai.json</key>
		<data>
		j+xOwvUwf4hy4pX4/jwIPjZSnq0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/map-bms.json</key>
		<data>
		j5vmAwTPPXkuq1RG3N1ZuQYYzHM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mdf.json</key>
		<data>
		2ENeSVpXrW/Lu9SOGm0TVvSx3ck=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mg.json</key>
		<data>
		g8yOny2jHu7r3y3Bl/8BiGj8BPs=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mh.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mhr.json</key>
		<data>
		+ugfHWDRVYXpPjOYr5thuTNtgdo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mi.json</key>
		<data>
		iaRpgCjw1By3Yvq/z066q01x7Uk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/min.json</key>
		<data>
		6qJdS05U78WTRkisPudO48NTdow=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mk.json</key>
		<data>
		O3eIKeyp4tP3ANviGGX1nWx/0R4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ml.json</key>
		<data>
		k6iVfjI4nZohdAxi2VkGzKUNo4k=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mn.json</key>
		<data>
		dekdKU6JPkTQsT9Uqa+xI+sAIpo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mnw.json</key>
		<data>
		ooa/KIpZkK33bQB75wKiAP2LbMA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mr.json</key>
		<data>
		IuwB6eCHJSdT/o+Tn3GifmmgFOc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mrj.json</key>
		<data>
		jnp1MRDPwBos4aUaHVkI20+WZv0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ms.json</key>
		<data>
		mMTAVxgaK2gBJqCjcXAlufZ1SB8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mt.json</key>
		<data>
		OjguUol1Azn9hLeXnLJlFSjdNI4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mus.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mwl.json</key>
		<data>
		qp/k6fcE7m8nHcdnMtOIWquUDQQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/my.json</key>
		<data>
		/YjizNCM+mGBU1cbXTthKkoK2Ho=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/myv.json</key>
		<data>
		1kBZ0P91oRImBnb7OWTSftVZi5E=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mzn.json</key>
		<data>
		yAJe/3w9kPYdB0h3SDOpcI5i/lA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/na.json</key>
		<data>
		YkxJEy/9kX1Bi62uYnK2Pk1enKg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nah.json</key>
		<data>
		Hs5gNpUt/GsQtTiGWoTTVxNYSPA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nap.json</key>
		<data>
		TcgBqcBrbU4j/Xh0zWpShyRxHp8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nds-nl.json</key>
		<data>
		vNrmli5VY9J8i7UPH4OBRboukCs=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nds.json</key>
		<data>
		w0ME5tRqtuL7R5jTGgNJold9k8s=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ne.json</key>
		<data>
		5Vv38Zgx5/0eqBIflCZ9oXxXvwg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/new.json</key>
		<data>
		+OerPrTfNFGqkaRavFtNqvzdp1Q=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ng.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nl.json</key>
		<data>
		0iUvgag3HfevKH8RvvFg4ytd+zk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nn.json</key>
		<data>
		W0xiYkqu55XGrlKQDjFgsdbu3C8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/no.json</key>
		<data>
		HM6xRFVZ36memTUDPn4VZ1Wqipw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nov.json</key>
		<data>
		1L0RfcP5jimaGok8lyg5m4ZWIro=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nqo.json</key>
		<data>
		pYpRxGIAXw18pjYIBJEsoFGFxm4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nrm.json</key>
		<data>
		mm0MEBUlOwe0nwsLHfU6akqMz7Q=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nso.json</key>
		<data>
		OASAirO2gl5dzns5mynkwwYQHbU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nv.json</key>
		<data>
		WOxsYl5sCj0YqzBCx+KzvudM3dQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ny.json</key>
		<data>
		zP7PQRJj6PI+xtA3ScS5wnxY/HU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/oc.json</key>
		<data>
		fJ1Ihoo5WCLgNb2cn2tn5d5O58M=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/olo.json</key>
		<data>
		MxuZTaVPcjafHCtezWNFClXx8SI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/om.json</key>
		<data>
		lXb92bj2zfiteTuWgFmoZn1ZTAY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/or.json</key>
		<data>
		k52FMMCmnys+J3QOBy8Cc6A96dg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/os.json</key>
		<data>
		NrxmTq/qJjx+V+MtZwmz15WinSE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pa.json</key>
		<data>
		DvaWqBR7et/8i2tFxLXQkRA1u+Y=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pag.json</key>
		<data>
		gQvo9SmIOyeSKVAqAAUYPllx0lw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pam.json</key>
		<data>
		kMchNxb2TqW9SUsI7U0nua0VT/s=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pap.json</key>
		<data>
		e41MYVw+GGKSKB3JkaF4tlqtTKM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pcd.json</key>
		<data>
		6tpjE99iMXOH1P4psQ1mPV4A8RI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pdc.json</key>
		<data>
		77yZdxZ8gnkOMu45PAo+GS2RBew=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pfl.json</key>
		<data>
		zXDaw3nMSLGNQEQ9gIsJ03RT0jE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pi.json</key>
		<data>
		7lJ7ub7UGDTjy20fSRWd2GJ/Skc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pih.json</key>
		<data>
		ZVK65D7StnZRQ/YYstVLA3Wzl8g=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pl.json</key>
		<data>
		fYhL4MIdLxs6gt0UlW2tClhVfns=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pms.json</key>
		<data>
		qNnhNc3vaIDC0NZRk7yOwhDw3Dg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pnb.json</key>
		<data>
		Qv5mBHRCyHT7tK9X4bx+wyAcHRY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pnt.json</key>
		<data>
		3PJ/ta+dmc2SBLColne+mvT8cJE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ps.json</key>
		<data>
		Hw1EIx2z+ZddSg0KYn5aS+Y60iM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pt.json</key>
		<data>
		RqH/i1QU5TT6vVG7Urv3wgXK94M=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/qu.json</key>
		<data>
		14bbV8PCLzFPNLgkxLRj/3ILhrA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rm.json</key>
		<data>
		saQbFp3p0xY5DgB/vifcCfndGuA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rmy.json</key>
		<data>
		bq2LbGf5Vs1R0ZTl8Uwh+RXxvp0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rn.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ro.json</key>
		<data>
		NmEaps7KUuhWpHSRP5GRKd3pTHA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/roa-rup.json</key>
		<data>
		WqXxtdttqCPZngXADNF+Xb0zdaM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/roa-tara.json</key>
		<data>
		RjYTwPnJUFtUmA37i+N95OXRp2E=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ru.json</key>
		<data>
		i1kk784q+dsiPNyZybQSz7d/nNc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rue.json</key>
		<data>
		+QPV+ChZ16nx3xnQoDaND5jn5Ew=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rw.json</key>
		<data>
		D1AVQPoc4/wTeat0o+P0xXAoShc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sa.json</key>
		<data>
		C8rUhMA9dRhEOzSM5cpIZH5sahc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sah.json</key>
		<data>
		jUKmdP9JmCCyuUf9xuiBqKvvItI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sat.json</key>
		<data>
		JZIhIlW1Dg/pCpvx+1V4R19mv/A=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sc.json</key>
		<data>
		/OubT4dnOwe6rbf4M9E5yLfElPQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/scn.json</key>
		<data>
		9QhOqpxsg3vrTzSUYMD8oRxm4gY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sco.json</key>
		<data>
		vHD/wwc/1dFi966p3gVI/QN9nqU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sd.json</key>
		<data>
		mFGpSLzV98/ARpdn+m5NCOM7sXQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/se.json</key>
		<data>
		rRSYMXws/EgKPYXvcmXRNwI5Vyk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sg.json</key>
		<data>
		Fj6MACaQrzZOBbHVQL6Zxor4h8E=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sh.json</key>
		<data>
		Ccgzsd3WkT7q1c7XoBgwAPuFwFI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/shn.json</key>
		<data>
		tm1y9I2hPX1/zWavRGlsEcrY464=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/si.json</key>
		<data>
		4IXox4KoNS96AptF+APahhLvnUo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/simple.json</key>
		<data>
		BtCzyS2A71T7S+rvZ34bD4oShBI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sk.json</key>
		<data>
		uWrF60gbS6gwWsiEMIHqyj0rTno=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sl.json</key>
		<data>
		aOhJXzqdMqzDw2+oBnbr6GuUoz0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sm.json</key>
		<data>
		8ny1eQpNQeu/ISRcIiUsbsMjc84=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sn.json</key>
		<data>
		aKpoN0l+G9Om338Xen0YqmJ2288=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/so.json</key>
		<data>
		aGZzTagJnkGAMfUvLjKI/tR3EbE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sq.json</key>
		<data>
		Ko2owvXrj86Jqo6ZFUdSh5xnY4g=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sr.json</key>
		<data>
		ovPxd7okTq1Eq4QgGv4VDRlWZA8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/srn.json</key>
		<data>
		hzHHSXLoXNfXycQpp46o6BuvCsU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ss.json</key>
		<data>
		Ez4g1x+AhvAhQUQn6Td6QUdKzX0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/st.json</key>
		<data>
		3hGsMpcHgWsrvlzId4FT7FxOxXA=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/stq.json</key>
		<data>
		UjbawkHhK2IQsfYgBtTH55QVl2g=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/su.json</key>
		<data>
		VpeFZ/LZBzd4LcY6g15EqBDve50=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sv.json</key>
		<data>
		EJRWn08Pl8WIkM83JDpGgvlydRM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sw.json</key>
		<data>
		wniZyo/99c5KGk5XQjhINgUk6Ms=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/szl.json</key>
		<data>
		3USrdvYckGK1ypThXBlvb+3athM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/szy.json</key>
		<data>
		cw6Fx9JkuIoQBqmlvZKkydYxIV0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ta.json</key>
		<data>
		4jTVAj9n2/yQURMxL7W/m3E+YYk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tcy.json</key>
		<data>
		nFr90AIQDfvW7ykOGxMXaaB08uk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/te.json</key>
		<data>
		AAO3x39sTvqlrpgK/SM7COtbaMo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/test.json</key>
		<data>
		+NpCS0spNTRR2yIqHamzXAG/aew=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tet.json</key>
		<data>
		m+QV7fFvsdtJrC3AgunztDDCG44=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tg.json</key>
		<data>
		b2HUuiXANjRcEFqB2BX6hcH5Qg0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/th.json</key>
		<data>
		Vms9wYjKlkheep58NehgPn3dcYc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ti.json</key>
		<data>
		6SZNU0uyy9H11kRblW5xIDCIhpY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tk.json</key>
		<data>
		rhsBd5qibGodoVe2YyzyQxT6Ahw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tl.json</key>
		<data>
		Wzu7Y/ILSPW2GJylK8Cq2DVxOpg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tn.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/to.json</key>
		<data>
		imRsIcm/unhr7oj7gs9OLuK6l8M=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tpi.json</key>
		<data>
		sfE3ItBcvPchE9KMPKxvDg7L2YU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tr.json</key>
		<data>
		UI3kjbI9VhH2q6/iGP8NziFF6hc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/trv.json</key>
		<data>
		s+mpG1rRT/K/CrJa7PfpTd0ONGo=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ts.json</key>
		<data>
		KwKhCgKFQ0ltoIoo3p8zdCdko1M=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tt.json</key>
		<data>
		2mdIfID2OhdDyNpQf9f2xsvj5PM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tum.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tw.json</key>
		<data>
		YmDLUCrNaskH17kj+OrXbn+eEwM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ty.json</key>
		<data>
		4NQC/09k8jpUJjG2Yt5K/Ls+bFE=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tyv.json</key>
		<data>
		US8wtyYhQANaDPtjZEDZxuzrbfk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/udm.json</key>
		<data>
		GvBbNxqNAfEqs3QRcES/Ki5iD04=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ug.json</key>
		<data>
		f9SZ/xZW+VEMD8wJW+ErIRG8RqQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/uk.json</key>
		<data>
		Og23eAmCQSiU/WyAgjf2A5TzhEk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ur.json</key>
		<data>
		LiPsC5Kxaw46ONhGj2656Hb8mhY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/uz.json</key>
		<data>
		D6uxRunfP5gyuT0eDeB+ajhfZOc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ve.json</key>
		<data>
		MJ7/alqf0+ulpz+m9L0BRm3dIR8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vec.json</key>
		<data>
		5EZBT1DohFA9GWRA2bPQwjDPPw4=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vep.json</key>
		<data>
		hXSbmILZnL7t76WE4XTsvnydsV0=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vi.json</key>
		<data>
		WpauKshTn7rafKSjwevBbo6sTU8=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vls.json</key>
		<data>
		ZN+90oDTFWFxhcnQMSlENmbz/NI=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vo.json</key>
		<data>
		ADj2mm0PGsZZH+Zneo+vNiAUWJU=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/wa.json</key>
		<data>
		dj2O3yPmDgx9z0Ws1L4NaOK0MrM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/war.json</key>
		<data>
		B8N5svWnqydDFb/RR65NxOD11ic=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/wo.json</key>
		<data>
		b/pumlRrI2LJid2tJV1ubHU+5Lc=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/wuu.json</key>
		<data>
		7J6s9ILP/4b5ibeL19Zq35L7JZg=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/xal.json</key>
		<data>
		peDkfd6ih5eCyhAqtbgUqiDf+Fk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/xh.json</key>
		<data>
		vNv7IbgpM08TAGw/Ry2DmjOtCgM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/xmf.json</key>
		<data>
		8esrfAJpnEPqbTF2fkyFwrFxprM=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/yi.json</key>
		<data>
		tB9AXN+7N4UpiU1uErHqKTVtlzw=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/yo.json</key>
		<data>
		eGXV3XEdRVREdXbZWW1fUZ6TaCY=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/yue.json</key>
		<data>
		UY2xcDmGnEErcgnC5uuaXPtjHJk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/za.json</key>
		<data>
		+m0DXGhdH9ofjireba8OfXbY8ak=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zea.json</key>
		<data>
		2dMFJ3i2kTFBxBukwG1wBufJAsk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zh-classical.json</key>
		<data>
		flac7qDnjDFzRkFEOu9LhbeY+7s=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zh-min-nan.json</key>
		<data>
		IBcp7/qtwQPvd9qThC82tE8I10A=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zh-yue.json</key>
		<data>
		UY2xcDmGnEErcgnC5uuaXPtjHJk=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zh.json</key>
		<data>
		g4DuNS/JVOqaUE+WsbrmpA6tONQ=
		</data>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zu.json</key>
		<data>
		lWhh/Oj9vEZJaISRJM5WZL7St6o=
		</data>
		<key>Frameworks/WMF.framework/yi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AJwiMYrvbx5xiuuNJ9hKrC6XM4U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/yi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			XDRbOYyNzuNdDPBrBrrKMbGIBg4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lnTwT8cjmjoikSqyOIGlXJBkLRs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/zh-Hans.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			S6xGWTthfmmHje/vBLdJJMd6w2Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/zh-hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FmTYFkxbHbX3mJA19K6M8XWGuMo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/zh-hant.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			EXMlqdD6ID+KEhrbQTNKWnLlOdI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>HintViewController.nib</key>
		<data>
		MIEt3e3odXb5fm9ai5z9QzYUQgM=
		</data>
		<key>ImageDimmingExampleViewController.nib</key>
		<data>
		T2Hxhq+cnaf8QYsMzJkwQiFBm7I=
		</data>
		<key>Info.plist</key>
		<data>
		PRhZvdoygK4PG6H6Z3tgs9NmGrc=
		</data>
		<key>InsertMediaCustomImageSizeSettingTableViewCell.nib</key>
		<data>
		GBF3/E3w/bdqbSk3tkqdEXAlzIc=
		</data>
		<key>InsertMediaImageInfoView.nib</key>
		<data>
		U1i8Se7FJkXEO1FqWXZrPSNxvGk=
		</data>
		<key>InsertMediaSearchResultPreviewingViewController.nib</key>
		<data>
		Nui4+koAdBa9Kdxiez66znvkcic=
		</data>
		<key>InsertMediaSettingsButtonView.nib</key>
		<data>
		LrNY1AmAJ/ACm4vHaQABkuAFJCo=
		</data>
		<key>InsertMediaSettingsImageView.nib</key>
		<data>
		joOCyYz+vV4iFSBVkwX0Cz0ynd0=
		</data>
		<key>InsertMediaSettingsTextTableViewCell.nib</key>
		<data>
		tz8Wyb6s5c4QUqg2LFHtVRGzis0=
		</data>
		<key>Launch Screen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		RDjJ30gwomL9T6D9FaNa8kF02X4=
		</data>
		<key>Launch Screen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Launch Screen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		U0x8EM0781fCPb62BbZIXKeDlZ4=
		</data>
		<key>LibrariesUsed.plist</key>
		<data>
		GmIenv8YkhYePomUbewaq3mfOLA=
		</data>
		<key>LibrariesUsed.storyboardc/Info.plist</key>
		<data>
		wKJTOKrMtpPs8DTYCk4tS9ATyCM=
		</data>
		<key>LibrariesUsed.storyboardc/LibrariesUsedViewController.nib</key>
		<data>
		n3IB8MqtKUfv+GoV0isO5unLFF0=
		</data>
		<key>LibrariesUsed.storyboardc/LibraryUsedViewController.nib</key>
		<data>
		21PXPHDW3zZOQ23SSxgIe66Mfs0=
		</data>
		<key>LibrariesUsed.storyboardc/RAl-lu-nmB-view-Lt1-Fz-AJg.nib</key>
		<data>
		LB52mb2H1zU6//aegtIC0//OH+Q=
		</data>
		<key>LibrariesUsed.storyboardc/Var-9e-kfX-view-QKe-YT-pRO.nib</key>
		<data>
		mu3RYZbAfZVbPTUxzbaypJOX/xY=
		</data>
		<key>LoadingAnimationViewController.nib</key>
		<data>
		XPCvmmXfw645bgkULctnIkaX6MU=
		</data>
		<key>NYTPhotoViewer.bundle/NYTPhotoViewerCloseButtonX.png</key>
		<data>
		4upCs8I8MyHtJHoQQ00Ya2nmh58=
		</data>
		<key>NYTPhotoViewer.bundle/<EMAIL></key>
		<data>
		zfKkovJ0Zs0/AmJOg/yAKdf2iHI=
		</data>
		<key>NYTPhotoViewer.bundle/<EMAIL></key>
		<data>
		OvB7ZfbpWjlayKeVLJYP08ho7FE=
		</data>
		<key>NYTPhotoViewer.bundle/NYTPhotoViewerCloseButtonXLandscape.png</key>
		<data>
		1HC9yrTDhp0sdBB+1k/URtZB5fU=
		</data>
		<key>NYTPhotoViewer.bundle/<EMAIL></key>
		<data>
		T0FFjVnaJA9Ee+ioEdSBK7mrjYE=
		</data>
		<key>NYTPhotoViewer.bundle/<EMAIL></key>
		<data>
		hUnRk6hkU9Z8NPWkTTEHmNzFGD8=
		</data>
		<key>NewsCollectionViewHeader.nib</key>
		<data>
		seVQwMJEW1jRHKhzLvmGkC2H+Is=
		</data>
		<key>NotificationBackgroundError.png</key>
		<data>
		Qic1Rpv8QmxUPpleNi2NrFXhnOQ=
		</data>
		<key><EMAIL></key>
		<data>
		eCfCrlLS7FpiOZtXYjOEIOEX7HI=
		</data>
		<key>NotificationBackgroundErrorIcon.png</key>
		<data>
		1/COQl8YB39Bp3nJubovdyb8Fvc=
		</data>
		<key><EMAIL></key>
		<data>
		wnksDlAFkn2kMJ/LpyR4nPfPBmw=
		</data>
		<key>NotificationBackgroundMessage.png</key>
		<data>
		Qg7rdz3zxru3iCBydGk3HPx9Rqc=
		</data>
		<key><EMAIL></key>
		<data>
		dXqinMjdATRmEp8UsAAWU03NxP4=
		</data>
		<key>NotificationBackgroundSuccess.png</key>
		<data>
		Z6u7LJL4oE7WM14DZkL1sklrXX0=
		</data>
		<key><EMAIL></key>
		<data>
		zES6yUQFPwHiclWTAhsr+D8BGzc=
		</data>
		<key>NotificationBackgroundSuccessIcon.png</key>
		<data>
		ILoXNchteNDC6TjAJIPYxrFoV5I=
		</data>
		<key><EMAIL></key>
		<data>
		CJp8asXfWNprqaOOdPTiRIVNS/0=
		</data>
		<key>NotificationBackgroundWarning.png</key>
		<data>
		EJhGSYbAMUAof5/vLphczBmGEGw=
		</data>
		<key><EMAIL></key>
		<data>
		K2C7Gn7db+hdLo/f+wcgym0xXy4=
		</data>
		<key>NotificationBackgroundWarningIcon.png</key>
		<data>
		a58B7UbhqHniIrsqwhjQZZ6bEE4=
		</data>
		<key><EMAIL></key>
		<data>
		NfDLao7VLBo90Q0LDP0lOuhjcA4=
		</data>
		<key>NotificationButtonBackground.png</key>
		<data>
		J+0lFAO83oNHMxw+rgOF8zsRW24=
		</data>
		<key><EMAIL></key>
		<data>
		/B4xhNHQYvAwdqpjdMBzfptndfA=
		</data>
		<key>OldTalkPageHeaderView.nib</key>
		<data>
		GH3HFdqwec3iTDtHCKJq06BSkgI=
		</data>
		<key>OnThisDayViewControllerHeader.nib</key>
		<data>
		U+96QRnXNHoB/d800uTb/J/sVd4=
		</data>
		<key>PageHistoryComparisonSelectionViewController.nib</key>
		<data>
		lgNTw+6BzC6628mLR+4HU7Mw9K8=
		</data>
		<key>PageHistoryCountsViewController.nib</key>
		<data>
		PuQO3Cvhj4cztRXQQtv0TQgROCo=
		</data>
		<key>PageHistoryFilterCountCollectionViewCell.nib</key>
		<data>
		IcWSy4EDUJZw2WmgpExLnF6sZMM=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Places.storyboardc/Info.plist</key>
		<data>
		Zz68m+QvJ5llNNFLGQi+u4WJRIw=
		</data>
		<key>Places.storyboardc/Places.nib</key>
		<data>
		ZWzzIFwrzkaqMaEMt/HZ+4aUlKc=
		</data>
		<key>Places.storyboardc/UIViewController-pK5-Ai-Kzp.nib</key>
		<data>
		wQ0L7KFf/iJFBlMNQsVUO2NWduw=
		</data>
		<key>Places.storyboardc/pK5-Ai-Kzp-view-pBD-gM-dPf.nib</key>
		<data>
		Zq1lv2lIQriZgh8Tb6I4TmV8bGs=
		</data>
		<key>PlacesSearchSuggestionTableViewCell.nib</key>
		<data>
		7GN25jlrYZrBVjHGTDsHchWQvZs=
		</data>
		<key>PlugIns/ContinueReadingWidget.appex/Base.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cEfGGYjSufquKwjwGzLJzyNvyrM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			0MIPa0qiDjReRwwobTsnJ01z+l0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<dict>
			<key>hash</key>
			<data>
			bMg1hW+qXlrCQA8PBvq3MTLxFyw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<dict>
			<key>hash</key>
			<data>
			WIcNPfYQNR29DirP3NOTyzUN5jE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/ContinueReadingWidget</key>
		<data>
		UENFlLyuKimclGMABtNzzDEiSyU=
		</data>
		<key>PlugIns/ContinueReadingWidget.appex/Info.plist</key>
		<data>
		IChb407IZdih+45dJirLHgK6FuI=
		</data>
		<key>PlugIns/ContinueReadingWidget.appex/_CodeSignature/CodeResources</key>
		<data>
		3R+RBazMY2yDQMw65NjWCnFr3TE=
		</data>
		<key>PlugIns/ContinueReadingWidget.appex/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cEfGGYjSufquKwjwGzLJzyNvyrM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			0MIPa0qiDjReRwwobTsnJ01z+l0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<dict>
			<key>hash</key>
			<data>
			avP7r9rv18Lm8ENT4oKUFbu+GqQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<dict>
			<key>hash</key>
			<data>
			4r63aTp8hfdeXr78RPz5R9y7QBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/FeaturedArticleWidget</key>
		<data>
		L+IkI3LDRikXdnc0vEIlNTqsa5Q=
		</data>
		<key>PlugIns/FeaturedArticleWidget.appex/Info.plist</key>
		<data>
		sgqWaKfQW33PdbFhZ6LV0sCZB1M=
		</data>
		<key>PlugIns/FeaturedArticleWidget.appex/_CodeSignature/CodeResources</key>
		<data>
		3aB5zrN/zC7qpNAwD1xGTbpFr4A=
		</data>
		<key>PlugIns/NotificationServiceExtension.appex/Info.plist</key>
		<data>
		RImPspRd1cc+UnAfN4M432xDSFM=
		</data>
		<key>PlugIns/NotificationServiceExtension.appex/NotificationServiceExtension</key>
		<data>
		KxrOVcS11/g3BdDNzh++7Py+BAk=
		</data>
		<key>PlugIns/NotificationServiceExtension.appex/_CodeSignature/CodeResources</key>
		<data>
		lcPPdqefxfSq9u707KfnEIJvmhA=
		</data>
		<key>PlugIns/TopReadWidget.appex/Assets.car</key>
		<data>
		fQZ34tjaSYGFFeGEOI12IdN0CWU=
		</data>
		<key>PlugIns/TopReadWidget.appex/Base.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YUvHXdacPYbeo67wT2HsAMx+bWU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			0MIPa0qiDjReRwwobTsnJ01z+l0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Mj+UqIePeD3fZVvbBtO1DovMmmU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<dict>
			<key>hash</key>
			<data>
			aaUtExK8wxGkTdbkloZytUecYPA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Info.plist</key>
		<data>
		pZTq3JK+RiC2PRIvgl6AYkH0qG8=
		</data>
		<key>PlugIns/TopReadWidget.appex/TopReadWidget</key>
		<data>
		fu+NmpgfhCyfj0moatcMOz75Vqo=
		</data>
		<key>PlugIns/TopReadWidget.appex/_CodeSignature/CodeResources</key>
		<data>
		NhiiZvE3/5hJVE/26Iz+bLRyrgk=
		</data>
		<key>PlugIns/TopReadWidget.appex/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YUvHXdacPYbeo67wT2HsAMx+bWU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/WidgetsExtension.appex/Assets.car</key>
		<data>
		utD3UOYWgpRg1uWECFp3hBlzhYE=
		</data>
		<key>PlugIns/WidgetsExtension.appex/Featured Article Widget Preview Content.json</key>
		<data>
		DAj/wy/1IO+w7/21Q7CV791ON/w=
		</data>
		<key>PlugIns/WidgetsExtension.appex/Info.plist</key>
		<data>
		BxbNdw/BmQBfiL2h1gYORgsgOlM=
		</data>
		<key>PlugIns/WidgetsExtension.appex/WidgetsExtension</key>
		<data>
		6oygjwGyn46/h1pCmz3HL4ovsN8=
		</data>
		<key>PlugIns/WidgetsExtension.appex/_CodeSignature/CodeResources</key>
		<data>
		ZfntHrRqS3ekvvYT7rUVgZ4i+nI=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Assets.car</key>
		<data>
		KGBDolE1ecZcyRn/1szB9miTvYg=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Info.plist</key>
		<data>
		BObq4lsI+PiG4TRuln2OQ2bwpTM=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Anatomical heart.png</key>
		<data>
		Wx/3Et+bui+a+iazNX1qNPVk0No=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Astronaut animated.gif</key>
		<data>
		cg/9MnbtvODEudFJRr+iIL3YteU=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Astronaut.png</key>
		<data>
		QjW4zs6nlezwG9zLeAK4Uml7yvc=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Blackhole.png</key>
		<data>
		NL4Z88vUoOiQWeJlgNTRF4E0uw0=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Books.gif</key>
		<data>
		Ag9qdzilbEZpx9OMjJ0PGKpYm3g=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/BrainFood.gif</key>
		<data>
		NYqzXfAoT49Dv4Dy992y2fzQUCo=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Butterfly.gif</key>
		<data>
		tsw05c+qAFEQOHKTz+miheBuukg=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/CatLaptop.gif</key>
		<data>
		3k1w+AoJ/bzhbPD0dcjE2hats7E=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Citation needed .png</key>
		<data>
		Yxt5lhaXvI6zzWISd+AtAczeJz8=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Citation needed animated.gif</key>
		<data>
		Po+YLKwJTJyMB6SyJlsaPZFCCc8=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Dragon.gif</key>
		<data>
		7cdNl6hOW0Z/Dnms4FqCtP6NVok=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Facts.gif</key>
		<data>
		HtPy1Wd3xFmnczVlnmr/6WpUztE=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Heart eyes.gif</key>
		<data>
		knI8sSuCIWdKA3E0BhJpuCG4+Gk=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Help lol.gif</key>
		<data>
		FGHETXNHW7bXqPAHBugnyQLH5vw=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Idea.gif</key>
		<data>
		BPP4GEbMEuBi6vQ0PQCHHZ8t17g=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Info.plist</key>
		<data>
		p6dEHVcy6iC6uePByw/GUIFuavM=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/International space station.png</key>
		<data>
		Pfp/aMPUocs0dscCcCHlUmCfzvQ=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Math meme.gif</key>
		<data>
		JyGC8Zzu3o+HF4lSlD2DVHyoBnE=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Newspaper.gif</key>
		<data>
		LQHumTFspzx1fpTSw5xkSXZyGyU=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Note taking.gif</key>
		<data>
		QJVh5jqWxv/2JPkCrHvScjb1Y3A=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Rabbit hole.png</key>
		<data>
		wNocaiA6QTmURNbQA8gmhd5G/OE=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/RabbitHole.gif</key>
		<data>
		0+GhLjf4CNWUoULoI72P9qzO5es=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Schrodingers cat.png</key>
		<data>
		pMsDvsIU6qd+myROB9z+9k0moDE=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Science.gif</key>
		<data>
		61LHTQ+HMXCANrlFB5e/ttgkmdk=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Scientific blackhole.png</key>
		<data>
		zhivXUQNnLVfm9MCJX62gwaENPc=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Spacedog.png</key>
		<data>
		MSmxhtNvEUJDnX/NJYt70iinVZc=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Tea dark skin tone.gif</key>
		<data>
		PVPKdcQHCLcL3CqYBO/B9OMoBp0=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Tea light skin tone.gif</key>
		<data>
		O2zfLsGWXnb65qtXHmuyvDZCS8E=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Teamwork.gif</key>
		<data>
		7GRCQfSHF97mJMzLX9anfd1yTx4=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Thug.gif</key>
		<data>
		U2aGUgx+8WRRcr3CW5qysAweNWs=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/What.gif</key>
		<data>
		1o/hJtk55CKgBBXbD0sqZoReilI=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Wordmark.gif</key>
		<data>
		80OKXEISasEtquk+HcH9oSApAxo=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/World.gif</key>
		<data>
		ss480riWdL7K4EwYeSUSHXqy7bw=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/Wikipedia Stickers</key>
		<data>
		5tB95yTmIOmAwjg0KQVOIeKILl0=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/_CodeSignature/CodeResources</key>
		<data>
		LFV8lKX+tY4ynSUCoPnmifbQahs=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<data>
		5Ois/asD3PvrczAF8Hokbjyo97w=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<data>
		YCmqU4yhqE7unghiff5ow2GnW1I=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<data>
		uBaqIYzoH49C9/4Khu0PajNMov4=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<data>
		7CbRKu28I4vUuThqz3V5rLzYilg=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<data>
		AVrpsSNBHyhTNXWWUJRRFr6lIyw=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<data>
		jmenFhXLl/+pMe9HRUFyzhKkmgY=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<data>
		fj8SAiNWWg4j8ti1OvsK7klNtD8=
		</data>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<data>
		gARL1ibmWZ+tDUCb9kEjXWR81DY=
		</data>
		<key>Properties.js</key>
		<data>
		wZllTGvjeUXOw3eTzWS+AYxP3jM=
		</data>
		<key>RMessageDefaultDesign.json</key>
		<data>
		XhREMZyJclezYtJibVe/RmiDRwY=
		</data>
		<key>RMessageView.nib</key>
		<data>
		WrWbb/aI9ZrXnk+BdvlDmVz0lNQ=
		</data>
		<key>ReadingListDetailUnderBarViewController.nib</key>
		<data>
		aFnczJHJokD/aOTw9d8O2BkCToI=
		</data>
		<key>ReadingThemesControlsViewController.nib</key>
		<data>
		dARjd/llAKOa8W/RiIzQPPoMYGU=
		</data>
		<key>Saved.storyboardc/DbA-ai-MSH-view-GCG-ME-1AZ.nib</key>
		<data>
		5N7LFKSQKgBf+sDuVDrjoGCCLUI=
		</data>
		<key>Saved.storyboardc/Info.plist</key>
		<data>
		J5uIkK0pr4FdXx3r2K/6g7WdhDE=
		</data>
		<key>Saved.storyboardc/Saved.nib</key>
		<data>
		T4liZE8RipSNddhe2v5xR4aOKYM=
		</data>
		<key>SavedProgressViewController.storyboardc/Info.plist</key>
		<data>
		Q0yF2f3SvuiEx6arpEmmA55fj2U=
		</data>
		<key>SavedProgressViewController.storyboardc/SavedProgressViewController.nib</key>
		<data>
		U4N1+cKCuOBqtVM8wmY5F247O4s=
		</data>
		<key>SavedProgressViewController.storyboardc/byl-9N-rP3-view-Bog-93-sHh.nib</key>
		<data>
		lQqJJhEjuepuVb0LUmdV4j0/Qf0=
		</data>
		<key>ScrollableEducationPanelView.nib</key>
		<data>
		XqAtjIQvhuJdlimqUSHUeUtAgFo=
		</data>
		<key>SearchBarExtendedViewController.nib</key>
		<data>
		pAlrmsRxvMwLUsIaBzmXcPaKK4M=
		</data>
		<key>SearchLanguagesBarViewController.nib</key>
		<data>
		rRHoXsl0DCZDoHwHtdTIRMPDOG0=
		</data>
		<key>ShareAFactViewController.nib</key>
		<data>
		GQ+6VtHnryr+APaZVEo6uZo/Fvk=
		</data>
		<key>ShareViewController.nib</key>
		<data>
		ny8YtuYJBmh4I5GVEWL7wQlJyJg=
		</data>
		<key>SubSettingsViewController.nib</key>
		<data>
		0w8X9ssOP3oH5CLna7hFw//cmvs=
		</data>
		<key>TSMessagesDefaultDesign.json</key>
		<data>
		9M/fbh9llPZ3fFq1KYsMuNoLggo=
		</data>
		<key>TUSafariActivity.bundle/ca.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BV+z9R9hwiP27NH82+k2pT1D11Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/cs.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3Dbo57ciBbZQ+8PZl/s/USffoGo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/de.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nLTKuJXYSP+2d2B8fRcfH2oGWM0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/en.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iSMFCrqoItORM+bV82atE2iHj44=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/es.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PcowoxzT/PbWv+MdZUdjxNethGU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/eu.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PuLQ8P+cnuApvvgef1D9l1wHdvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/fi.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			48n/ooyleelJ+iTyHQxaZ2NURt0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/fr.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5Lbl8gMZXepOAV5YDEI60SPqGvQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/it.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iUStji5WNeMtHxsmW0M+cMxx5Tc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/ja.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gVsuIHB2RcjoWZzh4X5/cXcOesQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/ko.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wuS08r0o8rZUgdCf3e5X5cLc10g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/nl.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iSMFCrqoItORM+bV82atE2iHj44=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/no.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3dH2/ndSmhZUJs2mjQkE6aCDV3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/pl.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			U2mbXu9Rrbv/kcjVGiVDZ4xNfeY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/pt.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ai1SDc6IRojP36hgD9bR+slQ8J8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/ru.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qwSCEFjEKvYyLHDuMa9Cxht8NME=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/safari-7.png</key>
		<data>
		ss7OD0lWNN3ym+0kBwMtQpe0OTU=
		</data>
		<key>TUSafariActivity.bundle/<EMAIL></key>
		<data>
		+3tKOWHvJV4FBcGMHtec7rBwU/E=
		</data>
		<key>TUSafariActivity.bundle/safari-7@2x~iPad.png</key>
		<data>
		Q30F41MiUEjHQKeT4fE8Nmln57U=
		</data>
		<key>TUSafariActivity.bundle/<EMAIL></key>
		<data>
		i1YgLg5ziperpH1bfT4WTEnTbjA=
		</data>
		<key>TUSafariActivity.bundle/safari-7~iPad.png</key>
		<data>
		4ksm/Nn9+X5zwT7yWLwVAKIBym4=
		</data>
		<key>TUSafariActivity.bundle/safari.png</key>
		<data>
		SezgAFfRMlEl0aAvsqHCxdVF8Xs=
		</data>
		<key>TUSafariActivity.bundle/<EMAIL></key>
		<data>
		4SvQCUDrtSXx/71KcwWm1blDMAs=
		</data>
		<key>TUSafariActivity.bundle/safari@2x~iPad.png</key>
		<data>
		JihHtIXqlIEq5NVG5np3IlhGLM0=
		</data>
		<key>TUSafariActivity.bundle/<EMAIL></key>
		<data>
		DQdjufqwDaJSl6LyIeyFTzAhgyM=
		</data>
		<key>TUSafariActivity.bundle/safari~iPad.png</key>
		<data>
		nKNO6lI6tzIaVWyqEgpqzW0x0Yc=
		</data>
		<key>TUSafariActivity.bundle/sk.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0WdSj525f9hIRZgcsJEDx0157OE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/sv.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kCv892Dcaq0bztxX5ePA/SczciU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/vi.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aycBIKqCz1cIsZduxC1RVkvAz1U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/zh_Hans.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ymy32vZnv0V4cOoHf5SJv5n+Pp0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/zh_Hant.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BROKJ5GNE3SvK0YWiIxEdxCtqqM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TableOfContentsCell.nib</key>
		<data>
		DjX3mdWWyToh5TeeI5R9+K0D8YM=
		</data>
		<key>TableOfContentsHeader.nib</key>
		<data>
		uc7UxyupaNzNW63bHpFG4JcFQvA=
		</data>
		<key>TalkPageTopicNewViewController.nib</key>
		<data>
		SBA2PQwdsiJzHPIzv/2Cxxn0LBg=
		</data>
		<key>TextFormatting.storyboardc/Info.plist</key>
		<data>
		vIRZCfXi3bLZUDI4vboVoUMhEGM=
		</data>
		<key>TextFormatting.storyboardc/TTT-UR-cQr-view-Wo1-Zn-ue4.nib</key>
		<data>
		Iin8c+MTEU+qPLdd3zt+grts94w=
		</data>
		<key>TextFormatting.storyboardc/TextFormattingInputViewController.nib</key>
		<data>
		GIwM5HYTyCBjdkR1rwDbxb4IoWQ=
		</data>
		<key>TextFormatting.storyboardc/TextFormattingTableViewController.nib</key>
		<data>
		LxjEf5kEjzVwev32mAK6ZecZ3B8=
		</data>
		<key>TextFormatting.storyboardc/TextSizeFormattingTableViewController.nib</key>
		<data>
		IYOPx95gmYi4Eqs7+1P9ILor0Bo=
		</data>
		<key>TextFormatting.storyboardc/TextStyleFormattingTableViewController.nib</key>
		<data>
		+2ZGltrdE4HYDTeSprInoq3RXmE=
		</data>
		<key>TextFormatting.storyboardc/c6k-HI-Rwr-view-Qjc-KZ-bns.nib</key>
		<data>
		+pKiECfcCMAwWQTK59nYkn3wolg=
		</data>
		<key>TextFormatting.storyboardc/hyT-L3-Svb-view-iOZ-ez-5tC.nib</key>
		<data>
		+pKiECfcCMAwWQTK59nYkn3wolg=
		</data>
		<key>TextFormatting.storyboardc/rO7-JZ-2MQ-view-CrV-ni-bka.nib</key>
		<data>
		bm3fJSGUk9MIQ7YxyDK80WhSfO4=
		</data>
		<key>TextFormattingButtonView.nib</key>
		<data>
		mxIsV5Z/2PI6DTPjiN0FzxBtLQg=
		</data>
		<key>TextFormattingGroupedToolbarView.nib</key>
		<data>
		hLLnfoKSdjh7LRpW9EotYRxMvPA=
		</data>
		<key>TextFormattingPlainToolbarView.nib</key>
		<data>
		EiVL/FfjdRBE0YfmNXlYOeKKmA0=
		</data>
		<key>TextSizeChangeExampleViewController.nib</key>
		<data>
		uaHiEjvz91tISEdiXObVpBYhgRI=
		</data>
		<key>WMFAccountCreationViewController.storyboardc/Info.plist</key>
		<data>
		FdvAn3mlug3DI7+ocHyZTNq+gvQ=
		</data>
		<key>WMFAccountCreationViewController.storyboardc/LsX-JK-bT6-view-c5n-ZK-2d9.nib</key>
		<data>
		f0s36+C8HKz76HLNRKtrbIUxX+I=
		</data>
		<key>WMFAccountCreationViewController.storyboardc/WMFAccountCreationViewController.nib</key>
		<data>
		pehB7x/8uMS6w6CgcuJuJu3gMQU=
		</data>
		<key>WMFArticleLanguagesSectionFooter.nib</key>
		<data>
		y2OXwlZdIfY8CIS1ecgxrBjvxA4=
		</data>
		<key>WMFArticleLanguagesSectionHeader.nib</key>
		<data>
		diizpglkHl/rUz0lNog3UUKmPIw=
		</data>
		<key>WMFBarButtonItemPopoverMessageViewController.storyboardc/Info.plist</key>
		<data>
		nxpyqRZqKlBsxaiJXg1fFaUfSnM=
		</data>
		<key>WMFBarButtonItemPopoverMessageViewController.storyboardc/Twi-Do-8Ec-view-EFr-M1-WKG.nib</key>
		<data>
		FRtT4mSZMAmozE+si90Xti1GNJ8=
		</data>
		<key>WMFBarButtonItemPopoverMessageViewController.storyboardc/UIViewController-Twi-Do-8Ec.nib</key>
		<data>
		AhID/Qk3/Lgd3VRZHfwdGs0e2zE=
		</data>
		<key>WMFCaptchaViewController.storyboardc/Info.plist</key>
		<data>
		z6EaVqvyTWtDbTXXkcFWo1lF6bs=
		</data>
		<key>WMFCaptchaViewController.storyboardc/WMFCaptchaViewController.nib</key>
		<data>
		usq1K5U48SmCaKJXRo8RvOSSj10=
		</data>
		<key>WMFCaptchaViewController.storyboardc/sIA-fC-CW8-view-2UF-VY-OwF.nib</key>
		<data>
		4ZtT+k4YeCZQlhtPAQQQ41K8u94=
		</data>
		<key>WMFChangePasswordViewController.storyboardc/HvE-aJ-r18-view-1vt-ta-cot.nib</key>
		<data>
		a54HwfSdzi42j79SEs+tDjbeqnI=
		</data>
		<key>WMFChangePasswordViewController.storyboardc/Info.plist</key>
		<data>
		DbLarXcnnSfaE1S7jo94DDiEoHk=
		</data>
		<key>WMFChangePasswordViewController.storyboardc/WMFChangePasswordViewController.nib</key>
		<data>
		+nWHEQtxp4aFJcDZfBC2og+5G3g=
		</data>
		<key>WMFEmptyView.nib</key>
		<data>
		Q11+8zoa8ISW8EzOkuuldsAHOZE=
		</data>
		<key>WMFFindAndReplaceKeyboardBar.nib</key>
		<data>
		L6wo5RcoPH7M69Cgwtf1PxFawSI=
		</data>
		<key>WMFForgotPasswordViewController.storyboardc/Info.plist</key>
		<data>
		7AmYqq0lvC3DHClqkqcF8ZRy81U=
		</data>
		<key>WMFForgotPasswordViewController.storyboardc/WMFForgotPasswordViewController.nib</key>
		<data>
		gsEmbsy0GgIlzIuVE1BNm5IW8aU=
		</data>
		<key>WMFForgotPasswordViewController.storyboardc/a7r-jI-djU-view-GYS-km-eqa.nib</key>
		<data>
		66I/umG+AHqwaMm7xQ43zuw6crg=
		</data>
		<key>WMFImageGalleryDetailOverlayView.nib</key>
		<data>
		cFcmoUHhomwH3KQilxzZGAZwR6s=
		</data>
		<key>WMFLanguageCell.nib</key>
		<data>
		6D4RcKAsVbAJwjXO91Y35cDsEvU=
		</data>
		<key>WMFLanguagesViewController.nib</key>
		<data>
		CBVJceUt6wJ8YWEcMUPKYVWMNf0=
		</data>
		<key>WMFLoginViewController.storyboardc/Info.plist</key>
		<data>
		xNzgaFwJs1g0nhgcs/Wx9juzaAs=
		</data>
		<key>WMFLoginViewController.storyboardc/WMFLoginViewController.nib</key>
		<data>
		bOkUyVfLueQ8JFaM71oQ9FD5FJs=
		</data>
		<key>WMFLoginViewController.storyboardc/mbm-n6-GWW-view-4dr-i6-dn7.nib</key>
		<data>
		fvecI00erMO9wp1ksVt3NmQUsNE=
		</data>
		<key>WMFRandomDiceButton.html</key>
		<data>
		4dhegEeAyHeNxv5JqCnjMDx4t4M=
		</data>
		<key>WMFRandomDiceButtonRoll.js</key>
		<data>
		wlMlNhwtzk0bXx168I1EAQyLI2w=
		</data>
		<key>WMFReferencePanels.storyboardc/Info.plist</key>
		<data>
		j9oFxBvlq8APOz4gCfOynA7d32w=
		</data>
		<key>WMFReferencePanels.storyboardc/WIr-N7-igK-view-Ktd-hA-m9P.nib</key>
		<data>
		JejEowM0w9dET/PISbMfPu3p/HY=
		</data>
		<key>WMFReferencePanels.storyboardc/WMFReferencePageViewController.nib</key>
		<data>
		wVdDaz9d956hN0+efXiOK0Jynhc=
		</data>
		<key>WMFReferencePanels.storyboardc/WMFReferencePanelViewController.nib</key>
		<data>
		SexyleaMbIwwwTWe0/2IyYKX4CI=
		</data>
		<key>WMFReferencePanels.storyboardc/spA-3k-AHf-view-Hh5-za-gpf.nib</key>
		<data>
		AFkINfKCJaCXhE9Ixa9QgpuMyOg=
		</data>
		<key>WMFReferencePopoverMessageViewController.storyboardc/Info.plist</key>
		<data>
		DfT968MbKmj7DmS/qqc2vDJUTBo=
		</data>
		<key>WMFReferencePopoverMessageViewController.storyboardc/UIViewController-rh8-A3-vLx.nib</key>
		<data>
		GHDT+weN3bxqQuXfxbwnosFmEwM=
		</data>
		<key>WMFReferencePopoverMessageViewController.storyboardc/rh8-A3-vLx-view-JhK-nO-OQy.nib</key>
		<data>
		cUReILiVUQCXr67QHwOgqZmLjec=
		</data>
		<key>WMFSettingsTableViewCell.nib</key>
		<data>
		PRkZxHF3SXW1nOb0mTD+4GFnZjs=
		</data>
		<key>WMFSettingsViewController.storyboardc/Info.plist</key>
		<data>
		qOC87ZzXU1c1+iK8sxBhoYppvY0=
		</data>
		<key>WMFSettingsViewController.storyboardc/LWA-GV-VZP-view-1oL-zz-ml3.nib</key>
		<data>
		09tADRcB6yl6g4MMGGRv2oC2FCA=
		</data>
		<key>WMFSettingsViewController.storyboardc/SecondaryMenuViewController.nib</key>
		<data>
		GOpz3o7GK3bRWsEw73RzZG7nPdo=
		</data>
		<key>WMFTableHeaderFooterLabelView.nib</key>
		<data>
		R6HbVTQEq2AqKvuldkJfjmngXgs=
		</data>
		<key>WMFTwoFactorPasswordViewController.storyboardc/AT3-3L-21R-view-shM-H7-5vu.nib</key>
		<data>
		btn+kHMh5okQGHK74ug2Q1UZjNw=
		</data>
		<key>WMFTwoFactorPasswordViewController.storyboardc/Info.plist</key>
		<data>
		2HI1kb4blrhSFRVx1OGYkdqS0Hc=
		</data>
		<key>WMFTwoFactorPasswordViewController.storyboardc/WMFTwoFactorPasswordViewController.nib</key>
		<data>
		oeobMqkaY4KUFNQqJFtPlVYA6KM=
		</data>
		<key>WMFWelcome.storyboardc/HdP-0o-ZZC-view-lzy-Eu-ayy.nib</key>
		<data>
		XjMYcM+LSukafqOWZTKcYZJEEj4=
		</data>
		<key>WMFWelcome.storyboardc/Info.plist</key>
		<data>
		At2hWMhT5A5u3mPBeFMtJOjkerY=
		</data>
		<key>WMFWelcome.storyboardc/Sz5-Ln-qWW-view-g5O-Z4-Myl.nib</key>
		<data>
		TWJiWAmv19VqtNYS4vBH2xWYyYo=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomeAnalyticsViewController.nib</key>
		<data>
		iFkyMDuB++yUGMIH05vw5mrejlQ=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomeAnimationBackgroundViewController.nib</key>
		<data>
		IXtmAQ/p1kHmvyYZHIwDR4nWNYA=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomeAnimationForgroundViewController.nib</key>
		<data>
		jwjg0zJi/ixXPMk8S5cm6srRxhI=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomeContainerViewController.nib</key>
		<data>
		7XOLcWGvJd9M6o14GDL+b391a1Q=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomeExplorationViewController.nib</key>
		<data>
		KbXgUOkGb2k2OfmKM0p/vJjBhg4=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomeInitialViewController.nib</key>
		<data>
		UVSo6QRuKBYvqX0KM3TIW3cA8TE=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomeIntroductionViewController.nib</key>
		<data>
		qxK2aripCoHtg+3b7fV3xxqcU/8=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomeLanguageTableViewController.nib</key>
		<data>
		dqudP2ApMpgpR/XlEzj4uwgkLIc=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomePageViewController.nib</key>
		<data>
		6HZHS9X9CRaIOLrTKK8Kv6e3vCQ=
		</data>
		<key>WMFWelcome.storyboardc/WMFWelcomePanelViewController.nib</key>
		<data>
		CsglGaynWWB/9yTS5r8IhpKuaeQ=
		</data>
		<key>WMFWelcome.storyboardc/aoo-cp-EUY-view-h3n-cK-21Q.nib</key>
		<data>
		Syk9NEWGpeMvKzwX4GvOkV6HWlc=
		</data>
		<key>WMFWelcome.storyboardc/i9b-ci-8HO-view-ihb-9I-oEg.nib</key>
		<data>
		p5abQaLjzUrp5+x7evJjl23qEmw=
		</data>
		<key>WMFWelcome.storyboardc/ncK-V0-zLz-view-HVN-iR-kMH.nib</key>
		<data>
		t6b0TW5jSLBXCklgJyhyIOPUvTA=
		</data>
		<key>WMFWelcome.storyboardc/phf-K5-0TN-view-GFc-RE-fPO.nib</key>
		<data>
		PLJCgSFBAuhSLYJMpsCOtESgfIo=
		</data>
		<key>WMFWelcome.storyboardc/qLZ-5x-Kxd-view-Ev9-ZH-utG.nib</key>
		<data>
		CT4vfUe9XT2CJeD1PgburBOs3VM=
		</data>
		<key>WMFWelcome.storyboardc/sbE-3l-bqs-view-wCD-x0-Vga.nib</key>
		<data>
		20YeDYUG+TpaV3LOxa9T0du++xc=
		</data>
		<key>WMFWelcome.storyboardc/uX3-NZ-3WE-view-ahe-qt-nPa.nib</key>
		<data>
		OTugeP7Z91qVpDbKAUs5+O/4hnY=
		</data>
		<key>WelcomeContainerViewController.nib</key>
		<data>
		YtODaNezC+VGcg59xyORf/8cEsY=
		</data>
		<key>WelcomePanelLabelContentViewController.nib</key>
		<data>
		eMlNNplAVP/ZLs9ZVUJFl5l416Q=
		</data>
		<key>WelcomePanelViewController.nib</key>
		<data>
		etdSqYT91NFRT7jKe9odo9et8i4=
		</data>
		<key>af.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ar.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			L+55hqNsiA8e+Nva0x2ClrdFylw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>as.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VLSUDLxtOXeK1VjaxjXWRXz4jAk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ast.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bn.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lQQH5sfpi/lCPcSQJn3o/ma1DhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>br.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bs.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			08unn48gEUC6W6pvqQKKitU3JJw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ce.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			f1d68/78P9ec4T0/Tc3pXsgoLo0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ckb.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vz/BtaYBlTVros8WJbmHLeC0aXc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cy.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wE9s1YhA1+QCoB1/6FJsB1B/els=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>diq.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eo.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gv9k9u58GM8MH8F5iCEx3Do4/WY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eu.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ktmw7mthuMXZegj7Yt1gvkpKL24=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fo.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Z7bwDY2N89VteMjscoSMKjLeqGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>gl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>haw.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			A++jLiBXU6WoBRGY/wad37X3gdk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>he.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Hm7Z5EVEgOsjwv6SwUpWaMDHYs8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			q9vl8UMfJmUzXhFsSeLhImSkC10=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hrx.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hsb.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hhmPmCfmkE/1/ZtrS+RK4EWkbvY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Z7bwDY2N89VteMjscoSMKjLeqGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hy.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hBIFk39Ho+eo1QEwvK1q1SA5Mp0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>is.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kDSvFTn5Y7X04JGaKTvBFISeFEM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ka.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CSfD1Kp+NXbTulQncNZ8gDILgxk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kcg.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>km.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4sdnN+F333/lz7wkbYbTqvbrJRE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kn.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G6zV5WCcy6uM5WjIfmXGzoai6Uw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3XahhUste6Uuyih2K7dsZNB/qSo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>krc.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QjPDvVxLvgIMP6cwu5xuaiBFY98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ksh.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JYogLEEt81aUpHV+iklEVYogh8U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lb.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lt.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zc3Vcy9YuVrS5zoGjnFwSToY4ns=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lv.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YM1e5is+39FP6BpiuHOjYN1Jbiw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p8iRkkbv4i1QgvOqldYqgp1+XNo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ml.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			MHAM5PRX29xEsMgvoESQUqCkNVk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			q9vl8UMfJmUzXhFsSeLhImSkC10=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ms.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>my.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wOe/BZ43xNBmI+ogxYynZ1S2UbY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ne.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			q9vl8UMfJmUzXhFsSeLhImSkC10=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nqo.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>oc.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			r8Zb+WOCPTumvE6R35BHXpQx6H4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>om.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PZhfwh8wNM0GMd9xqjSyOP2b+mQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>or.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+2mXVyby37NbVsuYrQuNgFJVjLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pa.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mBJRO5Xvl/j5CdTAEVJ6g72sPQY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ps.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kBIAXLUhR6QXSubqP2aQ4lnBiq0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Z7bwDY2N89VteMjscoSMKjLeqGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Z7bwDY2N89VteMjscoSMKjLeqGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QjPDvVxLvgIMP6cwu5xuaiBFY98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sa.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			q9vl8UMfJmUzXhFsSeLhImSkC10=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sah.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dUlj4iPJSC1R8d+H63OLHPVfik4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sco.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LMYQ1HgQmWjIaO/cZn6yscF0Fg4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sd.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Jj0+MU4rHuI929kuUpOOuoNH62E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Z7bwDY2N89VteMjscoSMKjLeqGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sq.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sr-EC.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p8iRkkbv4i1QgvOqldYqgp1+XNo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>su.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Z7bwDY2N89VteMjscoSMKjLeqGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sw.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ta.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			j/bS594KM7xke1x/tY96YOS83vQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>te.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6GRzC0iIbjGvIbYo3vVtMyf8CPc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tg-cyrl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QLKdZMdG9XCBAHdQlEMcWzRiYUw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			yvGMYLSQ4SC+I7Ap5s/nbVWyNf0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HDpuv5b8I9SBEcg2De9E3MHlMYM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ml6b1f4XhyKB1u/t/ZSbmof510c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ur.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SD0uINSsKs+zneNlhU284vFaBig=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uz.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hQqGO4HZy3j0uJteZJbLEC0QD1g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vec.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HsIopdIw3HjKn6+TflQaHMTdnO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>yi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RdygfY0HgaysMWux0wMjP+d5R4U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PPPHEkzrFCIWQjKotTmMFjuHeIE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			39orImxuJovK/+vw1KeMdd/xo3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>AboutViewController.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			IG/dDAHbMujYCFTkYg4cnmcSqjpDvFx+jut3N2RFdSA=
			</data>
		</dict>
		<key>AbuseFilterAlertView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			7Z5ziS/pcoXUqRl6lltSRSLgrODj7hmZl0tbw3FEtt4=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			zhh+vjeCKuwqjfCCgNfdq32zToKK/2wpas33clsYCeE=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			UzYZGS0S6TCWd4VN95xBHfJikk5tSLVCHFcD2tNrgjQ=
			</data>
		</dict>
		<key>ArticleAsLivingDocHeaderView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			5z+42iYyhbHOH5ZGPzW5mNs1FTioj2/JhYaTwVt76ek=
			</data>
		</dict>
		<key>ArticlePopoverViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			RMpqrEnxICAwLZ72WKJZvcoEFNyE/w9VqM0/h15h5VM=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			PxwlCtoXOyfdtnjufQrArrBNqM4WmZtSIRNLcLieUiY=
			</data>
		</dict>
		<key>BeKindInputAccessoryView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			g9LZYbALjqA6mSPIbwLqJa9u6OEEqoFEd7Z2bsYXC74=
			</data>
		</dict>
		<key>ContextualHighlightEditToolbarView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			aQzZLnKLLtthHTqz1pprM1vacsqMDTXIWCJ740C8PnA=
			</data>
		</dict>
		<key>CreateNewReadingListButtonView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ftG2VBSdhb4e6kLCOkxJVxgpFfQ2oPpMetYJpFZ5DTI=
			</data>
		</dict>
		<key>CreateReadingListViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			uDuPCchJ5vNWcpivlSTQijX2ECtslsLiphR7h6ZKlmI=
			</data>
		</dict>
		<key>DebugReadingListsViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			/XGcrn03/KNhz/cL56Qnle1MrG2nMLctl8WWi3Jqm1U=
			</data>
		</dict>
		<key>DefaultEditToolbarView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			9fgh8fs21C1EdGQhg1WVozJN60f8ChifaV6nczI/rH0=
			</data>
		</dict>
		<key>DescriptionEditViewController.storyboardc/DescriptionEditViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			rceytDNDI7ndDOGoczDxxV2oQgWg/a0HnwbHK4cJ2B4=
			</data>
		</dict>
		<key>DescriptionEditViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			jfo3Edv7S42Xt+9OFM7HRWL1/ci7My8npvkLvWRV1wE=
			</data>
		</dict>
		<key>DescriptionEditViewController.storyboardc/a7r-jI-djU-view-GYS-km-eqa.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			wHvke5dFnruJKUCGlUyfuS9Ple0GY3fT3QjzkC+4tYc=
			</data>
		</dict>
		<key>DescriptionHelpViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			MO86+CNgAK1hE6iD4Z0EK/5TF7ZYvceZpODMvolpCoM=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomeContainerViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			+8azP1AXB9kTL7d4Qu/xVrSQb6fIi3yBJSa3UBOm07c=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomeContentsViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			yJuG0UzfE4eGN4ElaWfXKXSY7wj7JbAYGlEcDEZjCTY=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomeImageViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			AAjugfhuqBtRJ3IDCGZibFvdpY/aS0Ak/Z6k2iNT7jM=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomeInitialViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			MBlZMqxD8BtRCJT4WMAdxZAxhOswJ4aVVUCUSCTofEg=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomePageViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			5pAO6Vx7Gk8rtOeV6GDjqkYwas7u7R48TmwLDv9O6+A=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/DescriptionWelcomePanelViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			oGb4mHRZBC4U4TU33PNUuc9MK+rQeqIMtY5GkoTEt5k=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/HdP-0o-ZZC-view-lzy-Eu-ayy.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			4JaAjU+gxgskPZ8aCYYP2cjzcQMNgZcDK3HoGQRPdFE=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			k4z81tMyWmL5bBVl4rq/nqZLaaCtfgTz6KWQg8DJjxo=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/ncK-V0-zLz-view-HVN-iR-kMH.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			IAocpB+LU2Dx/qVDfxG+FTlu4zJL1GOlmjJ7CJaN3eY=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/phf-K5-0TN-view-GFc-RE-fPO.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			kz8Efi5BhE0I19yrKCMBhyUEoOlvgfrJZt63WA7xVHM=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/qLZ-5x-Kxd-view-Ev9-ZH-utG.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			jB3axTSicjaada0mJ8z9WZARWc+kpFagFFZeyRa/vCo=
			</data>
		</dict>
		<key>DescriptionWelcome.storyboardc/sbE-3l-bqs-view-wCD-x0-Vga.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			/eM3vzlThUqKNer/VHU9vZNFtQXJif7OVTwCgnJLqDw=
			</data>
		</dict>
		<key>DiffHeaderCompareItemView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Ilpbj7mHyL30KVlHPCd3A9qoLXVLNkemQ/LCzXycbyI=
			</data>
		</dict>
		<key>DiffHeaderCompareView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			COy7xo/M5TXclTJPP61n4DN4Oyb7MpdWymOPHc6DgRI=
			</data>
		</dict>
		<key>DiffHeaderEditorView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			zj+jvG5hTlBFZyDEvirwA0gmtpX7TUmbOumbXd8VcN0=
			</data>
		</dict>
		<key>DiffHeaderExtendedView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			F0itmaTN2wzae6+jo/89XiN4AAeazSbvT/veAs1a2WA=
			</data>
		</dict>
		<key>DiffHeaderSummaryView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			v0ukbEKlzYS3W9UHMChBfAT0LOLpO25X4JKSGLTAS7Q=
			</data>
		</dict>
		<key>DiffHeaderTitleView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			7l63c+FaYhHou9xcpvBC/EoTBW0BLhn3IIMA5In6CBY=
			</data>
		</dict>
		<key>DiffListChangeCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			MmArVq6gT33+4XWaV7OI/0TNgbmgbZYGMtNBUYLtweY=
			</data>
		</dict>
		<key>DiffListContextCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			99+LrNUxs9oTSi8pIi2H9XDiOCDVQJKq0l+lq9fGaug=
			</data>
		</dict>
		<key>DiffListUneditedCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			iEkcvyMxnMpywuf/ALNLbQ9GcA5Rd2Gu4yabrT7oESc=
			</data>
		</dict>
		<key>DiffResponse.json</key>
		<dict>
			<key>hash2</key>
			<data>
			m9m+3vM7jvzZl8srZ1tarxlyDyYmlj9xy9zYdrxSV4U=
			</data>
		</dict>
		<key>DiffToolbarView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			C5zqfe1/S3Z0SSLTYCJUY2fYiMUYyN1bfH45vbP5d/I=
			</data>
		</dict>
		<key>EditLinkViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			PJ3//dpnrg5JCDgfDYDWK85IpJSo146CFKGD86Hx+Vk=
			</data>
		</dict>
		<key>EditPreviewInternalLinkViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			nWSzJ1zJUmitQ5wJpOq4fJznetrxa5t2pdevuxs338s=
			</data>
		</dict>
		<key>EditSaveViewController.storyboardc/EditSaveViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			LdSa38dcBPxpjHK5LJ8D0Yj/ojGZrKDFYwXPqqKt3xA=
			</data>
		</dict>
		<key>EditSaveViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			twdLxsye1su2VqgvYnC59uUou8NuE1i0a66PTGwwt7E=
			</data>
		</dict>
		<key>EditSaveViewController.storyboardc/YCc-it-nqb-view-tP4-ul-Au7.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4zTDzTqmNVQAtNmAYPL3vZ+PWwqWG5Qt1KdZO9rHUU=
			</data>
		</dict>
		<key>EditSummaryViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Ypykxe8cTuqnCwI1avVvHJUe2wz8CAkcc+9bTwHUHTw=
			</data>
		</dict>
		<key>EmptyViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			szKqoTc3HIuuX3bqEmFtGrKxNSuiaQOcEJmol+zS/vM=
			</data>
		</dict>
		<key>EraseSavedArticlesView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			606WUvXvJr9wt3clPTsCzceHlOxCyZSAtA+HOvQrXlc=
			</data>
		</dict>
		<key>Featured Article Widget Preview Content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9dAbe+P45znDfipMNta+sSkkuqrzuO+dbdPiaSJOzYk=
			</data>
		</dict>
		<key>FocusNavigationView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			V2HIDrk1m+Q49PIl3zdutajVIYYEEW/3KVHP+ZKkBbE=
			</data>
		</dict>
		<key>FontSizeSliderViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			5xOtJ1eoJQUz5jjxB+lQZCkjQlWklyE9PsfH6tTuQpU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			39CutSwvh6b7g2L5EQRDRa4xfB2Kzh4PQ0UCWYN+c6I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/BatchEditToolbarViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			m4YsQSYeIsa7dh7+KAneWhAveSSu+BY9n/CQ6Goe5/8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Cache.momd/Cache 2.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			rwU5bh6t1jub+uwjle1XzKNt1NAYg7zYbMNwPx3AOqg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Cache.momd/Cache 2.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			yh7EiHPMOBfRQVyLFxelnu4CZSKmBQRx6VxfTrH644s=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Cache.momd/Cache.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			4W0wHyIdU/c4oUiSgHo5PqLLjsbwN2wHKhM0g/NdBUw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Cache.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			VxbDp2gQrN6CCjPDkrqWLmmPrYqZkmIZdxY16F2u5+A=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/CacheItemMappingModel.cdm</key>
		<dict>
			<key>hash2</key>
			<data>
			qVWtphWLIQsbj4Lh+0RaWxfWiJuTln5YmwdHJApzI6o=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/EventLogging.momd/EventLogging 2.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			MboOcdRRTGOzQlVUrPqCdSBVeQjikP/7mWqBjZ4iV/4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/EventLogging.momd/EventLogging 2.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			pC7uiMcazzgjiGKf16GIlir18mYcpX2HvyB8QQlgfg0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/EventLogging.momd/EventLogging.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			TQIK5NOyLDtBIk8ETqvJh6TvkRrZ1hdfGI8yVAilDWk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/EventLogging.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			G3RPkhUjmOupYO4AQJQO3uJCXIUhduZsBamecdP21x4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/EventPlatformEvents.momd/EventPlatformEvents.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			j25FDJUJrFH2Z2mTAOs74uxOJUYlZzNaPp3h9gSAqzo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/EventPlatformEvents.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			d0hsG+XVzuFReC4fTr3y0CaaJDz7aC7AO9RH2u7ZlQ0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			mXbmG7Z85PMjUpFFpFHK80RKZTqqRGEFPBLKVcNrWAo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/MediaWikiAcceptLanguageMapping.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VIjOKwTgdfIlFbdJ9P6ULEvPMGSf/nBx/4N/Caj1R/4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/RemoteNotifications 2.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			Ka86knpDmlp2zbZd2atyfm1t/gHIIvVhsyUVxeLd8I4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/RemoteNotifications 3.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			cJiAbGzodqK/zGWA1wKEB/sMwRIn+kviGEhq8B+NEJc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/RemoteNotifications 3.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			/E1Whh8GUYssZnpXeKDQhA32Wa+yN47AmJZCRg5YeaM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/RemoteNotifications.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			mesYO03c1BBGxkDG6IosPmenmI94i6Oxc/u22Newod8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/RemoteNotifications.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			LrzJtijkOpIyFd8703sSjj5cbTZxaSENDqlpFzQ6goY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/WMF</key>
		<dict>
			<key>hash2</key>
			<data>
			zyn7NLxgnPl0/XTOoS4fVvs+7IuvpxFOjCzVAyCqroE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/WMFArticlePreviewViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			w0hEnitthbq64PIhGTRHOGw1OJIFVoHm6QDI0bLWtZQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Wikipedia.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7geWbpldfjjLJT76yPsf/VGRMArKmVJ/IjcYWRZIfZM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 2.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			L4cJr6/qAcSs6BSXYBvFR4dDbQ20ZQIZ0mrs0B1g5lA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 3.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			koyEN5IxsQxMVzHhjU5tYECgTjakx9FiU5vm0MF50fg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 4.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			KMn7OsQYgaHL27yNq1/nkP3KBrOWXb7qH8PIRIDn+zc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 5.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			ODGTlGMfOafPdJ1BIl/ZSeOa5HxwLq6f02g5arUnESc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 6.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			aQPo+Rdk8tJvUdOUqKvfPaFhSTgBaRTrZ/cmMbsx6LA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia 6.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			/Z1vWuYudlmD3u5aG4Kq/DyWVDctdxBe1ejt1ybawvk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/Wikipedia.momd/Wikipedia.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			MYrUthZPyXJ6M7Tjyfz3XiXhwEyXJ7O2A5xYkF7OCyE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			4G3BQ8g1o1BM9lmbi+/WIqVvxZJWp+TEmeWRxdT8wPI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/af.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Z/ANpnRznotmhCJN7F++6/D9hsaGViLognR7FCwcuPc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/af.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			riK+xTcs2V8hjOOnznRgpEGU2InqyiZ3EOYM3AIqi8c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LUW7EM5jFog6xTODLfLh/sARVSISrVN7wQpyryqQOtM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ar.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			Y4N8ZBbi3KS7hWm99sr02RdXGzUnPRrvZ1pOyI2Fdx8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/as.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vGXcOcmRKkviDAKURxhultOckiUxxY7o4vEr8998z30=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/as.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			mxbmGU6VUu0eEg+QoAv3vF8Z48SIISvZKGmO9iAD314=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/assets/WMF_Black.pdf</key>
		<dict>
			<key>hash2</key>
			<data>
			7D3/IiSNWVnsgB6yUmhsxfeY/JyF5oudSZeb+Dnovrg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/WMF_White.pdf</key>
		<dict>
			<key>hash2</key>
			<data>
			Kp4/mkMpEJqcZlIc9Ym85dK4GwQcZoVh//JlLQql+u0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/about.html</key>
		<dict>
			<key>hash2</key>
			<data>
			JSo9Bo5CrdpoE6aCQL027xWCFbmRkJB/BLLgETl6AjY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/about.js</key>
		<dict>
			<key>hash2</key>
			<data>
			nUXe9/epkPjmLsNQakPNCJtQLhJFsSalZij5OpSNcnA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-black.css</key>
		<dict>
			<key>hash2</key>
			<data>
			1wBb/Dv1PlgXc4UILA6XTh3DIPEit+RUVegGalc8/Y4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-common.css</key>
		<dict>
			<key>hash2</key>
			<data>
			J80UiLa9l1Srlll/B3CvuZJEXHUbW7RKi6bbJ8Bca3U=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-dark.css</key>
		<dict>
			<key>hash2</key>
			<data>
			yiu2zHdt4r4yDhFqII3tAxipVRF9hG8XZQ0OtfVZgCU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-editTextSelection.js</key>
		<dict>
			<key>hash2</key>
			<data>
			xVWavDFKhzX7LVji1MgfpQkUmlaeExgkmzPJZCZzkPY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-index.html</key>
		<dict>
			<key>hash2</key>
			<data>
			dlVY9hTeJLPZ93+C8WgabqTzKnKg9HqAM5/70UQrR5I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-light.css</key>
		<dict>
			<key>hash2</key>
			<data>
			D9FPIMBaQfRbuZGpkLEXtUB3dplTw4T5Hh8nKPVHza8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-range-determination-bundle.js</key>
		<dict>
			<key>hash2</key>
			<data>
			incLAMsQFd9PkS43cHqluHEwMbwWC4G/tLdgytl832I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-sepia.css</key>
		<dict>
			<key>hash2</key>
			<data>
			hfEUtSgttQ2Z7DzN3HnCeSYeWBPJDTinol6OoQWr/ME=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/codemirror-syntax-highlighting-off.css</key>
		<dict>
			<key>hash2</key>
			<data>
			uL+rXVLkZfAAFyV/AG6PvMQ15bqBwmbKg/tjjCcQ80k=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-aa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ab.json</key>
		<dict>
			<key>hash2</key>
			<data>
			km4HTlYUHla9+jpTZQ9S/XjEs6TtbvvIqBjU/arxNwA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ace.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ady.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-af.json</key>
		<dict>
			<key>hash2</key>
			<data>
			y/x52Hl7OKyuqSHlDPbKu24ihvvm77R2JGtaaJR92uI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ak.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-als.json</key>
		<dict>
			<key>hash2</key>
			<data>
			B7BttAVDywOoBGGrGomQcYUv92g8OnvlZIaiM4z+KrQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-alt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-am.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-an.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j9K9/6WyqrYU+/0Wy3krVXxNfM9a9+7zDAahlhBQ0ak=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ang.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TuQMpuDG2Jb40tdONo+4GhGXdv9tbYI35UsbZV40gHE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-arc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vXmxX3pUsXI8r5VmjTAeEOQRJCW+YIp2MVoS9w3Dils=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ary.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2YnjELsScBrd2DFfUz7JQrxShsd6sTtNTe8mXlp1mu4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-arz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hS9W3sFO/AgV5oLstkhw8usxscvrf3GAVgD4h5mDBnQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-as.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iziPoTpMg7f8JABKRAg+XqLcNS/TLD/TBXgZjYD6B9w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ast.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-atj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-av.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-avk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s22A58bVNVGJnh3jWZbALWPp5D1peDsfltwEvR4bHc8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-awa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PoMpYe7rt6MxjVjoysYyY+HAxXBl2/acXf9AvqxUHyY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ay.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-az.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0PxOX6pSDq7iInpbKOwcZz3OaiPnFsPbO8fK+UJsSGQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-azb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8FgDjINeDSrm1fmn78DWs/duaf+Qd7u4qfTb7IBp09Y=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ba.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYE9ILu/BBqsp+7cq8YX880LbLceD08xf9tnnTlFSHM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ban.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5SveJ18LzKL0h1C6cXFFUSh5aitml3waR39YGKfrOoE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bat-smg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8IOntdbm3asZZb6gluUGTlFnFznbHyERj6KgOS8+G18=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bcl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PIeyAVd1giby6ZVewsIbn6dUcnmNGZqcM/eixzci7FI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-be-tarask.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qwiNXVn3b5Mget1YZMXcbRGQp6HPsSWeVN3bTMhY9iE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-be-x-old.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qwiNXVn3b5Mget1YZMXcbRGQp6HPsSWeVN3bTMhY9iE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-be.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zMTJmK6OCvIagyu+8ZAtln6gZZI2NdcYHe21+abTvvo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			sISC4m4xC7WKhp24AcdnUI4a2VJivps2+mEfvaOuXYY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bjn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LS8UFShmYzSXERNptAVfsEB2PQBOFIYZUS+AVQVaCvg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bpy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			B9Ejo2lVXicnFj5CF5IiT1KoUVsranFNL9otBRh2bgs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-br.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nKXMBbw21msHV8de6OjadORK6zvyBBFNdvXKBWGCEe4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bs.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nduhYNi5HKcbvWxYdFSVwn6r9JX2ogGyLzXpqvo0iGs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bug.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-bxr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ca.json</key>
		<dict>
			<key>hash2</key>
			<data>
			n7t6qEBHjBgv6uP2o4iG+0FtuFmNZZWzIdIukvs51qU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cbk-zam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cdo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VBQ7J4yKQflXe55DtC3FdzBk3UKXWz4984VPp46esmU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ce.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PE32hQS5wl1mtaitvUwUK6tSyy83rvsIWQv0PcM9YKk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ceb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ch.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cho.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-chr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-chy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ckb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bJYlW2vUc7+yQ1hPNb+zyJouGReKYyUVOaH42XCPzzM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-co.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-crh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cNAnPi1BlEFqA29saD0Lkqabf8S2xN0FpJj+d/FgOMg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cs.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZX33zKhEYzv61YQLwMe8h+/Ze5OkvuFPEdC+naZsf1A=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-csb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gu/aJUsFb9TOkYbzPf+SuEnOxxsjjqUnOlP5TMXWvGM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			NzuLPjW83/3p7KnBSHe1aUPHmMnzX+9l3AfyQ8H/YLA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EvwcXj0oOxrozRZP4Iiul8joYCQJo2YJe5CgSKR9RyY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-cy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LeB0QIQgTpC0l3y4Aqtv3cyem/XEG6SZlWld2PYC5Jc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-da.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dDv60x1CLjuv+W9uhXtXQmv65LCREyMVnXE9XLlfR+w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-de.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PJbHkBxebK7/nC/UiEwyIfgK+9Npu5K7+Du54Xq3d2o=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-din.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OiOVMqzDLI2y2BDd7bjnsNNSLCRA2HPFBIHS8HMxutU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-diq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BWIQO8L1Ti9j0zTAIfO65TVUvHZ2vNJn+WPcaHAhmPU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-dsb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-dty.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-dv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-dz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ee.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-el.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4Wtdv7MtZU5l3IMI1UEQ9hpyyZS6YcPJ8j5nwsm7NEU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-eml.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-en.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ecwidrsII3BYm+mNyB4caR/9snYpY2hSIsiPBP8Ad5M=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-eo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E74NTd83+3K5noxrTQl1ziVbOztjZ5nEFkDAve5JuwY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-es.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Oyid3t3PkqkUNuw8DVycAyRzBNdjTg9kU48wm9ySO48=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-et.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EvimKbRxVKWZSPVdk5NTBn4Ij0KL+qG+/1X8e0JkH5o=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-eu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			YkkRE6jCGgRjMfvHg36q3idudK0mKlaKrdFo2KkH9Ac=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ext.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iZ/YcoisMbb2e+5ON744nEChZ/84MZOVMwDwwXq+Lfg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ff.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3OXEneqTs2ucPXv24QUSt5m1vVqNiYSf3wH0P3WNq14=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fiu-vro.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EvimKbRxVKWZSPVdk5NTBn4Ij0KL+qG+/1X8e0JkH5o=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ejEl51VxhLu4sF96IVLmrm+tgJwR5FgX7yFeLmAp5zw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			37//7juxMngcHdINWHQB3rRCkNbgtZ984bYgx91zKMk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-frp.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0j6Z67sAd+KxOib7txKvjCPbtNnqwvYyUncHwKU95+c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-frr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2iGfOo5Xy4PU5XfcBF3RBIZHWdR5y3dWHNogP4PGMN4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fur.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-fy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			D4ncevxVPyRfyYFtGU1MKS0hnL+XCBQ0nYAgoTrNnt0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ga.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8tMdoCLKFSOw356SUyDoqCFyPg7KU6fwIMutUTyNfxw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gag.json</key>
		<dict>
			<key>hash2</key>
			<data>
			HeRSSXK3cQrQga1Vsk7q9YetknY134DY3M/3DvZoAgw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gan.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yV08qP0sX4V1zUldcOWMp9BcCNFwsk4tTQxooQhxANk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gcr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lR/XfyPORVvztkzOhE+N74GTGDwMwOrIJguTgXYmU9M=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-glk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4PORIH0PmD5zsjh7sBF+XzM8csVacqvBC48Lo+erv+Q=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gom.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gor.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-got.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+6S6Ytnwrq5FB2X8J66IPuYY60ioPqNzKhjnho5DXOE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-gv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ha.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hak.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VBQ7J4yKQflXe55DtC3FdzBk3UKXWz4984VPp46esmU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-haw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IrdeKVrv2GvMoEzk2zG7UhF3tebbW6lVVwOFnSM4LhI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-he.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RcoO/ELqCguIP1nUk40GS9MC5U/KoH10A90FBj8ITk0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+NSt2W9J89UluctJ9xZSwPG005YvRRn/uzhbgWim7Jk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hif.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ho.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2qxkA+fzI2nioEzvrlUg3QZ3PqQTJf3mgI7L+bgaDoc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hsb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ht.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kuX2NyxT8ez+r/aiSxqkH7tS/QiSNCaRUmleE+vBFxI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6aS3Mu5aW3RWzEzLrPX+rviG9XeUBzfP+UH8xUpCwgw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b79BUuwEdlYNJL9I5fKWbr39IMH5gwC7ql2PXAtRi20=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hyw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			alWIEjqXAK6hxk6HBKwJqwBJTKXy70oqli1WLPxSN2E=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-hz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ia.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-id.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQpTGOnXuSX0xGU+b3uPiN9HIS8x4SkpDZ6o+f8CZ8s=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ie.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ig.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Rl2nrt9F3k9QGuYLOYHIMDiM2aIkGniuoF9tOQzKQjk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ii.json</key>
		<dict>
			<key>hash2</key>
			<data>
			QJeJwuJzQMldXMZTcx4X1IoV9Eta66LIY+Fd1g3rmFI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ik.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ilo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-inh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-io.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1MRtbI2gLje8UeNaHDZaaYJeaQ1CZpIEVcNpPrRR+zE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-is.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7oo20YBNAwSWCmcgfYtGYjnveK40Maea8yS1wUqCGA0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-it.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-iu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ja.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eYvFnT+/zwGMMGNq4wXHP0WoJfP1QqRRRepxzS7PzJU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-jam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-jbo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-jv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ka.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6JM8dhi2z2otqdhGGtFWZO3Y9YadU1KzxwGiPeUgYoo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kaa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PM4dRKPfIFssZUOuknic2h5yAaM/D43qn/H55J+qrZ4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kab.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kbd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kbp.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ki.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			F9vbGkVnzos2MVdwxGf8Ecn2AmjgVew6htsagpxInuo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vAs5Oxz9IcN6HEXCBtKz8Z/9+PQV5zgGtF0dpzZQLWs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-km.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+BeDkidyrOVhc5ODpcUWGWOXavxt6tQ+0S4q8/pJvuw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ko.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Mmrtckt/QdK+5H9EKEjFFb1PpP128N/+rsr+xp3Hak4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-koi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-krc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E7+3MBUezRmSSZpYjJKjBI5oRApqeGflZ9N7LStXdBo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ks.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ksh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qfmp8Svq7sSZ1MUz8HK2Z1xw3C2ldMxmrRWqFWoNxas=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ku.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kgWhpwbOIICVxqURfyPkhllmmmG1dmY+FQgOvt2J2zo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-kw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nENhNHd3Sh/KKptdDTq39cMYRJIhmTG1m8oq/WPmMCo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ky.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-la.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lad.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TTOk4Ljr6Bj5VTx0RwtEHK8hvg6KizhK3g5MdkhJJkc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			mKxVWLuzmfVP5r7lDVwJtvy0FSc0TZchK7RPhxn5WOs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lbe.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SSEdZ24posqkqqa5nz/qPBU3mSxMhe3boZUqRcaAutI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lez.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6gNfmPy9enuEHzruU1V2GkHH/MR7R0KzRRhLS3WB6gs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lfn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-li.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uonDqk94hhn9zZr/UYlA0/Cg4DaOHhTbAbZJ5oDCDjI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lij.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lld.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lmo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Nh0pGd73Ogcg1iVtJ4wJKbS/vDexO3XM/wUxXAjJUzU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ln.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lrc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4PORIH0PmD5zsjh7sBF+XzM8csVacqvBC48Lo+erv+Q=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8IOntdbm3asZZb6gluUGTlFnFznbHyERj6KgOS8+G18=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ltg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/TFrz8Vkru02OxULsXydIbV9Rgk/+fiwxdbn9AxUc7Y=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-lv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/TFrz8Vkru02OxULsXydIbV9Rgk/+fiwxdbn9AxUc7Y=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mad.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mai.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PoMpYe7rt6MxjVjoysYyY+HAxXBl2/acXf9AvqxUHyY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-map-bms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mdf.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uZshUwQtmD+Ngrq6UqPp/rU/Le3KqoezNuplyRO+sSE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+y5VnGXAzuECPCAk5DQT/Pkj6UYOdfUnyzDa3Fwko5s=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mhr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Y/evbw87nzHvp7SbcHeDCaVSl5HBwRxNwuLQx/b2Uck=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-min.json</key>
		<dict>
			<key>hash2</key>
			<data>
			A8E3DlLsYlMBBeP8AbdRotGm5/0VWbWoIWUBUtljmqk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BLA1gKqkGDUdFPsV5236O+7rnmF3dhvK4SuOa2WGDRw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ml.json</key>
		<dict>
			<key>hash2</key>
			<data>
			mTsz1Scf6WQXmeI098Y4rNivHU2d1uslUPce2ac8UD4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			QtxLpN1P46gl33QGUI7lgV8JMSZBtDj/GjOAzsYDbpE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mni.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mnw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			HqGLH+JrxDitRhZe860p3PW9PzYq1V8Fvb6K6/39MNc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TLoNXsCAn1hP/03JTVDNx2xEsP9Hrvu+eJgWdA1f10c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mrj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Y/evbw87nzHvp7SbcHeDCaVSl5HBwRxNwuLQx/b2Uck=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			wDiuH6jrDdkc+3YZ9kkkeeVL4dO+HCaLJChj9glPqL4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			85wyBRmnKggPWbhx5+ODMI+udYUdl2ZX/MLDMHREebo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mus.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mwl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XYpbAq2NBZICoizglGhLHungDn7KMz54U1gu0fPuRrI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-my.json</key>
		<dict>
			<key>hash2</key>
			<data>
			MWP1ArB4JxZnfJbUP7zxcLW5prFTE6NlksY3BfSoEzE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-myv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uZshUwQtmD+Ngrq6UqPp/rU/Le3KqoezNuplyRO+sSE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-mzn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PPM5Bfm7G6WcKpVsFG9l+9RZgi4yL4eBcB703Xmigw4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-na.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nah.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nap.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nds-nl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dNIQ6/ohyROSgKPEwku9UQqW9s7EnwevhssHChegIAs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nds.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XUnZWKWzu0oDvYYwJc1B3sC/Eke7T8mPFgccYVnsvSg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ne.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-new.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ng.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nia.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uonDqk94hhn9zZr/UYlA0/Cg4DaOHhTbAbZJ5oDCDjI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LQtYYQaTZWSJa4BQRavoy6A8NlaAK5oAkwPvq4gHHeE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-no.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5LnoBqCKS5ywB48JL93qpGJi/Zw2biBuGkT7a+ct5js=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nov.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nqo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nrm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nso.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fB+SjXJmpnFWOLD8ohkUGr6L9b4icwDISPOiwrbOfnk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-nv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ny.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-oc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ljqbbLZXDYBc4+tl1c7xCDTSEZmw8NS9P96LwfoG1KI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-olo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ycfCFm2fslbxIt25jZMhcI8PS7Dwvb2a9GJ5jLvNzBQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-om.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-or.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GVigIj6L65V5nL2fMZtMUpv+MZGasKkgZo6bCI1CvGQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-os.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6XbvJ0UJq+fs/WM1bR3SFYqaXUoEf5Z2i7FF895OpV0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IngQfc9vYWGMTqtba26avnqnTSWhL3mkH9EZd0hXxzA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pag.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pap.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pcd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pdc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pfl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pih.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			J4sc7XogR8lXHXdnYqbp0LVkjw/UkllclDjwCnSkT2I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pnb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pnt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4Wtdv7MtZU5l3IMI1UEQ9hpyyZS6YcPJ8j5nwsm7NEU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ps.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9/oNepu/0cAYdnAHgoejLkvQdnznqEhBgQPPWRjd2vU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-pt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			w4FN6eUeYH6iYtBzUuwPNSNPNYmves6M33PSf8oxgZA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-qu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			YE4j9yA4N+guxSSeBafXlEbNq9oOozgIVNiLkn+HwsE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rmy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aNn3Rg+8pJZ0/ZwsMhztDvEQLOxpeYAurEa8Tze3TZk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ro.json</key>
		<dict>
			<key>hash2</key>
			<data>
			q8hSCXp9RQKF6aeG9qPnOWgQEb5K6JSMKZD2C6vt63o=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-roa-rup.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aNn3Rg+8pJZ0/ZwsMhztDvEQLOxpeYAurEa8Tze3TZk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-roa-tara.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ru.json</key>
		<dict>
			<key>hash2</key>
			<data>
			76w0FjZ0Io0NBvl++Y8zy+7pTxu5YEiZ3LuKTRcplFg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xTwo1Op9PQSKGdYvsRYKHlMwOmTEyT1v5TlNGXZSZtM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-rw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RkptS+WYLdoN0ar2eMtjq9aGIWc1Gp+mic9FsAm16Ak=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sah.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OuzPw8CJj6RqR4RM+zL/4NFMifTTPa/N0MFDrLeBbso=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sat.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-scn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sco.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			k7agiFPhsBsqLd3zI8xClbpXmA9ZXJyNsiZPyYNX3Rs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-se.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7CxM/zTjrVD8k0SbF9rwKYDQR2eGoE5qrrj2wEh+U6w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zRLfu9hS6ui4SyTZ2aDjO1lU39EoVmary6bfcsRewqA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-shn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-shy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aamsBGtAXRJamKJq9FGK3+IMylyXuTfGpdozTfD0oLg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-si.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0v1BMYsL89qIvcrWmmv26EYiNWq3x5h7oHjLLSwxWqE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-simple.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l/MAknISTBwD4XnNbP+qf/rQImBPo2GdGiUgi+zbNBU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			oEKXZ7mDYTaCdx1NMxQjKhjd8zUP9/bTVDNDeTdYXbA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-skr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			oDp+JsnqoaRtNelSYp1Wt6PcJMVbDggSvwK6lZaHMtY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dc+D7YY1Z8yQUJq1+VVdVlngWMXWdJcWSuA1U0PpN/A=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-smn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gMEJLgxAy9i8Cls31MWrTDsRr4SBFhxb/wCln2bwxVM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-so.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eiAKTEH+Br9c56Yo0jgdxqQsIONW0C6fVWBFMK4YQqI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ljjH/TMHIWpz1CNwHFDKs1nqHSwsL834t/7gai79R00=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-srn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/uFH8rK803fFRwAF72CExPBFdJyfo3nQ5qo0m1oEjMI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ss.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-st.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-stq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-su.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			T296BjriNGee+TXdYkut02pi6kfJWP5GuKhLup5JZhc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-sw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-szl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gu/aJUsFb9TOkYbzPf+SuEnOxxsjjqUnOlP5TMXWvGM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-szy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yV08qP0sX4V1zUldcOWMp9BcCNFwsk4tTQxooQhxANk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ta.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4eVrXV8kRpR3Ky+xvnw9FJy/xUoP0hPVzFoOuJbWaDE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tay.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yV08qP0sX4V1zUldcOWMp9BcCNFwsk4tTQxooQhxANk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tcy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-te.json</key>
		<dict>
			<key>hash2</key>
			<data>
			c2Yux0Sq1gfs7XV5JJvYsyEGmIl1lIY+M6fwjnQfHK8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-test.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7PwqlgPkdt3WSf8syEqJmZP+zn/ehb6GjEKX+7/4Vro=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tet.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0eV+I8sDq46w/sJUM6Q2kdrGtHXpBi6iMYawTN8at0Y=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cWCRfl9Lc65yyw/wz+ZIi/l1w3O18gKS5xbi9eDt/VQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-th.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iIibTuQ9r0wI2kQ2k9OFgTNa5jaI1Pz44rlL0K0wLvg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ti.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qjnAm/9pU3YuXeT+slXZoDViYsLxBKCx7/zC96KOwBE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-to.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tpi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0ZrGO6npw1OtOyaphijpOdFa7sAXuicHnzVK+lbEyEU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-trv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yV08qP0sX4V1zUldcOWMp9BcCNFwsk4tTQxooQhxANk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ts.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EEd9zps9Po/IGqb2uqj51liHuj7aiherJ5Ts2I2SKzM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tum.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ty.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-tyv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WJw8Yxly6JRX03En2itR3blAZlDNdEVFwImvg2E2SdM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-udm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			x/lP37rqO1ha9CR0RH6RZA/kfHJ2h9+zw4KCJEzLJjw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ug.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-uk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Mf4Yyw0Ja6Jt7Gw5QHjhHujCgqmJl63dtNLdPEl3guE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ur.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CT9BZ5WY6SHiFiZgT+HgdmMfp9mG1c3xnXCxjTf0Das=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-uz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BYXaDiyZi4o6OcbuGEMs23t39npqqMqbCpMjeVK1Dwg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-ve.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vec.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vep.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AhRet3vtTh4o2tARO7/euDDJx9U2hIerTypIy0++p/c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SD2CbATpv9P10S54fS6ZVOkFakVNLJp3L+KvXrEZ0bQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uonDqk94hhn9zZr/UYlA0/Cg4DaOHhTbAbZJ5oDCDjI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-vo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-wa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/ErBsW2IOMDBuK6kLl6ndI1vo/EkRGQZVFaR1NS8/bc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-war.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-wo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-wuu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eblEJD/oW1dGUDrCyBKYOxqhRiycQEY15UgBhfpYEm4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-xal.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-xh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-xmf.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6JM8dhi2z2otqdhGGtFWZO3Y9YadU1KzxwGiPeUgYoo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-yi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yCQ+3ov4mGKum4LAXQj8zgJoe1Z8yflmQBHuJsSyBJo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-yo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-yue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-za.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eblEJD/oW1dGUDrCyBKYOxqhRiycQEY15UgBhfpYEm4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zea.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uonDqk94hhn9zZr/UYlA0/Cg4DaOHhTbAbZJ5oDCDjI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zh-classical.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FELPHPBmxISSBcDhL5fu/RVQe+R1oQCOYCu/+WstxNs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zh-min-nan.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VBQ7J4yKQflXe55DtC3FdzBk3UKXWz4984VPp46esmU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zh-yue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			juE9i2ivxZ6rIdtAWoqdMJsDSYOK4J8mkHefxLuucLE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/config/codemirror-config-zu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/addon/search/match-highlighter.js</key>
		<dict>
			<key>hash2</key>
			<data>
			OqdzzAEAnj3LTg5Tjd3SS/+oyJ7PNUNO0AQIjQGWpxQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/addon/search/search.js</key>
		<dict>
			<key>hash2</key>
			<data>
			OaLZTYAOjuREyqsgJI5jptSGWW1WYdZTaDxwoLAzfCE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/addon/search/searchcursor.js</key>
		<dict>
			<key>hash2</key>
			<data>
			yIM68G0QKt2N8AnzJaag6oaPl36kurDsenBBm3MJTeQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/ext.CodeMirror.js</key>
		<dict>
			<key>hash2</key>
			<data>
			UCAzramvnbrO1zNi9TyuRr0XcArrUE0Vhvw3hv077tk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/ext.CodeMirror.less</key>
		<dict>
			<key>hash2</key>
			<data>
			WrEDinT6dyQ1fhxTye5AdSabmbz7X/gWVMMrMbjIbts=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/AUTHORS</key>
		<dict>
			<key>hash2</key>
			<data>
			YxJ4kBACDSRu4ADL+ZJvJ2Dcx/cZ6f7SkcqHW0EB7Ew=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/CHANGELOG.md</key>
		<dict>
			<key>hash2</key>
			<data>
			epnzuTgDRcF6uIINS7L7MyLcLm/aNNcM/sjtu/C7H44=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/CONTRIBUTING.md</key>
		<dict>
			<key>hash2</key>
			<data>
			tmOO63L6XkzKaDwGlMMtgAcQtofKOgcZ9d6pYlt0bmU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			o/L+Ksa0caqAxzfF0oPdBJvckDpzg17m1NLKwC/dU78=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			2FeVglk/cdRsoOhYhTJlyQxNsaMVwcGQ4ANSNPyo9ww=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/lib/codemirror.css</key>
		<dict>
			<key>hash2</key>
			<data>
			o/v/5fOniPHMAww4EAIfRBFV0SeoqksPrY0Yq5x+wSM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/lib/codemirror.js</key>
		<dict>
			<key>hash2</key>
			<data>
			bb9fBUGYStMIQkqdLofYlVP7wHAs4wzBIfEody6Zlpw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/clike/clike.js</key>
		<dict>
			<key>hash2</key>
			<data>
			+JM5/AVZZHKAl1LR1/l95+gGuaRbgrX0yxtZVGIKFdo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/css/css.js</key>
		<dict>
			<key>hash2</key>
			<data>
			A3dmy5MnEwoDZJmeJUoj98Al4uPPcF47typ3TSLhA0g=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/htmlmixed/htmlmixed.js</key>
		<dict>
			<key>hash2</key>
			<data>
			YtBvhTDO4A27QPGmzVTybSqjmNNEi8Uc9KPvyytMoTA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/javascript/javascript.js</key>
		<dict>
			<key>hash2</key>
			<data>
			JVE5V7hEM86cO1BxmUe98oLJbQPn7Fs+YvF5MKl+raw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/php/php.js</key>
		<dict>
			<key>hash2</key>
			<data>
			5GC7UR5LMpPOzLZ1rOb+nOMX7Mgqpfo4ya2uUmN4X0I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/lib/codemirror/mode/xml/xml.js</key>
		<dict>
			<key>hash2</key>
			<data>
			NxOvsagQgFp4PUFHqDHjO7xGEJfevuI8fuj/W82MGj0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/black4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			uKMjsFbP9KuwhfH4lqmCG+HbUB0rsUDV3O/p4CZe/Ho=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/ext2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b7iaCai3RFXbGN4kLmqY79bh5Omx6RyNSwInXbn9r+k=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/ext4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KcrAoWSjExaljtm/M67WarCCCLM1NyrAmo6FzCV5Vog=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/link4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			cv31N7AES7yAeTPWzfMwQc2gvwYyBYtz4A/AbSHa0U4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/template4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kUj4zmyVzitdXSk/Pu2npfiESkzzZOwMd+ZYtOKIHPQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/img/template8.png</key>
		<dict>
			<key>hash2</key>
			<data>
			gJNO9AN/3V5IETahxpShoAJIgXpDfUx/hLuBxVCcPGk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/mediawiki.css</key>
		<dict>
			<key>hash2</key>
			<data>
			sNQFY5E/iRvhM/Xqarp7tmokiWVy2AV3x49FbgxoKW4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/mediawiki.js</key>
		<dict>
			<key>hash2</key>
			<data>
			3jZkG3yPni4CtnWte0N3Bhoq+FvxN+p38XtH19sOsJQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/tests/qunit/.eslintrc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Hww0MSjO5ImgyfcOuFzxC8d3uD6TJSzi4aEbr8QXgs0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/mode/mediawiki/tests/qunit/CodeMirror.mediawiki.test.js</key>
		<dict>
			<key>hash2</key>
			<data>
			mw3DLNGhEmwJ0PnsIWo6a5GVyEgi6ja8cNZ3S5hGF2g=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/.eslintrc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RyjOKJlUEgk1ZKcl5XaERBls/uRzlIAb9WRIxWTC6KA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirror.init.js</key>
		<dict>
			<key>hash2</key>
			<data>
			jB5T/LkPZGhgqZlOlctxIIcG1qxzEa6sBaLDs3ctlDc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirror.init.less</key>
		<dict>
			<key>hash2</key>
			<data>
			3JZmrB3Ag7lAc0Cz3MfOve3Rjv6BWn892+CcGFVDf0E=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirrorAction.js</key>
		<dict>
			<key>hash2</key>
			<data>
			3iDwuiJ6BxJh7goP0uAmBMKEMXDXd1Hj/kZb3Hmq5X0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirrorTool.js</key>
		<dict>
			<key>hash2</key>
			<data>
			nrDxVRxalkVdi8RPlgcv9S41nAR6I7v8f+2GdADO6rg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/index.js</key>
		<dict>
			<key>hash2</key>
			<data>
			R/TuSA4Imv/7xbcp2cLj2+Cb/uygEOeIQKiiEO6vP1c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/base.css</key>
		<dict>
			<key>hash2</key>
			<data>
			wmR113aU0O5+kRxHzLv0qdVMyvKwBnOSVPaE1WjRFZs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/build/PCSHTMLConverter.js</key>
		<dict>
			<key>hash2</key>
			<data>
			hjoD10vjm/EtwZcTUFifAwskmJDEixXTsxc3l8ZjtiI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/build/Polyfill.js</key>
		<dict>
			<key>hash2</key>
			<data>
			2F5lkRGKN3v0xDSAeTY3ZaZZObdKcKH9SlzqFwXYfBY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/index.html</key>
		<dict>
			<key>hash2</key>
			<data>
			LnNRCPmo+VSWt1Avrx7bshVLRu1PsV5gvj5ugQhnr6o=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/pcs.css</key>
		<dict>
			<key>hash2</key>
			<data>
			UJjhVgEO/Zb2AxbXaSJ2+hNpxl1TNxVaaXGMNuBH4zc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/pcs-html-converter/pcs.js</key>
		<dict>
			<key>hash2</key>
			<data>
			hQ0uPg91i52JOaZ2VPQ0LpKXPnJ9vpM6xUe2AcBGnBE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-base.css</key>
		<dict>
			<key>hash2</key>
			<data>
			CGc1O3DweJsHmQuzM7Qj/tAXHiLojbOqLIA8afX1Z1g=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-black.css</key>
		<dict>
			<key>hash2</key>
			<data>
			dkUeVYXnJwsDBu080HXJ++wgiRwuVqMhdO4Jofe2gIA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-dark.css</key>
		<dict>
			<key>hash2</key>
			<data>
			ffmWmvRLXqilXqhnxRR9hWQPria1w3sjNZxgkSoC/VE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-light.css</key>
		<dict>
			<key>hash2</key>
			<data>
			6Osztv9RZELf4RASYcS5GIzxbXrJAVtUM9tGSU7zqQw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/significant-events-styles-sepia.css</key>
		<dict>
			<key>hash2</key>
			<data>
			C2x2xkkIOMKOhpFwuwUVdEzVw0alDx1hlemzXJGwMK0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/assets/styleoverrides.css</key>
		<dict>
			<key>hash2</key>
			<data>
			yPE8hg4RyQho3izdgacdb5YgOp78hv5o5qSdsrDdEmg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/ast.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nRuM+rrim9miPyWDoBHK9MGT5Oxqeu2mWWdwxoy3+B8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ast.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			sUMuuZbiCfiK9KoPcC0ME9mzNgsGzo/zFJQ1sjywuuQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/av.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			6U00wkYbF7XTUYylWaJQmclil/X+ZXQ+DOEQdlo82f0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/azb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ba.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bAKSkexc8vAFs7I+dCaxwUZp3BBPH1AD6qyjeNc6U7g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ba.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bgn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bn.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			neIqm3f+ySDKX/U/jTYD694Map+gFH6Sej8i+Jc5HeE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			knIHkReBFlRRE7dRDhVQPvXRcwqG7mg11roD8WbdpDA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/br.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CSF6thMJ+bNHcBwnfRae7BWfFKpJeIDqLxmEIvPRq5E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/br.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			la8NVS4jKDDiMG18kcvayganj4GdsHg4Q1URqhvn4w8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bs.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RKeM8Zt8tlCZOtLUunZR2hCYECpPu2QHr9/2Ni6gdeY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/bs.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			IUuDaGigT3zrndaPRK/t+Ealhcvn/VWgfiaoWqKpkYQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SE7uU+dy9KqCupOG4MWz955szr3Nhrb93Txs5G7yt3c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ca.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			npNhNF926TVjO9EeKT+AXr3Qch0cDBdt/0e6GbEsKaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ce.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			eOcV22XJY4RUiOdgMnzHxEI/2jPrw6ywlJV7TxAVzMU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ce.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			dXazVbf7HiNqg2ObuQN+8r3mkg7T1unS7/eCh7QiNHQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ckb.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Fxtw+SQedkr3yGFUwaDO6NgHSq07jwwv+3b3SU13PDo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ckb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			eo8AgS/Kht7TYU+2WJ8Ji5eZQHyF4baPML3upfg/pn4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cnh.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NSjl3aYiqVHTz+IYdvXRqMqthtrUFgTkj0J1wo/7DkU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cs.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			qyOeoFJHZuTDxl5DhergnWlJJfcE45ZQ5zKb8FOPOOo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cy.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aQBRcw1gwDiiNUPbU3axfIK4sDUkvy+s5AmEzYrzMLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/cy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			akoRs+PS8x5Jj4aRt5wgDQAbj8oK7/cyVWo4yoIvhjw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rRO7F7BC6an1pd1LPAwaVUl7EzS7XPicLE859UwlNqU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/da.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			CY7FlJFPOORiV9OOW9By21AH0ZnRfR8d+cbO2Y9sx/U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fygGjsuxs/d7Dh7gqSbvHxaLSVAIhBZKZxwmYSmqT5g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/de.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			en5YdbnN72kgyXmM7w+hNYE1c99XthM5A/90N5/tQS0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/diq.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jYNdAUWR24asD/MYehGhzgGwY8pE2cTkl3NmglHjoK4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/el.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			MZ7l6HmtMZowadPGgRE236SDwgdL4EGfZxQgf/asCPQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/el.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			HatNriqunp163xwqPyqm/FSE5TFW0Hge5yjIriakcq8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			67nBHLTzEfGwp4HBHf6EBV9yJNJm+V0gPQRQuJiR3wo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/en.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/eo.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2LjAkcqhUKoQKylpVKK5ApHbT2JfP8AdI0SWuYS6CP4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/eo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			5gn9lYHQEpzDN4gFffR96SwpXMOycquYFDeWzv0Ng38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EN8yrzRgm9r6F/01nBW2LWcpTXz+nzFz+0rzjL+aHec=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/es.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			yBcoUKvPtJqNiek54xdlYADEVi12DDYP/gHGtx/l758=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/eu.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			948wgRLVA+aFBZcdXK1G630SwnrYbLunCBTGZwds2pU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/eu.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			rGipNvHU9moGlCzIA4X+uT05O3oe/ls3wNQArAl3qjw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fa.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ji7qxfQBvh1F9J6guzIXywrtbh9rxV100xkng9GKdRY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			56JmCi1MGu+NadFtncl+nnqBH2yTIrV3mPTPM/jVe/M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Wx7zC8q0RVfzyVHCVWbifYm5MJwmWjfoxamfgrUQeOo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			YxFeGlIj9ZBtwEc4AvtWyWX7hnTK6gjW3eNh50y5EhM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fil.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			6U00wkYbF7XTUYylWaJQmclil/X+ZXQ+DOEQdlo82f0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fo.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			TH+W8xIOYAjzy/wmrJh5LT5x30vzKq1k2toOApfNRpE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hcLWSwGkFHnrDyngvWRZpCsVqPPcTlFMUHVbN/32YNs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/fr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			IdGFY0ocSMbC2FwQ180Pur7PMrCBBfak8VrBQYnHpgY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ga.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/sCeqvmz37NieyG1NewfteneRFOXr1mb5sQWVqvX1ik=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ga.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			9mCizxvIdStftX95b5+wSYunoTDiMbWaUvomDOtN1Ek=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/gl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			itlyWvw82GCULjSwDji/6ft3x25PqZvz2dB9WnTOPqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/gl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			ByvwONe0586DIIsYwL7gjv9GE4FtaaIs2gZvzrLLLM4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/haw.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			c5s9Jm7NTphuNCuRT7JY4+7hrR1r75RDTkcqrGHpwt4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/haw.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			+T/OBhWLUJtMBoq/L3MjOkxhgUVcq+8p3F4t68fxoYU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/he.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h4KtlteiZxWhlazY8SNuAktyWEinCb+DDWJ4s3Sepsk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/he.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			Y5/5WUJGoAfcWDwLtDZ/gBM9HzTmPhx9VU3VeCVzFk0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jdhY88u+jXLK47kq1dVsT42zmXzAla9jauVwyloqz5g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			7jubhQmkNzIQmuj4sa+IQmT9NgnIc0FQEL81oSbsjCM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GJMu1+k1eUr9pZygUgHS9mAB6o2U6qpTsf6LinVFisw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			pEdFPep8u5ETFJbRvw/YAgQ5LmyYDDKR59oLn+kYozk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hrx.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hsb.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ZJf8dtZ3CQLh3ESNhrP09kyYqn0IBJ4rMt/CUogz/QI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hsb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			fH6ZaOqzW+H1VRvQTztvLqn+KbivvbKG+2kj+dOxRrw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3+dNiqffKvGKQALAhhCTGqdcJWx3X5GHD4B8rxHae6o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hu.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			DY6G6/+mH1sCgqSL9ulbbQK7xHxaPEPbYhUpKHKYZPc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hy.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RQYtrKRM7XylWkEtGJXniF7VP8Up9WazyJP8uMcbNfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/hy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			gyXc5RM6dYVIxiBIo46etf3akbfb3NEhceS6Zsp1zF4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/id.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+IJQ2vG9/en2/FAfj6gCZQSgkUZVt4EfdKuFBbUc548=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/id.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			hubHVXQr95JqBl6ig0lHEBPFXjXEQBXwB2XjmdmW8wY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/is.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IbkpGXmZMM28OP1B5K66h9bx7+2cuXfXOBlJ1/zXEgk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/is.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			XHtIIxRet4EDXUqXbBHE0Bq5k7IsbBRcI9yaH7cm4vI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LoNY38bDl2wPjMrUbfavadqgYN3d75xACRczH9jXrUg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/it.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			BvfbK00r791eC4KaULOzVFHA7TU9Xe8H/ogvO8fqc6s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CkGnyJ4XRDST9PSwYuuDcG2r8dii+abZX7Wl36kK10M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ja.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			7CCunQhFTRU++ZElEFfbbnDAcS8U/qZorz12+XsMsKk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/jv.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k0iZ2qa8PVt2wmfusEVkCNaliab3tA3iWb/EreRT+Kw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/jv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			fL+pm/rinaTHkCdu2gANWM/teMZCbNMM2S14/DqnJNU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ka.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WQhXQdIEsL6MCwgK8Y2vUoccIchgk+/B8aZAx9Fe2b0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ka.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kab.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			46gf9BMa4ymbMWRPAl6EJB8rw73WJu49oc9DDyQRyMA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kab.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			PpCBs1nOieGFnsfiS/TtsC642rwrOH3roxvvHZLq/6Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kcg.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SdsY1+Cqx++Hx6Aj2rIDdKN7rfn6CZTGRqHaSnrmmLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kcg.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			GjY2AdRscNWio76tg0+LkQWbJ8pwJCnSX1Kczr+7Tlk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/km.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V9TnW/OrFJRNRyRquOZSaARnoB0KejzxAWwgQF6Y1As=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/km.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			KcB52/w+TdbYG1x/08l2ds2XRHTD/Ql13JO4KYOUDAU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kn.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zG4/CUUV9ld4sHZPeKsTC1u6+x8rOqftMDl8+4k1Fp4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/kn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			P4eK4lDsPaGcLfsNhE3osixDl/VnPz/kfCACKv07ts8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ko.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			0QnXNjyAX22TI0J4qgkWDNLXvqvElWUqudNxEQndzOU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/krc.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			f/X4juIw5lF06mDh+pFFtTT0lp0sOFXbZEOGqkat5DQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/krc.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ksh.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V6e45tkqpq2WY3S55HyT7qqpold8ylZFxfovGKOH9Sk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ksh.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lb.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4PmGQCy2U04iufKaUWShVS6yf5ciTbV2sccn8PoFgr0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			iCFiVua+lIGCdPi0E5B+uVJ4ZOooLp4nRYpLGwAeHok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lt.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			z9v3+mWN9n/ymVZaMHak+fDtCnecQaGVeLdgFXQG6L0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lt.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			TKIYvxf8SVu+T1xkW40hs7qQ7KtJqfBkw8wvXnAfNvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lv.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xjSJh/2bGPxpU4Wc5WTvLIGCdq65qqaXAQYxtQE0gig=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/lv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			6HMCqNJkypyNBJi+mQF/ykTtohybRkkZkVtf4MK7YT0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mai.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EQZJMqGSmvZZeGPom8SZZkBgKZDWmFQ6iBrztpYMHKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mai.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mk.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xhq/jO7dvgOG30tRSd356j4Gi+UCcKe7GR7IKHWOg4I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			qCKsyAWZofUUyFEnrTKnHQn4Vb+iPu5w2kGxf1l9BK8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ml.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IUf4Whgp1tez2DFWp/qh/X0t+Ci4AQZGQWdD+nMfDkc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ml.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			4rg97K4GcoLUUihfDJdpg+paVqisi0KscnFkZyn989U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			H0IiqPiEZ5IEIF+3asNSdtPRmJDoRh4TAuegB28VNnk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/mr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			/bEANG+g5a/4trxdghjMf5/+jSyv0B6W2fwPX+CRxFA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tDxopG7te/bPTlotHbjqLPC5SW3UD59Pt7hT6hGcvvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ms.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			sLncV1Wztw7W3u5mzyMXT12l5g+lh8lRNqN6PTS9zJc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/my.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+CLC0nDG4h0GIFAeT8g68K1aRZRY7Z3rMs7exdWw5Z0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/my.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			CyO8b/RTu1ie78/iIXpa3dPGIwSzsSJiNEPEFp+gMFI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nb.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			YuGsl/at5Y+34yIu/oMl31TG+MANUiXBRmzVxGr3Dg8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			wJZW/LkCU6F71A/DvUoqJ+TB/EDEdq0mRpO18x2Fb0I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ne.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qEb/fI76mN4wFGTU2HLyg/nJBGp+gGq9iPHjJQCXR1g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ne.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			TWTZ2E/+Si/F0w8P4CZDKc6fZn56BDjSTqui8IIIvSc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			M27tNGmXKtHvRRojAimK/SnKta7SbArg/ziYHbjf9vo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			JqcZMj9nA5AN7Eqym9EzqSbveNT2xVRAbejGuou86wM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nqo.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kIa0jxwdZ+31zAgw9xlBK1O+H/S8MwJkVSJs59+BbHk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/nqo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			UytJ2HCbDOCKYll2b6QxZNhiW69IcyLQooKLoL7Gq1o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/oc.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pmx+2+eZOJxk0JN6EiZD8evb0xZm7o6GbwXo9NUICzk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/oc.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			KV+Zs91TQo6HMrfAB2dDxY9C9J86cfp1aTtm5gIxFRA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/om.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qCrZeA+MDEIZlfxa/LVvJ7lADBYYGVLuxQKWCnKjjhk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/om.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/or.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Z6xihHBYqYlio+y3SmoqgUuTQx02YipWBwxf4Aq1WOA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/or.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pa.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			djwjdGJEBovLlL1l8lTio7ZXBU1NX7pXkscH2rZ7p3k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h2q3pJ+OL2pWIkUTVNOU5XBwr6IunCUduYL+gF0st+4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			ZoAdBIFiwxDjxRQFy1qoddYG3ExctcoRyKHJX4GIsKc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ps.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NJH95vSPJa2QrdvXX7SZdB+gz7V1RxURz0D5DqPkNuo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ps.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			rBFgnXM/dNb8Wf8RZOk2XrUa9jkUWaJQ3Ck90lic+Pk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pt-br.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NfE6oO4jUZSiZcXfmrtG32qtyfEHTK1Vi3Vz7pBM9ZY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pt-br.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			keydQ5QaP4JiapcyRkbBFhfxEoppzquQSr3/1dogyeU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pt.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DH+gnkIR6UafWY7hxspHcVJg0Fvh6VmNSk8Os8Ubax8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/pt.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			HeQuJpXG5ZdZMP4Y/r9qHYAkFVDeTNndsJlDgiiFrhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			B8H2FFQeSv8Og/rXU99mWJbd5YFsR5wHFos1SSuJaPI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ro.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			dVD5q5pkWLCGBV8BPj6x7HWC76EgYJav5jbW5mQhNJw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tkQw/wjDZj0oGoQjKweBFymWDaqTHJvukdJaAtjTOIM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ru.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			OJnOvC8d+2SW1KnTc3WrlrHo5Q6DsWpFeBmjuxXQUNg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sa.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			d+LIHB2TRC8ra1J/2J9AKfZM9cK2q9MNIXjBFhj+f3E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sah.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ZIy/0GTRAUFeCFtf7E+VU0EtrXd3UF1b4e5NcDlhex0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sah.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			ws+ErLre69Pv5orPEfQhWXqBljAAlL+tQPFpik3v0pY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sco.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3mFdecleUxqqYKenSMCKm6aAONZnlXQnEY1d4uRUS3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sco.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sd.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			H1VSx3geTi79tcIMuz40+P5rArmV50uPi0bJUcLCdd4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sd.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			MSbwUEKYQs4124sNuc6He781XkCsu5s1fkgf9F4+Xz4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			BTkBCmJ7oEYYGR2XYp7l6xwUvzdwnSYgAiWaDhcxseA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			XOjtBDSGuF5uJGQbY6+7y0bU9KJUF0lHTGQPNMULe80=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2vRvCmKD/GQkkORKHEY5BHiWVn7HeMYj2qEvYwi+F40=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			CTtmI30mzRFaXH1qfbVGgbT5CrwYepbMD1UuiKcFKFs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sq.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KTLZ150JnqrCuqI9ILiN8G5vWvpYXL9Nt2ArgLw5+h4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sq.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			VmIpBh28Wdp1oVNVJUMwVBTM1ap1GY7WshFIjjVL3FI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sr-EC.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UnUNVqpxUsCHRyazJVYI9esfuuN7GtLrmwB8jjfihws=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sr-EC.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			C5WpZfcE+Il6twz3lWhRNkHKe4Flh6Zg4OSkeUCLbVo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/su.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bfjjsT3Jl5CtWkLFX4oOLB0ZKIM+mjl4mK6QRHlIR9g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/su.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			BbONaABZPoMyy639b5fjJvhqlbpLk4Lr+Th9PtTfQqM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			sfb2ibTLv8UN1cfTa7ayS5D+kG916dfk34m28wGZ/5c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			N6aeF4oLAxaCPV/Ga2N5ZzEDJ8WKG32TN4/qi9EKS/c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sw.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8xDB0+52Ej+Rds/mx4/uv/kDH5pCyeCi4NhTdaNBkfw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/sw.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ta.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IOU7ywp59LYHSgLjeif83NAcz7+R2PxUpUJS0vrcop4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ta.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			i9AlU5CzDk3eG+aACkOdBrkYI67a2DaAzRKgac2nuok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tcy.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nsUhxWV7QLDLu2ez9syThlpLLzVgsb73+8MzmhD1Ma8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tcy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/te.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			BlCShfppW8NkN84A6JZGsutQrMD2evyoSV6PpMKBXUk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/te.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			5At4XhDgY10GzUWw4PSrcXW0x/QB767zR4EgJSYjNpE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tg-cyrl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oQchXWZVy6tOregjFgSgGgEVzX4GSmK5/mjW8O5vSWg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tg.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			6U00wkYbF7XTUYylWaJQmclil/X+ZXQ+DOEQdlo82f0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/th.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4Y2d5INnp27hpbXrw25bKb+CCP5oYYqXyA1DP7rDVVQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/th.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			fGXb1psCuxKlsf0Sg9i52hiF87Q47hAusamZ4Xam9pk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ppdIxYOiJo5381NHewV2Pb9O3suOlMgpWzXN9Ee99rM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			yzZcBrY0jdGwlzzuP6KXdg2TIhFkiKtnSxGvBsfAeXk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/tr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			DNg6cqCwQML6sA7M7fwnPHNAYz4hL5hMgdRJeHfbwyI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xTwmBx2yZltHmQH6s8mL/LyiSvs/WxyRqw8zM/gjQ/U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/uk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			vxGNzzWVZGS+/ZiPkvSwYf92Zw7ZiRm2mI/rgOhSfCg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ur.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Vl6VXPos4y4yH/t+NIp9Ub38+kOV5HcBHygjZpNvXPo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/ur.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			nkzjZSqyM9cYzNkebyRZ0hDxn1WZUdkt+/LSg18/lc0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/uz.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			q5oDOXco/bAxFyI9ArrksJCKXRvuJ0n6Q9pmZ4LhppQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/uz.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			4SnFHCpKA7+t2iBE7IUWEe68de1+74ZwJE/nAV4TtSw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/vec.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nNQOX+Ci2vjC180YFcUSUZt18E/exba2VRnNckPKhTU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/vec.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			/o6AD17KxaLCc704DhtZTs2Yev3tJlVVGAFWk5Cwd8k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6V4/iNGbje0rZNixKgRq+lBzfXXLCOqsIohiiTn1Yb8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/vi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			zPZGRgB/7Ws9cPrHaVRXGdF3zjjYWsrEqeSNHCFVQIM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-language-variants.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Adjr/SzqvKf4RmT/jVWpnNxkmWQLFd1+mN/rUKwZ4aI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-languages.json</key>
		<dict>
			<key>hash2</key>
			<data>
			o+vTCvCcwzzjp5m81o92Epsgi4oRLFL8wLncO+SKx3s=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/aa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ab.json</key>
		<dict>
			<key>hash2</key>
			<data>
			p5hXMDHEdwlLOicMq6IRMjkxS/ag+qwVM8Ka+C1zRNk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ace.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xYILm8W7hl1djhib/pMgzX3jEWU67jOhrQBNeDQYbwo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ady.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RhcWBJ3NDIRUNW9VQh9WLIWci4pEa8+kLgq+8YV40Uw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/af.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VZCPipdFDwCPh+IOwLnXxwN4oKXrdpNsS9bfLgjTJzw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ak.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JOX4W3drpgfbd3qm4zt8qIbEKGCUQjTIJmStxjQzAMw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/als.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iheN00zcE+tM/lmaZip/V4qwUxOhS6aG9z8ExSxdsHg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/alt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			h+s+XmVKtDF3M2LJndL9AEDdsnaH6N6z5An+DwK4U/g=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/am.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VYwgF+k+I8Wk80IEv7xhhw5VB/ppsBqmWsYI6ItPdfk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/an.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zwfZoDVbNCiQ1u4J9MJC/fOJJGk5yZqwz6aa5jUPpb8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ang.json</key>
		<dict>
			<key>hash2</key>
			<data>
			k6WrCkd1Fmp2gWC/asNoIBqVF+5vrt2h8PES7izq6wA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			V0M5/3CAvIdOfZp2Uh+cPy4GKADfcy1XXizttxO6e0A=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/arc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SyBM6gpkZW9fnclbwR26KUE+i/7Drp9lqF5LvOoJ9f4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ary.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GWUUatW9gvUW7Wj0gi8vBJx+vN+GKUtU+Cz3KJlXCiU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/arz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DDo99mRZiRDsaS+MMUiZJ8OfHAyoXtbXG+gKsm8BSBc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/as.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eMKyRnYi6fv1a6OlO43/Gz08Mtu9D73fUMeF1enekhY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ast.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LW6iZfV8gsJtxQ30ZP5VF6JmVks+LOIQz0e9mv/45bg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/atj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6/EecLgfZCh1jW8r/dYTwkBb0rXY4HAFLn0sGx5Hbgo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/av.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8CDvbCt+SpuyXP5DtaNcZ9tuNSQIJJi8A6khPF2WizI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/avk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			42OFrod3Vzlkw9vuGgqO9Bc6RFluUSoOzjLKN5e+6Ws=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/awa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KQ5Lk3d3mj/gBFb1leVJUKb0XHbzZtzFJzEO1WxwTvk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ay.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EjfyxgVPW5mm7b2fzS/vx7AuiDfvRXIz2ZUOp02Bb10=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/az.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yZsqNb+tcmrcKJHBDxtRAz9fS60zRkhhuHY6rhXhWbY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/azb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			18kiqUF5Q49aUHwZnnXmU0A3xhjwSIe51RDucaGpnN8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ba.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9/zSVt6vrCJ7GQ4pWzkl6E6EqbZQ2MylADD6OjRZ300=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ban.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VwRFdO4MNW3FBWEcBEHfweBeZHgPaxgxJxeHEhWlWxQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TK3Z9xviNvOeUsyyHjlL7lwGw5AQJ28aNBvpr2G4wJw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bat-smg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vE8UB/V/+lajY/rjjwWjOFH0oWLPN1NrakaB0aaN0ns=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bcl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Sqx8eegQWX8U7XGumjGxrcglA94PIS+PJil+JyfcJ18=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/be-tarask.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l4gcvPqZPJsdRUCklgAmOa+vUcs/9o+WafGj6Y3zbZw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/be-x-old.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l4gcvPqZPJsdRUCklgAmOa+vUcs/9o+WafGj6Y3zbZw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/be.json</key>
		<dict>
			<key>hash2</key>
			<data>
			epa+YywHmZo4I6i05DDpuLkL9Ci6xECxGTDLfu3UkJw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5pPp4fYi17tBWQLzILSiIp6VXDIxwNdpYZVpNz8z9+s=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GQwJ38uyTjuFHuHYXWeAugGXpeYATnxsjzCuKdMXyUA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jq472Bc9VCSbKO0nxLS2+5dBZn9WqXEGOjJeZ+tjBIM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bjn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LnAdNUdBkH6EdMtvbu5mVd/RZVfg05VHeE4eHzOK4OE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			acP4yB2lEpIrEvvUqnokub9JpJpwikuM5NQsf5/+C/U=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ANFNqhiDOr3sCUwCZVlAnpjISXQAyz1ndjplKCDjnqo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8eHiuP1Y/wg83cG5k17KmM+RUQYdItNCZ900osKo2xM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bpy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5PcpTRGNqNqhmLfeX0ckmkaY6hhxfU/IwIZZaPiph8c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/br.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nVYVkJAz1XJ12Ny3Hq1vnfjFjSjwAPKuU7Qlxgv5m/w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bs.json</key>
		<dict>
			<key>hash2</key>
			<data>
			P195FtiWLUeR/q49fUy1fO9zw9XNlylC+CYCwIcsgls=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bug.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hidnctIJulGPSeODSLjWM2/jtEpicp1MziZA6E9YM04=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/bxr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3wXhQfC9keO4aR9RjdpLDB44EJJxABSZfj6u8rv2/pI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ca.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IZDfIQk5AnI9x04F1tRXWB1w8/GsTLCnOsiWQRKfypo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cbk-zam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j5SlCgNPTQ2EXF/vXxpjmxqsjAaTcvOXqXmW+bvtYWM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cdo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VxTjLUG6t31MibPhCWAoJ7/2/xs5ZNsJrg0UTZiBrMc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ce.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Gy8MZRBORhrs+DjS1Zf+UUqn9cnyD956T7GFCGckb4E=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ceb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9Y4SoFsGvzQcGUHvJ2ZK0dw224mQpPKCnEF8Xnp/aT8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ch.json</key>
		<dict>
			<key>hash2</key>
			<data>
			77znLRecle3mhltkZrvNbIxNtj7NGH1QOyZQgftQhig=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cho.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/chr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			k0hz5WDCXC4SVIeNUkZA5+cqAhZqgDu9YJibx8QlHa0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/chy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fx0njJ11R9QVMR+7igNteJowMDVGxHthSbWNMuZI49I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ckb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Yj+/VcpkBt/y53Ce/XAP4jWgbdm31ucZF+1ZI6wLNhY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/co.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BMMx70tmfp8l0btYqf/6gF0YNRm0ypvNacMhsBWm/KA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0xeL18l6R7aEFsCo+W/3xxUQkIuwbCMzhiyIHDyNBjw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/crh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Pk4c7OSFd2MzWUefGFH92ENE0Uzv9xGrWZfr4JbjInA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cs.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3Xw0WcBSdr7TiR745OQcP4E6DLYTZB/+EhaoJ6eOd34=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/csb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			NIAGYEqjMv/LJB+uZ/s0wahlOc3FjPdYcfkDhhgYwfA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cCnNLm7aaX35LiU4xjPbS5ThlwRT3THMljpn249FzLs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			a4LixZZV2TahuD4e7XxEUe8hZmU2EopRYhC42rka1p4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/cy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ff+7JO+4jSnoOWNQgB0GHM+Rwir7X1DRb4MboDD0ZRQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/da.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XqWD7LcX5pnNpdvq7QmG0opO+0xUJ/4Kxwm5yduISPM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/de.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lKCSPMVN+8DP41TDdTUZyNqS+NYtJx811/SBtuZG3ic=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/din.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1imcXf6zBSB9zF7tCuCT37ivwvKFioJarrYYNFVfn6c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/diq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UEF6CUGhOqNkrln3uMgKcFTJmHxc2ryHA2/3hjBwZro=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/dsb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			88GT+jWf683d8Me6Eezgq/YJ1PSLCprMgO1f1+aVL1k=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/dty.json</key>
		<dict>
			<key>hash2</key>
			<data>
			R8exrUiLnpaO7OThru4o7Zgg8gsN9xOf+Vax+n+JHfs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/dv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			R/9joIw6a/FgMxEgVT5f1Jka3G/Plcwyx2lWoUI/oyc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/dz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			H346cgMg47k9BINfeYz8PNsAKYCiI/jRM5L1d8NLNEk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ee.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fY8iokiWKxcyK11X1Cg70ltdyf0iMvu0G49I5dzTHcc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/el.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4PGiK/m/VznDqgvkhsr+R3sy0TFfzwR/0lsr4BBSn4M=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/eml.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nstkjswahB6GYpvFfXsJYO5DC5CrQJR3qm/8/Oi8O2I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/en.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iAVcBk/ueEQX13Gc6cm6FPoxWIEgg9n1aWFh+grx5Yo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/eo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RyFBSw+Toxh+cEJtlX6rVjWwyQnkYzKVvBqoSFFrhOQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/es.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1U6riZyAvjnzwPjM/pLCa7GDjvAtg9wgvb4feZILco0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/et.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IpZTz3h/0EHI0GAeSMq/9ITAvqIuhHX3AS7FZdE8o90=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/eu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			auYmZB3W4Q2c9oGA/AQN/nz+5nOdjURvn1CCfChrUWM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ext.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yiR1592orT1EeIXuSsOG8r9SvSTg4O268jT7lYiDmQg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AdvO/JhN0RikgtDzDkc/wozd+E7puV1NKts/5sqw3AQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ff.json</key>
		<dict>
			<key>hash2</key>
			<data>
			905lSbnHY2POzgaOsSZyic7LbyYdSnw+5wv7Xl9IuBg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KLsOmQgxS/22r8y3PzRo9nbDzpWl4zaFm5Qrm2y8yVM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fiu-vro.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+dgGJSn4TZzedivGM7PWTKlY9rMinxBZlt1+JCRl1Ao=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BDMVvTSa0fm3fRkuzhKH0h7V0pNBjO2BK9Lz3imbNE0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WdEg0593Y/yyIqiFpxM5mHDXO06NBi36l7k35d7ahFg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Y0VoSP0xyuOhTWY1nTXpmDvGGDQ+KyVGjE7ssDgQc7Y=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/frp.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+RB+zRyjdMgZHj3AbNDyD9lAD6D/3ZqQhogPCAwJoDE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/frr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qIpzoiasgt0i4IGxwE6uOYXJAHQc41WBL5IHJ6otqDc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fur.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BAQaKaT7znH7oCmvOXghrKazw3CPmnHHSceCJNquKdw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/fy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DEw+U8dqbHwwZJlscPuxYDoCw50sc4j0eLpW9vXYVbs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ga.json</key>
		<dict>
			<key>hash2</key>
			<data>
			rRcHaRmi/vYglTLUNnC5fnqCC/q7MSz24qVR4mZESTA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gag.json</key>
		<dict>
			<key>hash2</key>
			<data>
			wCLR/+9nZwYf9b+iTMDjO89ntQUVZYRSwap5B9r0xL4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gan.json</key>
		<dict>
			<key>hash2</key>
			<data>
			sA8RwX3/qfzFSRy270iBRcxSdoF0lQ6v/OVyAc1P8gE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gcr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			liZr4WuVSCJjau4efD9VlWJ1R1J9PgpLCkAkJrtqOhA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Aq7z5l+ILgq7bORW5yOHxDUMGJLansGI8csdhjlL78c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Yg09nzxYO8IXYyC7kxgEvSvZosnVOUTsw2j/xFK2xCg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/glk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LmUzWdVRaE3h2wd7Hs4YF78Yiq9Oh+MkLgxF7vgrOvM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7N8F6aJYidRcEv2p1JW/ONPbgKyi3X2tg6BMmPJPf6M=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gom.json</key>
		<dict>
			<key>hash2</key>
			<data>
			C/3LG3NK/uEYHBQ2zmyKhxvwdNyj73DSJS10KycugEI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gor.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xKj14VogQt8xi0zWjLSt6aLa0CUtpLGuqH2ACqjTq5M=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/got.json</key>
		<dict>
			<key>hash2</key>
			<data>
			rdi12g9yVgnOtK3JmvuYFTUO4NVOzNjGIE1SqFQnmPQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			HxA6bHJS7mIZCK/DUVjW0QCZlTW1lWirFNIwsz2j2LE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/gv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KAlZDmL5RtsGCzFMOKWmo4WIF1WBV1a06QiNwqKQxWA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ha.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qpNVDfpnF9m2b/k3jFMp64ZzHanYF1rSbBJFR+E843I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hak.json</key>
		<dict>
			<key>hash2</key>
			<data>
			MoJhFzKO9FZN10HaIejxxjeXVeAdO4/WYGXuzVeeDas=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/haw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OE6dkLSkIMUNZbFr3SwhuaW99JTZx5ajxh4cVSKlI6Q=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/he.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4Gzrf9e7cS3CqkEjkWSW0h2CcFDkj0T5MHnuQX3ag7k=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6eF1UX21E88LTtT4D9ZRfqm7HUHU7hBAbK8wstzC1Ow=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hif.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LKWeyhyP7d35/lthgdgCkR8qD+Rr7YOUnTppMKOOFpI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ho.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kg/rRKAy2LA03Z6MMh/l0MECix/yH3d9B2Yy9CRd9vk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hsb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Hv6YPdULKNUDkEwWaw+osAA3bnn2UJRnYqxeaKKE/Cs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ht.json</key>
		<dict>
			<key>hash2</key>
			<data>
			R4kxXtpUpWdXSP2E3IqA3393OR9z/IXddJjjDTtTImU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3h8gDblLUBDeE6zXwUF2uOy8S50CQh7mvtexkCLt8ow=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Ob5CSKVyeW/aPXl3nbq0b9UL0quq6XS7zxlwpFLXhxE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hyw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Ou62xtl1srXX3579AtrJrWA5JqkRmI74XH9pSg5bFMw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/hz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ia.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BTW6WjFP08fgydaHuk13ZVzT+mvDKQw5//HG3Jcz4AY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/id.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Vo/SpwVosApiM5J/JJS01CSOe3l7jT7v0b+N7WeOxEk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ie.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThT4zBRqaX9rb0VQvsxClbD4MDfR9vmqcYhfJJvOaIg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ig.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LoNp5jJRPzScKb5DZCFd9LSk3o9pkMtu0qUT2+Snibg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ii.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dGyMAA0W4k1OWaT26H5uMRzvtXNcQCcCu0LBkgofuCw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ik.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EIcQEBTcfIY2GzY392dbjdiaRtTanPy2xjxFaH8GxLk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ilo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+xZs3T/SIfpwinLIXJaL+timIHm/KvMklXRdKl4KlqQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/inh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZYVrSkFpcXZq2Rn4RF1d647mr2TRFDSuypeht2IM+sA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/io.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+gHu0clFOGvtRHbMA++Q7FvIwBS8DJrX/XD7PeIlqw0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/is.json</key>
		<dict>
			<key>hash2</key>
			<data>
			rKvhPFt0cHVyy+EdsvfBa/PIxklLJz4j9N2Fb+6oNY4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/it.json</key>
		<dict>
			<key>hash2</key>
			<data>
			NUKFEaEs+dVjftz50iW1Dfm0WsFsQkyytDqcBoDs0E4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/iu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			koKwoMU2ixLR9Lstn+T4ZE6C/01dI8skGgdqdnFMW0w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ja.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZIC05BRQEsi06nDdySkX112zV5R0m1Zds55jXHbkzwk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/jam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZHm0CpwUCdrERAIiucHQ+AI6q5718QD/MBF+mCKUsA8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/jbo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jdYoJP7kjL/1ris76tqkLuiEifEPl9jSfauU9A5fDI4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/jv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JNOkaxMJwhdaqdbibjfn0eMfP+zSkTlKGZOz9Dds2i8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ka.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uqy/zmvXxtwhgPs086oNHPLlOa3ly3dUQxEudeHCLuw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kaa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			d4Wg02f4HY7tqXRcsn57i2zfPHYfWJs6OrzEjBYPKLI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kab.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IPX+OtjY/gOtbTjMfFhJtqfnH7z2FHBOO/gDwO8jv3Y=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kbd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5pMc4aHZa0bl1jqjpRDrw3U3XNBUhAb3wMPbWUXLNVA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kbp.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5prV+3rmT1XWV5+9UXds8P+D7GJhHLEuFh+SsbNSk8k=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nscjKkf1vlhm6VsH4x94vn3XjiHyNGlcQ6+g70uGFRI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ki.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OUh8krfNMYv/nZ1cR/n0AkIvhqLR5Bu37hm6HKH952I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bnKIIKNP4fsNjbppUdQg6bRQAPB9VZF28x7/uzei50c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/km.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DWwKqJ2hLkHe3aQ/TaLXa1sSmAQO3ovsKhDavhVIVxY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5LgRN+oJNeJbRGmBH/Sd/JMV1AVwSQSWkuDeusQUPBA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ko.json</key>
		<dict>
			<key>hash2</key>
			<data>
			85NpBA1WzFBYGevhSyDT5uOjananGFP1mtCTXrGOXes=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/koi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7CembEo5TpSMg3dgHpONRgZkjvW+eTfsSUijzzTxlZw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/krc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PzQC69xyrLhrt2VzqErUD9ojpfrCazEtFawwaIT8Uv8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ks.json</key>
		<dict>
			<key>hash2</key>
			<data>
			41iVr6rTuFikwNUtnSCZTUJY1rNuR2azsVBljYMjE38=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ksh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			89mxK/9kzNIFB/Ce4XkFfThIj6qZRQh2Zv/NTiAzBqc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ku.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fzVyiOeAlpoPHzjK8o0proCXZMN1idSzQ+rEdHVKwDA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IyBuvjof4tnYURXNlt2VurQaig0JxPTHukfp+nvdzJM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/kw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1Ip6ZwSNWzL9wLldzqPwpSdBvhbVAGGrAjMTbtnvPW8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ky.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GOczrxaMGs+khFmFB4+ClvI3s+kbCIb5+/4m2VgQ9Pw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/la.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hSC+o9al9mScyUzmUSYDTCis77au8eZ6yLIt2MZAvso=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lad.json</key>
		<dict>
			<key>hash2</key>
			<data>
			teABA4R9n3ght7BS3sptl8e/5dYaOTbstPuDnyEFBbw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jmEU7uFQLPmaVfKMn0Sjswr6hFDo1ix2lnN5Zi9hXLs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lbe.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kphuCMHRbtGc9bKDyUZ03/H3nOB5QswIlJTKiYKUXnA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lez.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kJ8qZyga0I9b6it9SbyZoEOUf+hBqNBrrCTs6F9M+7Q=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lfn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			YNdGAiQey7gf2wgfof50P3npWv4/UCWzwYFkrvI5nkQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			x7JDXtlPBdgBefaqlsjY64IEpC7uTbMHKrg5446RYhk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/li.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1YcshA0devcy3F4pFaqoV3jm/SlvLfAtci+i7WdnMLY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lij.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7xExLZSH38aWfSFYP5ASYNTbS3O1xCO6yPzQ6QISmQ8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lld.json</key>
		<dict>
			<key>hash2</key>
			<data>
			wWtmfhIo9iX6ZMgh5B0dnS2zcTr8BiNEMWV8sO0b7Is=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lmo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jSOyPzA5ahlzumu4vzvapOpdXC2Me4jkJlW5K1QEVRY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ln.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hFz6GpO2GgL0ex9bC2VNJX2gYxpF/5oBB/sbGbvgZWs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TcgainR8suVCA1nHG+MBCI1TUWJBYDQlWXijRRkqJDE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lrc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DLiB/yS9JBW1yxm7lv/u8NraRvNrKNdyfoVyfQXaBHU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			A52YTCxlcGkU2ppjz/WtVLPZvBzuqO6Pz5YTaZ9gMfo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ltg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hJ/+bFz6u1vEs5RSqb+3hiXHuR5C6/lBDZmJvCkuR9I=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/lv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2iQ1v6tlYd7nbYsZ50lSFwelf/KgIkxF8iA7YbYgIRI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mai.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+nC0lenFdbMODo0HTUt81j4cQwQzQgSUVhA6PsfVIwQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/map-bms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3ervYYW4czwXRFabY4645Gz/VeqxBC0owgkvV+YFYMc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mdf.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jD6YGrmIx33pWtQpsvN7DkkqVO5seOtCjOyd0KS1ZZI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j13K/LmAM3ExFjq3ysnzjRT/NdC5gU9W9d7DGyDUUYs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mhr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1Y0c53xjOe83CS2zjW9FSRBgDTaRrqQ38T8d74nupJM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7CVnDcKPsLD4Qs+lSGF7AKV+P6uGPJmC/Z63tNkgimU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/min.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dm/Bpc/TaTj87VxrIXpQMm5BWyGfg0PG/zSW+CKcKwE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EZpl53u0ck7eFtEfrC+PyYQjG37mxpVnfv1NIYK3ZUw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ml.json</key>
		<dict>
			<key>hash2</key>
			<data>
			w3QAEDDRz+GNUCM6hwF30ufDAJf9uOdzBzTdWL2OBjE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			U2KpBBRVuY1LpecSniBNMWM80jQowESYerfuGU5v3GQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mnw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vfVWJzwrjJkJGap7lSnO3XDxm/FEd2vaI+FJSO059aQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XxdBogF9DWVTCeB7dz+kSgZyskiRELr6AhhvzdWPQ0A=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mrj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			61YvTLjNF001fDngw2ajuS3sz1FVDBflGb0vs1jsT48=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E3COyNn4pVEHzmWeQ4Qp2uQExwJfCaFtSmqO4rZ+NI4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			d5MixQf6nmTFzS+CnW3vzQIFGr8XLrbH7tzyaMmaCoU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mus.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mwl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UDuV193X/XhIAchQ7237rsCexMZwWjm7UxK5B8iQgvE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/my.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UuMNBunR/YSJbZmBdR+nlmv8MvN6aiShrlwr9/sZWXo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/myv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LgrHSWhXNJXDM4MBgbTbtzRJBCPCWLdWSwsNVkGW9Xc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/mzn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ltgvdYsf2RfYn3iFpMJFFtlUiay7LBgIJhbS0xo9A9g=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/na.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Zs5TpVv6kfRFirE7WCsACrifwPeU6PZ98iTuzm+WJZE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nah.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j8bhsWAjhXiAcAdxjCjxr34ZzTi8Gkr7HGF3rp+A6nM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nap.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hCsP/gRGYJBTtqDufTyDqHla3f1DOT/HFjMmqtPsckM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nds-nl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l6Qt4wAovBtBAomNSYf5+tik12eqnL3WI/KPdqoTb1A=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nds.json</key>
		<dict>
			<key>hash2</key>
			<data>
			k4z+YJv3F7mf6+QRZnwugwfCaND6LDb8V2/F8uITJY0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ne.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Cgkp5ET34f+jfVktk3fA6Bpnionhbz3p7a5obkvUev8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/new.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PrGKJjbUenO32G5mQtX6t0mKNVhoEWL4daMQSTOo0Z4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ng.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kQUJ30/X0ULnyBEiK7hGshD7O78gsTr4uu+BQqr7YSE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s9iP/2C0FwbQepBNwzpIhNVUTUhLsariNTuRzJo6P8Y=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/no.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1LsDNzKpu/MaLCp4wlFA9DDC357gyVetec5K9I/zuGQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nov.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SgIMwlWm1j+Ddt2tySeSb22qcJf6RoYxdjGMGQOxGe8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nqo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aTpjUZl/kIS4YG27UHFCfDFjfeD956Yi7Vj2JjQ+M2A=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nrm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BBFZJvbwlWbqgJzao1KfWnCkHEscj7ByUMouEcV/Bfo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nso.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qaXIVn9ixCcVk+Ptc/S79LN6S8C9uUKopdQrJ2wJPTo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/nv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gZkaCT1Sl6GrtBYwix0Yc5XPA0FVRzJF1Q9DMK+h8LU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ny.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UiYQXZXKh4dxFnjmlRhsEXwLWpl5826EOgANuUXaB4w=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/oc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3SREAZdl2D9DOEMkDmbfYR+2+k5G9Rt5JZqVAkK5wZY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/olo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xLfeFbFs7h/6p+RYb5WtgPxu/CxW4QsCSTu4mtpDqXA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/om.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xwVpP+omh8y6wMn89XYZrXgZhB5ZtuLWahgHVlO60kA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/or.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bJwk/XFffsXet2RfWufq9IANkt+EOtVP99mL7GjQHAc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/os.json</key>
		<dict>
			<key>hash2</key>
			<data>
			N4hQcaqgZ+StOc4KUEbFPjtx8f5auKUO6TraqpLIaug=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XM/5gaTvuww8AGXEfZCy4bZiQjVZEq5vvurTCFH35bc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pag.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CAQr/vGzhBLbHaY+4RSJRz1eEKVqteudQVl6SgrnEo0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Zrh3wWtqa/ANgR3NdT53I2/h0Zc2zi4kwgD4IsCmJak=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pap.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GUlFUb+PxhzHA8kBYeJjPG4lNJ1r6IvnORjkOeS76xQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pcd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ogU0qy+qLhSf6XOkHfbZygghXAP5D1RKckkdzMAu/A0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pdc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GqXqaVJ+6CT/ItL7UODcO1nsNtd9TzodauinnUOYlsE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pfl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			o4mtZE9dAuLXBj/2exJcctckE7WWOycqQqZZzu55SeM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			77lN7WIr5AwlKJEoVjwG0cPeTrL7Y7IgjDFI2osWwCc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pih.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PU6wc4tD7wYbzKiq744ep1Rd9edqi3+iDVJbILurv5c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			imKuZ3ObvoU0FgZMeUbtgMsYCcuQ2dE6Cc5TeTEsAM0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8hcFgzJgiF6oLVzQz6pLAe0yyCY/9qPEQ/F5NWEhgVk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pnb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			If4PimugsFyMnEihR4kRkSORL+vrP5SAqhflZ4wit5Y=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pnt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iRrSu20eyFGMjrfQteYfTG4SV/LmFK6/5AUPrhbTjZg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ps.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2qQGOd6kEwOOjSALPjwPGlwqa7PfS2UaJX9QmNVotV8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/pt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GIMaOLx4NJEHwd46qpIs5XgoscmTdfsYKcjLTR4g7Ic=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/qu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Z+uiGVZY5sMt93lGIVI3ynfuSfQjJkjaX8RYPfXwgyY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			oDCIybSqa4VXRcVY+uTQEW3dzqa1XJUibmoJb1cOe60=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rmy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			B300aDmRKCKRzSO3buuCu3e7t1N2F51GyibV0PiFyIQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ro.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Om96D6/yfu7dQS3HauSCQDzaINrV1cDdcKdNwv8B+X4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/roa-rup.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kRhaFwKnImFMm+MN6D5woVXA8+JSPo2tegkxHDy2sUg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/roa-tara.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0f5frGqoXV9JscM584b+o7vwhQ8egBXo4XlbLIpQn3s=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ru.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dXFHUueSYfFZF1U9/IXzVUpTEd39sU2tQRgV+ayUdoI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			87NqdaIgfsb7YtsRKxTssDhaHVCZinjSEACN9KSv2c0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/rw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nd2+dHsgqv73NsZkcXqKDefK+BZYvAio2oJZJhdMVLU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+F/2djQtLyDmYfkGtVaUAVkbFqndUognYKBMlMbq2q0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sah.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ABF0uNXhGKUgWcsw9YmMcdKGPvRks0ZjJNprMomozJs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sat.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zb5jeYGtkYnknQOemLw0Eh7tZmReYpIMj7sfhF8cYak=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E/Omdv2frGaxFmvOQR/rd8e+lzEbmCwqqYNggVG7w+M=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/scn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			h364y6RdORLyNtb/IE4B9pL4sPcL5KF6HGaM6UwctSI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sco.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KntQsAqNScEkna3X4f/zdoGMsxkpiZzd1Pk/cUYQdXY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			18xExIvh75sfcPwxArQkvdWM/yU4wZxH9zBvyuJpHdo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/se.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZWgvs/gPAsDXKm3LcAdC3B2fxJRxSSpvGGc1vjm23GY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			F2UCGuIERmhd+PgnIc/8NNv6lrai840CegZ7sTDYO48=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zOExrRTzGfGeJ9efdfL2y87Dl7MTKWZWKVuMAUCrrMc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/shn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JOeQ3jbxIHlBbkc6xAfHgIFlbyhgONKwIj1pBcWeDLs=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/si.json</key>
		<dict>
			<key>hash2</key>
			<data>
			u9as7kvk5IoeBXxS17ecItiHMnNWsBzLjXOrDzytcM4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/simple.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4vecvkH3t4PwyhrpyYizvV1SFynNYY02zsHH/YJWkbI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yOzCSsx3gW2mALdTIhlQ5V4bk0toAZM++BVKAig+wts=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uxE7DvsXc5X/ZV9Iw+I+twxxls/9sIAFS7+V+KhrhsM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AfWTmuDiXk/1rdb1fv5Y2ncZAz81SEVRkeXFMayDkbc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uo/LzSHkYoTR0wm9yK5el8dqII9aI5yzLDX9KOFRaiU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/so.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hGuXhTQiXCY/gyaCvvEQeynh7ZUmgHui/K22x++sx3U=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XC0BjSF/8qwMync/iPnffzMP144TWLZdldsNLs3O/CM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gPxbSpaZd9ahE6Rzkij7HN+aedtwZYZniPj2lHbnBhA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/srn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JAevvGMM8IX0GHm5/DRwfM8yYKjxEr9uKwn75Xd7qwE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ss.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q5KUAWGQf0H6PjxrR7BOJ5g4d1MCAF1+b8ZFuNxzZcI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/st.json</key>
		<dict>
			<key>hash2</key>
			<data>
			w4dM+6EuucocIdK2oYjbaO8k6zEmPrmk285YeBgfsyk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/stq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			a8LmAAfd3S/6PdT4DLR6Od2sPxonWr/DP++o/QeQ7FQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/su.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1CweziyRJQJecS6RBqGm4CUqMLCuzO1hAb4Hpwq2Q0U=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8lOhcjaGF0252u7oTVzDCcHzq4FmtHbbiKPBvhuMhbM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/sw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aCs1U2V1b5K0JePO+dEBl8QaS4SJH65nOqa3W4riaq4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/szl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yA9Mp/ftDfZ7K/2rRDuFI/Vs+GFIdnNv6N9nR5FY67A=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/szy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SPoAYGkP5jLvvjtIrAXOrUhcSfxDwGdLonKy6hrIIME=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ta.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xweXVAaT5Ga4cOxvqBjXYQFNhEfnANS9WLsjFVxu1K4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tcy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			S02llvq1KO87jd9QJHIiXn6G6QQtPHJog/fdRM05TBU=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/te.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lQbhRSl1Hz7ONo3UjI7BXSzKxpUVseRlonIAOhJ1XZE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/test.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cGQvCpzKUQxjPK5eUugaOQnvV9WJoAdYCaF81zIurKI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tet.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DPht0EOcSrtEr/t2YOf6gBOZW5xx0fFi7zujAA6+LvI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j2viR1z3Yp+HRTNS8sLuT3oo3yA+BynQgRSUfrHpu/E=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/th.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FN+624ULN4BQ4HJP0BZQItjI1ZhhsE1aVnZoZksKu5U=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ti.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GhMA3TpBGxexgOEFlOac8+zPpmMQsgtPz/SSRhwW6zg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aRfpFjIMpYeK2ySl5NuMQ4MpdD8b4IaO8XxCUjxOhNE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			MAQzH2FSr7Gl27JxlMQaYjkGsWYcNGW53Ht0CH2ZcYQ=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/to.json</key>
		<dict>
			<key>hash2</key>
			<data>
			sGGaA21Qmx6rVz8H3NNGz+fBX8scdlkBp3qEgJ5hOjI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tpi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LHbYJiDPn4cNK3btnEi2YRSXyFV/SDIvqoG/2xUqZdw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			STX8TKAb52ZGNSGL3uEstApLHq6g8o9uSQmEv/xQ1y0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/trv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			u8K/BHUk5Ubh4mCsa11SuWDgnuBm4xgvQY/zcnYgOik=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ts.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RPFaOmAhiTPv1CEuMsASVpxwAi5+EB0xTK1gZ0BV90Y=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DIEKHA8nzollisxrcT+P+cMFQ0y88FaW0hTmBbZuJLo=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tum.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			QUHYJi0+3mEKRXRZH7W8HIuAZnEpEsRvtGWeBr5151g=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ty.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BEm6bSN7I34THzZI/+sCUqzKYgzShncqAH5ZnuAZcB4=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/tyv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TUfCwGa67VUuj9nGPHFF8w+zXHm/NVv3Q8/M0ghSOCE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/udm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			S/kC7w9fv4w2ZrgH4r6h6sYBr8ffVcLquBaMt/2xaeY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ug.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qwsl7hqxmPmcsaBck4XjLJx+IaD8bHGBznZ7bQljM1Q=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/uk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EX+5CCT9JUEgXr3Qaz+n7lO21IpphUBIHtqq7akf4rg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ur.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8CaUjWCWHcP0xCr61v4foR9pzxJNaH4m45y0vrVV0B0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/uz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7g9k0sD8mUTbSqcq8QchhT1xygy0s89iqxSHoWXbSVY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/ve.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3dxQwNg12aaZyTZcMqyoRIcrJM5nY8XEbnlkxHYEir8=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vec.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7eLsPSlCPWfMLfFKA1o7KZPoFwtfPh1BEPbH4m9FfIk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vep.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FTFEhGhrndG8U2tZr+uRz87OqDT6Q/yEz2QigYv1dKc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			rh6A5tAyWa/29xjKblw3wQf7i5M3dhCa/xUUrVbfMNI=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DGiMvp2ToMrgP4Q9F5t4IJvAyAapHDLTJ9xca61OJQg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/vo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qmzmzpgX3uPubaoOoqk+qFgRzH3w5DAqpZAI66ckK9o=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/wa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xfEot004SNlFx+iCABBnzWUAQSIc17kr6pNVpEKu9dw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/war.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X87y2G+XVZKLKEA74uW3GxzQQ59W0bVHcuIHFrRgwdY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/wo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hT5h8CRuu8LSij8Hgt0iMBYLfyDqpZj89wE3446VOJw=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/wuu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xJfUbmv0qi3q23Fa6Y1SVTyKcsX66loXPmNqlCVVBtA=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/xal.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8z6UZko/Ex0vAtRrmKYAB5thKaHoumitH+2XM//SgJk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/xh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			pvGXjYVWi4TVRFtClfti/CpLsBxlIY12dHroVm+6qWc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/xmf.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+R7IrPruurke1i3JnQXJ10ecZ3d/U5v5zVPw91ZGWCg=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/yi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bETXwQ20ho/y+L6aG9rYxW3RW/pL/CPceaVplOAvelE=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/yo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XCAnt46S0rM4VfidVj09pFXczIpkVIA2eY+8hvXhuLk=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/yue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E7K1S5irkAeiiv90redb35FXGFTv9StpSOme9y9mv+c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/za.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1OpfSObMYITasfB2ZWg2Q/zA9xZKHhDZDiq7+6/lZWM=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zea.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CCrOCwhBbaSexYrGhu8Pdy2XTATohCzA0nIJugAom5g=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zh-classical.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RO6+ggBECLcboMlwzTGoau0Sgw6OUls9PFyUrcaUnnc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zh-min-nan.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2nzvEL3OiNRaRCGrA2zJFUIkqn5FyC3wKy2jCnUoQtc=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zh-yue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E7K1S5irkAeiiv90redb35FXGFTv9StpSOme9y9mv+c=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IlnCNkwnrhms+R3fyuC+lebm8v9MpPJOUmC53pdJ9nY=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/wikipedia-namespaces/zu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RKOQ0cC5PtcJ5dA0wqc6PrGHLRJsgec0M/5B5FfNIe0=
			</data>
		</dict>
		<key>Frameworks/WMF.framework/yi.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GOiEX5J8nd7K1B6YL8oqaV4V1A4/Cl19jbyuelp4aQ8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/yi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			AIgwpb60yZwyyIZUL6izUZh3priIqUZsqgN4z84q+Tg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4+MsjPbY7c9iKEHkzZikGax84m8EfOiPlTa4eP1TCwI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/zh-Hans.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			35oZLr3U9WZIIXPOUoNry94aJqQXfJgyRfQ0HlY76Wc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/zh-hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ixGBC8MiMj0vkCHLHNCH0Tioyrkt1DTKeCs/UonGLtI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/WMF.framework/zh-hant.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			rF6TZn0YojDmGJWlvhw4s27yTWPtuPIUA0PSXtNPyvQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>HintViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			vG8GN4arp7BdGcCzzsqxboJAsQrf2Eny08vILporu8A=
			</data>
		</dict>
		<key>ImageDimmingExampleViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			4UtxeA9Ak/S8t9sxY+4rNUzjm/XhH6P354AMjLMxL2s=
			</data>
		</dict>
		<key>InsertMediaCustomImageSizeSettingTableViewCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			1741m/GC6ihb3Ma/HLNalu9VbyaRl2CpEuIpq/7uvtY=
			</data>
		</dict>
		<key>InsertMediaImageInfoView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			CcMMK+8jadClRWMIyv6cZ87dm2x4w1RT2/N5tbRzViI=
			</data>
		</dict>
		<key>InsertMediaSearchResultPreviewingViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			FJ8txKXefxdMlm4i7giZNbEzHZ5mBEp/+AhHGaiaxL0=
			</data>
		</dict>
		<key>InsertMediaSettingsButtonView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			+5LAVqFqQWAgHrvQYJWiIlr9rqytnedT8IjszxsVpHc=
			</data>
		</dict>
		<key>InsertMediaSettingsImageView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			cIkOOgmT8HZ2keX3gyTyN/0q5Bjj2QtGyuIyZuDjblY=
			</data>
		</dict>
		<key>InsertMediaSettingsTextTableViewCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			yK01B9Inc07fgVCUEadGVIWO7ITTbDmXHnJ7/mH0XLc=
			</data>
		</dict>
		<key>Launch Screen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			jUtMEjzK1ESrFtCQckLqNgEC8rPyhaJC5vA8EdLDklo=
			</data>
		</dict>
		<key>Launch Screen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Launch Screen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			cWq7q+iQcmpSoaFGecnyPdKFEPFwRD3slgUmxFADkVs=
			</data>
		</dict>
		<key>LibrariesUsed.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			2rKGf22kNFzV3clC5ZzAhJedA6S0e7WychyGu4Sh3j8=
			</data>
		</dict>
		<key>LibrariesUsed.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			DViTyRAbOSXBG6gGwazA7XYT23YwULfW7HjrFuPQrRo=
			</data>
		</dict>
		<key>LibrariesUsed.storyboardc/LibrariesUsedViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			kMuJTI0RY7RPkFp4noAL9qwiEuXce9Po3klV0VsjxsE=
			</data>
		</dict>
		<key>LibrariesUsed.storyboardc/LibraryUsedViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KyC8VsjVtDI63ry7tRPcAeaMZUz6sPcChABE8KXuScM=
			</data>
		</dict>
		<key>LibrariesUsed.storyboardc/RAl-lu-nmB-view-Lt1-Fz-AJg.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Y30i9G284B4q934ZEUL6/ENjUjWfiljW9xZoxGo40xE=
			</data>
		</dict>
		<key>LibrariesUsed.storyboardc/Var-9e-kfX-view-QKe-YT-pRO.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			yNYHeX/3QQCR8LFxWGwnifg3J1zA1dY9SagogKfi2GA=
			</data>
		</dict>
		<key>LoadingAnimationViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			madhBr19GPHDWudvLkEeUeDp60qA7WrU9KRCSUIzUF0=
			</data>
		</dict>
		<key>NYTPhotoViewer.bundle/NYTPhotoViewerCloseButtonX.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4TqEemGO27xLyNsmMiNo+UuuHmjJkeYWLONJRwasNR4=
			</data>
		</dict>
		<key>NYTPhotoViewer.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			DSAceVEMVcFyBsZdyc/fHgyEzPJH6itBCdjsBASq1Co=
			</data>
		</dict>
		<key>NYTPhotoViewer.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ea5kLNGCmx0/BJL4xy5kEGT7TC4/TiWkF1GRSNDmBlU=
			</data>
		</dict>
		<key>NYTPhotoViewer.bundle/NYTPhotoViewerCloseButtonXLandscape.png</key>
		<dict>
			<key>hash2</key>
			<data>
			heneGremqtBPSZjpQmr5BZQ3fLznc4PctmRONstGCIo=
			</data>
		</dict>
		<key>NYTPhotoViewer.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			MvbgNnNuzTcYKnFBErHeS/k5FgqI1J4lonKnLdCi1zw=
			</data>
		</dict>
		<key>NYTPhotoViewer.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			O5zbSQWt6aZ/jNl2Cy3agcYTniTV2Jj+X4vSe0vRQQE=
			</data>
		</dict>
		<key>NewsCollectionViewHeader.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KlH7uKdZ6lRc/+sQE1QRVp3Q0Eqbj0J4SapVYDFCllA=
			</data>
		</dict>
		<key>NotificationBackgroundError.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qHhr8BRoz+KCnebrq1PQIxvlsYvP9hTdZRjmj6lcu4Q=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ikvF++5yvKXpj1Vvs9JfxavW3qlYLjbPiaMqSJIsSKI=
			</data>
		</dict>
		<key>NotificationBackgroundErrorIcon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			X+e+SPuuRb0NPTKqwZxUiZepOMoX/UPoZIpt2nU9q4M=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			nvZsiGnpdbkd8myLOI9V9HdLKoKXpHdRg6KgzaZPUKg=
			</data>
		</dict>
		<key>NotificationBackgroundMessage.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4DhbeNz4Pb1S0OpVvUB1eoRVWCuiT3KAyaQC1/QdOl8=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			xq++v/DwrlHrCxal3cmxKbcdtz4bQRPslYQw7TktNTo=
			</data>
		</dict>
		<key>NotificationBackgroundSuccess.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GCJ4DJjFv3Z04MswuxwQYqjkH7g5BjKMdVBLrsnPmC4=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			XyfPzEqe7R1FiizwaWBHpUpwR/aAfGgXBeOZFpJzzCg=
			</data>
		</dict>
		<key>NotificationBackgroundSuccessIcon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			D1gJ6v4RdWewjmbofgjkfgFGJjsurwjY6D8rQoPUU84=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CTWWqmVgtxFw/ZgD/a9RkvY71eQFi5QjIV4snfZxQkQ=
			</data>
		</dict>
		<key>NotificationBackgroundWarning.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CxA1+JfkiiUfdNmeyOuVma1ZH8CN6RsNvaX0vyDnuMo=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Nz/z74mh1ifUIk8bGFQwKEOzMTvnSZqfnXaZoqGynHM=
			</data>
		</dict>
		<key>NotificationBackgroundWarningIcon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+SEhA/2j+yr0Y0tI7KkNhuCGhhKc18hc2C3hgAlmUQ=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CEiK0Ec9A+G8dmCakOGEdsglRGEn2mXpmZ9jThCJUsE=
			</data>
		</dict>
		<key>NotificationButtonBackground.png</key>
		<dict>
			<key>hash2</key>
			<data>
			k0sT/mP0eU0iZX6WHxIxA5I+gwNsDWLuv3g0ahK2uto=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			08Aodsj8E1vuLxkneKz25q3GSfOwf90FE4z2og+b2PM=
			</data>
		</dict>
		<key>OldTalkPageHeaderView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			L4XUsDdRt1BpNdubprhHeht/gE81zcZ7ic3wXHuou1c=
			</data>
		</dict>
		<key>OnThisDayViewControllerHeader.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			mf1wzsjyPnOoAaU17GoyqbBz69OEhhoHlFXH3Jss36Q=
			</data>
		</dict>
		<key>PageHistoryComparisonSelectionViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			2FqdmQHykUD6HkOPd0WuG3h8fmfoIasB1Z606782qwQ=
			</data>
		</dict>
		<key>PageHistoryCountsViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			uIXzoGkb3KTzVXqyjEd++jVll/9Ve/OLDnOFVmk/pFI=
			</data>
		</dict>
		<key>PageHistoryFilterCountCollectionViewCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			j5xiSnqP2bb+rBGjSatL1si9B97xpJuLjsPzw9tpZts=
			</data>
		</dict>
		<key>Places.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			rttK3+27s/8gW1EL5gpkx6nqkiOrIauhClDGybf3vxs=
			</data>
		</dict>
		<key>Places.storyboardc/Places.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			qAO3vygyc3LI3iGZv4G0PfvykHbg4ZHeJtp/kWwckus=
			</data>
		</dict>
		<key>Places.storyboardc/UIViewController-pK5-Ai-Kzp.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			AAV+wyrdFsR6r7QScyxdzMyb1UNYSa14CV1yxt9BOKA=
			</data>
		</dict>
		<key>Places.storyboardc/pK5-Ai-Kzp-view-pBD-gM-dPf.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ghPIzUyCsdB387egESFcLowfBddIkSLWrCVG31LakBQ=
			</data>
		</dict>
		<key>PlacesSearchSuggestionTableViewCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			yVhdo0FcbkKINIVnrIvep8JnhS8vSTSlORWZaLepnak=
			</data>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/Base.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			N2neWCXPMgE/LJyV9ZHFRuNVMJMLFfhyT3QjeRqjsqI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sOic6ugSKWwGvBSX5ZyYEUE+pFecCWW0YXAo5CNbmCQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BkIvmhPYE93ldvoOkddUjmGtjYdVQvziSGr9Vl3QcIQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			8qwi2c3eeGtm/QFM26GpJQbdeBd6dS9oWKz9suC81G0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/ContinueReadingWidget</key>
		<dict>
			<key>hash2</key>
			<data>
			xL4vl16L++V3UHVDYxeBC2oFQgt/cbgjZHdw8YEuLd8=
			</data>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ETL/ZBLIYc9fvdzbtbQkNR7tIn1NHHL1Z0tzqeqrsmY=
			</data>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			jWrWraXthp4wQZ0jAaygMPczus7QVWGkPk/gdxYWGwY=
			</data>
		</dict>
		<key>PlugIns/ContinueReadingWidget.appex/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			N2neWCXPMgE/LJyV9ZHFRuNVMJMLFfhyT3QjeRqjsqI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sOic6ugSKWwGvBSX5ZyYEUE+pFecCWW0YXAo5CNbmCQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			F6c/6UlxwTAHntvzcJsdAE5X6QcWqziHB8o1q39hsfs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Tg19EvF8SGoMGIKQl9gTiy3XTZOktk2MhNe6Al4IWVQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/FeaturedArticleWidget</key>
		<dict>
			<key>hash2</key>
			<data>
			6KIsFTudIIVXdDxY6vFI6yxDwaY+tFhBGlRTv6BCRjg=
			</data>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			aZxNPVHIL6a6xvaFgqS4D2R1dMcwiZEM3Cgyen2LlGI=
			</data>
		</dict>
		<key>PlugIns/FeaturedArticleWidget.appex/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			sIkBODmF/zdReVSKYlvTFo45swJz1dcRKD9ebIfd3qY=
			</data>
		</dict>
		<key>PlugIns/NotificationServiceExtension.appex/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			OazHfxSefRGZB7ceC1WPnnveVn5IaqsYomeiYQZInYY=
			</data>
		</dict>
		<key>PlugIns/NotificationServiceExtension.appex/NotificationServiceExtension</key>
		<dict>
			<key>hash2</key>
			<data>
			I5vcFy1XTbyjcPHC1G3BPg6N9g59K0amHAZ6N/53tDQ=
			</data>
		</dict>
		<key>PlugIns/NotificationServiceExtension.appex/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			r+rDwTjhtwZj7BeTnVs6AZ2jlpZXTw2iBR4PbjpfQlI=
			</data>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			Y0coxoHCD3iyE5LOGK7GAdLSOdZfvIjm4KfzHvq9y7w=
			</data>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Base.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FcNK+/1g39O3Nm0XCZiM3Zge8KNGhsvHrVHG08NPPu4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sOic6ugSKWwGvBSX5ZyYEUE+pFecCWW0YXAo5CNbmCQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Base.lproj/MainInterface.storyboardc/M4Y-Lb-cyx-view-S3S-Oj-5AN.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ONjPdmODSX1OZ0ECOcmwXeSTBsYidjIV1LiIiTyL9uc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Base.lproj/MainInterface.storyboardc/UIViewController-M4Y-Lb-cyx.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			N+OywOVev2f3fzqdaCRA/HHE2cfI+yWpACpt9U+coOE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TopReadWidget.appex/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			R3dyR9VMN7zRJ3Db90ocfKC4yj5tNZD53sueKAutnmo=
			</data>
		</dict>
		<key>PlugIns/TopReadWidget.appex/TopReadWidget</key>
		<dict>
			<key>hash2</key>
			<data>
			7j9HmoNeGbX8zepsq5Z2O9v6MY1nwpVwW3YMfaeNneY=
			</data>
		</dict>
		<key>PlugIns/TopReadWidget.appex/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			yuKQe7VfRU0BxMcsErn0SVG5heGIxV+7Eu8Mj0d5+AM=
			</data>
		</dict>
		<key>PlugIns/TopReadWidget.appex/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FcNK+/1g39O3Nm0XCZiM3Zge8KNGhsvHrVHG08NPPu4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/WidgetsExtension.appex/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			5PusHmc9Cl0szNCzAICh2UdRqAwqjqxYQgQnf2+f3Lg=
			</data>
		</dict>
		<key>PlugIns/WidgetsExtension.appex/Featured Article Widget Preview Content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9dAbe+P45znDfipMNta+sSkkuqrzuO+dbdPiaSJOzYk=
			</data>
		</dict>
		<key>PlugIns/WidgetsExtension.appex/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			2bsgZZqQ46CInv72es+Twh2YlpnkLbhD8RZoJBxlwtk=
			</data>
		</dict>
		<key>PlugIns/WidgetsExtension.appex/WidgetsExtension</key>
		<dict>
			<key>hash2</key>
			<data>
			Q9XSYq5ZCbwVqH3WQRJE3nSgYN4u8Ci0x+l+OFFl9as=
			</data>
		</dict>
		<key>PlugIns/WidgetsExtension.appex/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			enIHw0RfYjmYLOnz2l8BsWokhFenH1z0rpag5SMmKdo=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			TKUSWW64icmxmmAFLu18pnbFLmWpoDIFWhNwaxUvonE=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			lWOTsVUGXaLRREmvMNNCWEuUP4CFsNOXukl1s2yS958=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Anatomical heart.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ogHW0ssTwN+JhVJ6UlcdUjfFKA+ZI06tUC/ifqWuCxY=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Astronaut animated.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			RDdKvZJI+WwjP+7Z4EhFZa//eZbnOXInu9abdlkVLV0=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Astronaut.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KSiiqPAqrOS9uk6rPmvg1b9PR1vcJbrP1WTST1ctTV0=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Blackhole.png</key>
		<dict>
			<key>hash2</key>
			<data>
			o+//syp98gMJP2eBuIuTIBCaT5mcnnm6i40E33kRuuI=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Books.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			vBOBWcYqhp4vt6i4FhLWMrjMstx7LIBfl51TV55d1WI=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/BrainFood.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			qfQhgFV+iXUFXeWRdXoNALTMIZTm3+ZgU4fXBsGTZ7k=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Butterfly.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			a/gnDT5btacjwOY6Rubt/XX9YbOr0XqQfCYSxwqF4kQ=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/CatLaptop.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			0P13D04+Rwnvf4/Ol/QefxN7sqrJIYH+dfCp8m38SB4=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Citation needed .png</key>
		<dict>
			<key>hash2</key>
			<data>
			Zna4qbRCTlrsIo78x8L5cKCaZuFDdSVgleDfoktwtvg=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Citation needed animated.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			TBe8F43Cdz/eZbegDT/wB8jO4+JPABBiF+zMLqTR5Oc=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Dragon.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			qL7yB0rEwlBw/Y1F4X/qSS6tc6fYUh2MPoATuGzesqw=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Facts.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			NEqcRZVfF4/KD6oh+oTsHD0JtMTaCkMtUBILQGkYHvg=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Heart eyes.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			6lwJ8eU75akYezsOzKVT/7CClXUPT6kmTnek5Gie4yM=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Help lol.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			RIygD3ZmiREvhCd0YTPNg37XdjUr/EnA5l3YsThyRg4=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Idea.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			VGGHDMjdWjnN3i0Jx1KW87HP2I/QVyncfWPcova3glI=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			r1UO32mfAZa4/ESuEGMniuMps6QFDgWcxiB5HgnRR8Y=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/International space station.png</key>
		<dict>
			<key>hash2</key>
			<data>
			C9qn8Z3MrGyBIDE4GvnPur2LB7gPIY5Aazek6o55oRg=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Math meme.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			41e+AIe/pK2kCh6eaJxRsKpdYWcNrglwrV8WAP3M0DE=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Newspaper.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			8qrppFDjHqyYT204Kue55pM0k9XuWt/FOW/+vjmiCKY=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Note taking.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			FZM8mp3xJn+agdhLsarnmjjpb1NbPLspmPpfPXR8yW0=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Rabbit hole.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Ltk0yaUnjb0MBe0RZl3pGX4jWpMLhZ3FJ/SapszQOFk=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/RabbitHole.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			OhKmhBs7Z5y8jn5Y0TkUtu8WrtcSL926yjuqomDeo7o=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Schrodingers cat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1m0TrP8BTQfRZpNjs08gBhaXt+Fot6/8pbhiHFL1jYE=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Science.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			Xo9mvXmv1T+/fzP2/iHqZXS4tCs+J1/r8GAQMV708H4=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Scientific blackhole.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CahmFx01M31PLpq6YduUcMUKAXIRzCdJ9eaq9MEyz+A=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Spacedog.png</key>
		<dict>
			<key>hash2</key>
			<data>
			t54L2E8E8sIKAak/HuijK4jLWY0W/Ey7C80wI5l/fL4=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Tea dark skin tone.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			kRl6sodzPQTqa0ck3o97GuFBg1esqZSAI55eLcI8Q7o=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Tea light skin tone.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			8LxPI8RAHmo5zSwIFnP8mhASuGZvH1ZOJB5kf8FsBhQ=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Teamwork.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			TKZMV/5TgbDeL4fsTF10zMwfc0o6c0lAkulNzuv0VpY=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Thug.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			4ZAqaT9YTPwJeISqTPOOjsbWlQI+D6CouqBnI/6y7qY=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/What.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			oH0yIeP2KPWRkuuIJa8hjc4jQ50DHm0FC1jBdnJ02l4=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/Wordmark.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			z5Vc6pD1tljOfB6Xf23f1Wfmis7a7ZIjwmblY9jc3DM=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Sticker Pack.stickerpack/World.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			cy+vtjLpyaemOzTqVwhfOYGD0nQmXrH8A7BAuuR9/yI=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/Wikipedia Stickers</key>
		<dict>
			<key>hash2</key>
			<data>
			VJ4nn86ATBq2ND5tcYSA+SPtmxJCWsp7E84qM5JiATI=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			hPEQ/hml2JyvAoSfZZqkuxRXbIrgOPacmQTQtpFXCos=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			TIKAZ5DPPevSp7UNhA5W1TAqomC6Rdx/JM1XIpxWpis=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			JGOpYMhj6blti1kwZQMsHvlnWJgZgtsex1caOoBR1TI=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Rhgnt0G5ygA1QoIGpa9kjGOuD79mACAZicToZpoyogw=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			P8g9aAp876947c8bjtxgdYrA2th6v9mY5jsem7EloUk=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			IF+4dI3IULXuvu4p2zkHAsq71HGnVaYRCrAsKBFZ4B8=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			OBWg94TDo9P+BQlmYAmB/IuEMvc5QaIe6ZZMpMdsEn0=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			yCJLdij5kdMhyRQY44vcS6oo3KreWGAXPJvJUFpLsV0=
			</data>
		</dict>
		<key>PlugIns/Wikipedia Stickers.appex/<NAME_EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			oZplFMlM3EnZN0/ypAJgrPeN4p6hVP+tsij0wlABtHI=
			</data>
		</dict>
		<key>Properties.js</key>
		<dict>
			<key>hash2</key>
			<data>
			7b5SmbMTVr7tqL2JzX46Fip52vASmVcTBuNOTNLMZ1A=
			</data>
		</dict>
		<key>RMessageDefaultDesign.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OsnlQlshDrvKxTwv3t0fZvK5PwhT3fL/isOG2QaF/oc=
			</data>
		</dict>
		<key>RMessageView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			JZ1WtmtaHmX7Zxue3zgU0SrNeZYtQIn/YuTXSNN0wds=
			</data>
		</dict>
		<key>ReadingListDetailUnderBarViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			qibwefV6QlS6XNLZ+t0cyvbQvIZcP7xiEqujepf6UTg=
			</data>
		</dict>
		<key>ReadingThemesControlsViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			vkatg5KOs2uIDGgigo6HFRYDULUAQVUQQ6eeBlw5COo=
			</data>
		</dict>
		<key>Saved.storyboardc/DbA-ai-MSH-view-GCG-ME-1AZ.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			gy7GH/Q6VD0hyVr56687PTa8CedwcI1ycl2O5tKedpo=
			</data>
		</dict>
		<key>Saved.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Cjc60ek6MQH7QWpFROjmXwwpeP33+2hwnz+mBgZL4pI=
			</data>
		</dict>
		<key>Saved.storyboardc/Saved.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Mwj6Lu+jDZ3Hvz5eHgYAtYcsTA6xftpr5Z8cL4iBG4o=
			</data>
		</dict>
		<key>SavedProgressViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ieMZQ5ZC4W8w74sDam5uLCnyakCa96th57mB95BywRQ=
			</data>
		</dict>
		<key>SavedProgressViewController.storyboardc/SavedProgressViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			6KOtbIXJMnt8uANeBBhV/ddAEGtjzco3x7yrR78Y8KY=
			</data>
		</dict>
		<key>SavedProgressViewController.storyboardc/byl-9N-rP3-view-Bog-93-sHh.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			MgQuiWxiKFr8Oo9kjJX/0dfud5PBlscRQFtf1z4aezY=
			</data>
		</dict>
		<key>ScrollableEducationPanelView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoW/9cyvD8htkyV2iKQi9xqZM09nTk+dCHonLV5Duo=
			</data>
		</dict>
		<key>SearchBarExtendedViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			5KeprjJ/elet4aUmXLMoz99Im2iKcrU5/9qORw6yu0w=
			</data>
		</dict>
		<key>SearchLanguagesBarViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			zgwCHeSVcDp+aUBgGCWtolPCbZO9Wln7tSK0xFPCEQw=
			</data>
		</dict>
		<key>ShareAFactViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			OR9sgzTFcOeSaTaeIVS5w2LbQkfn3GdhePyfVZC1ia4=
			</data>
		</dict>
		<key>ShareViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Sl/9UQYHv27E++gHwclQ8ohqhY3lTjR3N1cQFp325qk=
			</data>
		</dict>
		<key>SubSettingsViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			dx9FSKzEc3fN8f/QIvGZ/DnY1drjJ+M0/ZjW2NROdFQ=
			</data>
		</dict>
		<key>TSMessagesDefaultDesign.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LYjqnitobB3aKlp4SeLC05cevHBxIrAfpinQkGHbyMg=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/ca.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ncomTqptJcW2ajZ7kD/KcfHd+0NSXIqGfKQPii7x/YY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/cs.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ezpeDj7SDO+4EiGgh7wUjtWk853NlrFXdyS4YAA73lw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/de.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LtcbmJhi5kJ3AJEz+wPCSQGyOaNvN2f/TZ3bK4XGNws=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/en.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KDengfWP6xMy0cE/l14ee539O1OnLYZQdHNm32GDcIQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/es.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Ke20+KloJOBkCtKf6d80DuBN88oZB/J2YHNyOKkIJGU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/eu.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			uUaYBNv96vb1QR8oRvnGoR/M6nUbeJkTYypxpci0K/k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/fi.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ny2dxkJwAc2PY9GBHu+9YWRu50m9M57s1NdDcBXq5AQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/fr.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fQxmjm8SFsg3wXQbbWa4Fn0DlEaPi6QavfDhKPUl3H8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/it.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1hMCPRZd/2y07i1DG7hyhJjmz1BhFdy8gqNfUQ/SX1A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/ja.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			62iZPBqB+ym96leKu8lv7Ymc4hyVx6rB2R5wNC1CE+Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/ko.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			piQSXtHn47F9CXHh9tzLp6qr5unvgRXQgbCQoFV/+Wo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/nl.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KDengfWP6xMy0cE/l14ee539O1OnLYZQdHNm32GDcIQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/no.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NtB5BAc0NWKvO0tLPWFKRwu8zDbO0U6jx32eBo2zreg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/pl.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FnEuRRC0rVdVOa/cq5nNiL3khfx9IE/jADUpVhtSov0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/pt.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			K6C8Y+iM5dMOYtN/XmFQnKijxgBK4u0pzCnyYTW3CSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/ru.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k9fpZ0zV2C8bVg+njodQlYBAz/CxQkM5GlLd285N2r0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/safari-7.png</key>
		<dict>
			<key>hash2</key>
			<data>
			gxV1sisuKJSHQ8T+Qa30pxcsIVtDFvSVogfgsP+ryok=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			LOghO824JL+4CUzr7gpo6AznYDXEeom8JfWc93ZcBWQ=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/safari-7@2x~iPad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			E91q4VLgDMgB9V+HwwK0pdq1KkoLJrlCO9t2H4cja6o=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			JmfL6dxYMQFswQbmioyzf0TUjNfo9JxWiJK4GoSkdjA=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/safari-7~iPad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			a5OtGIqpFQ/8DSD17dH7waXRjLlfW1lPCB8pHQ2+YT4=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/safari.png</key>
		<dict>
			<key>hash2</key>
			<data>
			wxVFswfm3VUKOyJ+QJ9J+wGudsF5m02qps1piAdm+1Y=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			6w3s7O3C8u1kcuClYvlg+YVI3ZKcQGGlvhsKkan2ugo=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/safari@2x~iPad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8GVVDqtzQaHHTOzGM6FIShzAmHDHCrzG8OqMGsS1pdc=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			W3IGQzii7sg+wCKuZQNUg9+A2rWGHmfY0FXAKF9qTOQ=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/safari~iPad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ImivK1dbk+OoSl9mDyLQMYHF/qhibwn5flweK1Sn/10=
			</data>
		</dict>
		<key>TUSafariActivity.bundle/sk.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3axcSGXoxOVOvIZjIeibVsJXlrsQC66Jq6RZlXrwXhE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/sv.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8yO5k2octs/ZZSuGAzjFXMYjw4sXMrfNIyx/i0vIdeQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/vi.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NflpZccFredzCXDBXSsg3ogqtn12+2/SENxEOR3z8TM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/zh_Hans.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HU5EweZ/K1qDCuPmPk96LK2QVR4PPb81jIJfqn1kmmk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TUSafariActivity.bundle/zh_Hant.lproj/TUSafariActivity.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			o+8At5VWFCi/sZxra7xl9bp5rdFsSqsUtU+oIfJQg8o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>TableOfContentsCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			wAhg6AAKYiLwTcf850NWVY30RAoT/d4eiJ/7hxVw7jI=
			</data>
		</dict>
		<key>TableOfContentsHeader.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			dm6xdQUfsG0o0IVs1QlwHbtcCiDSSodJ1x0TApSZfg0=
			</data>
		</dict>
		<key>TalkPageTopicNewViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			7vMr3bABUdoWm2/UtPzF7vqavlvFyqeSnHSX5kimO3c=
			</data>
		</dict>
		<key>TextFormatting.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Eoiea8EMS2xYeOj+ZvJl7mEkHftzdcpkSwfzachyKVQ=
			</data>
		</dict>
		<key>TextFormatting.storyboardc/TTT-UR-cQr-view-Wo1-Zn-ue4.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			uLtln/VcI3uKIpiQQ4/yVSN/AyRQjGHW36/H0bENr9k=
			</data>
		</dict>
		<key>TextFormatting.storyboardc/TextFormattingInputViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			j9X+qOZzDd7PNHGpdUc0XFsRc9kkEb/DOQl22NFjzEo=
			</data>
		</dict>
		<key>TextFormatting.storyboardc/TextFormattingTableViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			f8sY84a5a8xz/x8CzZ+Yusdh5o6R9TxEjRJoevnhu24=
			</data>
		</dict>
		<key>TextFormatting.storyboardc/TextSizeFormattingTableViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			wNejRwEOHHlCzqNezViTsmTEuLTtvPRnwTungxTKMSo=
			</data>
		</dict>
		<key>TextFormatting.storyboardc/TextStyleFormattingTableViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			hBMB7p/WZ8nKU0A0tKqB67QzD1/GQjU0r8WHvRgNSkA=
			</data>
		</dict>
		<key>TextFormatting.storyboardc/c6k-HI-Rwr-view-Qjc-KZ-bns.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			gv+FpBPFnLArkHTSzsfRqLbm95HV9mCH5Rcq9vbDW3M=
			</data>
		</dict>
		<key>TextFormatting.storyboardc/hyT-L3-Svb-view-iOZ-ez-5tC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			gv+FpBPFnLArkHTSzsfRqLbm95HV9mCH5Rcq9vbDW3M=
			</data>
		</dict>
		<key>TextFormatting.storyboardc/rO7-JZ-2MQ-view-CrV-ni-bka.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			mTVxwpnTMg/BJacXr/Y8igs9YrjFVSxWOgfWR8HIWlE=
			</data>
		</dict>
		<key>TextFormattingButtonView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ndIxrozqA4N1wUk9DAzFnAE2DHrnkVYNx9Yo/4G8QWw=
			</data>
		</dict>
		<key>TextFormattingGroupedToolbarView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Nws1kmZukKrNxO8tUJzcUP9SeH4u60WDnIuLXFXCp5Q=
			</data>
		</dict>
		<key>TextFormattingPlainToolbarView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			+hwx78uT6ktNIsOb1Z8NCVZ3NR7StgQ1Hcg6yGgvztE=
			</data>
		</dict>
		<key>TextSizeChangeExampleViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ZQZVb0Zw7x0G4zDXp4TMUys0TmzUPTTdopwnXz2GobQ=
			</data>
		</dict>
		<key>WMFAccountCreationViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ssm8wSQyNLBbM1Wn16fo2mOd4VYkn920Zg0ZMQkowDA=
			</data>
		</dict>
		<key>WMFAccountCreationViewController.storyboardc/LsX-JK-bT6-view-c5n-ZK-2d9.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			THOlqYfv9AqEeI6jQPtWCBdcVJTN39ZnoVfPzOuTkKE=
			</data>
		</dict>
		<key>WMFAccountCreationViewController.storyboardc/WMFAccountCreationViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			rKb+HfYEblM6HIH5fylValPkV4Aj7fDhlGpusdSY1no=
			</data>
		</dict>
		<key>WMFArticleLanguagesSectionFooter.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			8SJbAEhDuchN6JRtFIraGa6i23StvLKXKsEkfM81JdA=
			</data>
		</dict>
		<key>WMFArticleLanguagesSectionHeader.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Anu+YHt3PtBODRqNeOjVWPhfRF36T4Fe+O2yhHxTtZU=
			</data>
		</dict>
		<key>WMFBarButtonItemPopoverMessageViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			pdtEIjy+AsZrjLJoDNKZncOMmHd0PrvShsdCeOHnQ40=
			</data>
		</dict>
		<key>WMFBarButtonItemPopoverMessageViewController.storyboardc/Twi-Do-8Ec-view-EFr-M1-WKG.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			b09EP5XTwOLmVXOp1whUMxFZqqmX4W5OUgg9kEMi4so=
			</data>
		</dict>
		<key>WMFBarButtonItemPopoverMessageViewController.storyboardc/UIViewController-Twi-Do-8Ec.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			EyYACuJQnKSNAxtqxsKaejxJyh53+7fnU0405zGXQ6A=
			</data>
		</dict>
		<key>WMFCaptchaViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9w7K8iO7KbFSODUWTbPojzCJ+jMJ0xK4zELnB2u51TE=
			</data>
		</dict>
		<key>WMFCaptchaViewController.storyboardc/WMFCaptchaViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			TyD7SFC8NdTnsG3I8jC/vsMGoIrehIcyJbBvpfg0z2Q=
			</data>
		</dict>
		<key>WMFCaptchaViewController.storyboardc/sIA-fC-CW8-view-2UF-VY-OwF.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			gRIK4JJbfme96PiFd5N12XTlVDu8vNdxMq7/zd9NTtY=
			</data>
		</dict>
		<key>WMFChangePasswordViewController.storyboardc/HvE-aJ-r18-view-1vt-ta-cot.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			kjgLW0cu9La9NOxSxle6WU2SRYG9c+wHlXiPwt7Md44=
			</data>
		</dict>
		<key>WMFChangePasswordViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CIWJEOLRYhyJ9XKp5x7bqD32FjrdWtLz6NGqIXzOlEE=
			</data>
		</dict>
		<key>WMFChangePasswordViewController.storyboardc/WMFChangePasswordViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			uLy94KpB/dlBfVhhkqc7w0XUHQlXUiVDbty5uCdPyTQ=
			</data>
		</dict>
		<key>WMFEmptyView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			/RAO1O4E/SMnyfTVe99MgRkXG5lWhZejhKAMsciwUME=
			</data>
		</dict>
		<key>WMFFindAndReplaceKeyboardBar.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			yOLzhtOei6izjopH2lAq6YWhEk33hHu6VArhPR/NVOc=
			</data>
		</dict>
		<key>WMFForgotPasswordViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			VsNwYRgeSk5YLdSjRVEtyixY9O71cHOhYyPjdLSRW6k=
			</data>
		</dict>
		<key>WMFForgotPasswordViewController.storyboardc/WMFForgotPasswordViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			S1TIS8qTI0bGsJ9J9bB1k7L6KLEF4E7h6IVhamE1qag=
			</data>
		</dict>
		<key>WMFForgotPasswordViewController.storyboardc/a7r-jI-djU-view-GYS-km-eqa.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			17h3y2gs3ET3QYcQF84kkKBtI3PUQ7mqu27Lp9OuW7w=
			</data>
		</dict>
		<key>WMFImageGalleryDetailOverlayView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ZbHXViXduaC71bsKakSMLTlC3buRdT3VMeJLVscalKo=
			</data>
		</dict>
		<key>WMFLanguageCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			7KrgacHYb3Pa4djqkIuuEb9Ik1HgNTIOgAxmQbpT57Y=
			</data>
		</dict>
		<key>WMFLanguagesViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KTRP4TZ/xoih8BvgzzN669rpiee2O2KqtcLJEQemcV4=
			</data>
		</dict>
		<key>WMFLoginViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			TQOT/4w7WrJTyUSnYB+aGhwXOkOu6lHypp0x09v7hUY=
			</data>
		</dict>
		<key>WMFLoginViewController.storyboardc/WMFLoginViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			d6MplM91NAsvg4VQpo1SsJzwS7r/PvCneyZ5Qb4qmL4=
			</data>
		</dict>
		<key>WMFLoginViewController.storyboardc/mbm-n6-GWW-view-4dr-i6-dn7.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			GQBrO7HtinSpbtJM/jY1vaOV5cP+1NP1AsUbXxuyFvw=
			</data>
		</dict>
		<key>WMFRandomDiceButton.html</key>
		<dict>
			<key>hash2</key>
			<data>
			VCKUpM3MmsE8BXa26M2PrfzVub+nT3RtQRI19oUiFFU=
			</data>
		</dict>
		<key>WMFRandomDiceButtonRoll.js</key>
		<dict>
			<key>hash2</key>
			<data>
			8LLwTb3PL0QzO7D4gER2kLN/7ARE7peuzGEa+EI2QgE=
			</data>
		</dict>
		<key>WMFReferencePanels.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			eRIKuEuh4KEKySZK/KS6Dvvy/gBGhgr2NLw76zbgqHM=
			</data>
		</dict>
		<key>WMFReferencePanels.storyboardc/WIr-N7-igK-view-Ktd-hA-m9P.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			OLN1xlut/FExjqNYTuon2NtFrUKuAuYQFlC9xjJGlvM=
			</data>
		</dict>
		<key>WMFReferencePanels.storyboardc/WMFReferencePageViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			s7iObXzlPDP0WfTMK/ezA3HK3o/Wd0ev2m4mWtvryYI=
			</data>
		</dict>
		<key>WMFReferencePanels.storyboardc/WMFReferencePanelViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			UyRTnOHd0zB3mARhWZxxT7NKOkxwFj9gF+QGn96GpRo=
			</data>
		</dict>
		<key>WMFReferencePanels.storyboardc/spA-3k-AHf-view-Hh5-za-gpf.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			MWfFCS+uqVaWOlIeHu+MJwuVa52QBdtFqMtLESQ5egU=
			</data>
		</dict>
		<key>WMFReferencePopoverMessageViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			uFaGtr7CtYctqwIgJZBm5u8TMNUjUaSVcR0gfhMN0I0=
			</data>
		</dict>
		<key>WMFReferencePopoverMessageViewController.storyboardc/UIViewController-rh8-A3-vLx.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			DfmcwKuZoaEQZmKRaQtNlM5w70rr6sb3YZxUCz6CPBg=
			</data>
		</dict>
		<key>WMFReferencePopoverMessageViewController.storyboardc/rh8-A3-vLx-view-JhK-nO-OQy.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			77P6CPBpYgwZ+sc8ld7Jegyf1Y6OJDHmKb7awAVsIyM=
			</data>
		</dict>
		<key>WMFSettingsTableViewCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			SNHRdOmWi8+zk96McBH57C2CRqqBa7pjnpt+eH/61Rc=
			</data>
		</dict>
		<key>WMFSettingsViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			MNTpc5RSoaqre2rAX8Y8ukxqmQh0ZhK9bt/RmOPyv+Q=
			</data>
		</dict>
		<key>WMFSettingsViewController.storyboardc/LWA-GV-VZP-view-1oL-zz-ml3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			urKP2v5eVMR8SnkwpRkyjg4FJwZaavNkhngC528Hmng=
			</data>
		</dict>
		<key>WMFSettingsViewController.storyboardc/SecondaryMenuViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ti9iDuOypORnPtqDDzbsc0uFLp0dRpc0UR6O3X+YCkg=
			</data>
		</dict>
		<key>WMFTableHeaderFooterLabelView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			e5957umHAQznIPa+ZDgLaxmBXAY1yqMDuHQwXyEmpiw=
			</data>
		</dict>
		<key>WMFTwoFactorPasswordViewController.storyboardc/AT3-3L-21R-view-shM-H7-5vu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			90sReedAMoNUMLC8x6Dfr8lC5esCPtEPv0cgjIkyZMg=
			</data>
		</dict>
		<key>WMFTwoFactorPasswordViewController.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			u1TWDp5EdMynbz7gL1wdePkFimgPgHLd3vieZpgEE+w=
			</data>
		</dict>
		<key>WMFTwoFactorPasswordViewController.storyboardc/WMFTwoFactorPasswordViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			67v+5uiYCIzLu2/dRts6SUYs7XIrovaGUbYIDGGIxXw=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/HdP-0o-ZZC-view-lzy-Eu-ayy.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			j4r9tpqhDGmaoBnrAIVs2MbvVsfygmpVXbwb70QaQ6k=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sNo4NKWst+SqScWNtUK/dvSwr4omii2PTQF5k57xicY=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/Sz5-Ln-qWW-view-g5O-Z4-Myl.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			f72Xbvlqt6PHZXIFkBGqCFIagzg/vvttwK3gNzAdL8A=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomeAnalyticsViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			NEkRMj88Rkr6VX7f8HrxfTWoykPWF0OQsiew4ovjHMY=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomeAnimationBackgroundViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			POmkBrOAP5Edj0e9w7Io90FHz8uy3rvPNHMfML+Jx64=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomeAnimationForgroundViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			sApd9PdJ1HJXYtnzhWdMNRF3iFgXYqXFHFtdlS27A6I=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomeContainerViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KF95UMPnrccSPZcuuPBAgxkaSMHzdWMrcmVXgMepLYA=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomeExplorationViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			hAB5I/TKll88X0m0Ej2sSWyjwFs77MkhakxvYGJuMWs=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomeInitialViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			rKcfixbQcPDxTritsv9xFDTEghOrBBuZSnOHvVIgCeU=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomeIntroductionViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			syvsNOe8y+Ns4puG4V2pVzWKsKrvwQDIAQQ4Ax0GnKI=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomeLanguageTableViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VMXcV68qReWJ5y47QIwoz2dQUG/uFmTHcuMAjo/Um80=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomePageViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			xctmm43d74GjYcxfFUcl9DSETMzYwyELCTzXFT18prw=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/WMFWelcomePanelViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			93nEF0R6S/s4i20MeaWBjyEZA8p5kl02lEVnM6NbLSQ=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/aoo-cp-EUY-view-h3n-cK-21Q.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			IS23SNy3fqn1ybqTqQjaKjGioMTWvqtZze2sc0sPakE=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/i9b-ci-8HO-view-ihb-9I-oEg.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			fgE1fN7txAnJHNe0vvEsHF+YBzoz2+josRnLaDEpxpU=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/ncK-V0-zLz-view-HVN-iR-kMH.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			tBYbDivkjtVoQyPcemrrTvmxxlmXPtjWBwVvbNBOYc0=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/phf-K5-0TN-view-GFc-RE-fPO.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			kz8Efi5BhE0I19yrKCMBhyUEoOlvgfrJZt63WA7xVHM=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/qLZ-5x-Kxd-view-Ev9-ZH-utG.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KUzrEgAjbrvNRFpnFS22R4XjF0lAAYVAAlA5M7ZaIKI=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/sbE-3l-bqs-view-wCD-x0-Vga.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			w+2eFDR9cRT/4j2bunjiVCiNCiat/pM5kanZgIOQO8k=
			</data>
		</dict>
		<key>WMFWelcome.storyboardc/uX3-NZ-3WE-view-ahe-qt-nPa.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			o67++JjfvKpkih2A+s7WQ6ljObYGavOFG9aeNX4/4NE=
			</data>
		</dict>
		<key>WelcomeContainerViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			wixBr0RW2y6bcEokfWUZZaXHol0oJlJkCWht9gpZgCs=
			</data>
		</dict>
		<key>WelcomePanelLabelContentViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			yiH0YqiXKOQWDsvh6DHJwZlQOok5Ngr6oIXNKaoiud0=
			</data>
		</dict>
		<key>WelcomePanelViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			vyPd2U5mtkGsk5IL8+NWpRSRR/RSCBI0c7kM7vr4O4M=
			</data>
		</dict>
		<key>af.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ar.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1X7AXBKrTpQBNMh8z/Qg27Ug+axg7kuzpHg/JznSDPk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>as.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V7sUtUyfZu0/LSdCs2+MxQzP15MTSd8qkXz2qN2ecE0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ast.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bn.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WoXmg1RREFiPQUohlv+mpZIhj+HZ5v4qQzuNGgTmZbs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>br.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bs.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HImRX6Ig6Yi02prK7oMBAV3YIsmbXWr0025WgoD7Vwo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ce.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WJPXQuh/AeREjE+mF8/eQc6J96lyLYbRzpsUo9ZPhZM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ckb.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ds7UbslD9/D6+FFLdIJr8NR8VIRwNRj+1VDwYOMuLW0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cy.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			9YO36Tv1y2LF4EBL6s7oE8jiwO2WMYQRXrH9CF1Mcbg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>diq.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eo.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			F9ahOy8yMC2hHUabrPoS0w0DIqNHnKet3zGgUJUsF28=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eu.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			J6ptUt/CiWko0aRafQEVwqIoDOX1la1PLNlpofBd0dM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fo.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HofpG6SlIzncjT+pNriRZ+A4rxYMS4YNs5NcntXTb3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>gl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>haw.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			y0UWguongzBsTixTMCx9StCGhq9R66lwrTyRFW4vUA8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>he.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AgHEdhkufMS3fjkZfTUKPVLaYTe9OQC/LkxatoT7S0w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			E1pDACFs50KriigSGIL4VtkXs1YHEQ246r+5Q808uZM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hrx.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hsb.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2+zqywmwOjC+nCTA/8iohG9kvR/yhMOBuOJDlhO86hw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HofpG6SlIzncjT+pNriRZ+A4rxYMS4YNs5NcntXTb3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hy.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			b/e8RscZKYGpiY2R5E4KI+iEXmJFL/5QfeoIS+IdS9o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>is.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			W6KR5SgjMi6BdGfbdD/ABqSRBUChiQn1ZpB8rDsJmXM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ka.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			yHgL2u/xQiom63jcx0kdMHHh2U4KE/1CYs3xX1itVP0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kcg.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>km.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PkngWJtcm7jCJ9EDQmm1CsFt5ngANRtqSx7ilJGkAvE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kn.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XaMwANIaC823rMNdJ3BFMljXW4z6gEQ/SbpvLL6zEQk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xrCHzXVayAN73rexknncY7r3u/p7c+8JImSfhr4NlGk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>krc.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			84nRSulbb5DXeGxaINxhXcDga9oS9yMIOQzBNxiZKJo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ksh.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ndxCOVV0f8EN2gE7Osc0tw1weWxjqghCTo6YiXPBYio=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lb.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lt.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FMx/nta/eQnzLVAGA7SNBzpFF0b/XTyOpLogMjE0idw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lv.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ytBrI2RqWg9eaMQs7a3AlijgEJV5idPiGr0MEfS0TaU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			p0CjjI64+xUgUtnz6GKKxPi4ioR9Q7wNfnLAFKqUq7k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ml.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			72BG10u8LTw3F2Rwv0XxxuOc6h/EHoWlOMCLmgfmPvs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			E1pDACFs50KriigSGIL4VtkXs1YHEQ246r+5Q808uZM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ms.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>my.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AJWaBQOkbfnQ8ZARwKJFnFOVqODqIz0IPOD/KR1Qawo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ne.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			E1pDACFs50KriigSGIL4VtkXs1YHEQ246r+5Q808uZM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nqo.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>oc.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			p1bgot8DNbm8jUQD9E4rTj+HMr23xjZadCr0m2PczqI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>om.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5JNmLyIrDp74/Qftt9vznMwfhd3/8pBzRAXFBv2aidQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>or.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IGvmvkUafEm2a8wVdd0UUiThogNdtCb2m8zpP40St8Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pa.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gBXndRqKsUmLW7cPMKQB88vfVFdObxiPTn8qnAFVD/M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ps.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UpY4+2+0QLFPxAPfRzz5UOrVhQOhFPdNV/WihrfK9NU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HofpG6SlIzncjT+pNriRZ+A4rxYMS4YNs5NcntXTb3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HofpG6SlIzncjT+pNriRZ+A4rxYMS4YNs5NcntXTb3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			84nRSulbb5DXeGxaINxhXcDga9oS9yMIOQzBNxiZKJo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sa.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			E1pDACFs50KriigSGIL4VtkXs1YHEQ246r+5Q808uZM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sah.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h+l4rPGbC0WruKUzoTjLvlk92oNbA9Hxz7VKiM7vFkw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sco.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6ufa9xmolNUt7dHdzVSPHg5MTjpQGGjeOopQY75NS3k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sd.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EmuTYMNMdn0m/uEo+m+KQLvOuxMphhp8RPCEd7ext3M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HofpG6SlIzncjT+pNriRZ+A4rxYMS4YNs5NcntXTb3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sq.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sr-EC.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			p0CjjI64+xUgUtnz6GKKxPi4ioR9Q7wNfnLAFKqUq7k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>su.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HofpG6SlIzncjT+pNriRZ+A4rxYMS4YNs5NcntXTb3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sw.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ta.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oUVXQJT0HkVUoFsz3495+oE78dYOPjBM5Sxll1hqRkk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>te.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PM/vee6ZM7tqnTYbW1g7ktf7c4Q0b8hvwIlnCLwVdBs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tg-cyrl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GjZsW4wH3qZat87Jncyfef4trVvfwAIIDXKaw0bxiSE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FIgH325LJdEjPSs8Jwz2NYyUQm765UkStCiXe8nbUz8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ftp6z1S4JCiT91SvGA7Pr3NuXeR/0F9LdwxuYI9hmWs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ikLmTZN0mIeLQrToPi6sHv/+b9lB8rxNCiZiCXGhlmY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ur.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hBwUltx86i7KmnWWYsLJiG2K9D/dLA3EdstXTjCejCI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uz.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			F/zlOAMa6djAZCeA+otjymA0XO8az4N738+3jikaW5c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vec.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jNwXTBjvrTN8baPhrEVsOU+tqAJuQGYU4dKYZbwQvQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>yi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			H//tjPw1DTyhDVrCP9sxOXUzUjLDc/urxQjgHfnkECY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1NmXnHLpGUD2CSXR0CsEqDPlSpZYeBaXay2tE5OrUD8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			njEoclmUVcxirm4L0TQtjMg9/u4EY1XDs9oOkPwIOmI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
