<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Assets.car</key>
		<data>
		lrVR85MLgm7m2cfoO3ZX1nhusrE=
		</data>
		<key>BatchEditToolbarViewController.nib</key>
		<data>
		qEQVXnu/PbcVBsC45KvxzMvddpc=
		</data>
		<key>Cache.momd/Cache 2.mom</key>
		<data>
		BwrzDc6sbIB540F8FzgubJXfFxU=
		</data>
		<key>Cache.momd/Cache 2.omo</key>
		<data>
		+x9ZY3M6oizhkkOYb+CEs3wljCM=
		</data>
		<key>Cache.momd/Cache.mom</key>
		<data>
		LCHCTYxA99zvWxY6vdkfw+4qu70=
		</data>
		<key>Cache.momd/VersionInfo.plist</key>
		<data>
		DfjrTcCKHa0HShR1ZZJUqrBGmxA=
		</data>
		<key>CacheItemMappingModel.cdm</key>
		<data>
		UB8ah+PIrQmunJwnMaKujE5U/p8=
		</data>
		<key>EventLogging.momd/EventLogging 2.mom</key>
		<data>
		bR+BaPoFKhYoYFuRFshr7cgzWog=
		</data>
		<key>EventLogging.momd/EventLogging 2.omo</key>
		<data>
		8CBmDSwsp7Y2ScV/OF6LvDzDyP0=
		</data>
		<key>EventLogging.momd/EventLogging.mom</key>
		<data>
		uIxjVcKsAz3JOpoD9QEbVAsh6jk=
		</data>
		<key>EventLogging.momd/VersionInfo.plist</key>
		<data>
		xGngkN7IJNQwyx/9+9R2rnRh8Mc=
		</data>
		<key>EventPlatformEvents.momd/EventPlatformEvents.mom</key>
		<data>
		ow++q0zkDc+6srZ19rMbi9HbJWU=
		</data>
		<key>EventPlatformEvents.momd/VersionInfo.plist</key>
		<data>
		LYV3Gfa6Mt1HOSv6WIHhA0cL96w=
		</data>
		<key>Info.plist</key>
		<data>
		VOzNXHcXIBdvgzBtO9REZLR4xbU=
		</data>
		<key>MediaWikiAcceptLanguageMapping.json</key>
		<data>
		I1fL5KV8X+sUSAEhY6xCiBFM0wY=
		</data>
		<key>RemoteNotifications.momd/RemoteNotifications 2.mom</key>
		<data>
		hCWyYbm9/syOf0SynOvqUcWVlFM=
		</data>
		<key>RemoteNotifications.momd/RemoteNotifications 3.mom</key>
		<data>
		WBtEGSoq2psGUu8xIv8PbRAejsQ=
		</data>
		<key>RemoteNotifications.momd/RemoteNotifications 3.omo</key>
		<data>
		/8V4RY6Yq1hW4YkAl0R52Ku/FcU=
		</data>
		<key>RemoteNotifications.momd/RemoteNotifications.mom</key>
		<data>
		zo547tlzlBVbAwcKscHGrpMXPBk=
		</data>
		<key>RemoteNotifications.momd/VersionInfo.plist</key>
		<data>
		2gHA6qJqXHqdpNkbRtNxgTylLAU=
		</data>
		<key>WMFArticlePreviewViewController.nib</key>
		<data>
		qnIMZHn2vedo7X5lhtMPzCS36NY=
		</data>
		<key>Wikipedia.momd/VersionInfo.plist</key>
		<data>
		zWW7INFXYeXWQJjF+15wpp/v+T0=
		</data>
		<key>Wikipedia.momd/Wikipedia 2.mom</key>
		<data>
		bhwlLIgGyy4yWn3jNEQKlUXqbtw=
		</data>
		<key>Wikipedia.momd/Wikipedia 3.mom</key>
		<data>
		LkF74faJ+9DQA0RfKlxkumn/cOw=
		</data>
		<key>Wikipedia.momd/Wikipedia 4.mom</key>
		<data>
		Ey0eSw02AEOHj6+S9KNaFTFMpCA=
		</data>
		<key>Wikipedia.momd/Wikipedia 5.mom</key>
		<data>
		ohi2ZpXgzADqiGjh4U0IQnVwV8w=
		</data>
		<key>Wikipedia.momd/Wikipedia 6.mom</key>
		<data>
		Y58fGNQo0Cvu6IVP1+TU8in7fyY=
		</data>
		<key>Wikipedia.momd/Wikipedia 6.omo</key>
		<data>
		pOTNSrhln7u4KgTRTw1BBfU4ATU=
		</data>
		<key>Wikipedia.momd/Wikipedia.mom</key>
		<data>
		MFLjdwVXQ+uC7D7jpgokcva/k40=
		</data>
		<key>af.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NgqWtMCsreGN8iQ6KEifge+im0c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>af.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			lTC62+vbXSY+ynXoSoqPCELacp4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qQJvsAYk4pWx849/dv3ZSkmt8vE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ar.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			KqrQgvr7Pgxnuwz3sKqr0GPNw4c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>as.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/dvPoKo0djMnr717It8vDUCKQyw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>as.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			mb7vdYKRd1H32GH6eizxOTTHeUE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>assets/WMF_Black.pdf</key>
		<data>
		ITOQyLmkW/SRtwyxmKcqU9cpkqM=
		</data>
		<key>assets/WMF_White.pdf</key>
		<data>
		g2Fu0l/R7AiyQBUUnbG5oiSzQfo=
		</data>
		<key>assets/about.html</key>
		<data>
		71zWFcemVc/3OYWPEXi8nW2EDZw=
		</data>
		<key>assets/about.js</key>
		<data>
		DhWj1UbMXLWhWP5HpcLVfo+EX9k=
		</data>
		<key>assets/codemirror/codemirror-black.css</key>
		<data>
		yWBqMDNS/L2JIhJ9HCwJtMlVu64=
		</data>
		<key>assets/codemirror/codemirror-common.css</key>
		<data>
		RfyS6Qlb8CXGhi9YVK/MEYzkJ88=
		</data>
		<key>assets/codemirror/codemirror-dark.css</key>
		<data>
		Im5Cn8OCi9HoVMuMLKPkBPnbNTo=
		</data>
		<key>assets/codemirror/codemirror-editTextSelection.js</key>
		<data>
		57SJGQ0rUytjTb7QjspAfMc5E2c=
		</data>
		<key>assets/codemirror/codemirror-index.html</key>
		<data>
		PIKxMFhbX8bZ1SzP/Zgmj3TMdoE=
		</data>
		<key>assets/codemirror/codemirror-light.css</key>
		<data>
		E5Dcm9NyaIq2nZPU62aCZ+WcCOg=
		</data>
		<key>assets/codemirror/codemirror-range-determination-bundle.js</key>
		<data>
		bEIqv3WkeqhGEo1UrJ2XYH5yUHs=
		</data>
		<key>assets/codemirror/codemirror-sepia.css</key>
		<data>
		GoyX9pXKutwyLat8Pa/n7JrGRYg=
		</data>
		<key>assets/codemirror/codemirror-syntax-highlighting-off.css</key>
		<data>
		fdlouvr705+hL5KOqiZ/FBPcYPw=
		</data>
		<key>assets/codemirror/config/codemirror-config-aa.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ab.json</key>
		<data>
		zTdNrDs6uJyZc3zpHz14vikWaM8=
		</data>
		<key>assets/codemirror/config/codemirror-config-ace.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>assets/codemirror/config/codemirror-config-ady.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-af.json</key>
		<data>
		I73819yTF0EsTodl2+BU1fk8l5U=
		</data>
		<key>assets/codemirror/config/codemirror-config-ak.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-als.json</key>
		<data>
		3Eq8agAgulLI+FzpQo6Qg/QuT9c=
		</data>
		<key>assets/codemirror/config/codemirror-config-alt.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>assets/codemirror/config/codemirror-config-am.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-an.json</key>
		<data>
		bVjz4g8A7VQUWucyorJRR9/MZtw=
		</data>
		<key>assets/codemirror/config/codemirror-config-ang.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ar.json</key>
		<data>
		Nu2SDZPasYpmqDd/S8w0JrkyulM=
		</data>
		<key>assets/codemirror/config/codemirror-config-arc.json</key>
		<data>
		7Z/E8vXk3lU+mhm9qREXVFdOluo=
		</data>
		<key>assets/codemirror/config/codemirror-config-ary.json</key>
		<data>
		Hm4o2reN/0EN/oE+iZPbsCG7GB8=
		</data>
		<key>assets/codemirror/config/codemirror-config-arz.json</key>
		<data>
		ETOItgKU5jn3IkObRqgBrDbaBds=
		</data>
		<key>assets/codemirror/config/codemirror-config-as.json</key>
		<data>
		3QXl57Xifsnae2lqSBB5ktGL+cU=
		</data>
		<key>assets/codemirror/config/codemirror-config-ast.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>assets/codemirror/config/codemirror-config-atj.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-av.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>assets/codemirror/config/codemirror-config-avk.json</key>
		<data>
		XjfRqxSSQoj7IqGPpIjHnejIhrY=
		</data>
		<key>assets/codemirror/config/codemirror-config-awa.json</key>
		<data>
		2rU6riSVKuwjgsJ5yaXdrw7xb78=
		</data>
		<key>assets/codemirror/config/codemirror-config-ay.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>assets/codemirror/config/codemirror-config-az.json</key>
		<data>
		645uMqly8LCqajqOxSVimxbcZ5U=
		</data>
		<key>assets/codemirror/config/codemirror-config-azb.json</key>
		<data>
		ovjrsLMwx0BSxY3Xz3PevbLrjrM=
		</data>
		<key>assets/codemirror/config/codemirror-config-ba.json</key>
		<data>
		8S55J7143TiP7oJvH7O9StE+WCU=
		</data>
		<key>assets/codemirror/config/codemirror-config-ban.json</key>
		<data>
		YeqWTi/CkNp4jJxVKxiFqvXkbaw=
		</data>
		<key>assets/codemirror/config/codemirror-config-bar.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>assets/codemirror/config/codemirror-config-bat-smg.json</key>
		<data>
		t5VE9wUH1dyxrXvzcWhqKrTT1Ec=
		</data>
		<key>assets/codemirror/config/codemirror-config-bcl.json</key>
		<data>
		//KNMzpdmxhmpvJAt3abgeDUeBk=
		</data>
		<key>assets/codemirror/config/codemirror-config-be-tarask.json</key>
		<data>
		myW1b6JXHaRpL5gXOrrSshOfo3M=
		</data>
		<key>assets/codemirror/config/codemirror-config-be-x-old.json</key>
		<data>
		myW1b6JXHaRpL5gXOrrSshOfo3M=
		</data>
		<key>assets/codemirror/config/codemirror-config-be.json</key>
		<data>
		sCzKlvO5Xye5E9EPLuPwL/ORdAs=
		</data>
		<key>assets/codemirror/config/codemirror-config-bg.json</key>
		<data>
		uymHUAI69elcWzKwTo0+hi1v834=
		</data>
		<key>assets/codemirror/config/codemirror-config-bh.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-bi.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-bjn.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>assets/codemirror/config/codemirror-config-bm.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-bn.json</key>
		<data>
		i0aUZfAJBVFq5a1TLTQ2LgYPaJU=
		</data>
		<key>assets/codemirror/config/codemirror-config-bo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-bpy.json</key>
		<data>
		oL9OpW4ktd4uj6YXFfS2ywCoBd4=
		</data>
		<key>assets/codemirror/config/codemirror-config-br.json</key>
		<data>
		BxUdTM+gXun24f6vhdr2MI52Oxk=
		</data>
		<key>assets/codemirror/config/codemirror-config-bs.json</key>
		<data>
		f74giBGTGTZYJ3JZCh9YJbIouNI=
		</data>
		<key>assets/codemirror/config/codemirror-config-bug.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>assets/codemirror/config/codemirror-config-bxr.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>assets/codemirror/config/codemirror-config-ca.json</key>
		<data>
		VgVChIyTpuhB98zZq2llKqajG3Q=
		</data>
		<key>assets/codemirror/config/codemirror-config-cbk-zam.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>assets/codemirror/config/codemirror-config-cdo.json</key>
		<data>
		LSxzQOjoQsxRPinjoDk+pSrFv3U=
		</data>
		<key>assets/codemirror/config/codemirror-config-ce.json</key>
		<data>
		r5i94V18AlgZwz37QQDrHLssLIo=
		</data>
		<key>assets/codemirror/config/codemirror-config-ceb.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ch.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-cho.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>assets/codemirror/config/codemirror-config-chr.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-chy.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ckb.json</key>
		<data>
		9wWfZ0wdbrq93ir+WLR4xTdM8e4=
		</data>
		<key>assets/codemirror/config/codemirror-config-co.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-cr.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-crh.json</key>
		<data>
		z+nv7HH2q/5yqwD7XR0tS13fTBI=
		</data>
		<key>assets/codemirror/config/codemirror-config-cs.json</key>
		<data>
		X82QPrPFsgpAoM3EjWGBgT7sEEU=
		</data>
		<key>assets/codemirror/config/codemirror-config-csb.json</key>
		<data>
		rk8fjiWdwrcJFrsfy61yy5fu0NY=
		</data>
		<key>assets/codemirror/config/codemirror-config-cu.json</key>
		<data>
		WW6z8LJYgexw3P/AQ6az3F6xXOw=
		</data>
		<key>assets/codemirror/config/codemirror-config-cv.json</key>
		<data>
		az47y1lS0Zj7ytr2sWmcJ7pU4fM=
		</data>
		<key>assets/codemirror/config/codemirror-config-cy.json</key>
		<data>
		GVSY8tTzoIY+q4y6OxhRpFOD2JU=
		</data>
		<key>assets/codemirror/config/codemirror-config-da.json</key>
		<data>
		bK3GYBF1b2LH+4r+zj/Ak1+Iqak=
		</data>
		<key>assets/codemirror/config/codemirror-config-de.json</key>
		<data>
		5Q8o5FqF9LXY58esZ224hdqnKIw=
		</data>
		<key>assets/codemirror/config/codemirror-config-din.json</key>
		<data>
		TxJLk2R+3OTbHCwUcC5o+Mi7778=
		</data>
		<key>assets/codemirror/config/codemirror-config-diq.json</key>
		<data>
		pElMHdyuEEfk0cYn3yMZk2Mo8YY=
		</data>
		<key>assets/codemirror/config/codemirror-config-dsb.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>assets/codemirror/config/codemirror-config-dty.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-dv.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-dz.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ee.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-el.json</key>
		<data>
		OdDNk4rBvppnWuG2nb/bbGnUwO8=
		</data>
		<key>assets/codemirror/config/codemirror-config-eml.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-en.json</key>
		<data>
		N/q725getNHsJDPKjIYZpSMTKPw=
		</data>
		<key>assets/codemirror/config/codemirror-config-eo.json</key>
		<data>
		cIjEZEUekSZIxmn4pBhC8roSKzs=
		</data>
		<key>assets/codemirror/config/codemirror-config-es.json</key>
		<data>
		f9x/OzPGMWF4sTmjUgv2Q7Dw60U=
		</data>
		<key>assets/codemirror/config/codemirror-config-et.json</key>
		<data>
		zSjlayV4R/nawWImOWw7VDIhE2w=
		</data>
		<key>assets/codemirror/config/codemirror-config-eu.json</key>
		<data>
		p8TLPmNrRXU2PQOuf4gD2HdfY5E=
		</data>
		<key>assets/codemirror/config/codemirror-config-ext.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>assets/codemirror/config/codemirror-config-fa.json</key>
		<data>
		CtLM1u1VWdUarDwcK77Qbvni8y4=
		</data>
		<key>assets/codemirror/config/codemirror-config-ff.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-fi.json</key>
		<data>
		rN31VzUGJGSRjZsda8YuujeJfZA=
		</data>
		<key>assets/codemirror/config/codemirror-config-fiu-vro.json</key>
		<data>
		zSjlayV4R/nawWImOWw7VDIhE2w=
		</data>
		<key>assets/codemirror/config/codemirror-config-fj.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-fo.json</key>
		<data>
		ODPO3Y198DU/8c/IRomyr3KxJcA=
		</data>
		<key>assets/codemirror/config/codemirror-config-fr.json</key>
		<data>
		D/0hzopIpm6OaKBYWDuX0dXfDHM=
		</data>
		<key>assets/codemirror/config/codemirror-config-frp.json</key>
		<data>
		h5W7fKvBGGsk19RAd5ZEQwxGWVQ=
		</data>
		<key>assets/codemirror/config/codemirror-config-frr.json</key>
		<data>
		jcgfzd72vyJotfFs474u07jTvN8=
		</data>
		<key>assets/codemirror/config/codemirror-config-fur.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-fy.json</key>
		<data>
		+5R53P4UpjA/mmof+vQy6aYMX4Y=
		</data>
		<key>assets/codemirror/config/codemirror-config-ga.json</key>
		<data>
		zL+GGGJXIWXkBq6a6mRJfOReBWo=
		</data>
		<key>assets/codemirror/config/codemirror-config-gag.json</key>
		<data>
		UX8j3MQCFzVzO4F43pwiwd4jvPo=
		</data>
		<key>assets/codemirror/config/codemirror-config-gan.json</key>
		<data>
		uLdYqbmE887gxf3jd03eFxgbaRk=
		</data>
		<key>assets/codemirror/config/codemirror-config-gcr.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-gd.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-gl.json</key>
		<data>
		eAdyhryJn2yvFoYCGD75oVaZqm4=
		</data>
		<key>assets/codemirror/config/codemirror-config-glk.json</key>
		<data>
		2I0ytfmpKhiQkRfV1KjCB1qUJbk=
		</data>
		<key>assets/codemirror/config/codemirror-config-gn.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>assets/codemirror/config/codemirror-config-gom.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-gor.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>assets/codemirror/config/codemirror-config-got.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-gu.json</key>
		<data>
		rk3h/e0mWrF0WafcBQU4n0Lg++k=
		</data>
		<key>assets/codemirror/config/codemirror-config-gv.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ha.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-hak.json</key>
		<data>
		LSxzQOjoQsxRPinjoDk+pSrFv3U=
		</data>
		<key>assets/codemirror/config/codemirror-config-haw.json</key>
		<data>
		z2j7vrbkjq/0FamFA6/vBGkuoeo=
		</data>
		<key>assets/codemirror/config/codemirror-config-he.json</key>
		<data>
		GKxPpBozsFZFrU3n62L8hXbR0jE=
		</data>
		<key>assets/codemirror/config/codemirror-config-hi.json</key>
		<data>
		xSHabSXQduKYWggyMueGqFNXfHI=
		</data>
		<key>assets/codemirror/config/codemirror-config-hif.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ho.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>assets/codemirror/config/codemirror-config-hr.json</key>
		<data>
		WxM/B82qxTKk0D4I0nq8Ozd47LI=
		</data>
		<key>assets/codemirror/config/codemirror-config-hsb.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ht.json</key>
		<data>
		W6wiUL2C/OF3XhiN10Q/B53dgtA=
		</data>
		<key>assets/codemirror/config/codemirror-config-hu.json</key>
		<data>
		KgfPW76fXBDJ8ERTQ2XuTuQ+6Xs=
		</data>
		<key>assets/codemirror/config/codemirror-config-hy.json</key>
		<data>
		Xwy8drse48WCKXkeEVVZ7IIY1Sw=
		</data>
		<key>assets/codemirror/config/codemirror-config-hyw.json</key>
		<data>
		1jIf2r57zaggHvjqydDcAL9jIsE=
		</data>
		<key>assets/codemirror/config/codemirror-config-hz.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>assets/codemirror/config/codemirror-config-ia.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-id.json</key>
		<data>
		mJeZ2uGXMzBFE9HzeIKaLJU3K6k=
		</data>
		<key>assets/codemirror/config/codemirror-config-ie.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ig.json</key>
		<data>
		HaB4JTLUDaePtNSOChKE+/CNF/Y=
		</data>
		<key>assets/codemirror/config/codemirror-config-ii.json</key>
		<data>
		TK6qC7C4OoAUbhRHoqUNY1EQXko=
		</data>
		<key>assets/codemirror/config/codemirror-config-ik.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ilo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-inh.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>assets/codemirror/config/codemirror-config-io.json</key>
		<data>
		UFG/tKHbpxprDZQo9R/z0KhNiTs=
		</data>
		<key>assets/codemirror/config/codemirror-config-is.json</key>
		<data>
		iaZ/0Zc2pVTjGU+9QAAVecr9Lto=
		</data>
		<key>assets/codemirror/config/codemirror-config-it.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-iu.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ja.json</key>
		<data>
		67KpTmrO0mIV88Bv0XmxIcx+La0=
		</data>
		<key>assets/codemirror/config/codemirror-config-jam.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-jbo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-jv.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>assets/codemirror/config/codemirror-config-ka.json</key>
		<data>
		E8Plevlp14uYskWyBxaeRddn3Tk=
		</data>
		<key>assets/codemirror/config/codemirror-config-kaa.json</key>
		<data>
		gkYWM+bGTpvfEtBJqHlFcfrFRPE=
		</data>
		<key>assets/codemirror/config/codemirror-config-kab.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-kbd.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-kbp.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-kg.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ki.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-kj.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>assets/codemirror/config/codemirror-config-kk.json</key>
		<data>
		2uXmk402KTuHWbjI8Bnh/rXeRHk=
		</data>
		<key>assets/codemirror/config/codemirror-config-kl.json</key>
		<data>
		c+hHVy7w6K8SL+ZeFQ5wTzdvYUE=
		</data>
		<key>assets/codemirror/config/codemirror-config-km.json</key>
		<data>
		BQr/NaMv/CEKOUlewLFYsTX2HG8=
		</data>
		<key>assets/codemirror/config/codemirror-config-kn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ko.json</key>
		<data>
		Hgw3Y1bQ1Q/WcZTZ8X+rQjK0Eb4=
		</data>
		<key>assets/codemirror/config/codemirror-config-koi.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>assets/codemirror/config/codemirror-config-kr.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>assets/codemirror/config/codemirror-config-krc.json</key>
		<data>
		oAvMAtoZC1ZHVFXKioWCENnw5J8=
		</data>
		<key>assets/codemirror/config/codemirror-config-ks.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ksh.json</key>
		<data>
		Y2GnKouTNgagU28R8YbDWI+IJ+8=
		</data>
		<key>assets/codemirror/config/codemirror-config-ku.json</key>
		<data>
		UPXlULLzkKAmCPwlReCi11TNwLY=
		</data>
		<key>assets/codemirror/config/codemirror-config-kv.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>assets/codemirror/config/codemirror-config-kw.json</key>
		<data>
		YxqdVFYUskQjY16n6WP5u4vbAWk=
		</data>
		<key>assets/codemirror/config/codemirror-config-ky.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-la.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-lad.json</key>
		<data>
		E5itV9piEf5V0jypjoBcWb4Ub6E=
		</data>
		<key>assets/codemirror/config/codemirror-config-lb.json</key>
		<data>
		ysFLwhSfIy7dzvTDVYWtekXL3tA=
		</data>
		<key>assets/codemirror/config/codemirror-config-lbe.json</key>
		<data>
		CafJ9WeY+fqTdzKD9wfzKckIyxw=
		</data>
		<key>assets/codemirror/config/codemirror-config-lez.json</key>
		<data>
		TKrnr5aBytHVdPnuYUMhfBIwvYA=
		</data>
		<key>assets/codemirror/config/codemirror-config-lfn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-lg.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-li.json</key>
		<data>
		j3cKztE56n0hsaSCOqJXjBuLBTY=
		</data>
		<key>assets/codemirror/config/codemirror-config-lij.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-lld.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-lmo.json</key>
		<data>
		uWDBOwP8xN5BQM+5L4yVGO3lH1k=
		</data>
		<key>assets/codemirror/config/codemirror-config-ln.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-lo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-lrc.json</key>
		<data>
		2I0ytfmpKhiQkRfV1KjCB1qUJbk=
		</data>
		<key>assets/codemirror/config/codemirror-config-lt.json</key>
		<data>
		t5VE9wUH1dyxrXvzcWhqKrTT1Ec=
		</data>
		<key>assets/codemirror/config/codemirror-config-ltg.json</key>
		<data>
		tnQszZE4deaCTRxM0bgvhZPGgO0=
		</data>
		<key>assets/codemirror/config/codemirror-config-lv.json</key>
		<data>
		tnQszZE4deaCTRxM0bgvhZPGgO0=
		</data>
		<key>assets/codemirror/config/codemirror-config-mad.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>assets/codemirror/config/codemirror-config-mai.json</key>
		<data>
		2rU6riSVKuwjgsJ5yaXdrw7xb78=
		</data>
		<key>assets/codemirror/config/codemirror-config-map-bms.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>assets/codemirror/config/codemirror-config-mdf.json</key>
		<data>
		9MOAPy5mCclinwBdbgOjWsFAJ/w=
		</data>
		<key>assets/codemirror/config/codemirror-config-mg.json</key>
		<data>
		1ltA4oo/jRBVZWrLPSGUoUbH/d8=
		</data>
		<key>assets/codemirror/config/codemirror-config-mh.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>assets/codemirror/config/codemirror-config-mhr.json</key>
		<data>
		OYhkDGsdgY6N636pK7BRN/D9Jks=
		</data>
		<key>assets/codemirror/config/codemirror-config-mi.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-min.json</key>
		<data>
		WDeLpvOZ2JmjLEUzkh1kiZaTxdo=
		</data>
		<key>assets/codemirror/config/codemirror-config-mk.json</key>
		<data>
		AqXAC4S4gZGAI+g8MmOjO241xI4=
		</data>
		<key>assets/codemirror/config/codemirror-config-ml.json</key>
		<data>
		rtWqJgh6kKZEEa+7kFu+WYAaryA=
		</data>
		<key>assets/codemirror/config/codemirror-config-mn.json</key>
		<data>
		HTEdRAKkqtZlGBuJL3gllNEv83U=
		</data>
		<key>assets/codemirror/config/codemirror-config-mni.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-mnw.json</key>
		<data>
		4B+Gtx7hsm8bAiZsjz8+3B+pGLI=
		</data>
		<key>assets/codemirror/config/codemirror-config-mr.json</key>
		<data>
		vFnEVyZe+dBcB8lKLaiRGR3U1RY=
		</data>
		<key>assets/codemirror/config/codemirror-config-mrj.json</key>
		<data>
		OYhkDGsdgY6N636pK7BRN/D9Jks=
		</data>
		<key>assets/codemirror/config/codemirror-config-ms.json</key>
		<data>
		v1PMWX9JALlJoWlfkyaQYVFi2es=
		</data>
		<key>assets/codemirror/config/codemirror-config-mt.json</key>
		<data>
		KCTicMbfhEH7WZF9gA6NtUTTVoM=
		</data>
		<key>assets/codemirror/config/codemirror-config-mus.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>assets/codemirror/config/codemirror-config-mwl.json</key>
		<data>
		AXKHYaepkZ3DXQbnvfyl4BLaDX4=
		</data>
		<key>assets/codemirror/config/codemirror-config-my.json</key>
		<data>
		uD6HK7O6846DEQc96BslRmbHArk=
		</data>
		<key>assets/codemirror/config/codemirror-config-myv.json</key>
		<data>
		9MOAPy5mCclinwBdbgOjWsFAJ/w=
		</data>
		<key>assets/codemirror/config/codemirror-config-mzn.json</key>
		<data>
		x3+T9NIxhzODeLfzTe3IqDPvJrs=
		</data>
		<key>assets/codemirror/config/codemirror-config-na.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-nah.json</key>
		<data>
		bPVS9f0XJUnrOHtgvo/HMLP5INg=
		</data>
		<key>assets/codemirror/config/codemirror-config-nap.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-nds-nl.json</key>
		<data>
		lik9vCHoc+d7jcdQIAaKEO9p85g=
		</data>
		<key>assets/codemirror/config/codemirror-config-nds.json</key>
		<data>
		89yutkpUKSuh6CZrGBnePOr+xf0=
		</data>
		<key>assets/codemirror/config/codemirror-config-ne.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-new.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ng.json</key>
		<data>
		jksLUtlEIGGzcT5Q7PWB9fUczmI=
		</data>
		<key>assets/codemirror/config/codemirror-config-nia.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>assets/codemirror/config/codemirror-config-nl.json</key>
		<data>
		j3cKztE56n0hsaSCOqJXjBuLBTY=
		</data>
		<key>assets/codemirror/config/codemirror-config-nn.json</key>
		<data>
		jg15Z7E8Jh3u0IcjmnqCg2b+MFM=
		</data>
		<key>assets/codemirror/config/codemirror-config-no.json</key>
		<data>
		Hg26oDqEIJPbzpnWIhY2wnOYJnY=
		</data>
		<key>assets/codemirror/config/codemirror-config-nov.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-nqo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-nrm.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-nso.json</key>
		<data>
		7wyifuMip1P0lUKZ4jAoSb4NM7A=
		</data>
		<key>assets/codemirror/config/codemirror-config-nv.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ny.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-oc.json</key>
		<data>
		Qe3JkBG/b1alTjyirShG0pce8/Q=
		</data>
		<key>assets/codemirror/config/codemirror-config-olo.json</key>
		<data>
		j/KgeuCOTJQnNcjjkOhssVK+NsY=
		</data>
		<key>assets/codemirror/config/codemirror-config-om.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-or.json</key>
		<data>
		sAXpUs+9KHzmNCv19KlS9lkBg0s=
		</data>
		<key>assets/codemirror/config/codemirror-config-os.json</key>
		<data>
		yxKU1ki4QKl39UbeJvD7woSBLl4=
		</data>
		<key>assets/codemirror/config/codemirror-config-pa.json</key>
		<data>
		O4w0K/ICDKMM9O5T9v38/LX7QVw=
		</data>
		<key>assets/codemirror/config/codemirror-config-pag.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-pam.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-pap.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-pcd.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-pdc.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>assets/codemirror/config/codemirror-config-pfl.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>assets/codemirror/config/codemirror-config-pi.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-pih.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-pl.json</key>
		<data>
		KJYKOZ9UsxL2O08irJQgvzOnBlQ=
		</data>
		<key>assets/codemirror/config/codemirror-config-pms.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-pnb.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-pnt.json</key>
		<data>
		OdDNk4rBvppnWuG2nb/bbGnUwO8=
		</data>
		<key>assets/codemirror/config/codemirror-config-ps.json</key>
		<data>
		b6q5B8whAj9Wx3ouVzgEbdQ4ZhY=
		</data>
		<key>assets/codemirror/config/codemirror-config-pt.json</key>
		<data>
		nL7j8vJt62TdsSdAq5FEGPQbIxY=
		</data>
		<key>assets/codemirror/config/codemirror-config-qu.json</key>
		<data>
		1SgIEcUxGmXv+fY6zgmLabdGAco=
		</data>
		<key>assets/codemirror/config/codemirror-config-rm.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-rmy.json</key>
		<data>
		UdVASIU2ZazvweWbIuzB/Wd5zD4=
		</data>
		<key>assets/codemirror/config/codemirror-config-rn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ro.json</key>
		<data>
		t19UX480peG2vqy4+82v71N8HyU=
		</data>
		<key>assets/codemirror/config/codemirror-config-roa-rup.json</key>
		<data>
		UdVASIU2ZazvweWbIuzB/Wd5zD4=
		</data>
		<key>assets/codemirror/config/codemirror-config-roa-tara.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-ru.json</key>
		<data>
		Zpaox6mE8OTx1UlFsoJNz9bWYu4=
		</data>
		<key>assets/codemirror/config/codemirror-config-rue.json</key>
		<data>
		NqpW/xdm77NEGpqdOLY1xYTzVeo=
		</data>
		<key>assets/codemirror/config/codemirror-config-rw.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-sa.json</key>
		<data>
		pfRYnV7ni3PNpk/1YeBE7CcBGC0=
		</data>
		<key>assets/codemirror/config/codemirror-config-sah.json</key>
		<data>
		i1zAjvEK7YxphRTJnY4/NCJynxE=
		</data>
		<key>assets/codemirror/config/codemirror-config-sat.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-sc.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-scn.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-sco.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-sd.json</key>
		<data>
		4b+3ibDHxP55y8+9/tgOOJ3LgwU=
		</data>
		<key>assets/codemirror/config/codemirror-config-se.json</key>
		<data>
		PQ+06aK+RelI5snIZcoNUE6tUTY=
		</data>
		<key>assets/codemirror/config/codemirror-config-sg.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-sh.json</key>
		<data>
		2U4kScNenofrw+z3dKG9ngnzFH0=
		</data>
		<key>assets/codemirror/config/codemirror-config-shn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-shy.json</key>
		<data>
		ulNRQwXUjUircDsbD/C/X+pG+ls=
		</data>
		<key>assets/codemirror/config/codemirror-config-si.json</key>
		<data>
		g+T3NwgVm5YpgHU3JimQbbRbw7c=
		</data>
		<key>assets/codemirror/config/codemirror-config-simple.json</key>
		<data>
		+f3hOUAYaapFcoCQZ+mP+6KExhY=
		</data>
		<key>assets/codemirror/config/codemirror-config-sk.json</key>
		<data>
		hx6EWehveJB8tlWM3fG0lIEhPvQ=
		</data>
		<key>assets/codemirror/config/codemirror-config-skr.json</key>
		<data>
		3K94MyIwY2LDWZNe7wNCV7zVuy8=
		</data>
		<key>assets/codemirror/config/codemirror-config-sl.json</key>
		<data>
		YBt5kMhUFJU1OtHivBP19Cnb55w=
		</data>
		<key>assets/codemirror/config/codemirror-config-sm.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-smn.json</key>
		<data>
		viraM4aHzqqC5SCNA+3xUUvUGe8=
		</data>
		<key>assets/codemirror/config/codemirror-config-sn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-so.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-sq.json</key>
		<data>
		UXj+CzmnKnfyT9DDIEZKCi+BVCE=
		</data>
		<key>assets/codemirror/config/codemirror-config-sr.json</key>
		<data>
		pKByFsijvveOmbKQ8cPeL5HoHvo=
		</data>
		<key>assets/codemirror/config/codemirror-config-srn.json</key>
		<data>
		sdHhpVD9/vxmfK3rGZVAtTSlfpY=
		</data>
		<key>assets/codemirror/config/codemirror-config-ss.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-st.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-stq.json</key>
		<data>
		3KmZ/eWoi0Eh/BSjonnZDrZzsKs=
		</data>
		<key>assets/codemirror/config/codemirror-config-su.json</key>
		<data>
		yfoRWHMBgUIXONUzYxgXIFWoafI=
		</data>
		<key>assets/codemirror/config/codemirror-config-sv.json</key>
		<data>
		rz3BEmoEVBBNFCCjwgK0jXBFHDk=
		</data>
		<key>assets/codemirror/config/codemirror-config-sw.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-szl.json</key>
		<data>
		rk8fjiWdwrcJFrsfy61yy5fu0NY=
		</data>
		<key>assets/codemirror/config/codemirror-config-szy.json</key>
		<data>
		uLdYqbmE887gxf3jd03eFxgbaRk=
		</data>
		<key>assets/codemirror/config/codemirror-config-ta.json</key>
		<data>
		KSK4xPJV93aqPvy5+jyeIfOpRNU=
		</data>
		<key>assets/codemirror/config/codemirror-config-tay.json</key>
		<data>
		uLdYqbmE887gxf3jd03eFxgbaRk=
		</data>
		<key>assets/codemirror/config/codemirror-config-tcy.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-te.json</key>
		<data>
		RXY4hEVPQCT7zwsfx9dmkJjN8/Y=
		</data>
		<key>assets/codemirror/config/codemirror-config-test.json</key>
		<data>
		LBCEAJcLB7M/cf8e9pdsU8WRAHQ=
		</data>
		<key>assets/codemirror/config/codemirror-config-tet.json</key>
		<data>
		2PcccUm1pv5yriJKKmiXCF5NzNg=
		</data>
		<key>assets/codemirror/config/codemirror-config-tg.json</key>
		<data>
		TqnFRMdWc2NTVXvKozWzGbM4PyA=
		</data>
		<key>assets/codemirror/config/codemirror-config-th.json</key>
		<data>
		EbyBLBfZHJOTziTEbDOSVFI7TFw=
		</data>
		<key>assets/codemirror/config/codemirror-config-ti.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-tk.json</key>
		<data>
		P0moHuonmrE5Jcbt4Ajv5Pnyyzo=
		</data>
		<key>assets/codemirror/config/codemirror-config-tl.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-tn.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-to.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-tpi.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-tr.json</key>
		<data>
		rBhUDwu0oY2GsI2HzQEIsdgQsCI=
		</data>
		<key>assets/codemirror/config/codemirror-config-trv.json</key>
		<data>
		uLdYqbmE887gxf3jd03eFxgbaRk=
		</data>
		<key>assets/codemirror/config/codemirror-config-ts.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-tt.json</key>
		<data>
		bBFDxzyMLQJwAiQeAhdeVg4fuOQ=
		</data>
		<key>assets/codemirror/config/codemirror-config-tum.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-tw.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ty.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-tyv.json</key>
		<data>
		w8aHJvWEYJffDco2zsF6RtyNUCw=
		</data>
		<key>assets/codemirror/config/codemirror-config-udm.json</key>
		<data>
		vWZsWTrRqZyJisfS2bx2XORmdWQ=
		</data>
		<key>assets/codemirror/config/codemirror-config-ug.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-uk.json</key>
		<data>
		ad5k7XewhRGvIXq4irq7vCfBZIE=
		</data>
		<key>assets/codemirror/config/codemirror-config-ur.json</key>
		<data>
		+KuHCm5e4t1kA3PceSUvxTuqvQk=
		</data>
		<key>assets/codemirror/config/codemirror-config-uz.json</key>
		<data>
		ZmRA9HoV2ujOx6LAoUmqDvkDWZs=
		</data>
		<key>assets/codemirror/config/codemirror-config-ve.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-vec.json</key>
		<data>
		qjdovHrTAuIsfS1kXUY11S8UTuU=
		</data>
		<key>assets/codemirror/config/codemirror-config-vep.json</key>
		<data>
		UnmhCVj0W13QbPb+T8rDaMkxeUU=
		</data>
		<key>assets/codemirror/config/codemirror-config-vi.json</key>
		<data>
		ihOzW4uAo1lIg27UrvuHnq7vxMQ=
		</data>
		<key>assets/codemirror/config/codemirror-config-vls.json</key>
		<data>
		j3cKztE56n0hsaSCOqJXjBuLBTY=
		</data>
		<key>assets/codemirror/config/codemirror-config-vo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-wa.json</key>
		<data>
		Vgmil/wplQmLPzqR17bwC/8D7cg=
		</data>
		<key>assets/codemirror/config/codemirror-config-war.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-wo.json</key>
		<data>
		E02mASuVHyQB+ddXXHSRH4BiotE=
		</data>
		<key>assets/codemirror/config/codemirror-config-wuu.json</key>
		<data>
		GqEd+AkVoXGD++gSCHua3dLxkB8=
		</data>
		<key>assets/codemirror/config/codemirror-config-xal.json</key>
		<data>
		tJfv5Xq0zEvVn5Hvxn72Kob5QnY=
		</data>
		<key>assets/codemirror/config/codemirror-config-xh.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-xmf.json</key>
		<data>
		E8Plevlp14uYskWyBxaeRddn3Tk=
		</data>
		<key>assets/codemirror/config/codemirror-config-yi.json</key>
		<data>
		aieJg4xtYz6OLXXKooY1h3rBvJA=
		</data>
		<key>assets/codemirror/config/codemirror-config-yo.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-yue.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-za.json</key>
		<data>
		GqEd+AkVoXGD++gSCHua3dLxkB8=
		</data>
		<key>assets/codemirror/config/codemirror-config-zea.json</key>
		<data>
		j3cKztE56n0hsaSCOqJXjBuLBTY=
		</data>
		<key>assets/codemirror/config/codemirror-config-zh-classical.json</key>
		<data>
		UPX2pWi+K1y9YUi3cIYYYDoP6mg=
		</data>
		<key>assets/codemirror/config/codemirror-config-zh-min-nan.json</key>
		<data>
		LSxzQOjoQsxRPinjoDk+pSrFv3U=
		</data>
		<key>assets/codemirror/config/codemirror-config-zh-yue.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/config/codemirror-config-zh.json</key>
		<data>
		zmQGGhnslUZ5PhG1gp4CwxG/sIY=
		</data>
		<key>assets/codemirror/config/codemirror-config-zu.json</key>
		<data>
		keftB7UJjhSbWYrMLnUVgwB6QMs=
		</data>
		<key>assets/codemirror/resources/addon/search/match-highlighter.js</key>
		<data>
		db7UyaGOcfDu66HXWPjT0sRkzf8=
		</data>
		<key>assets/codemirror/resources/addon/search/search.js</key>
		<data>
		1+N2k+iw6/ycioeLQ0tkLfhxMbM=
		</data>
		<key>assets/codemirror/resources/addon/search/searchcursor.js</key>
		<data>
		2lLTyDTCtNSNIkMj79o/BSrU2P0=
		</data>
		<key>assets/codemirror/resources/ext.CodeMirror.js</key>
		<data>
		xp7iuZ1u2hxm+nIUvti88YfiV64=
		</data>
		<key>assets/codemirror/resources/ext.CodeMirror.less</key>
		<data>
		fPt3mXz+Xy7IPzKUJAisBl5c+eo=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/AUTHORS</key>
		<data>
		wLt2jj7IOXBpbylzWNE749i/UA0=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/CHANGELOG.md</key>
		<data>
		O5AXgPIEPT2k2rMrVYHWHqXFKbI=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/CONTRIBUTING.md</key>
		<data>
		C7SA9G2NrUCRlmfs4lz9eTW4utE=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/LICENSE</key>
		<data>
		xDV1SZkf1wkNCstoXuFc+yohV1A=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/README.md</key>
		<data>
		I0p/cE+0PBgzR/w8gUdWFQxj1d4=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/lib/codemirror.css</key>
		<data>
		Ha94ns+dc5z+jfKVUX3UvYRt7Do=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/lib/codemirror.js</key>
		<data>
		Big5HmGmq19KvV/djdnHWOaw6Eg=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/mode/clike/clike.js</key>
		<data>
		xSYzNNi4hcWhB6U6Ro91DKupL9w=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/mode/css/css.js</key>
		<data>
		VcpTTW13lfgV/Wf3xSv//dZuq+0=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/mode/htmlmixed/htmlmixed.js</key>
		<data>
		PFZaQqDUDoixzBOfuyCN+A7wGbs=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/mode/javascript/javascript.js</key>
		<data>
		3V/dI6T1A7msqSE27zrFG1SoFMY=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/mode/php/php.js</key>
		<data>
		twWEOqBYF04ANt7T6232kNnDLmk=
		</data>
		<key>assets/codemirror/resources/lib/codemirror/mode/xml/xml.js</key>
		<data>
		exwQlxf+ySS9d/5QUaJIr/BBtkU=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/img/black4.png</key>
		<data>
		6LWcrPHr5/iiIQICD6re4BS3eoI=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/img/ext2.png</key>
		<data>
		WTBLMkNJZxwU74hBuReh6ISpmCU=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/img/ext4.png</key>
		<data>
		JjK65QSBQEaHxSyA4t+rYSKa6LI=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/img/link4.png</key>
		<data>
		AHhyJkxuEsrrAE8tTvf2ldMW1dg=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/img/template4.png</key>
		<data>
		O9DfX5K7WJr7XISYwGPy5Ln512s=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/img/template8.png</key>
		<data>
		UsnEPyGNoUJmR53NCz3KCK1a4vY=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/mediawiki.css</key>
		<data>
		7pdLT6GIJEHiIBoydKhZgtS59XA=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/mediawiki.js</key>
		<data>
		6XAvl2imycaWL4WzqVVqox+fmOc=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/tests/qunit/.eslintrc.json</key>
		<data>
		gtW8LR1D2sSdT43ZBWVctx1lTGQ=
		</data>
		<key>assets/codemirror/resources/mode/mediawiki/tests/qunit/CodeMirror.mediawiki.test.js</key>
		<data>
		+mXjHCzGj8kESvD40wHOScuZzag=
		</data>
		<key>assets/codemirror/resources/modules/ve-cm/.eslintrc.json</key>
		<data>
		ZxkL9lwGcwB4CU9nqrE9BTnuvzg=
		</data>
		<key>assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirror.init.js</key>
		<data>
		feMC3iFeDXEVOGU0aBLX/x2ZEtU=
		</data>
		<key>assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirror.init.less</key>
		<data>
		uRaEgOZsJxFiZ+dR85RWwwCXDIo=
		</data>
		<key>assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirrorAction.js</key>
		<data>
		ks/WRynT6cHETzA8Eo+tkk8if/A=
		</data>
		<key>assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirrorTool.js</key>
		<data>
		XD9S6O6Jlal8lEfxg7hu85kZnVA=
		</data>
		<key>assets/index.js</key>
		<data>
		bAZaK/cRnc9W/2W8DjjF+O6BJWg=
		</data>
		<key>assets/pcs-html-converter/base.css</key>
		<data>
		f+IJpKp6T+Bm918XFcaf3eXpM+w=
		</data>
		<key>assets/pcs-html-converter/build/PCSHTMLConverter.js</key>
		<data>
		8JqUvIK9vQ/QCTGTmiXBcq9gTd4=
		</data>
		<key>assets/pcs-html-converter/build/Polyfill.js</key>
		<data>
		7oR4M7SIJ6LE0qU1Jk/EAtyZcsE=
		</data>
		<key>assets/pcs-html-converter/index.html</key>
		<data>
		OD0Hit0EsuCs11KIKSsPsc7oQfY=
		</data>
		<key>assets/pcs-html-converter/pcs.css</key>
		<data>
		KP1Czsch/i8JSJUqrLZF7pWeLXU=
		</data>
		<key>assets/pcs-html-converter/pcs.js</key>
		<data>
		Cd3d/e/unrCen9u8nBKL0o76/dk=
		</data>
		<key>assets/significant-events-styles-base.css</key>
		<data>
		y33O0LA2r1CDjfbIx9TibXpsbX0=
		</data>
		<key>assets/significant-events-styles-black.css</key>
		<data>
		rRATxJXK7T1vaEgaDSIEoD5QoG0=
		</data>
		<key>assets/significant-events-styles-dark.css</key>
		<data>
		2CGLKgRqueoe739xYF6JfF/+XV0=
		</data>
		<key>assets/significant-events-styles-light.css</key>
		<data>
		Za1aK6VxDzHM87PMtwmN8ooSIpI=
		</data>
		<key>assets/significant-events-styles-sepia.css</key>
		<data>
		2ZoAvebOwLnUezirUF29mdhcVlo=
		</data>
		<key>assets/styleoverrides.css</key>
		<data>
		IchKVQWtv6Rf/RFdTj+kyAD6YvQ=
		</data>
		<key>ast.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KzwtKPqyYqRq3F7mJ+wlPrsPYtg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ast.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			reoI6cKlCz82wGoeiclfe8el2ls=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>av.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			psYVYd3a8fP+NC0UcfPdemgXH1k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>azb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ba.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Olpw7F/6UGlZY4ZOdSyhtbIbbSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ba.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bgn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bn.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iAfVYuY//PQvke+OHDVVOGgq1A8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rEGnkAUnJt6kW2c2HY1sdOuOdnM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>br.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			t8SEvn+hIANskJshPwp1ZMs1GOQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>br.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			iW6TIYGqi5XYcy59JwnurXoNAgA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mauNRGV+oi1iHQv0H9o45NarNIw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bs.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			YvGXfzJmd3h8hkvGq8q6B+J2F8A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B4NPx13/8jg5tu97pgk2e93UtdI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			g2H+x6irH412gsQeapcbdMgh1w4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ce.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7Bpe6ddVOignc2nmdpdlkoPi6QI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ce.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			9aaX97Gkd6r6nrgOeE/yy7dqKe0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ckb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+OXbdrD5erKkOD13XxGnniNwcZo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ckb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			Vx4HAHZyQeLzD/cJJG2Emo8+N4k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cnh.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YKWPs0dhQLPtiA+qXIdrEtSzKS4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			WFmKn74kzojjPNNssRsGtcT17nY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cy.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4mUupTBPTfjQFNFiaUQAtqkTZ58=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			ZE6tX4iQb4GyQQjc5Vc3DQt9fH0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LSJhRsfueY3InDljmcarRYHbZuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			6iObTBztpe1MSaWi/F9txq8knBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			V+zN3ki/CwoS5+G/mahqBVVX3EY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rbXURI9zqUBCSiHLn4XghkhaEMM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>diq.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oMUa4xrCUO06+AVJGyazkhB/Ka8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CQGx7L1yF72A/S/306aP5jTqit4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			9p6Nz6yNWwvo0wmrd0UtO9Xdwl8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3zbt3ioQMlm+kBqGgTCuWXu0VeE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eo.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NSflcyD9VWl8mgmEzl8i+4WKXUg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			suQ3PNTdK7H34l2EIfwQWCiSaME=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JhQHH/jwWewoW59WE/p2BAVYs88=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			mgo1f4BDN4aXOPUZsiyC6laGvgI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WjER5muF6VBdALPn3H4E9IXCUYU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eu.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			iSoGUVEmTv2GZMEu4OPH6dkjHbU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kUChSgM8c76e2SiRfP4hcbxWzVg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			L5z4OTJs5judmk81nYTZ5pWEfuw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aTm1hnMSjH6x16hUAULgAMqkQpw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			NOsD5EbXIXdnlXzO7lGIHOxSeVw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fil.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			psYVYd3a8fP+NC0UcfPdemgXH1k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fo.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			birWVLoe+a2KdrliU5Z1bE94AdA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8+1uvd4G+5XZDZXQ9+TpxTVLLv4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			YKiINf5l9TvTShj1Tf5Zj0xSlwI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ga.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bFmQrWDxOa31/32QUejXGgxUXgA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ga.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			LkF8p2zIIflXrVioeUGttUx64jU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>gl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pbS3Sx7HODsNR/ksCpnDerUabSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>gl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			7Eqt6W/dlrenJQHOpPIytzjE7Do=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>haw.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wVCO8wg258p9oPofZsRHZ2LtJFE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>haw.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			UVClAgKzIxHK04SCtZlv2brcLks=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>he.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1ECYIkESpppQ02SV41Jy8o2Zr0o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>he.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			LCHSI6tQ5eBFikKyXRHSiPgU8Tc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aAgC1Dh7Gq8duqWCKTECV2jdZfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			pEFzF8HVd6BhmhdrcrxckDJSEk0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iLr1Jg1uAarv/zYIX1ES0fEF53A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			NPqA0zW+OUUfRhPs1/86rnaAu5Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hrx.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hsb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TgzXPQTKAYlJcU3o/ZYlq8ricwY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hsb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			PfuxQgGDb3Zd3wIlgAw25KG4/0g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mVrD0Oz3CuHclVRcu38YjH4cWtA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			3iXZ729jFqtFwb4vp/TUDfi7kyw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hy.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QrvapCPKb8wHY8F9sGUIu1u9qPs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			3bjMnHOHedWg5RHshpY2yBhIPjo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Fj7cr40JQKBUgF7OBURjgCCayVc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			NKGJvSXnTS0ogYnJZ9w55Q0J0S8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>is.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pXg7JTgr9X4poGUFypbcHubaB2I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>is.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			GCcTZuy+f94gs0/6UWGR87qMGOw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mhmD0qdJ5TugJS6ay+xnL5jPOLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			awb9jsdWCEET2QlfRUw6IoBf6Bw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gMw/gaPR/PvLcyhWEZcnfbAaKcE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			BiH+Rkd1lGv2rkFfxbeV2qjDmHs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>jv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qHoDF4m2K/lqvJa56vvzk+2tXoE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>jv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			mOYNb555Ha8zj85F0paKMTYI0/4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ka.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AyiHHC15Vj7/cpEWe49dZvLgyKU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ka.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kab.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SIN5lyM6mcTUWREVZ8wb7e48gAs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kab.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			lZuu2bZ4nloQFJUi4MzjdHAd8bk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kcg.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+2O2KJf5Oha/66rL7f5ZPb1yGIo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kcg.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			hjHomhKxQ+uMbsQ//1ra19mBeew=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>km.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			o9WocPo25v4RBqxsrvPE72gRUv0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>km.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			RziQoMPOf5AwnmbUk42GlZfi7SY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kn.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3rtptLYd4xxFsOXJY2yL/ObTZGw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mohIHmFrj2ceKadP1Svgl9i04gk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			HbFmEQKxHwZPZtMaeJsThrR1n28=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>krc.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1S15vhdklQpd1K5Cx47UDy74sjs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>krc.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ksh.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ETQdsqOuXj8AZ53MWl6b91XQ0fk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ksh.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			U10TJYf+MzQ6f3seIsVb6uMKb5w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			z/njCjmFMiyk8d3WQm4BxVn0nd8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lt.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cGRKvh/OVXL8eyWj6eV2NENygOs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lt.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			DYUhyplK6gwGWSoBt6rhgLr+6Ag=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SuTmhKaEMq9fajCK2D4p6lwj+Fk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			RRjL8yy5e92igpykeNYPlGVbeKE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mai.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8wGp5azWnTKS/ckfNwswcOxyUAs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mai.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7hKOe6T/Rw/gcZqjAn/5nyf9qMc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			VnP28dpTdUH99mzW/OIrcob8zRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ml.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9SfEtQrqTMZzIS+t5o0uIvt9kRs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ml.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			7gqZKfLiTedZmDnlzDyMBTrrUOE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			V6OySj6fe+y9VY7yahvBqAGs6Kc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			+0dtON4ZHhZx3PeYhBAA3chWeww=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rQ+7Fm7ktwauhIe5r22TWoXjSHI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ms.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			opBofILjyKpCZ5VpVXKsG2AIVLE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>my.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eJxlSnYUD8BWYV75jxib78v75XA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>my.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			MwQuf/WeuhOVXXzlcNsa/OwnHrs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/31dhq3mgo77S5p3U87KpIRdvXk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			BwUbNyXRxR/xgucgd4AwxA9GEdc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ne.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ooZXlHBuBkQ1uRxluMDTtDUtImc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ne.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			SZ5yZtn4A7t1Y3zMC23OxZ7Nu7o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SVVBxbWbB6QbmsEDXh1VRxeW5Uc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			/CDjdR0oxsFVABulfyKndIyfYvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nqo.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FyWj29nNOYe/J3IhjtwtjI6cwOU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nqo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			1AgohmptMF7hspjq4TUN/7MDPJA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>oc.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kOIxj2CkgvljbwUpK+JmLFjCUlk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>oc.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			Tm2fTW9gen4nALsSlki4C/mEZvc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>om.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ljQ70J6AY7MoE9rDfC3nko1/e1A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>om.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>or.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rcu+U52j8xMAbs/OH/jWbDc/Okw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>or.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pa.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			b6FVDnvemh6iP+D6ZyIHQIRXwo8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5zhMLGkgS8GTvr/YZKFIWqkNEJ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			pGGfNg6I4qtj6PYGxxpNXkneccI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ps.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			u2gyHwCcZMkUpfutNHw6bB4gzHw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ps.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			Yv+BpKya0Hh4jOT6V2FsT94Y4EM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QBut+152m6nEvFM4q/yB2+KaxxE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			onhGrYGQ5yyoASHhpQQ2OgVDsNc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pUkXHYIeiTFa9ScCI6T2ggtSbR0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			gxFWu49e4dN6scMhUejVJHOiiLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BJ7FY8cffUK+KGA9ZnyQ3Aq0dTE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			StEaNT3ligHQOaRySd9b2PrUbc0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mFZ1LYGm4dCV77pVzNxCSs38HJg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			b3iGKZEtKL4GII27hSL3pJA063E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sa.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SB8NC3p1dzct8XzB1Rj1fxNtNjw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sah.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2fT7ooopSdlSfgGzwBfN7TUT2fc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sah.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			iE657j3rYf0OnNgnaWheazWIDZY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sco.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Zdu2OUtyseaURwmGUtTde7OUBrM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sco.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sd.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x4LcbTgZ4amlNfVtTqWLxRM+jRE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sd.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			g7uhG/TwBvDNQR181gu4pBsgoZg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NIcbazB2ExrNCpMh1sQEwiMvdIo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			lUqiYjog0qrHGeSjUTSJvqauh3Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lXXFErrT6xdu199MOBghF0JyJmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			j/tk6MF6T3aAmRTCrmH0daN4uHQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sq.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			J1JQhxwfdufp0Zzb94vumoIRzn0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sq.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			3CGVGRmrmq7hpbYWJlY3FuCn0uQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sr-EC.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KCuJpo3QCGW0wcEt7Ay30teZcLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sr-EC.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			UJ0R4bxjo3ig2odHFPLyspA1w5M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>su.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			L2b573b2WWMANLsB5Kqi2JBlzY0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>su.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			t9sk9vg4+XWUUOh+EaVkbtb3UDQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			r+d8Q4Pb6QtFiBJpc8mTHXp23GI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			saoR5rOqRA5LYNfvR4+0pNCD/PU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sw.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rqiO0Wp7rNr8tGXC54ZnaAXR/Hs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sw.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ta.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aZ+xw372aACMaBvlTzoCgUcXgQc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ta.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			hXjzpfoitA9lgediy+JBTxs3AHQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcy.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gncNdKG9pvtzcMDEbsHghp034lU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			rqeUXcjQNUbUdzI8PoXM3W/er8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>te.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FeRPl6Na6Rz4NIqeaAGEwZCz4cU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>te.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			9YYfG/v8qS8QpLolQMuSH4uY9kM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tg-cyrl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+5EnsJL8CJMJ97egblTYBMdJM8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tg.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			psYVYd3a8fP+NC0UcfPdemgXH1k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ULCxaXYKFvSn1anlx0edBc8py4k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			vmpNwKyAs3fJB1BPucc+Gw5YUBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XEvSKMmGBRE3eRVO3jeWN44lYtI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+W2hLwRHQgbZ+7LXdarH0phG8vo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			dHoPCW5NEnh43SvelZwNzRHkvqI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rn0VUzgVZPaM5jVPLxFgBiuVht0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			I5aIg9qrHOsl55RbdFCQELSPGKs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ur.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1aC+fqC6Hv3jBBTL3zkdPJwayY0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ur.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			i2yIE9gcBmMxS2YgZkzjnt9C3EA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uz.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XlVwrDaqeLsZgDoeXYNuRe52egw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uz.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			5UKZqGDt2rHvaOF02saD4/ybe28=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vec.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RUuJGq2byrqztVpKUwHL1co6ncE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vec.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			nGXyMxmK7cifDcrfH9xoEU2fPc4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ogrjRtLQXosb+LPVl9nHoJlcrb8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			MH/NpT0QsyyKcJFetq1IuJ+0c6c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>wikipedia-language-variants.json</key>
		<data>
		cSEZZ4BOU0GnJQdeBMvE/X8sjuQ=
		</data>
		<key>wikipedia-languages.json</key>
		<data>
		wliupIajUaWgDYYnBpzsIAgp2I0=
		</data>
		<key>wikipedia-namespaces/aa.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/ab.json</key>
		<data>
		yejb+AEAeOxS+P2taMoF3uzA2Kg=
		</data>
		<key>wikipedia-namespaces/ace.json</key>
		<data>
		yB1uowiysDgBnmXigEsd6bOLJPY=
		</data>
		<key>wikipedia-namespaces/ady.json</key>
		<data>
		Fdxlv3JSsw2Jf/sphpeDJ8xGf1k=
		</data>
		<key>wikipedia-namespaces/af.json</key>
		<data>
		xMbUBJNGdFQLEVHEGEoiTAcWxhw=
		</data>
		<key>wikipedia-namespaces/ak.json</key>
		<data>
		OH6L2/YX68oZJJLItE4BK0x7m9Q=
		</data>
		<key>wikipedia-namespaces/als.json</key>
		<data>
		xz174qz7xiOzMmZaHt2/WqjgGvU=
		</data>
		<key>wikipedia-namespaces/alt.json</key>
		<data>
		7vkbzayFHoK513afOVOyMJ0+k+I=
		</data>
		<key>wikipedia-namespaces/am.json</key>
		<data>
		xc1QAPrjfCxm/zpRp/jvuzbnsps=
		</data>
		<key>wikipedia-namespaces/an.json</key>
		<data>
		6dMhLNhskszKru3SdnJVPy6pyrI=
		</data>
		<key>wikipedia-namespaces/ang.json</key>
		<data>
		9j2SchCIpTVHVS3TvFtq2JCVBEk=
		</data>
		<key>wikipedia-namespaces/ar.json</key>
		<data>
		1V6Z0pBAN1/wy8GyCLp/3mLsuug=
		</data>
		<key>wikipedia-namespaces/arc.json</key>
		<data>
		xj3jIpxVtqNqOIDv0KccVlQi2Tg=
		</data>
		<key>wikipedia-namespaces/ary.json</key>
		<data>
		V0kn2M3jsNyqZhuuOQGgTuFD9/s=
		</data>
		<key>wikipedia-namespaces/arz.json</key>
		<data>
		83guhWMWz6sPOJTAJTpeJ3XFhNo=
		</data>
		<key>wikipedia-namespaces/as.json</key>
		<data>
		D1hLFEqO1t2d2SZXFkOf+uUFwYQ=
		</data>
		<key>wikipedia-namespaces/ast.json</key>
		<data>
		Wzg9ZQSvOis7RthNzUp2kkgMFVY=
		</data>
		<key>wikipedia-namespaces/atj.json</key>
		<data>
		z0qQHYZXDcbHDkU9q2U0PAVgCq8=
		</data>
		<key>wikipedia-namespaces/av.json</key>
		<data>
		EwkvORfCF5kzW/z5YwwUSMsrDls=
		</data>
		<key>wikipedia-namespaces/avk.json</key>
		<data>
		CIAocCTN5wpqJPE/pDAGDRZhDDc=
		</data>
		<key>wikipedia-namespaces/awa.json</key>
		<data>
		58Wtggb8i+fAppqwcR8zdaCCM78=
		</data>
		<key>wikipedia-namespaces/ay.json</key>
		<data>
		b2xLc4jCU1d92CUlvRUaL2hgkVk=
		</data>
		<key>wikipedia-namespaces/az.json</key>
		<data>
		R9jnHY86a6e1UpdzvSAp6Hum0XE=
		</data>
		<key>wikipedia-namespaces/azb.json</key>
		<data>
		62TF1zncBECT55c54I+a8XgoxQM=
		</data>
		<key>wikipedia-namespaces/ba.json</key>
		<data>
		KRm3gVz1uzGMVdBNdVZ+tdzD1Dc=
		</data>
		<key>wikipedia-namespaces/ban.json</key>
		<data>
		QFhDz3TO1w14FR7zB+CIvVpIfXU=
		</data>
		<key>wikipedia-namespaces/bar.json</key>
		<data>
		aaXyU2xeg8RenCBbF0UzWZuaufA=
		</data>
		<key>wikipedia-namespaces/bat-smg.json</key>
		<data>
		R/3gVvUjc/BUkZ7Fa5pmERTWcIQ=
		</data>
		<key>wikipedia-namespaces/bcl.json</key>
		<data>
		9qaqIx6KqYJDFctH08GYR3kKOQs=
		</data>
		<key>wikipedia-namespaces/be-tarask.json</key>
		<data>
		F9s53s3k6ZitIxLzIUL5qRpxuGE=
		</data>
		<key>wikipedia-namespaces/be-x-old.json</key>
		<data>
		F9s53s3k6ZitIxLzIUL5qRpxuGE=
		</data>
		<key>wikipedia-namespaces/be.json</key>
		<data>
		1oPZCYGQ/glNr/JgkDpBwUKL5DM=
		</data>
		<key>wikipedia-namespaces/bg.json</key>
		<data>
		jLDeQm6In1r0Y5aY3LHVInuUkcM=
		</data>
		<key>wikipedia-namespaces/bh.json</key>
		<data>
		oLq+JeZoONcrfk8PSyFsa6CmJFA=
		</data>
		<key>wikipedia-namespaces/bi.json</key>
		<data>
		gjtvtmFQdEgF+GyxNC24oZHhoJQ=
		</data>
		<key>wikipedia-namespaces/bjn.json</key>
		<data>
		ZIhY53Mgi9YZ7UarYFkWiGUtmHE=
		</data>
		<key>wikipedia-namespaces/bm.json</key>
		<data>
		zb2YDmigWJC7eZtO/ejbiWwyTIk=
		</data>
		<key>wikipedia-namespaces/bn.json</key>
		<data>
		aqmImTtMvE282xvriGAp8B95YPE=
		</data>
		<key>wikipedia-namespaces/bo.json</key>
		<data>
		OSKH8vkaWS6jOuml69Aw8vdODss=
		</data>
		<key>wikipedia-namespaces/bpy.json</key>
		<data>
		x3T7pRsZHMGF/AcuP+oWNu6b+q0=
		</data>
		<key>wikipedia-namespaces/br.json</key>
		<data>
		QP8ta0lzYs6xJeN4PmseUAjuLlQ=
		</data>
		<key>wikipedia-namespaces/bs.json</key>
		<data>
		XR86JT1u1x4py9wU19831folUoQ=
		</data>
		<key>wikipedia-namespaces/bug.json</key>
		<data>
		mdQA1DIMOpPthy+QqVZZua1s988=
		</data>
		<key>wikipedia-namespaces/bxr.json</key>
		<data>
		EaZ51Wh+YoAv/ak4mVG2+8kzylU=
		</data>
		<key>wikipedia-namespaces/ca.json</key>
		<data>
		xAJfH2wDfuPkeacdogIoQahK/5s=
		</data>
		<key>wikipedia-namespaces/cbk-zam.json</key>
		<data>
		GytT2pSO2uvtMU3D5XLSdDeDbR4=
		</data>
		<key>wikipedia-namespaces/cdo.json</key>
		<data>
		1Xcs+gZBng8NooDgoj/dXADG8y4=
		</data>
		<key>wikipedia-namespaces/ce.json</key>
		<data>
		M86S4zxnPcx0IPZUJKTmbNJM2AA=
		</data>
		<key>wikipedia-namespaces/ceb.json</key>
		<data>
		w6i4jJkt+FM6B4VgR3Qrkpo0F7c=
		</data>
		<key>wikipedia-namespaces/ch.json</key>
		<data>
		qP0UXT3xTPcu1bCyPgJPCUChIWs=
		</data>
		<key>wikipedia-namespaces/cho.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/chr.json</key>
		<data>
		dut1YjBvxRzJdkua2qU6sYiz3o0=
		</data>
		<key>wikipedia-namespaces/chy.json</key>
		<data>
		bG98RiHH2lN/RzqA+AffYXdxrdQ=
		</data>
		<key>wikipedia-namespaces/ckb.json</key>
		<data>
		HV6eoMVIyaA0hsLHLjMVOlgaOKY=
		</data>
		<key>wikipedia-namespaces/co.json</key>
		<data>
		1Z6FYMc33Zyf42vkIYf6GVP7Jns=
		</data>
		<key>wikipedia-namespaces/cr.json</key>
		<data>
		uNZ2qGvqi+i2UbO4iCkufTiFtzw=
		</data>
		<key>wikipedia-namespaces/crh.json</key>
		<data>
		C5E/S553D3Acj/4J3mSHCUf8lmg=
		</data>
		<key>wikipedia-namespaces/cs.json</key>
		<data>
		FT2SK2ghfSd4Awu01MD7kYEqml0=
		</data>
		<key>wikipedia-namespaces/csb.json</key>
		<data>
		a7DgFSVRotPLylQASYBnuHiHNY4=
		</data>
		<key>wikipedia-namespaces/cu.json</key>
		<data>
		tC83IRC4PR6KGu2GZQxeV3Aw5H8=
		</data>
		<key>wikipedia-namespaces/cv.json</key>
		<data>
		9yImRF6u9OkroIhieliBPWOXefA=
		</data>
		<key>wikipedia-namespaces/cy.json</key>
		<data>
		AW5+MwM41M9SS9ktdKTsClEDFyo=
		</data>
		<key>wikipedia-namespaces/da.json</key>
		<data>
		NE0ZKVaxJVwtSHYtXoj3gozmy8E=
		</data>
		<key>wikipedia-namespaces/de.json</key>
		<data>
		ji5UVwH1bqTN2Dwsp0geiTT7YC0=
		</data>
		<key>wikipedia-namespaces/din.json</key>
		<data>
		lsnvSWsIv9dYKn4zTSY51dr1tfQ=
		</data>
		<key>wikipedia-namespaces/diq.json</key>
		<data>
		ossi6CdRHQhTWrO/PJVJpQ5HGfM=
		</data>
		<key>wikipedia-namespaces/dsb.json</key>
		<data>
		B6kuIa8AbSigb3ChMThvKSvcfJ8=
		</data>
		<key>wikipedia-namespaces/dty.json</key>
		<data>
		zJpghfLJO327vnnb4EMi0hCb86E=
		</data>
		<key>wikipedia-namespaces/dv.json</key>
		<data>
		L6VvXwae5oxmTEz6sDklgFI6+18=
		</data>
		<key>wikipedia-namespaces/dz.json</key>
		<data>
		P88ZEDgEIASeZ8q6lMcHprahrEU=
		</data>
		<key>wikipedia-namespaces/ee.json</key>
		<data>
		kaciPDxqWMajCLKNEn1mNiDuX+8=
		</data>
		<key>wikipedia-namespaces/el.json</key>
		<data>
		shnPTJ3wmNivMh3wUGR7hqT4EBQ=
		</data>
		<key>wikipedia-namespaces/eml.json</key>
		<data>
		4K/4WtgcOpDCyryk1k2whSjiJIQ=
		</data>
		<key>wikipedia-namespaces/en.json</key>
		<data>
		veMs7tDzBzmHaGbwgWEuQJoQoPw=
		</data>
		<key>wikipedia-namespaces/eo.json</key>
		<data>
		Qf63fDfMLHIWVJZao21+sKsPrXY=
		</data>
		<key>wikipedia-namespaces/es.json</key>
		<data>
		BVb41r1MEYKuD8hMqhcN7JedKik=
		</data>
		<key>wikipedia-namespaces/et.json</key>
		<data>
		xWsRGCSHELQDndDl5hQT/8COCSY=
		</data>
		<key>wikipedia-namespaces/eu.json</key>
		<data>
		RtXYGXcIfGzT5PGAzahgbEuZJWw=
		</data>
		<key>wikipedia-namespaces/ext.json</key>
		<data>
		7vbC9TvhFdykw2urVHxtIBu17OA=
		</data>
		<key>wikipedia-namespaces/fa.json</key>
		<data>
		WDSBRW6xu5cgJIGS1vPlqbqaW5o=
		</data>
		<key>wikipedia-namespaces/ff.json</key>
		<data>
		YkBXmV/LTquiyyJs8HV7men+TNM=
		</data>
		<key>wikipedia-namespaces/fi.json</key>
		<data>
		wKuf9jm+lOlVfnTMP16ZeGz6xHM=
		</data>
		<key>wikipedia-namespaces/fiu-vro.json</key>
		<data>
		Zs8VsyXBAyrE2Nnx+kERb72emzk=
		</data>
		<key>wikipedia-namespaces/fj.json</key>
		<data>
		GG97wXs5NPIhe+G10CF4dap5yiI=
		</data>
		<key>wikipedia-namespaces/fo.json</key>
		<data>
		LVFrMmMUXk5KnYrTPI+rG5eF2fw=
		</data>
		<key>wikipedia-namespaces/fr.json</key>
		<data>
		r0967k4Mbb+ikc4X+KMWz17/tRA=
		</data>
		<key>wikipedia-namespaces/frp.json</key>
		<data>
		pGww0JRV0RtGmD3ktisu4n3e9m4=
		</data>
		<key>wikipedia-namespaces/frr.json</key>
		<data>
		/0Kj75r1UIdB3Cwxw3RDMU0CNRY=
		</data>
		<key>wikipedia-namespaces/fur.json</key>
		<data>
		NHXC+/ZY2KfU0G/LEVkuRVrlxX8=
		</data>
		<key>wikipedia-namespaces/fy.json</key>
		<data>
		HxLaW/Mc1doisrOVxa736NNzrh0=
		</data>
		<key>wikipedia-namespaces/ga.json</key>
		<data>
		BBTMCEXVE49E1PDzrI5ue86VCF4=
		</data>
		<key>wikipedia-namespaces/gag.json</key>
		<data>
		om25uA420qA57sfQ1FFRmuhpt5s=
		</data>
		<key>wikipedia-namespaces/gan.json</key>
		<data>
		ENJY6Wp38NmvT4xiUKNNwmkK+ls=
		</data>
		<key>wikipedia-namespaces/gcr.json</key>
		<data>
		pPFtYq8gdCiwwOf6/kJd+tayOmo=
		</data>
		<key>wikipedia-namespaces/gd.json</key>
		<data>
		xEjSFJ7ee2rr5R7+z+5iOc9RVWo=
		</data>
		<key>wikipedia-namespaces/gl.json</key>
		<data>
		/TQ0jMl1VC5r9Or2T9iKNzALmmk=
		</data>
		<key>wikipedia-namespaces/glk.json</key>
		<data>
		qAnqjVQpVWatKLdN0uwlZGSntRU=
		</data>
		<key>wikipedia-namespaces/gn.json</key>
		<data>
		J28cmXngzLhqHvhPiCMtin6sG8M=
		</data>
		<key>wikipedia-namespaces/gom.json</key>
		<data>
		+DMPi1yUZwM0FN1D3lDxbtR/uiA=
		</data>
		<key>wikipedia-namespaces/gor.json</key>
		<data>
		RuzP/UmZfF2SjgOESTnkqpYG82Y=
		</data>
		<key>wikipedia-namespaces/got.json</key>
		<data>
		JM175XvFab2NZzxdJA4yJFEZK7w=
		</data>
		<key>wikipedia-namespaces/gu.json</key>
		<data>
		1fBUmuBsbIy2BYyocug5ETcGp6M=
		</data>
		<key>wikipedia-namespaces/gv.json</key>
		<data>
		83NjE0d2Ff5Iwid1HSWMw+kzv7w=
		</data>
		<key>wikipedia-namespaces/ha.json</key>
		<data>
		Es8OnxaK64AR3E3hn1UsvyCGVvo=
		</data>
		<key>wikipedia-namespaces/hak.json</key>
		<data>
		9XSi8rxwJsHKn2D9IWApaiznl3A=
		</data>
		<key>wikipedia-namespaces/haw.json</key>
		<data>
		T+6YHFgz4cbOoCQUYzJWACBLYwE=
		</data>
		<key>wikipedia-namespaces/he.json</key>
		<data>
		HCmnO2rTAB9NXYyVZNuuw4F7xiM=
		</data>
		<key>wikipedia-namespaces/hi.json</key>
		<data>
		ZL/4g1KwIEMxqlaX94vhkRy21LY=
		</data>
		<key>wikipedia-namespaces/hif.json</key>
		<data>
		bmwbDeaCBpY4O2tsrEUwgznI+94=
		</data>
		<key>wikipedia-namespaces/ho.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/hr.json</key>
		<data>
		Xw3UPH615TA7TevNzfhUIYJ/q0w=
		</data>
		<key>wikipedia-namespaces/hsb.json</key>
		<data>
		hcHL8Jb9JvxrWUDPZjPFk+iFgU0=
		</data>
		<key>wikipedia-namespaces/ht.json</key>
		<data>
		0UwN9A3XI9FXTrmG70tWRex30Vc=
		</data>
		<key>wikipedia-namespaces/hu.json</key>
		<data>
		7oOAHjzFPLJDVMoa1qQDdkZcx8s=
		</data>
		<key>wikipedia-namespaces/hy.json</key>
		<data>
		FTL+R8OzOXWji1s9f0/I3O1MG68=
		</data>
		<key>wikipedia-namespaces/hyw.json</key>
		<data>
		ovcVrZHxHjMCfKA29c35Tu/6n2U=
		</data>
		<key>wikipedia-namespaces/hz.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/ia.json</key>
		<data>
		j0s18b+P9mPs6u5rGou05l2xXsU=
		</data>
		<key>wikipedia-namespaces/id.json</key>
		<data>
		5SfizABTPrnPsyH6lksNv/Gowa4=
		</data>
		<key>wikipedia-namespaces/ie.json</key>
		<data>
		PkMKf60f3r5xcoaMoi2rlqAt1uQ=
		</data>
		<key>wikipedia-namespaces/ig.json</key>
		<data>
		Rh39E0VDCmMZot6L/y1tdZ/TXJY=
		</data>
		<key>wikipedia-namespaces/ii.json</key>
		<data>
		LyW+GTczPgI0+SEII8OX35AgbOA=
		</data>
		<key>wikipedia-namespaces/ik.json</key>
		<data>
		vJFj2QInk1Iy3uxhPszYlRLjuXg=
		</data>
		<key>wikipedia-namespaces/ilo.json</key>
		<data>
		E31gn6Lib95pHqIVUdd2hcuNtvI=
		</data>
		<key>wikipedia-namespaces/inh.json</key>
		<data>
		ZWLiR2mckJJD49NEkRXShmHEczA=
		</data>
		<key>wikipedia-namespaces/io.json</key>
		<data>
		Z0Wf5+kdKNTWHuvBsPzZ7XV7sP0=
		</data>
		<key>wikipedia-namespaces/is.json</key>
		<data>
		2w+FWskbhMtSO7B8dCavVmviDj8=
		</data>
		<key>wikipedia-namespaces/it.json</key>
		<data>
		r3osiysZsYFN8ehf/qnOLirxVqU=
		</data>
		<key>wikipedia-namespaces/iu.json</key>
		<data>
		B/V03DiOga7oN72MrWaEzqMQYuo=
		</data>
		<key>wikipedia-namespaces/ja.json</key>
		<data>
		iyRSsm02C5gQWRBsHA8b2UoAFFw=
		</data>
		<key>wikipedia-namespaces/jam.json</key>
		<data>
		PBEf/iFEzjFtovurpf/y8nRH+zo=
		</data>
		<key>wikipedia-namespaces/jbo.json</key>
		<data>
		fyhglXFVtG1IUgBRH8qGl0S9q6k=
		</data>
		<key>wikipedia-namespaces/jv.json</key>
		<data>
		8ocWdpjG+gWKkpxxn5k2r2IycSA=
		</data>
		<key>wikipedia-namespaces/ka.json</key>
		<data>
		gBmzCS+WiIuXy/jYE7WUINKb4gE=
		</data>
		<key>wikipedia-namespaces/kaa.json</key>
		<data>
		wthV/l6xXP6XRwHpFXbjxtWxvZ8=
		</data>
		<key>wikipedia-namespaces/kab.json</key>
		<data>
		iloAfmN/xNquIdsnYecq9G1r158=
		</data>
		<key>wikipedia-namespaces/kbd.json</key>
		<data>
		Q5DuurSaIdCRK+tQkLAbe00pUoI=
		</data>
		<key>wikipedia-namespaces/kbp.json</key>
		<data>
		TOdzfxeZh3qSoENnkctNyJ1SpCY=
		</data>
		<key>wikipedia-namespaces/kg.json</key>
		<data>
		eBdyahPq5/4HI70gh1KjaqeDujY=
		</data>
		<key>wikipedia-namespaces/ki.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/kj.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/kk.json</key>
		<data>
		dYF8wRc6qz26G65waY8rtL+/7cM=
		</data>
		<key>wikipedia-namespaces/kl.json</key>
		<data>
		6o8L96Z+B+yN7IRN4C7S1ROAiOU=
		</data>
		<key>wikipedia-namespaces/km.json</key>
		<data>
		Jzww2r6KbrF4wLLZQtvbyPMKsFA=
		</data>
		<key>wikipedia-namespaces/kn.json</key>
		<data>
		cM9avpzXvMrpxWiNUvUo0h+oLcc=
		</data>
		<key>wikipedia-namespaces/ko.json</key>
		<data>
		L9/GKr8BdGuS/CNVsFpWWyHJ1Os=
		</data>
		<key>wikipedia-namespaces/koi.json</key>
		<data>
		wYWvsZadz2qNiPTsXfiUtC6/78M=
		</data>
		<key>wikipedia-namespaces/kr.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/krc.json</key>
		<data>
		KkY+TPKOX9vrfA3iQ/MD+SjMRR4=
		</data>
		<key>wikipedia-namespaces/ks.json</key>
		<data>
		B9LgznW7rlhLUUt+OlVyeM82+tY=
		</data>
		<key>wikipedia-namespaces/ksh.json</key>
		<data>
		7+hita0TldD8uMhecNwhW7z95ic=
		</data>
		<key>wikipedia-namespaces/ku.json</key>
		<data>
		RNHufqv3HFTkqLnIR2VveSEo9wo=
		</data>
		<key>wikipedia-namespaces/kv.json</key>
		<data>
		p8vZeD7U52V+gcQI7MrSIOITLIU=
		</data>
		<key>wikipedia-namespaces/kw.json</key>
		<data>
		B/kooCjnUEDeON1f9Qd8MSN29yg=
		</data>
		<key>wikipedia-namespaces/ky.json</key>
		<data>
		/QBWPd7cYtWT4USM5vlkqzimonU=
		</data>
		<key>wikipedia-namespaces/la.json</key>
		<data>
		/XebkRCBDlZGJ9iyRwyUW3RUupA=
		</data>
		<key>wikipedia-namespaces/lad.json</key>
		<data>
		cTIx9Y7x1DlenBQFv9sRI7u+Lrw=
		</data>
		<key>wikipedia-namespaces/lb.json</key>
		<data>
		HERzAFQCFIxlc5L2N0ghIWz9u08=
		</data>
		<key>wikipedia-namespaces/lbe.json</key>
		<data>
		Dd0fjo/rsBymit2NmhozTQbSy3w=
		</data>
		<key>wikipedia-namespaces/lez.json</key>
		<data>
		z45Y94WsEPl23N6CYAa127dKgNE=
		</data>
		<key>wikipedia-namespaces/lfn.json</key>
		<data>
		BC/LUnSCFh8MnkYgQvXSQh2GO2U=
		</data>
		<key>wikipedia-namespaces/lg.json</key>
		<data>
		TDdd3YbEwWiohrAxkZ2X+x+BP+s=
		</data>
		<key>wikipedia-namespaces/li.json</key>
		<data>
		XVogR5BcsWFlKH33QylUB5zxMg8=
		</data>
		<key>wikipedia-namespaces/lij.json</key>
		<data>
		COw+zEKtqSrfgEp/SXGVFweWP3g=
		</data>
		<key>wikipedia-namespaces/lld.json</key>
		<data>
		H4iZz/qMAOC2n32EEmeDmQ0nOBE=
		</data>
		<key>wikipedia-namespaces/lmo.json</key>
		<data>
		espLDO+WPYuKEv06xTomsWEVeP0=
		</data>
		<key>wikipedia-namespaces/ln.json</key>
		<data>
		jekq5lYo6ysfCZnJ144Ky/AIU6c=
		</data>
		<key>wikipedia-namespaces/lo.json</key>
		<data>
		cZBM9Ux8W1cA2qIxGwaEvkBVBxI=
		</data>
		<key>wikipedia-namespaces/lrc.json</key>
		<data>
		ly2eHns5eVkgp/hrxBPOK3B0oAo=
		</data>
		<key>wikipedia-namespaces/lt.json</key>
		<data>
		uUYc+8EHi1xh3+I3gHYAXspOEZQ=
		</data>
		<key>wikipedia-namespaces/ltg.json</key>
		<data>
		mAejBonY8QHoyHZCQEq2MWDT51k=
		</data>
		<key>wikipedia-namespaces/lv.json</key>
		<data>
		tYgh85Mbt9+IhVyiTtmVI4mMOpw=
		</data>
		<key>wikipedia-namespaces/mai.json</key>
		<data>
		j+xOwvUwf4hy4pX4/jwIPjZSnq0=
		</data>
		<key>wikipedia-namespaces/map-bms.json</key>
		<data>
		j5vmAwTPPXkuq1RG3N1ZuQYYzHM=
		</data>
		<key>wikipedia-namespaces/mdf.json</key>
		<data>
		2ENeSVpXrW/Lu9SOGm0TVvSx3ck=
		</data>
		<key>wikipedia-namespaces/mg.json</key>
		<data>
		g8yOny2jHu7r3y3Bl/8BiGj8BPs=
		</data>
		<key>wikipedia-namespaces/mh.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/mhr.json</key>
		<data>
		+ugfHWDRVYXpPjOYr5thuTNtgdo=
		</data>
		<key>wikipedia-namespaces/mi.json</key>
		<data>
		iaRpgCjw1By3Yvq/z066q01x7Uk=
		</data>
		<key>wikipedia-namespaces/min.json</key>
		<data>
		6qJdS05U78WTRkisPudO48NTdow=
		</data>
		<key>wikipedia-namespaces/mk.json</key>
		<data>
		O3eIKeyp4tP3ANviGGX1nWx/0R4=
		</data>
		<key>wikipedia-namespaces/ml.json</key>
		<data>
		k6iVfjI4nZohdAxi2VkGzKUNo4k=
		</data>
		<key>wikipedia-namespaces/mn.json</key>
		<data>
		dekdKU6JPkTQsT9Uqa+xI+sAIpo=
		</data>
		<key>wikipedia-namespaces/mnw.json</key>
		<data>
		ooa/KIpZkK33bQB75wKiAP2LbMA=
		</data>
		<key>wikipedia-namespaces/mr.json</key>
		<data>
		IuwB6eCHJSdT/o+Tn3GifmmgFOc=
		</data>
		<key>wikipedia-namespaces/mrj.json</key>
		<data>
		jnp1MRDPwBos4aUaHVkI20+WZv0=
		</data>
		<key>wikipedia-namespaces/ms.json</key>
		<data>
		mMTAVxgaK2gBJqCjcXAlufZ1SB8=
		</data>
		<key>wikipedia-namespaces/mt.json</key>
		<data>
		OjguUol1Azn9hLeXnLJlFSjdNI4=
		</data>
		<key>wikipedia-namespaces/mus.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/mwl.json</key>
		<data>
		qp/k6fcE7m8nHcdnMtOIWquUDQQ=
		</data>
		<key>wikipedia-namespaces/my.json</key>
		<data>
		/YjizNCM+mGBU1cbXTthKkoK2Ho=
		</data>
		<key>wikipedia-namespaces/myv.json</key>
		<data>
		1kBZ0P91oRImBnb7OWTSftVZi5E=
		</data>
		<key>wikipedia-namespaces/mzn.json</key>
		<data>
		yAJe/3w9kPYdB0h3SDOpcI5i/lA=
		</data>
		<key>wikipedia-namespaces/na.json</key>
		<data>
		YkxJEy/9kX1Bi62uYnK2Pk1enKg=
		</data>
		<key>wikipedia-namespaces/nah.json</key>
		<data>
		Hs5gNpUt/GsQtTiGWoTTVxNYSPA=
		</data>
		<key>wikipedia-namespaces/nap.json</key>
		<data>
		TcgBqcBrbU4j/Xh0zWpShyRxHp8=
		</data>
		<key>wikipedia-namespaces/nds-nl.json</key>
		<data>
		vNrmli5VY9J8i7UPH4OBRboukCs=
		</data>
		<key>wikipedia-namespaces/nds.json</key>
		<data>
		w0ME5tRqtuL7R5jTGgNJold9k8s=
		</data>
		<key>wikipedia-namespaces/ne.json</key>
		<data>
		5Vv38Zgx5/0eqBIflCZ9oXxXvwg=
		</data>
		<key>wikipedia-namespaces/new.json</key>
		<data>
		+OerPrTfNFGqkaRavFtNqvzdp1Q=
		</data>
		<key>wikipedia-namespaces/ng.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/nl.json</key>
		<data>
		0iUvgag3HfevKH8RvvFg4ytd+zk=
		</data>
		<key>wikipedia-namespaces/nn.json</key>
		<data>
		W0xiYkqu55XGrlKQDjFgsdbu3C8=
		</data>
		<key>wikipedia-namespaces/no.json</key>
		<data>
		HM6xRFVZ36memTUDPn4VZ1Wqipw=
		</data>
		<key>wikipedia-namespaces/nov.json</key>
		<data>
		1L0RfcP5jimaGok8lyg5m4ZWIro=
		</data>
		<key>wikipedia-namespaces/nqo.json</key>
		<data>
		pYpRxGIAXw18pjYIBJEsoFGFxm4=
		</data>
		<key>wikipedia-namespaces/nrm.json</key>
		<data>
		mm0MEBUlOwe0nwsLHfU6akqMz7Q=
		</data>
		<key>wikipedia-namespaces/nso.json</key>
		<data>
		OASAirO2gl5dzns5mynkwwYQHbU=
		</data>
		<key>wikipedia-namespaces/nv.json</key>
		<data>
		WOxsYl5sCj0YqzBCx+KzvudM3dQ=
		</data>
		<key>wikipedia-namespaces/ny.json</key>
		<data>
		zP7PQRJj6PI+xtA3ScS5wnxY/HU=
		</data>
		<key>wikipedia-namespaces/oc.json</key>
		<data>
		fJ1Ihoo5WCLgNb2cn2tn5d5O58M=
		</data>
		<key>wikipedia-namespaces/olo.json</key>
		<data>
		MxuZTaVPcjafHCtezWNFClXx8SI=
		</data>
		<key>wikipedia-namespaces/om.json</key>
		<data>
		lXb92bj2zfiteTuWgFmoZn1ZTAY=
		</data>
		<key>wikipedia-namespaces/or.json</key>
		<data>
		k52FMMCmnys+J3QOBy8Cc6A96dg=
		</data>
		<key>wikipedia-namespaces/os.json</key>
		<data>
		NrxmTq/qJjx+V+MtZwmz15WinSE=
		</data>
		<key>wikipedia-namespaces/pa.json</key>
		<data>
		DvaWqBR7et/8i2tFxLXQkRA1u+Y=
		</data>
		<key>wikipedia-namespaces/pag.json</key>
		<data>
		gQvo9SmIOyeSKVAqAAUYPllx0lw=
		</data>
		<key>wikipedia-namespaces/pam.json</key>
		<data>
		kMchNxb2TqW9SUsI7U0nua0VT/s=
		</data>
		<key>wikipedia-namespaces/pap.json</key>
		<data>
		e41MYVw+GGKSKB3JkaF4tlqtTKM=
		</data>
		<key>wikipedia-namespaces/pcd.json</key>
		<data>
		6tpjE99iMXOH1P4psQ1mPV4A8RI=
		</data>
		<key>wikipedia-namespaces/pdc.json</key>
		<data>
		77yZdxZ8gnkOMu45PAo+GS2RBew=
		</data>
		<key>wikipedia-namespaces/pfl.json</key>
		<data>
		zXDaw3nMSLGNQEQ9gIsJ03RT0jE=
		</data>
		<key>wikipedia-namespaces/pi.json</key>
		<data>
		7lJ7ub7UGDTjy20fSRWd2GJ/Skc=
		</data>
		<key>wikipedia-namespaces/pih.json</key>
		<data>
		ZVK65D7StnZRQ/YYstVLA3Wzl8g=
		</data>
		<key>wikipedia-namespaces/pl.json</key>
		<data>
		fYhL4MIdLxs6gt0UlW2tClhVfns=
		</data>
		<key>wikipedia-namespaces/pms.json</key>
		<data>
		qNnhNc3vaIDC0NZRk7yOwhDw3Dg=
		</data>
		<key>wikipedia-namespaces/pnb.json</key>
		<data>
		Qv5mBHRCyHT7tK9X4bx+wyAcHRY=
		</data>
		<key>wikipedia-namespaces/pnt.json</key>
		<data>
		3PJ/ta+dmc2SBLColne+mvT8cJE=
		</data>
		<key>wikipedia-namespaces/ps.json</key>
		<data>
		Hw1EIx2z+ZddSg0KYn5aS+Y60iM=
		</data>
		<key>wikipedia-namespaces/pt.json</key>
		<data>
		RqH/i1QU5TT6vVG7Urv3wgXK94M=
		</data>
		<key>wikipedia-namespaces/qu.json</key>
		<data>
		14bbV8PCLzFPNLgkxLRj/3ILhrA=
		</data>
		<key>wikipedia-namespaces/rm.json</key>
		<data>
		saQbFp3p0xY5DgB/vifcCfndGuA=
		</data>
		<key>wikipedia-namespaces/rmy.json</key>
		<data>
		bq2LbGf5Vs1R0ZTl8Uwh+RXxvp0=
		</data>
		<key>wikipedia-namespaces/rn.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/ro.json</key>
		<data>
		NmEaps7KUuhWpHSRP5GRKd3pTHA=
		</data>
		<key>wikipedia-namespaces/roa-rup.json</key>
		<data>
		WqXxtdttqCPZngXADNF+Xb0zdaM=
		</data>
		<key>wikipedia-namespaces/roa-tara.json</key>
		<data>
		RjYTwPnJUFtUmA37i+N95OXRp2E=
		</data>
		<key>wikipedia-namespaces/ru.json</key>
		<data>
		i1kk784q+dsiPNyZybQSz7d/nNc=
		</data>
		<key>wikipedia-namespaces/rue.json</key>
		<data>
		+QPV+ChZ16nx3xnQoDaND5jn5Ew=
		</data>
		<key>wikipedia-namespaces/rw.json</key>
		<data>
		D1AVQPoc4/wTeat0o+P0xXAoShc=
		</data>
		<key>wikipedia-namespaces/sa.json</key>
		<data>
		C8rUhMA9dRhEOzSM5cpIZH5sahc=
		</data>
		<key>wikipedia-namespaces/sah.json</key>
		<data>
		jUKmdP9JmCCyuUf9xuiBqKvvItI=
		</data>
		<key>wikipedia-namespaces/sat.json</key>
		<data>
		JZIhIlW1Dg/pCpvx+1V4R19mv/A=
		</data>
		<key>wikipedia-namespaces/sc.json</key>
		<data>
		/OubT4dnOwe6rbf4M9E5yLfElPQ=
		</data>
		<key>wikipedia-namespaces/scn.json</key>
		<data>
		9QhOqpxsg3vrTzSUYMD8oRxm4gY=
		</data>
		<key>wikipedia-namespaces/sco.json</key>
		<data>
		vHD/wwc/1dFi966p3gVI/QN9nqU=
		</data>
		<key>wikipedia-namespaces/sd.json</key>
		<data>
		mFGpSLzV98/ARpdn+m5NCOM7sXQ=
		</data>
		<key>wikipedia-namespaces/se.json</key>
		<data>
		rRSYMXws/EgKPYXvcmXRNwI5Vyk=
		</data>
		<key>wikipedia-namespaces/sg.json</key>
		<data>
		Fj6MACaQrzZOBbHVQL6Zxor4h8E=
		</data>
		<key>wikipedia-namespaces/sh.json</key>
		<data>
		Ccgzsd3WkT7q1c7XoBgwAPuFwFI=
		</data>
		<key>wikipedia-namespaces/shn.json</key>
		<data>
		tm1y9I2hPX1/zWavRGlsEcrY464=
		</data>
		<key>wikipedia-namespaces/si.json</key>
		<data>
		4IXox4KoNS96AptF+APahhLvnUo=
		</data>
		<key>wikipedia-namespaces/simple.json</key>
		<data>
		BtCzyS2A71T7S+rvZ34bD4oShBI=
		</data>
		<key>wikipedia-namespaces/sk.json</key>
		<data>
		uWrF60gbS6gwWsiEMIHqyj0rTno=
		</data>
		<key>wikipedia-namespaces/sl.json</key>
		<data>
		aOhJXzqdMqzDw2+oBnbr6GuUoz0=
		</data>
		<key>wikipedia-namespaces/sm.json</key>
		<data>
		8ny1eQpNQeu/ISRcIiUsbsMjc84=
		</data>
		<key>wikipedia-namespaces/sn.json</key>
		<data>
		aKpoN0l+G9Om338Xen0YqmJ2288=
		</data>
		<key>wikipedia-namespaces/so.json</key>
		<data>
		aGZzTagJnkGAMfUvLjKI/tR3EbE=
		</data>
		<key>wikipedia-namespaces/sq.json</key>
		<data>
		Ko2owvXrj86Jqo6ZFUdSh5xnY4g=
		</data>
		<key>wikipedia-namespaces/sr.json</key>
		<data>
		ovPxd7okTq1Eq4QgGv4VDRlWZA8=
		</data>
		<key>wikipedia-namespaces/srn.json</key>
		<data>
		hzHHSXLoXNfXycQpp46o6BuvCsU=
		</data>
		<key>wikipedia-namespaces/ss.json</key>
		<data>
		Ez4g1x+AhvAhQUQn6Td6QUdKzX0=
		</data>
		<key>wikipedia-namespaces/st.json</key>
		<data>
		3hGsMpcHgWsrvlzId4FT7FxOxXA=
		</data>
		<key>wikipedia-namespaces/stq.json</key>
		<data>
		UjbawkHhK2IQsfYgBtTH55QVl2g=
		</data>
		<key>wikipedia-namespaces/su.json</key>
		<data>
		VpeFZ/LZBzd4LcY6g15EqBDve50=
		</data>
		<key>wikipedia-namespaces/sv.json</key>
		<data>
		EJRWn08Pl8WIkM83JDpGgvlydRM=
		</data>
		<key>wikipedia-namespaces/sw.json</key>
		<data>
		wniZyo/99c5KGk5XQjhINgUk6Ms=
		</data>
		<key>wikipedia-namespaces/szl.json</key>
		<data>
		3USrdvYckGK1ypThXBlvb+3athM=
		</data>
		<key>wikipedia-namespaces/szy.json</key>
		<data>
		cw6Fx9JkuIoQBqmlvZKkydYxIV0=
		</data>
		<key>wikipedia-namespaces/ta.json</key>
		<data>
		4jTVAj9n2/yQURMxL7W/m3E+YYk=
		</data>
		<key>wikipedia-namespaces/tcy.json</key>
		<data>
		nFr90AIQDfvW7ykOGxMXaaB08uk=
		</data>
		<key>wikipedia-namespaces/te.json</key>
		<data>
		AAO3x39sTvqlrpgK/SM7COtbaMo=
		</data>
		<key>wikipedia-namespaces/test.json</key>
		<data>
		+NpCS0spNTRR2yIqHamzXAG/aew=
		</data>
		<key>wikipedia-namespaces/tet.json</key>
		<data>
		m+QV7fFvsdtJrC3AgunztDDCG44=
		</data>
		<key>wikipedia-namespaces/tg.json</key>
		<data>
		b2HUuiXANjRcEFqB2BX6hcH5Qg0=
		</data>
		<key>wikipedia-namespaces/th.json</key>
		<data>
		Vms9wYjKlkheep58NehgPn3dcYc=
		</data>
		<key>wikipedia-namespaces/ti.json</key>
		<data>
		6SZNU0uyy9H11kRblW5xIDCIhpY=
		</data>
		<key>wikipedia-namespaces/tk.json</key>
		<data>
		rhsBd5qibGodoVe2YyzyQxT6Ahw=
		</data>
		<key>wikipedia-namespaces/tl.json</key>
		<data>
		Wzu7Y/ILSPW2GJylK8Cq2DVxOpg=
		</data>
		<key>wikipedia-namespaces/tn.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/to.json</key>
		<data>
		imRsIcm/unhr7oj7gs9OLuK6l8M=
		</data>
		<key>wikipedia-namespaces/tpi.json</key>
		<data>
		sfE3ItBcvPchE9KMPKxvDg7L2YU=
		</data>
		<key>wikipedia-namespaces/tr.json</key>
		<data>
		UI3kjbI9VhH2q6/iGP8NziFF6hc=
		</data>
		<key>wikipedia-namespaces/trv.json</key>
		<data>
		s+mpG1rRT/K/CrJa7PfpTd0ONGo=
		</data>
		<key>wikipedia-namespaces/ts.json</key>
		<data>
		KwKhCgKFQ0ltoIoo3p8zdCdko1M=
		</data>
		<key>wikipedia-namespaces/tt.json</key>
		<data>
		2mdIfID2OhdDyNpQf9f2xsvj5PM=
		</data>
		<key>wikipedia-namespaces/tum.json</key>
		<data>
		7a+YsE7zIB2hJtQ53fhxCvqP0oQ=
		</data>
		<key>wikipedia-namespaces/tw.json</key>
		<data>
		YmDLUCrNaskH17kj+OrXbn+eEwM=
		</data>
		<key>wikipedia-namespaces/ty.json</key>
		<data>
		4NQC/09k8jpUJjG2Yt5K/Ls+bFE=
		</data>
		<key>wikipedia-namespaces/tyv.json</key>
		<data>
		US8wtyYhQANaDPtjZEDZxuzrbfk=
		</data>
		<key>wikipedia-namespaces/udm.json</key>
		<data>
		GvBbNxqNAfEqs3QRcES/Ki5iD04=
		</data>
		<key>wikipedia-namespaces/ug.json</key>
		<data>
		f9SZ/xZW+VEMD8wJW+ErIRG8RqQ=
		</data>
		<key>wikipedia-namespaces/uk.json</key>
		<data>
		Og23eAmCQSiU/WyAgjf2A5TzhEk=
		</data>
		<key>wikipedia-namespaces/ur.json</key>
		<data>
		LiPsC5Kxaw46ONhGj2656Hb8mhY=
		</data>
		<key>wikipedia-namespaces/uz.json</key>
		<data>
		D6uxRunfP5gyuT0eDeB+ajhfZOc=
		</data>
		<key>wikipedia-namespaces/ve.json</key>
		<data>
		MJ7/alqf0+ulpz+m9L0BRm3dIR8=
		</data>
		<key>wikipedia-namespaces/vec.json</key>
		<data>
		5EZBT1DohFA9GWRA2bPQwjDPPw4=
		</data>
		<key>wikipedia-namespaces/vep.json</key>
		<data>
		hXSbmILZnL7t76WE4XTsvnydsV0=
		</data>
		<key>wikipedia-namespaces/vi.json</key>
		<data>
		WpauKshTn7rafKSjwevBbo6sTU8=
		</data>
		<key>wikipedia-namespaces/vls.json</key>
		<data>
		ZN+90oDTFWFxhcnQMSlENmbz/NI=
		</data>
		<key>wikipedia-namespaces/vo.json</key>
		<data>
		ADj2mm0PGsZZH+Zneo+vNiAUWJU=
		</data>
		<key>wikipedia-namespaces/wa.json</key>
		<data>
		dj2O3yPmDgx9z0Ws1L4NaOK0MrM=
		</data>
		<key>wikipedia-namespaces/war.json</key>
		<data>
		B8N5svWnqydDFb/RR65NxOD11ic=
		</data>
		<key>wikipedia-namespaces/wo.json</key>
		<data>
		b/pumlRrI2LJid2tJV1ubHU+5Lc=
		</data>
		<key>wikipedia-namespaces/wuu.json</key>
		<data>
		7J6s9ILP/4b5ibeL19Zq35L7JZg=
		</data>
		<key>wikipedia-namespaces/xal.json</key>
		<data>
		peDkfd6ih5eCyhAqtbgUqiDf+Fk=
		</data>
		<key>wikipedia-namespaces/xh.json</key>
		<data>
		vNv7IbgpM08TAGw/Ry2DmjOtCgM=
		</data>
		<key>wikipedia-namespaces/xmf.json</key>
		<data>
		8esrfAJpnEPqbTF2fkyFwrFxprM=
		</data>
		<key>wikipedia-namespaces/yi.json</key>
		<data>
		tB9AXN+7N4UpiU1uErHqKTVtlzw=
		</data>
		<key>wikipedia-namespaces/yo.json</key>
		<data>
		eGXV3XEdRVREdXbZWW1fUZ6TaCY=
		</data>
		<key>wikipedia-namespaces/yue.json</key>
		<data>
		UY2xcDmGnEErcgnC5uuaXPtjHJk=
		</data>
		<key>wikipedia-namespaces/za.json</key>
		<data>
		+m0DXGhdH9ofjireba8OfXbY8ak=
		</data>
		<key>wikipedia-namespaces/zea.json</key>
		<data>
		2dMFJ3i2kTFBxBukwG1wBufJAsk=
		</data>
		<key>wikipedia-namespaces/zh-classical.json</key>
		<data>
		flac7qDnjDFzRkFEOu9LhbeY+7s=
		</data>
		<key>wikipedia-namespaces/zh-min-nan.json</key>
		<data>
		IBcp7/qtwQPvd9qThC82tE8I10A=
		</data>
		<key>wikipedia-namespaces/zh-yue.json</key>
		<data>
		UY2xcDmGnEErcgnC5uuaXPtjHJk=
		</data>
		<key>wikipedia-namespaces/zh.json</key>
		<data>
		g4DuNS/JVOqaUE+WsbrmpA6tONQ=
		</data>
		<key>wikipedia-namespaces/zu.json</key>
		<data>
		lWhh/Oj9vEZJaISRJM5WZL7St6o=
		</data>
		<key>yi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AJwiMYrvbx5xiuuNJ9hKrC6XM4U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>yi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			XDRbOYyNzuNdDPBrBrrKMbGIBg4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lnTwT8cjmjoikSqyOIGlXJBkLRs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			S6xGWTthfmmHje/vBLdJJMd6w2Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FmTYFkxbHbX3mJA19K6M8XWGuMo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			EXMlqdD6ID+KEhrbQTNKWnLlOdI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			39CutSwvh6b7g2L5EQRDRa4xfB2Kzh4PQ0UCWYN+c6I=
			</data>
		</dict>
		<key>BatchEditToolbarViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			m4YsQSYeIsa7dh7+KAneWhAveSSu+BY9n/CQ6Goe5/8=
			</data>
		</dict>
		<key>Cache.momd/Cache 2.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			rwU5bh6t1jub+uwjle1XzKNt1NAYg7zYbMNwPx3AOqg=
			</data>
		</dict>
		<key>Cache.momd/Cache 2.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			yh7EiHPMOBfRQVyLFxelnu4CZSKmBQRx6VxfTrH644s=
			</data>
		</dict>
		<key>Cache.momd/Cache.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			4W0wHyIdU/c4oUiSgHo5PqLLjsbwN2wHKhM0g/NdBUw=
			</data>
		</dict>
		<key>Cache.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			VxbDp2gQrN6CCjPDkrqWLmmPrYqZkmIZdxY16F2u5+A=
			</data>
		</dict>
		<key>CacheItemMappingModel.cdm</key>
		<dict>
			<key>hash2</key>
			<data>
			qVWtphWLIQsbj4Lh+0RaWxfWiJuTln5YmwdHJApzI6o=
			</data>
		</dict>
		<key>EventLogging.momd/EventLogging 2.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			MboOcdRRTGOzQlVUrPqCdSBVeQjikP/7mWqBjZ4iV/4=
			</data>
		</dict>
		<key>EventLogging.momd/EventLogging 2.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			pC7uiMcazzgjiGKf16GIlir18mYcpX2HvyB8QQlgfg0=
			</data>
		</dict>
		<key>EventLogging.momd/EventLogging.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			TQIK5NOyLDtBIk8ETqvJh6TvkRrZ1hdfGI8yVAilDWk=
			</data>
		</dict>
		<key>EventLogging.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			G3RPkhUjmOupYO4AQJQO3uJCXIUhduZsBamecdP21x4=
			</data>
		</dict>
		<key>EventPlatformEvents.momd/EventPlatformEvents.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			j25FDJUJrFH2Z2mTAOs74uxOJUYlZzNaPp3h9gSAqzo=
			</data>
		</dict>
		<key>EventPlatformEvents.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			d0hsG+XVzuFReC4fTr3y0CaaJDz7aC7AO9RH2u7ZlQ0=
			</data>
		</dict>
		<key>MediaWikiAcceptLanguageMapping.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VIjOKwTgdfIlFbdJ9P6ULEvPMGSf/nBx/4N/Caj1R/4=
			</data>
		</dict>
		<key>RemoteNotifications.momd/RemoteNotifications 2.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			Ka86knpDmlp2zbZd2atyfm1t/gHIIvVhsyUVxeLd8I4=
			</data>
		</dict>
		<key>RemoteNotifications.momd/RemoteNotifications 3.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			cJiAbGzodqK/zGWA1wKEB/sMwRIn+kviGEhq8B+NEJc=
			</data>
		</dict>
		<key>RemoteNotifications.momd/RemoteNotifications 3.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			/E1Whh8GUYssZnpXeKDQhA32Wa+yN47AmJZCRg5YeaM=
			</data>
		</dict>
		<key>RemoteNotifications.momd/RemoteNotifications.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			mesYO03c1BBGxkDG6IosPmenmI94i6Oxc/u22Newod8=
			</data>
		</dict>
		<key>RemoteNotifications.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			LrzJtijkOpIyFd8703sSjj5cbTZxaSENDqlpFzQ6goY=
			</data>
		</dict>
		<key>WMFArticlePreviewViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			w0hEnitthbq64PIhGTRHOGw1OJIFVoHm6QDI0bLWtZQ=
			</data>
		</dict>
		<key>Wikipedia.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7geWbpldfjjLJT76yPsf/VGRMArKmVJ/IjcYWRZIfZM=
			</data>
		</dict>
		<key>Wikipedia.momd/Wikipedia 2.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			L4cJr6/qAcSs6BSXYBvFR4dDbQ20ZQIZ0mrs0B1g5lA=
			</data>
		</dict>
		<key>Wikipedia.momd/Wikipedia 3.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			koyEN5IxsQxMVzHhjU5tYECgTjakx9FiU5vm0MF50fg=
			</data>
		</dict>
		<key>Wikipedia.momd/Wikipedia 4.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			KMn7OsQYgaHL27yNq1/nkP3KBrOWXb7qH8PIRIDn+zc=
			</data>
		</dict>
		<key>Wikipedia.momd/Wikipedia 5.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			ODGTlGMfOafPdJ1BIl/ZSeOa5HxwLq6f02g5arUnESc=
			</data>
		</dict>
		<key>Wikipedia.momd/Wikipedia 6.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			aQPo+Rdk8tJvUdOUqKvfPaFhSTgBaRTrZ/cmMbsx6LA=
			</data>
		</dict>
		<key>Wikipedia.momd/Wikipedia 6.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			/Z1vWuYudlmD3u5aG4Kq/DyWVDctdxBe1ejt1ybawvk=
			</data>
		</dict>
		<key>Wikipedia.momd/Wikipedia.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			MYrUthZPyXJ6M7Tjyfz3XiXhwEyXJ7O2A5xYkF7OCyE=
			</data>
		</dict>
		<key>af.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Z/ANpnRznotmhCJN7F++6/D9hsaGViLognR7FCwcuPc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>af.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			riK+xTcs2V8hjOOnznRgpEGU2InqyiZ3EOYM3AIqi8c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LUW7EM5jFog6xTODLfLh/sARVSISrVN7wQpyryqQOtM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ar.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			Y4N8ZBbi3KS7hWm99sr02RdXGzUnPRrvZ1pOyI2Fdx8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>as.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vGXcOcmRKkviDAKURxhultOckiUxxY7o4vEr8998z30=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>as.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			mxbmGU6VUu0eEg+QoAv3vF8Z48SIISvZKGmO9iAD314=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>assets/WMF_Black.pdf</key>
		<dict>
			<key>hash2</key>
			<data>
			7D3/IiSNWVnsgB6yUmhsxfeY/JyF5oudSZeb+Dnovrg=
			</data>
		</dict>
		<key>assets/WMF_White.pdf</key>
		<dict>
			<key>hash2</key>
			<data>
			Kp4/mkMpEJqcZlIc9Ym85dK4GwQcZoVh//JlLQql+u0=
			</data>
		</dict>
		<key>assets/about.html</key>
		<dict>
			<key>hash2</key>
			<data>
			JSo9Bo5CrdpoE6aCQL027xWCFbmRkJB/BLLgETl6AjY=
			</data>
		</dict>
		<key>assets/about.js</key>
		<dict>
			<key>hash2</key>
			<data>
			nUXe9/epkPjmLsNQakPNCJtQLhJFsSalZij5OpSNcnA=
			</data>
		</dict>
		<key>assets/codemirror/codemirror-black.css</key>
		<dict>
			<key>hash2</key>
			<data>
			1wBb/Dv1PlgXc4UILA6XTh3DIPEit+RUVegGalc8/Y4=
			</data>
		</dict>
		<key>assets/codemirror/codemirror-common.css</key>
		<dict>
			<key>hash2</key>
			<data>
			J80UiLa9l1Srlll/B3CvuZJEXHUbW7RKi6bbJ8Bca3U=
			</data>
		</dict>
		<key>assets/codemirror/codemirror-dark.css</key>
		<dict>
			<key>hash2</key>
			<data>
			yiu2zHdt4r4yDhFqII3tAxipVRF9hG8XZQ0OtfVZgCU=
			</data>
		</dict>
		<key>assets/codemirror/codemirror-editTextSelection.js</key>
		<dict>
			<key>hash2</key>
			<data>
			xVWavDFKhzX7LVji1MgfpQkUmlaeExgkmzPJZCZzkPY=
			</data>
		</dict>
		<key>assets/codemirror/codemirror-index.html</key>
		<dict>
			<key>hash2</key>
			<data>
			dlVY9hTeJLPZ93+C8WgabqTzKnKg9HqAM5/70UQrR5I=
			</data>
		</dict>
		<key>assets/codemirror/codemirror-light.css</key>
		<dict>
			<key>hash2</key>
			<data>
			D9FPIMBaQfRbuZGpkLEXtUB3dplTw4T5Hh8nKPVHza8=
			</data>
		</dict>
		<key>assets/codemirror/codemirror-range-determination-bundle.js</key>
		<dict>
			<key>hash2</key>
			<data>
			incLAMsQFd9PkS43cHqluHEwMbwWC4G/tLdgytl832I=
			</data>
		</dict>
		<key>assets/codemirror/codemirror-sepia.css</key>
		<dict>
			<key>hash2</key>
			<data>
			hfEUtSgttQ2Z7DzN3HnCeSYeWBPJDTinol6OoQWr/ME=
			</data>
		</dict>
		<key>assets/codemirror/codemirror-syntax-highlighting-off.css</key>
		<dict>
			<key>hash2</key>
			<data>
			uL+rXVLkZfAAFyV/AG6PvMQ15bqBwmbKg/tjjCcQ80k=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-aa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ab.json</key>
		<dict>
			<key>hash2</key>
			<data>
			km4HTlYUHla9+jpTZQ9S/XjEs6TtbvvIqBjU/arxNwA=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ace.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ady.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-af.json</key>
		<dict>
			<key>hash2</key>
			<data>
			y/x52Hl7OKyuqSHlDPbKu24ihvvm77R2JGtaaJR92uI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ak.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-als.json</key>
		<dict>
			<key>hash2</key>
			<data>
			B7BttAVDywOoBGGrGomQcYUv92g8OnvlZIaiM4z+KrQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-alt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-am.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-an.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j9K9/6WyqrYU+/0Wy3krVXxNfM9a9+7zDAahlhBQ0ak=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ang.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TuQMpuDG2Jb40tdONo+4GhGXdv9tbYI35UsbZV40gHE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-arc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vXmxX3pUsXI8r5VmjTAeEOQRJCW+YIp2MVoS9w3Dils=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ary.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2YnjELsScBrd2DFfUz7JQrxShsd6sTtNTe8mXlp1mu4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-arz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hS9W3sFO/AgV5oLstkhw8usxscvrf3GAVgD4h5mDBnQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-as.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iziPoTpMg7f8JABKRAg+XqLcNS/TLD/TBXgZjYD6B9w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ast.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-atj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-av.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-avk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s22A58bVNVGJnh3jWZbALWPp5D1peDsfltwEvR4bHc8=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-awa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PoMpYe7rt6MxjVjoysYyY+HAxXBl2/acXf9AvqxUHyY=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ay.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-az.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0PxOX6pSDq7iInpbKOwcZz3OaiPnFsPbO8fK+UJsSGQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-azb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8FgDjINeDSrm1fmn78DWs/duaf+Qd7u4qfTb7IBp09Y=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ba.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYE9ILu/BBqsp+7cq8YX880LbLceD08xf9tnnTlFSHM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ban.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5SveJ18LzKL0h1C6cXFFUSh5aitml3waR39YGKfrOoE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bat-smg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8IOntdbm3asZZb6gluUGTlFnFznbHyERj6KgOS8+G18=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bcl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PIeyAVd1giby6ZVewsIbn6dUcnmNGZqcM/eixzci7FI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-be-tarask.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qwiNXVn3b5Mget1YZMXcbRGQp6HPsSWeVN3bTMhY9iE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-be-x-old.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qwiNXVn3b5Mget1YZMXcbRGQp6HPsSWeVN3bTMhY9iE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-be.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zMTJmK6OCvIagyu+8ZAtln6gZZI2NdcYHe21+abTvvo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			sISC4m4xC7WKhp24AcdnUI4a2VJivps2+mEfvaOuXYY=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bjn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LS8UFShmYzSXERNptAVfsEB2PQBOFIYZUS+AVQVaCvg=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bpy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			B9Ejo2lVXicnFj5CF5IiT1KoUVsranFNL9otBRh2bgs=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-br.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nKXMBbw21msHV8de6OjadORK6zvyBBFNdvXKBWGCEe4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bs.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nduhYNi5HKcbvWxYdFSVwn6r9JX2ogGyLzXpqvo0iGs=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bug.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-bxr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ca.json</key>
		<dict>
			<key>hash2</key>
			<data>
			n7t6qEBHjBgv6uP2o4iG+0FtuFmNZZWzIdIukvs51qU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-cbk-zam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-cdo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VBQ7J4yKQflXe55DtC3FdzBk3UKXWz4984VPp46esmU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ce.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PE32hQS5wl1mtaitvUwUK6tSyy83rvsIWQv0PcM9YKk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ceb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ch.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-cho.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-chr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-chy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ckb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bJYlW2vUc7+yQ1hPNb+zyJouGReKYyUVOaH42XCPzzM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-co.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-cr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-crh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cNAnPi1BlEFqA29saD0Lkqabf8S2xN0FpJj+d/FgOMg=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-cs.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZX33zKhEYzv61YQLwMe8h+/Ze5OkvuFPEdC+naZsf1A=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-csb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gu/aJUsFb9TOkYbzPf+SuEnOxxsjjqUnOlP5TMXWvGM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-cu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			NzuLPjW83/3p7KnBSHe1aUPHmMnzX+9l3AfyQ8H/YLA=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-cv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EvwcXj0oOxrozRZP4Iiul8joYCQJo2YJe5CgSKR9RyY=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-cy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LeB0QIQgTpC0l3y4Aqtv3cyem/XEG6SZlWld2PYC5Jc=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-da.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dDv60x1CLjuv+W9uhXtXQmv65LCREyMVnXE9XLlfR+w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-de.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PJbHkBxebK7/nC/UiEwyIfgK+9Npu5K7+Du54Xq3d2o=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-din.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OiOVMqzDLI2y2BDd7bjnsNNSLCRA2HPFBIHS8HMxutU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-diq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BWIQO8L1Ti9j0zTAIfO65TVUvHZ2vNJn+WPcaHAhmPU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-dsb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-dty.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-dv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-dz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ee.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-el.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4Wtdv7MtZU5l3IMI1UEQ9hpyyZS6YcPJ8j5nwsm7NEU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-eml.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-en.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ecwidrsII3BYm+mNyB4caR/9snYpY2hSIsiPBP8Ad5M=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-eo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E74NTd83+3K5noxrTQl1ziVbOztjZ5nEFkDAve5JuwY=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-es.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Oyid3t3PkqkUNuw8DVycAyRzBNdjTg9kU48wm9ySO48=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-et.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EvimKbRxVKWZSPVdk5NTBn4Ij0KL+qG+/1X8e0JkH5o=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-eu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			YkkRE6jCGgRjMfvHg36q3idudK0mKlaKrdFo2KkH9Ac=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ext.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-fa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iZ/YcoisMbb2e+5ON744nEChZ/84MZOVMwDwwXq+Lfg=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ff.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-fi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3OXEneqTs2ucPXv24QUSt5m1vVqNiYSf3wH0P3WNq14=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-fiu-vro.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EvimKbRxVKWZSPVdk5NTBn4Ij0KL+qG+/1X8e0JkH5o=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-fj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-fo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ejEl51VxhLu4sF96IVLmrm+tgJwR5FgX7yFeLmAp5zw=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-fr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			37//7juxMngcHdINWHQB3rRCkNbgtZ984bYgx91zKMk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-frp.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0j6Z67sAd+KxOib7txKvjCPbtNnqwvYyUncHwKU95+c=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-frr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2iGfOo5Xy4PU5XfcBF3RBIZHWdR5y3dWHNogP4PGMN4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-fur.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-fy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			D4ncevxVPyRfyYFtGU1MKS0hnL+XCBQ0nYAgoTrNnt0=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ga.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8tMdoCLKFSOw356SUyDoqCFyPg7KU6fwIMutUTyNfxw=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gag.json</key>
		<dict>
			<key>hash2</key>
			<data>
			HeRSSXK3cQrQga1Vsk7q9YetknY134DY3M/3DvZoAgw=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gan.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yV08qP0sX4V1zUldcOWMp9BcCNFwsk4tTQxooQhxANk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gcr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lR/XfyPORVvztkzOhE+N74GTGDwMwOrIJguTgXYmU9M=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-glk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4PORIH0PmD5zsjh7sBF+XzM8csVacqvBC48Lo+erv+Q=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gom.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gor.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-got.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+6S6Ytnwrq5FB2X8J66IPuYY60ioPqNzKhjnho5DXOE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-gv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ha.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-hak.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VBQ7J4yKQflXe55DtC3FdzBk3UKXWz4984VPp46esmU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-haw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IrdeKVrv2GvMoEzk2zG7UhF3tebbW6lVVwOFnSM4LhI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-he.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RcoO/ELqCguIP1nUk40GS9MC5U/KoH10A90FBj8ITk0=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-hi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+NSt2W9J89UluctJ9xZSwPG005YvRRn/uzhbgWim7Jk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-hif.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ho.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-hr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2qxkA+fzI2nioEzvrlUg3QZ3PqQTJf3mgI7L+bgaDoc=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-hsb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ht.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kuX2NyxT8ez+r/aiSxqkH7tS/QiSNCaRUmleE+vBFxI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-hu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6aS3Mu5aW3RWzEzLrPX+rviG9XeUBzfP+UH8xUpCwgw=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-hy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b79BUuwEdlYNJL9I5fKWbr39IMH5gwC7ql2PXAtRi20=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-hyw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			alWIEjqXAK6hxk6HBKwJqwBJTKXy70oqli1WLPxSN2E=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-hz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ia.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-id.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQpTGOnXuSX0xGU+b3uPiN9HIS8x4SkpDZ6o+f8CZ8s=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ie.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ig.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Rl2nrt9F3k9QGuYLOYHIMDiM2aIkGniuoF9tOQzKQjk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ii.json</key>
		<dict>
			<key>hash2</key>
			<data>
			QJeJwuJzQMldXMZTcx4X1IoV9Eta66LIY+Fd1g3rmFI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ik.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ilo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-inh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-io.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1MRtbI2gLje8UeNaHDZaaYJeaQ1CZpIEVcNpPrRR+zE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-is.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7oo20YBNAwSWCmcgfYtGYjnveK40Maea8yS1wUqCGA0=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-it.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-iu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ja.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eYvFnT+/zwGMMGNq4wXHP0WoJfP1QqRRRepxzS7PzJU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-jam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-jbo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-jv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ka.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6JM8dhi2z2otqdhGGtFWZO3Y9YadU1KzxwGiPeUgYoo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kaa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PM4dRKPfIFssZUOuknic2h5yAaM/D43qn/H55J+qrZ4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kab.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kbd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kbp.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ki.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			F9vbGkVnzos2MVdwxGf8Ecn2AmjgVew6htsagpxInuo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vAs5Oxz9IcN6HEXCBtKz8Z/9+PQV5zgGtF0dpzZQLWs=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-km.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+BeDkidyrOVhc5ODpcUWGWOXavxt6tQ+0S4q8/pJvuw=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ko.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Mmrtckt/QdK+5H9EKEjFFb1PpP128N/+rsr+xp3Hak4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-koi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-krc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E7+3MBUezRmSSZpYjJKjBI5oRApqeGflZ9N7LStXdBo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ks.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ksh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qfmp8Svq7sSZ1MUz8HK2Z1xw3C2ldMxmrRWqFWoNxas=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ku.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kgWhpwbOIICVxqURfyPkhllmmmG1dmY+FQgOvt2J2zo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-kw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nENhNHd3Sh/KKptdDTq39cMYRJIhmTG1m8oq/WPmMCo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ky.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-la.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lad.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TTOk4Ljr6Bj5VTx0RwtEHK8hvg6KizhK3g5MdkhJJkc=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			mKxVWLuzmfVP5r7lDVwJtvy0FSc0TZchK7RPhxn5WOs=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lbe.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SSEdZ24posqkqqa5nz/qPBU3mSxMhe3boZUqRcaAutI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lez.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6gNfmPy9enuEHzruU1V2GkHH/MR7R0KzRRhLS3WB6gs=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lfn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-li.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uonDqk94hhn9zZr/UYlA0/Cg4DaOHhTbAbZJ5oDCDjI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lij.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lld.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lmo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Nh0pGd73Ogcg1iVtJ4wJKbS/vDexO3XM/wUxXAjJUzU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ln.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lrc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4PORIH0PmD5zsjh7sBF+XzM8csVacqvBC48Lo+erv+Q=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8IOntdbm3asZZb6gluUGTlFnFznbHyERj6KgOS8+G18=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ltg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/TFrz8Vkru02OxULsXydIbV9Rgk/+fiwxdbn9AxUc7Y=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-lv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/TFrz8Vkru02OxULsXydIbV9Rgk/+fiwxdbn9AxUc7Y=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mad.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mai.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PoMpYe7rt6MxjVjoysYyY+HAxXBl2/acXf9AvqxUHyY=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-map-bms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mdf.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uZshUwQtmD+Ngrq6UqPp/rU/Le3KqoezNuplyRO+sSE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+y5VnGXAzuECPCAk5DQT/Pkj6UYOdfUnyzDa3Fwko5s=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mhr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Y/evbw87nzHvp7SbcHeDCaVSl5HBwRxNwuLQx/b2Uck=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-min.json</key>
		<dict>
			<key>hash2</key>
			<data>
			A8E3DlLsYlMBBeP8AbdRotGm5/0VWbWoIWUBUtljmqk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BLA1gKqkGDUdFPsV5236O+7rnmF3dhvK4SuOa2WGDRw=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ml.json</key>
		<dict>
			<key>hash2</key>
			<data>
			mTsz1Scf6WQXmeI098Y4rNivHU2d1uslUPce2ac8UD4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			QtxLpN1P46gl33QGUI7lgV8JMSZBtDj/GjOAzsYDbpE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mni.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mnw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			HqGLH+JrxDitRhZe860p3PW9PzYq1V8Fvb6K6/39MNc=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TLoNXsCAn1hP/03JTVDNx2xEsP9Hrvu+eJgWdA1f10c=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mrj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Y/evbw87nzHvp7SbcHeDCaVSl5HBwRxNwuLQx/b2Uck=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			wDiuH6jrDdkc+3YZ9kkkeeVL4dO+HCaLJChj9glPqL4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			85wyBRmnKggPWbhx5+ODMI+udYUdl2ZX/MLDMHREebo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mus.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mwl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XYpbAq2NBZICoizglGhLHungDn7KMz54U1gu0fPuRrI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-my.json</key>
		<dict>
			<key>hash2</key>
			<data>
			MWP1ArB4JxZnfJbUP7zxcLW5prFTE6NlksY3BfSoEzE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-myv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uZshUwQtmD+Ngrq6UqPp/rU/Le3KqoezNuplyRO+sSE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-mzn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PPM5Bfm7G6WcKpVsFG9l+9RZgi4yL4eBcB703Xmigw4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-na.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nah.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FQ6ik6xDVroOQCaSkxt7LEz0SgjRPZWSgsAhF9ZJhRk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nap.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nds-nl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dNIQ6/ohyROSgKPEwku9UQqW9s7EnwevhssHChegIAs=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nds.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XUnZWKWzu0oDvYYwJc1B3sC/Eke7T8mPFgccYVnsvSg=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ne.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-new.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ng.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WrAL7LhInTI+IUJ/A0UxAr8zRPg7F7ht5fJYPAN3EiI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nia.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uonDqk94hhn9zZr/UYlA0/Cg4DaOHhTbAbZJ5oDCDjI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LQtYYQaTZWSJa4BQRavoy6A8NlaAK5oAkwPvq4gHHeE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-no.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5LnoBqCKS5ywB48JL93qpGJi/Zw2biBuGkT7a+ct5js=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nov.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nqo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nrm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nso.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fB+SjXJmpnFWOLD8ohkUGr6L9b4icwDISPOiwrbOfnk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-nv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ny.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-oc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ljqbbLZXDYBc4+tl1c7xCDTSEZmw8NS9P96LwfoG1KI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-olo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ycfCFm2fslbxIt25jZMhcI8PS7Dwvb2a9GJ5jLvNzBQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-om.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-or.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GVigIj6L65V5nL2fMZtMUpv+MZGasKkgZo6bCI1CvGQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-os.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6XbvJ0UJq+fs/WM1bR3SFYqaXUoEf5Z2i7FF895OpV0=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IngQfc9vYWGMTqtba26avnqnTSWhL3mkH9EZd0hXxzA=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pag.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pap.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pcd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pdc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pfl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pih.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			J4sc7XogR8lXHXdnYqbp0LVkjw/UkllclDjwCnSkT2I=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pnb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pnt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4Wtdv7MtZU5l3IMI1UEQ9hpyyZS6YcPJ8j5nwsm7NEU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ps.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9/oNepu/0cAYdnAHgoejLkvQdnznqEhBgQPPWRjd2vU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-pt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			w4FN6eUeYH6iYtBzUuwPNSNPNYmves6M33PSf8oxgZA=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-qu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			YE4j9yA4N+guxSSeBafXlEbNq9oOozgIVNiLkn+HwsE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-rm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-rmy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aNn3Rg+8pJZ0/ZwsMhztDvEQLOxpeYAurEa8Tze3TZk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-rn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ro.json</key>
		<dict>
			<key>hash2</key>
			<data>
			q8hSCXp9RQKF6aeG9qPnOWgQEb5K6JSMKZD2C6vt63o=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-roa-rup.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aNn3Rg+8pJZ0/ZwsMhztDvEQLOxpeYAurEa8Tze3TZk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-roa-tara.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ru.json</key>
		<dict>
			<key>hash2</key>
			<data>
			76w0FjZ0Io0NBvl++Y8zy+7pTxu5YEiZ3LuKTRcplFg=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-rue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xTwo1Op9PQSKGdYvsRYKHlMwOmTEyT1v5TlNGXZSZtM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-rw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RkptS+WYLdoN0ar2eMtjq9aGIWc1Gp+mic9FsAm16Ak=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sah.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OuzPw8CJj6RqR4RM+zL/4NFMifTTPa/N0MFDrLeBbso=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sat.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-scn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sco.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			k7agiFPhsBsqLd3zI8xClbpXmA9ZXJyNsiZPyYNX3Rs=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-se.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7CxM/zTjrVD8k0SbF9rwKYDQR2eGoE5qrrj2wEh+U6w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zRLfu9hS6ui4SyTZ2aDjO1lU39EoVmary6bfcsRewqA=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-shn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-shy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aamsBGtAXRJamKJq9FGK3+IMylyXuTfGpdozTfD0oLg=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-si.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0v1BMYsL89qIvcrWmmv26EYiNWq3x5h7oHjLLSwxWqE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-simple.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l/MAknISTBwD4XnNbP+qf/rQImBPo2GdGiUgi+zbNBU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			oEKXZ7mDYTaCdx1NMxQjKhjd8zUP9/bTVDNDeTdYXbA=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-skr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			oDp+JsnqoaRtNelSYp1Wt6PcJMVbDggSvwK6lZaHMtY=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dc+D7YY1Z8yQUJq1+VVdVlngWMXWdJcWSuA1U0PpN/A=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-smn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gMEJLgxAy9i8Cls31MWrTDsRr4SBFhxb/wCln2bwxVM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-so.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eiAKTEH+Br9c56Yo0jgdxqQsIONW0C6fVWBFMK4YQqI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ljjH/TMHIWpz1CNwHFDKs1nqHSwsL834t/7gai79R00=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-srn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/uFH8rK803fFRwAF72CExPBFdJyfo3nQ5qo0m1oEjMI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ss.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-st.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-stq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			b7u6UGZXf5LeKaLiPX+Egljdgy9yTeM8HEy/y/NMMAQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-su.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYoZ7LL5H6Vbv7RlIkBeK2GTTKcGxHEObP2SZkJxuYQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			T296BjriNGee+TXdYkut02pi6kfJWP5GuKhLup5JZhc=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-sw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-szl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gu/aJUsFb9TOkYbzPf+SuEnOxxsjjqUnOlP5TMXWvGM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-szy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yV08qP0sX4V1zUldcOWMp9BcCNFwsk4tTQxooQhxANk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ta.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4eVrXV8kRpR3Ky+xvnw9FJy/xUoP0hPVzFoOuJbWaDE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tay.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yV08qP0sX4V1zUldcOWMp9BcCNFwsk4tTQxooQhxANk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tcy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-te.json</key>
		<dict>
			<key>hash2</key>
			<data>
			c2Yux0Sq1gfs7XV5JJvYsyEGmIl1lIY+M6fwjnQfHK8=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-test.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7PwqlgPkdt3WSf8syEqJmZP+zn/ehb6GjEKX+7/4Vro=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tet.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0eV+I8sDq46w/sJUM6Q2kdrGtHXpBi6iMYawTN8at0Y=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cWCRfl9Lc65yyw/wz+ZIi/l1w3O18gKS5xbi9eDt/VQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-th.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iIibTuQ9r0wI2kQ2k9OFgTNa5jaI1Pz44rlL0K0wLvg=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ti.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qjnAm/9pU3YuXeT+slXZoDViYsLxBKCx7/zC96KOwBE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-to.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tpi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0ZrGO6npw1OtOyaphijpOdFa7sAXuicHnzVK+lbEyEU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-trv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yV08qP0sX4V1zUldcOWMp9BcCNFwsk4tTQxooQhxANk=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ts.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EEd9zps9Po/IGqb2uqj51liHuj7aiherJ5Ts2I2SKzM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tum.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ty.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-tyv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WJw8Yxly6JRX03En2itR3blAZlDNdEVFwImvg2E2SdM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-udm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			x/lP37rqO1ha9CR0RH6RZA/kfHJ2h9+zw4KCJEzLJjw=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ug.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-uk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Mf4Yyw0Ja6Jt7Gw5QHjhHujCgqmJl63dtNLdPEl3guE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ur.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CT9BZ5WY6SHiFiZgT+HgdmMfp9mG1c3xnXCxjTf0Das=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-uz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BYXaDiyZi4o6OcbuGEMs23t39npqqMqbCpMjeVK1Dwg=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-ve.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-vec.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s/ofsp+JSdR/J2FMdfphn63SNMGAudhJkI+a3+Mad7w=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-vep.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AhRet3vtTh4o2tARO7/euDDJx9U2hIerTypIy0++p/c=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-vi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SD2CbATpv9P10S54fS6ZVOkFakVNLJp3L+KvXrEZ0bQ=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-vls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uonDqk94hhn9zZr/UYlA0/Cg4DaOHhTbAbZJ5oDCDjI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-vo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-wa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/ErBsW2IOMDBuK6kLl6ndI1vo/EkRGQZVFaR1NS8/bc=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-war.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-wo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7fCbAT9OgvvSqLIgjeKcw6BQZ0AeGOYEoWsqBH+d/yM=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-wuu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eblEJD/oW1dGUDrCyBKYOxqhRiycQEY15UgBhfpYEm4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-xal.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q+qNV8qYYcjKVIcf29G9BC7lIhnePoPDR0aii1E8er8=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-xh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-xmf.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6JM8dhi2z2otqdhGGtFWZO3Y9YadU1KzxwGiPeUgYoo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-yi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yCQ+3ov4mGKum4LAXQj8zgJoe1Z8yflmQBHuJsSyBJo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-yo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-yue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-za.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eblEJD/oW1dGUDrCyBKYOxqhRiycQEY15UgBhfpYEm4=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-zea.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uonDqk94hhn9zZr/UYlA0/Cg4DaOHhTbAbZJ5oDCDjI=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-zh-classical.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FELPHPBmxISSBcDhL5fu/RVQe+R1oQCOYCu/+WstxNs=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-zh-min-nan.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VBQ7J4yKQflXe55DtC3FdzBk3UKXWz4984VPp46esmU=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-zh-yue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-zh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			juE9i2ivxZ6rIdtAWoqdMJsDSYOK4J8mkHefxLuucLE=
			</data>
		</dict>
		<key>assets/codemirror/config/codemirror-config-zu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X3RE6NIOz6Iqt9nsglyTmOhNnY34DkcCj8CySW5KJgo=
			</data>
		</dict>
		<key>assets/codemirror/resources/addon/search/match-highlighter.js</key>
		<dict>
			<key>hash2</key>
			<data>
			OqdzzAEAnj3LTg5Tjd3SS/+oyJ7PNUNO0AQIjQGWpxQ=
			</data>
		</dict>
		<key>assets/codemirror/resources/addon/search/search.js</key>
		<dict>
			<key>hash2</key>
			<data>
			OaLZTYAOjuREyqsgJI5jptSGWW1WYdZTaDxwoLAzfCE=
			</data>
		</dict>
		<key>assets/codemirror/resources/addon/search/searchcursor.js</key>
		<dict>
			<key>hash2</key>
			<data>
			yIM68G0QKt2N8AnzJaag6oaPl36kurDsenBBm3MJTeQ=
			</data>
		</dict>
		<key>assets/codemirror/resources/ext.CodeMirror.js</key>
		<dict>
			<key>hash2</key>
			<data>
			UCAzramvnbrO1zNi9TyuRr0XcArrUE0Vhvw3hv077tk=
			</data>
		</dict>
		<key>assets/codemirror/resources/ext.CodeMirror.less</key>
		<dict>
			<key>hash2</key>
			<data>
			WrEDinT6dyQ1fhxTye5AdSabmbz7X/gWVMMrMbjIbts=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/AUTHORS</key>
		<dict>
			<key>hash2</key>
			<data>
			YxJ4kBACDSRu4ADL+ZJvJ2Dcx/cZ6f7SkcqHW0EB7Ew=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/CHANGELOG.md</key>
		<dict>
			<key>hash2</key>
			<data>
			epnzuTgDRcF6uIINS7L7MyLcLm/aNNcM/sjtu/C7H44=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/CONTRIBUTING.md</key>
		<dict>
			<key>hash2</key>
			<data>
			tmOO63L6XkzKaDwGlMMtgAcQtofKOgcZ9d6pYlt0bmU=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			o/L+Ksa0caqAxzfF0oPdBJvckDpzg17m1NLKwC/dU78=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			2FeVglk/cdRsoOhYhTJlyQxNsaMVwcGQ4ANSNPyo9ww=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/lib/codemirror.css</key>
		<dict>
			<key>hash2</key>
			<data>
			o/v/5fOniPHMAww4EAIfRBFV0SeoqksPrY0Yq5x+wSM=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/lib/codemirror.js</key>
		<dict>
			<key>hash2</key>
			<data>
			bb9fBUGYStMIQkqdLofYlVP7wHAs4wzBIfEody6Zlpw=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/mode/clike/clike.js</key>
		<dict>
			<key>hash2</key>
			<data>
			+JM5/AVZZHKAl1LR1/l95+gGuaRbgrX0yxtZVGIKFdo=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/mode/css/css.js</key>
		<dict>
			<key>hash2</key>
			<data>
			A3dmy5MnEwoDZJmeJUoj98Al4uPPcF47typ3TSLhA0g=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/mode/htmlmixed/htmlmixed.js</key>
		<dict>
			<key>hash2</key>
			<data>
			YtBvhTDO4A27QPGmzVTybSqjmNNEi8Uc9KPvyytMoTA=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/mode/javascript/javascript.js</key>
		<dict>
			<key>hash2</key>
			<data>
			JVE5V7hEM86cO1BxmUe98oLJbQPn7Fs+YvF5MKl+raw=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/mode/php/php.js</key>
		<dict>
			<key>hash2</key>
			<data>
			5GC7UR5LMpPOzLZ1rOb+nOMX7Mgqpfo4ya2uUmN4X0I=
			</data>
		</dict>
		<key>assets/codemirror/resources/lib/codemirror/mode/xml/xml.js</key>
		<dict>
			<key>hash2</key>
			<data>
			NxOvsagQgFp4PUFHqDHjO7xGEJfevuI8fuj/W82MGj0=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/img/black4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			uKMjsFbP9KuwhfH4lqmCG+HbUB0rsUDV3O/p4CZe/Ho=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/img/ext2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b7iaCai3RFXbGN4kLmqY79bh5Omx6RyNSwInXbn9r+k=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/img/ext4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KcrAoWSjExaljtm/M67WarCCCLM1NyrAmo6FzCV5Vog=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/img/link4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			cv31N7AES7yAeTPWzfMwQc2gvwYyBYtz4A/AbSHa0U4=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/img/template4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kUj4zmyVzitdXSk/Pu2npfiESkzzZOwMd+ZYtOKIHPQ=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/img/template8.png</key>
		<dict>
			<key>hash2</key>
			<data>
			gJNO9AN/3V5IETahxpShoAJIgXpDfUx/hLuBxVCcPGk=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/mediawiki.css</key>
		<dict>
			<key>hash2</key>
			<data>
			sNQFY5E/iRvhM/Xqarp7tmokiWVy2AV3x49FbgxoKW4=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/mediawiki.js</key>
		<dict>
			<key>hash2</key>
			<data>
			3jZkG3yPni4CtnWte0N3Bhoq+FvxN+p38XtH19sOsJQ=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/tests/qunit/.eslintrc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Hww0MSjO5ImgyfcOuFzxC8d3uD6TJSzi4aEbr8QXgs0=
			</data>
		</dict>
		<key>assets/codemirror/resources/mode/mediawiki/tests/qunit/CodeMirror.mediawiki.test.js</key>
		<dict>
			<key>hash2</key>
			<data>
			mw3DLNGhEmwJ0PnsIWo6a5GVyEgi6ja8cNZ3S5hGF2g=
			</data>
		</dict>
		<key>assets/codemirror/resources/modules/ve-cm/.eslintrc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RyjOKJlUEgk1ZKcl5XaERBls/uRzlIAb9WRIxWTC6KA=
			</data>
		</dict>
		<key>assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirror.init.js</key>
		<dict>
			<key>hash2</key>
			<data>
			jB5T/LkPZGhgqZlOlctxIIcG1qxzEa6sBaLDs3ctlDc=
			</data>
		</dict>
		<key>assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirror.init.less</key>
		<dict>
			<key>hash2</key>
			<data>
			3JZmrB3Ag7lAc0Cz3MfOve3Rjv6BWn892+CcGFVDf0E=
			</data>
		</dict>
		<key>assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirrorAction.js</key>
		<dict>
			<key>hash2</key>
			<data>
			3iDwuiJ6BxJh7goP0uAmBMKEMXDXd1Hj/kZb3Hmq5X0=
			</data>
		</dict>
		<key>assets/codemirror/resources/modules/ve-cm/ve.ui.CodeMirrorTool.js</key>
		<dict>
			<key>hash2</key>
			<data>
			nrDxVRxalkVdi8RPlgcv9S41nAR6I7v8f+2GdADO6rg=
			</data>
		</dict>
		<key>assets/index.js</key>
		<dict>
			<key>hash2</key>
			<data>
			R/TuSA4Imv/7xbcp2cLj2+Cb/uygEOeIQKiiEO6vP1c=
			</data>
		</dict>
		<key>assets/pcs-html-converter/base.css</key>
		<dict>
			<key>hash2</key>
			<data>
			wmR113aU0O5+kRxHzLv0qdVMyvKwBnOSVPaE1WjRFZs=
			</data>
		</dict>
		<key>assets/pcs-html-converter/build/PCSHTMLConverter.js</key>
		<dict>
			<key>hash2</key>
			<data>
			hjoD10vjm/EtwZcTUFifAwskmJDEixXTsxc3l8ZjtiI=
			</data>
		</dict>
		<key>assets/pcs-html-converter/build/Polyfill.js</key>
		<dict>
			<key>hash2</key>
			<data>
			2F5lkRGKN3v0xDSAeTY3ZaZZObdKcKH9SlzqFwXYfBY=
			</data>
		</dict>
		<key>assets/pcs-html-converter/index.html</key>
		<dict>
			<key>hash2</key>
			<data>
			LnNRCPmo+VSWt1Avrx7bshVLRu1PsV5gvj5ugQhnr6o=
			</data>
		</dict>
		<key>assets/pcs-html-converter/pcs.css</key>
		<dict>
			<key>hash2</key>
			<data>
			UJjhVgEO/Zb2AxbXaSJ2+hNpxl1TNxVaaXGMNuBH4zc=
			</data>
		</dict>
		<key>assets/pcs-html-converter/pcs.js</key>
		<dict>
			<key>hash2</key>
			<data>
			hQ0uPg91i52JOaZ2VPQ0LpKXPnJ9vpM6xUe2AcBGnBE=
			</data>
		</dict>
		<key>assets/significant-events-styles-base.css</key>
		<dict>
			<key>hash2</key>
			<data>
			CGc1O3DweJsHmQuzM7Qj/tAXHiLojbOqLIA8afX1Z1g=
			</data>
		</dict>
		<key>assets/significant-events-styles-black.css</key>
		<dict>
			<key>hash2</key>
			<data>
			dkUeVYXnJwsDBu080HXJ++wgiRwuVqMhdO4Jofe2gIA=
			</data>
		</dict>
		<key>assets/significant-events-styles-dark.css</key>
		<dict>
			<key>hash2</key>
			<data>
			ffmWmvRLXqilXqhnxRR9hWQPria1w3sjNZxgkSoC/VE=
			</data>
		</dict>
		<key>assets/significant-events-styles-light.css</key>
		<dict>
			<key>hash2</key>
			<data>
			6Osztv9RZELf4RASYcS5GIzxbXrJAVtUM9tGSU7zqQw=
			</data>
		</dict>
		<key>assets/significant-events-styles-sepia.css</key>
		<dict>
			<key>hash2</key>
			<data>
			C2x2xkkIOMKOhpFwuwUVdEzVw0alDx1hlemzXJGwMK0=
			</data>
		</dict>
		<key>assets/styleoverrides.css</key>
		<dict>
			<key>hash2</key>
			<data>
			yPE8hg4RyQho3izdgacdb5YgOp78hv5o5qSdsrDdEmg=
			</data>
		</dict>
		<key>ast.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nRuM+rrim9miPyWDoBHK9MGT5Oxqeu2mWWdwxoy3+B8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ast.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			sUMuuZbiCfiK9KoPcC0ME9mzNgsGzo/zFJQ1sjywuuQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>av.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			6U00wkYbF7XTUYylWaJQmclil/X+ZXQ+DOEQdlo82f0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>azb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ba.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bAKSkexc8vAFs7I+dCaxwUZp3BBPH1AD6qyjeNc6U7g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ba.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bgn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bn.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			neIqm3f+ySDKX/U/jTYD694Map+gFH6Sej8i+Jc5HeE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			knIHkReBFlRRE7dRDhVQPvXRcwqG7mg11roD8WbdpDA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>br.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CSF6thMJ+bNHcBwnfRae7BWfFKpJeIDqLxmEIvPRq5E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>br.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			la8NVS4jKDDiMG18kcvayganj4GdsHg4Q1URqhvn4w8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bs.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RKeM8Zt8tlCZOtLUunZR2hCYECpPu2QHr9/2Ni6gdeY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bs.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			IUuDaGigT3zrndaPRK/t+Ealhcvn/VWgfiaoWqKpkYQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SE7uU+dy9KqCupOG4MWz955szr3Nhrb93Txs5G7yt3c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			npNhNF926TVjO9EeKT+AXr3Qch0cDBdt/0e6GbEsKaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ce.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			eOcV22XJY4RUiOdgMnzHxEI/2jPrw6ywlJV7TxAVzMU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ce.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			dXazVbf7HiNqg2ObuQN+8r3mkg7T1unS7/eCh7QiNHQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ckb.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Fxtw+SQedkr3yGFUwaDO6NgHSq07jwwv+3b3SU13PDo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ckb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			eo8AgS/Kht7TYU+2WJ8Ji5eZQHyF4baPML3upfg/pn4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cnh.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NSjl3aYiqVHTz+IYdvXRqMqthtrUFgTkj0J1wo/7DkU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			qyOeoFJHZuTDxl5DhergnWlJJfcE45ZQ5zKb8FOPOOo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cy.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aQBRcw1gwDiiNUPbU3axfIK4sDUkvy+s5AmEzYrzMLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			akoRs+PS8x5Jj4aRt5wgDQAbj8oK7/cyVWo4yoIvhjw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rRO7F7BC6an1pd1LPAwaVUl7EzS7XPicLE859UwlNqU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			CY7FlJFPOORiV9OOW9By21AH0ZnRfR8d+cbO2Y9sx/U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fygGjsuxs/d7Dh7gqSbvHxaLSVAIhBZKZxwmYSmqT5g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			en5YdbnN72kgyXmM7w+hNYE1c99XthM5A/90N5/tQS0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>diq.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jYNdAUWR24asD/MYehGhzgGwY8pE2cTkl3NmglHjoK4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			MZ7l6HmtMZowadPGgRE236SDwgdL4EGfZxQgf/asCPQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>el.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			HatNriqunp163xwqPyqm/FSE5TFW0Hge5yjIriakcq8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			67nBHLTzEfGwp4HBHf6EBV9yJNJm+V0gPQRQuJiR3wo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eo.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2LjAkcqhUKoQKylpVKK5ApHbT2JfP8AdI0SWuYS6CP4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			5gn9lYHQEpzDN4gFffR96SwpXMOycquYFDeWzv0Ng38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EN8yrzRgm9r6F/01nBW2LWcpTXz+nzFz+0rzjL+aHec=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			yBcoUKvPtJqNiek54xdlYADEVi12DDYP/gHGtx/l758=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eu.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			948wgRLVA+aFBZcdXK1G630SwnrYbLunCBTGZwds2pU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>eu.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			rGipNvHU9moGlCzIA4X+uT05O3oe/ls3wNQArAl3qjw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ji7qxfQBvh1F9J6guzIXywrtbh9rxV100xkng9GKdRY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			56JmCi1MGu+NadFtncl+nnqBH2yTIrV3mPTPM/jVe/M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Wx7zC8q0RVfzyVHCVWbifYm5MJwmWjfoxamfgrUQeOo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			YxFeGlIj9ZBtwEc4AvtWyWX7hnTK6gjW3eNh50y5EhM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fil.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			6U00wkYbF7XTUYylWaJQmclil/X+ZXQ+DOEQdlo82f0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fo.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			TH+W8xIOYAjzy/wmrJh5LT5x30vzKq1k2toOApfNRpE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hcLWSwGkFHnrDyngvWRZpCsVqPPcTlFMUHVbN/32YNs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			IdGFY0ocSMbC2FwQ180Pur7PMrCBBfak8VrBQYnHpgY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ga.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/sCeqvmz37NieyG1NewfteneRFOXr1mb5sQWVqvX1ik=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ga.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			9mCizxvIdStftX95b5+wSYunoTDiMbWaUvomDOtN1Ek=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>gl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			itlyWvw82GCULjSwDji/6ft3x25PqZvz2dB9WnTOPqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>gl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			ByvwONe0586DIIsYwL7gjv9GE4FtaaIs2gZvzrLLLM4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>haw.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			c5s9Jm7NTphuNCuRT7JY4+7hrR1r75RDTkcqrGHpwt4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>haw.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			+T/OBhWLUJtMBoq/L3MjOkxhgUVcq+8p3F4t68fxoYU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>he.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h4KtlteiZxWhlazY8SNuAktyWEinCb+DDWJ4s3Sepsk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>he.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			Y5/5WUJGoAfcWDwLtDZ/gBM9HzTmPhx9VU3VeCVzFk0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jdhY88u+jXLK47kq1dVsT42zmXzAla9jauVwyloqz5g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			7jubhQmkNzIQmuj4sa+IQmT9NgnIc0FQEL81oSbsjCM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GJMu1+k1eUr9pZygUgHS9mAB6o2U6qpTsf6LinVFisw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			pEdFPep8u5ETFJbRvw/YAgQ5LmyYDDKR59oLn+kYozk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hrx.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hsb.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ZJf8dtZ3CQLh3ESNhrP09kyYqn0IBJ4rMt/CUogz/QI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hsb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			fH6ZaOqzW+H1VRvQTztvLqn+KbivvbKG+2kj+dOxRrw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3+dNiqffKvGKQALAhhCTGqdcJWx3X5GHD4B8rxHae6o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			DY6G6/+mH1sCgqSL9ulbbQK7xHxaPEPbYhUpKHKYZPc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hy.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RQYtrKRM7XylWkEtGJXniF7VP8Up9WazyJP8uMcbNfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			gyXc5RM6dYVIxiBIo46etf3akbfb3NEhceS6Zsp1zF4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+IJQ2vG9/en2/FAfj6gCZQSgkUZVt4EfdKuFBbUc548=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			hubHVXQr95JqBl6ig0lHEBPFXjXEQBXwB2XjmdmW8wY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>is.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IbkpGXmZMM28OP1B5K66h9bx7+2cuXfXOBlJ1/zXEgk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>is.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			XHtIIxRet4EDXUqXbBHE0Bq5k7IsbBRcI9yaH7cm4vI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LoNY38bDl2wPjMrUbfavadqgYN3d75xACRczH9jXrUg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			BvfbK00r791eC4KaULOzVFHA7TU9Xe8H/ogvO8fqc6s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CkGnyJ4XRDST9PSwYuuDcG2r8dii+abZX7Wl36kK10M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			7CCunQhFTRU++ZElEFfbbnDAcS8U/qZorz12+XsMsKk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>jv.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k0iZ2qa8PVt2wmfusEVkCNaliab3tA3iWb/EreRT+Kw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>jv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			fL+pm/rinaTHkCdu2gANWM/teMZCbNMM2S14/DqnJNU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ka.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WQhXQdIEsL6MCwgK8Y2vUoccIchgk+/B8aZAx9Fe2b0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ka.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kab.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			46gf9BMa4ymbMWRPAl6EJB8rw73WJu49oc9DDyQRyMA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kab.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			PpCBs1nOieGFnsfiS/TtsC642rwrOH3roxvvHZLq/6Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kcg.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SdsY1+Cqx++Hx6Aj2rIDdKN7rfn6CZTGRqHaSnrmmLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kcg.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			GjY2AdRscNWio76tg0+LkQWbJ8pwJCnSX1Kczr+7Tlk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>km.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V9TnW/OrFJRNRyRquOZSaARnoB0KejzxAWwgQF6Y1As=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>km.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			KcB52/w+TdbYG1x/08l2ds2XRHTD/Ql13JO4KYOUDAU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kn.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zG4/CUUV9ld4sHZPeKsTC1u6+x8rOqftMDl8+4k1Fp4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>kn.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			P4eK4lDsPaGcLfsNhE3osixDl/VnPz/kfCACKv07ts8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			0QnXNjyAX22TI0J4qgkWDNLXvqvElWUqudNxEQndzOU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>krc.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			f/X4juIw5lF06mDh+pFFtTT0lp0sOFXbZEOGqkat5DQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>krc.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ksh.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V6e45tkqpq2WY3S55HyT7qqpold8ylZFxfovGKOH9Sk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ksh.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lb.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4PmGQCy2U04iufKaUWShVS6yf5ciTbV2sccn8PoFgr0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			iCFiVua+lIGCdPi0E5B+uVJ4ZOooLp4nRYpLGwAeHok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lt.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			z9v3+mWN9n/ymVZaMHak+fDtCnecQaGVeLdgFXQG6L0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lt.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			TKIYvxf8SVu+T1xkW40hs7qQ7KtJqfBkw8wvXnAfNvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lv.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xjSJh/2bGPxpU4Wc5WTvLIGCdq65qqaXAQYxtQE0gig=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>lv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			6HMCqNJkypyNBJi+mQF/ykTtohybRkkZkVtf4MK7YT0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mai.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EQZJMqGSmvZZeGPom8SZZkBgKZDWmFQ6iBrztpYMHKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mai.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mk.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xhq/jO7dvgOG30tRSd356j4Gi+UCcKe7GR7IKHWOg4I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			qCKsyAWZofUUyFEnrTKnHQn4Vb+iPu5w2kGxf1l9BK8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ml.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IUf4Whgp1tez2DFWp/qh/X0t+Ci4AQZGQWdD+nMfDkc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ml.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			4rg97K4GcoLUUihfDJdpg+paVqisi0KscnFkZyn989U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			H0IiqPiEZ5IEIF+3asNSdtPRmJDoRh4TAuegB28VNnk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>mr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			/bEANG+g5a/4trxdghjMf5/+jSyv0B6W2fwPX+CRxFA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tDxopG7te/bPTlotHbjqLPC5SW3UD59Pt7hT6hGcvvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ms.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			sLncV1Wztw7W3u5mzyMXT12l5g+lh8lRNqN6PTS9zJc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>my.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+CLC0nDG4h0GIFAeT8g68K1aRZRY7Z3rMs7exdWw5Z0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>my.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			CyO8b/RTu1ie78/iIXpa3dPGIwSzsSJiNEPEFp+gMFI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			YuGsl/at5Y+34yIu/oMl31TG+MANUiXBRmzVxGr3Dg8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			wJZW/LkCU6F71A/DvUoqJ+TB/EDEdq0mRpO18x2Fb0I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ne.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qEb/fI76mN4wFGTU2HLyg/nJBGp+gGq9iPHjJQCXR1g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ne.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			TWTZ2E/+Si/F0w8P4CZDKc6fZn56BDjSTqui8IIIvSc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			M27tNGmXKtHvRRojAimK/SnKta7SbArg/ziYHbjf9vo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			JqcZMj9nA5AN7Eqym9EzqSbveNT2xVRAbejGuou86wM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nqo.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kIa0jxwdZ+31zAgw9xlBK1O+H/S8MwJkVSJs59+BbHk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nqo.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			UytJ2HCbDOCKYll2b6QxZNhiW69IcyLQooKLoL7Gq1o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>oc.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pmx+2+eZOJxk0JN6EiZD8evb0xZm7o6GbwXo9NUICzk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>oc.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			KV+Zs91TQo6HMrfAB2dDxY9C9J86cfp1aTtm5gIxFRA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>om.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qCrZeA+MDEIZlfxa/LVvJ7lADBYYGVLuxQKWCnKjjhk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>om.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>or.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Z6xihHBYqYlio+y3SmoqgUuTQx02YipWBwxf4Aq1WOA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>or.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pa.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			djwjdGJEBovLlL1l8lTio7ZXBU1NX7pXkscH2rZ7p3k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h2q3pJ+OL2pWIkUTVNOU5XBwr6IunCUduYL+gF0st+4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			ZoAdBIFiwxDjxRQFy1qoddYG3ExctcoRyKHJX4GIsKc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ps.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NJH95vSPJa2QrdvXX7SZdB+gz7V1RxURz0D5DqPkNuo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ps.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			rBFgnXM/dNb8Wf8RZOk2XrUa9jkUWaJQ3Ck90lic+Pk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NfE6oO4jUZSiZcXfmrtG32qtyfEHTK1Vi3Vz7pBM9ZY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-br.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			keydQ5QaP4JiapcyRkbBFhfxEoppzquQSr3/1dogyeU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DH+gnkIR6UafWY7hxspHcVJg0Fvh6VmNSk8Os8Ubax8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			HeQuJpXG5ZdZMP4Y/r9qHYAkFVDeTNndsJlDgiiFrhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			B8H2FFQeSv8Og/rXU99mWJbd5YFsR5wHFos1SSuJaPI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			dVD5q5pkWLCGBV8BPj6x7HWC76EgYJav5jbW5mQhNJw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tkQw/wjDZj0oGoQjKweBFymWDaqTHJvukdJaAtjTOIM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			OJnOvC8d+2SW1KnTc3WrlrHo5Q6DsWpFeBmjuxXQUNg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sa.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			d+LIHB2TRC8ra1J/2J9AKfZM9cK2q9MNIXjBFhj+f3E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sa.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sah.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ZIy/0GTRAUFeCFtf7E+VU0EtrXd3UF1b4e5NcDlhex0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sah.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			ws+ErLre69Pv5orPEfQhWXqBljAAlL+tQPFpik3v0pY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sco.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3mFdecleUxqqYKenSMCKm6aAONZnlXQnEY1d4uRUS3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sco.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sd.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			H1VSx3geTi79tcIMuz40+P5rArmV50uPi0bJUcLCdd4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sd.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			MSbwUEKYQs4124sNuc6He781XkCsu5s1fkgf9F4+Xz4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			BTkBCmJ7oEYYGR2XYp7l6xwUvzdwnSYgAiWaDhcxseA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			XOjtBDSGuF5uJGQbY6+7y0bU9KJUF0lHTGQPNMULe80=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2vRvCmKD/GQkkORKHEY5BHiWVn7HeMYj2qEvYwi+F40=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sl.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			CTtmI30mzRFaXH1qfbVGgbT5CrwYepbMD1UuiKcFKFs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sq.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KTLZ150JnqrCuqI9ILiN8G5vWvpYXL9Nt2ArgLw5+h4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sq.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			VmIpBh28Wdp1oVNVJUMwVBTM1ap1GY7WshFIjjVL3FI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sr-EC.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UnUNVqpxUsCHRyazJVYI9esfuuN7GtLrmwB8jjfihws=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sr-EC.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			C5WpZfcE+Il6twz3lWhRNkHKe4Flh6Zg4OSkeUCLbVo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>su.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bfjjsT3Jl5CtWkLFX4oOLB0ZKIM+mjl4mK6QRHlIR9g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>su.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			BbONaABZPoMyy639b5fjJvhqlbpLk4Lr+Th9PtTfQqM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			sfb2ibTLv8UN1cfTa7ayS5D+kG916dfk34m28wGZ/5c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sv.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			N6aeF4oLAxaCPV/Ga2N5ZzEDJ8WKG32TN4/qi9EKS/c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sw.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8xDB0+52Ej+Rds/mx4/uv/kDH5pCyeCi4NhTdaNBkfw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sw.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ta.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IOU7ywp59LYHSgLjeif83NAcz7+R2PxUpUJS0vrcop4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ta.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			i9AlU5CzDk3eG+aACkOdBrkYI67a2DaAzRKgac2nuok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcy.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nsUhxWV7QLDLu2ez9syThlpLLzVgsb73+8MzmhD1Ma8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tcy.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Zz8EVdzfRkKpi/5D08xtSAXgVqtB59LK1N4PUmWGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>te.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			BlCShfppW8NkN84A6JZGsutQrMD2evyoSV6PpMKBXUk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>te.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			5At4XhDgY10GzUWw4PSrcXW0x/QB767zR4EgJSYjNpE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tg-cyrl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oQchXWZVy6tOregjFgSgGgEVzX4GSmK5/mjW8O5vSWg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tg.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			6U00wkYbF7XTUYylWaJQmclil/X+ZXQ+DOEQdlo82f0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4Y2d5INnp27hpbXrw25bKb+CCP5oYYqXyA1DP7rDVVQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>th.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			fGXb1psCuxKlsf0Sg9i52hiF87Q47hAusamZ4Xam9pk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ppdIxYOiJo5381NHewV2Pb9O3suOlMgpWzXN9Ee99rM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			yzZcBrY0jdGwlzzuP6KXdg2TIhFkiKtnSxGvBsfAeXk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			DNg6cqCwQML6sA7M7fwnPHNAYz4hL5hMgdRJeHfbwyI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xTwmBx2yZltHmQH6s8mL/LyiSvs/WxyRqw8zM/gjQ/U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uk.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			vxGNzzWVZGS+/ZiPkvSwYf92Zw7ZiRm2mI/rgOhSfCg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ur.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Vl6VXPos4y4yH/t+NIp9Ub38+kOV5HcBHygjZpNvXPo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ur.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			nkzjZSqyM9cYzNkebyRZ0hDxn1WZUdkt+/LSg18/lc0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uz.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			q5oDOXco/bAxFyI9ArrksJCKXRvuJ0n6Q9pmZ4LhppQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uz.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			4SnFHCpKA7+t2iBE7IUWEe68de1+74ZwJE/nAV4TtSw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vec.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nNQOX+Ci2vjC180YFcUSUZt18E/exba2VRnNckPKhTU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vec.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			/o6AD17KxaLCc704DhtZTs2Yev3tJlVVGAFWk5Cwd8k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6V4/iNGbje0rZNixKgRq+lBzfXXLCOqsIohiiTn1Yb8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			zPZGRgB/7Ws9cPrHaVRXGdF3zjjYWsrEqeSNHCFVQIM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>wikipedia-language-variants.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Adjr/SzqvKf4RmT/jVWpnNxkmWQLFd1+mN/rUKwZ4aI=
			</data>
		</dict>
		<key>wikipedia-languages.json</key>
		<dict>
			<key>hash2</key>
			<data>
			o+vTCvCcwzzjp5m81o92Epsgi4oRLFL8wLncO+SKx3s=
			</data>
		</dict>
		<key>wikipedia-namespaces/aa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/ab.json</key>
		<dict>
			<key>hash2</key>
			<data>
			p5hXMDHEdwlLOicMq6IRMjkxS/ag+qwVM8Ka+C1zRNk=
			</data>
		</dict>
		<key>wikipedia-namespaces/ace.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xYILm8W7hl1djhib/pMgzX3jEWU67jOhrQBNeDQYbwo=
			</data>
		</dict>
		<key>wikipedia-namespaces/ady.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RhcWBJ3NDIRUNW9VQh9WLIWci4pEa8+kLgq+8YV40Uw=
			</data>
		</dict>
		<key>wikipedia-namespaces/af.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VZCPipdFDwCPh+IOwLnXxwN4oKXrdpNsS9bfLgjTJzw=
			</data>
		</dict>
		<key>wikipedia-namespaces/ak.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JOX4W3drpgfbd3qm4zt8qIbEKGCUQjTIJmStxjQzAMw=
			</data>
		</dict>
		<key>wikipedia-namespaces/als.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iheN00zcE+tM/lmaZip/V4qwUxOhS6aG9z8ExSxdsHg=
			</data>
		</dict>
		<key>wikipedia-namespaces/alt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			h+s+XmVKtDF3M2LJndL9AEDdsnaH6N6z5An+DwK4U/g=
			</data>
		</dict>
		<key>wikipedia-namespaces/am.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VYwgF+k+I8Wk80IEv7xhhw5VB/ppsBqmWsYI6ItPdfk=
			</data>
		</dict>
		<key>wikipedia-namespaces/an.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zwfZoDVbNCiQ1u4J9MJC/fOJJGk5yZqwz6aa5jUPpb8=
			</data>
		</dict>
		<key>wikipedia-namespaces/ang.json</key>
		<dict>
			<key>hash2</key>
			<data>
			k6WrCkd1Fmp2gWC/asNoIBqVF+5vrt2h8PES7izq6wA=
			</data>
		</dict>
		<key>wikipedia-namespaces/ar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			V0M5/3CAvIdOfZp2Uh+cPy4GKADfcy1XXizttxO6e0A=
			</data>
		</dict>
		<key>wikipedia-namespaces/arc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SyBM6gpkZW9fnclbwR26KUE+i/7Drp9lqF5LvOoJ9f4=
			</data>
		</dict>
		<key>wikipedia-namespaces/ary.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GWUUatW9gvUW7Wj0gi8vBJx+vN+GKUtU+Cz3KJlXCiU=
			</data>
		</dict>
		<key>wikipedia-namespaces/arz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DDo99mRZiRDsaS+MMUiZJ8OfHAyoXtbXG+gKsm8BSBc=
			</data>
		</dict>
		<key>wikipedia-namespaces/as.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eMKyRnYi6fv1a6OlO43/Gz08Mtu9D73fUMeF1enekhY=
			</data>
		</dict>
		<key>wikipedia-namespaces/ast.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LW6iZfV8gsJtxQ30ZP5VF6JmVks+LOIQz0e9mv/45bg=
			</data>
		</dict>
		<key>wikipedia-namespaces/atj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6/EecLgfZCh1jW8r/dYTwkBb0rXY4HAFLn0sGx5Hbgo=
			</data>
		</dict>
		<key>wikipedia-namespaces/av.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8CDvbCt+SpuyXP5DtaNcZ9tuNSQIJJi8A6khPF2WizI=
			</data>
		</dict>
		<key>wikipedia-namespaces/avk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			42OFrod3Vzlkw9vuGgqO9Bc6RFluUSoOzjLKN5e+6Ws=
			</data>
		</dict>
		<key>wikipedia-namespaces/awa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KQ5Lk3d3mj/gBFb1leVJUKb0XHbzZtzFJzEO1WxwTvk=
			</data>
		</dict>
		<key>wikipedia-namespaces/ay.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EjfyxgVPW5mm7b2fzS/vx7AuiDfvRXIz2ZUOp02Bb10=
			</data>
		</dict>
		<key>wikipedia-namespaces/az.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yZsqNb+tcmrcKJHBDxtRAz9fS60zRkhhuHY6rhXhWbY=
			</data>
		</dict>
		<key>wikipedia-namespaces/azb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			18kiqUF5Q49aUHwZnnXmU0A3xhjwSIe51RDucaGpnN8=
			</data>
		</dict>
		<key>wikipedia-namespaces/ba.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9/zSVt6vrCJ7GQ4pWzkl6E6EqbZQ2MylADD6OjRZ300=
			</data>
		</dict>
		<key>wikipedia-namespaces/ban.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VwRFdO4MNW3FBWEcBEHfweBeZHgPaxgxJxeHEhWlWxQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/bar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TK3Z9xviNvOeUsyyHjlL7lwGw5AQJ28aNBvpr2G4wJw=
			</data>
		</dict>
		<key>wikipedia-namespaces/bat-smg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vE8UB/V/+lajY/rjjwWjOFH0oWLPN1NrakaB0aaN0ns=
			</data>
		</dict>
		<key>wikipedia-namespaces/bcl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Sqx8eegQWX8U7XGumjGxrcglA94PIS+PJil+JyfcJ18=
			</data>
		</dict>
		<key>wikipedia-namespaces/be-tarask.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l4gcvPqZPJsdRUCklgAmOa+vUcs/9o+WafGj6Y3zbZw=
			</data>
		</dict>
		<key>wikipedia-namespaces/be-x-old.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l4gcvPqZPJsdRUCklgAmOa+vUcs/9o+WafGj6Y3zbZw=
			</data>
		</dict>
		<key>wikipedia-namespaces/be.json</key>
		<dict>
			<key>hash2</key>
			<data>
			epa+YywHmZo4I6i05DDpuLkL9Ci6xECxGTDLfu3UkJw=
			</data>
		</dict>
		<key>wikipedia-namespaces/bg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5pPp4fYi17tBWQLzILSiIp6VXDIxwNdpYZVpNz8z9+s=
			</data>
		</dict>
		<key>wikipedia-namespaces/bh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GQwJ38uyTjuFHuHYXWeAugGXpeYATnxsjzCuKdMXyUA=
			</data>
		</dict>
		<key>wikipedia-namespaces/bi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jq472Bc9VCSbKO0nxLS2+5dBZn9WqXEGOjJeZ+tjBIM=
			</data>
		</dict>
		<key>wikipedia-namespaces/bjn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LnAdNUdBkH6EdMtvbu5mVd/RZVfg05VHeE4eHzOK4OE=
			</data>
		</dict>
		<key>wikipedia-namespaces/bm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			acP4yB2lEpIrEvvUqnokub9JpJpwikuM5NQsf5/+C/U=
			</data>
		</dict>
		<key>wikipedia-namespaces/bn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ANFNqhiDOr3sCUwCZVlAnpjISXQAyz1ndjplKCDjnqo=
			</data>
		</dict>
		<key>wikipedia-namespaces/bo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8eHiuP1Y/wg83cG5k17KmM+RUQYdItNCZ900osKo2xM=
			</data>
		</dict>
		<key>wikipedia-namespaces/bpy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5PcpTRGNqNqhmLfeX0ckmkaY6hhxfU/IwIZZaPiph8c=
			</data>
		</dict>
		<key>wikipedia-namespaces/br.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nVYVkJAz1XJ12Ny3Hq1vnfjFjSjwAPKuU7Qlxgv5m/w=
			</data>
		</dict>
		<key>wikipedia-namespaces/bs.json</key>
		<dict>
			<key>hash2</key>
			<data>
			P195FtiWLUeR/q49fUy1fO9zw9XNlylC+CYCwIcsgls=
			</data>
		</dict>
		<key>wikipedia-namespaces/bug.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hidnctIJulGPSeODSLjWM2/jtEpicp1MziZA6E9YM04=
			</data>
		</dict>
		<key>wikipedia-namespaces/bxr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3wXhQfC9keO4aR9RjdpLDB44EJJxABSZfj6u8rv2/pI=
			</data>
		</dict>
		<key>wikipedia-namespaces/ca.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IZDfIQk5AnI9x04F1tRXWB1w8/GsTLCnOsiWQRKfypo=
			</data>
		</dict>
		<key>wikipedia-namespaces/cbk-zam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j5SlCgNPTQ2EXF/vXxpjmxqsjAaTcvOXqXmW+bvtYWM=
			</data>
		</dict>
		<key>wikipedia-namespaces/cdo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VxTjLUG6t31MibPhCWAoJ7/2/xs5ZNsJrg0UTZiBrMc=
			</data>
		</dict>
		<key>wikipedia-namespaces/ce.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Gy8MZRBORhrs+DjS1Zf+UUqn9cnyD956T7GFCGckb4E=
			</data>
		</dict>
		<key>wikipedia-namespaces/ceb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9Y4SoFsGvzQcGUHvJ2ZK0dw224mQpPKCnEF8Xnp/aT8=
			</data>
		</dict>
		<key>wikipedia-namespaces/ch.json</key>
		<dict>
			<key>hash2</key>
			<data>
			77znLRecle3mhltkZrvNbIxNtj7NGH1QOyZQgftQhig=
			</data>
		</dict>
		<key>wikipedia-namespaces/cho.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/chr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			k0hz5WDCXC4SVIeNUkZA5+cqAhZqgDu9YJibx8QlHa0=
			</data>
		</dict>
		<key>wikipedia-namespaces/chy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fx0njJ11R9QVMR+7igNteJowMDVGxHthSbWNMuZI49I=
			</data>
		</dict>
		<key>wikipedia-namespaces/ckb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Yj+/VcpkBt/y53Ce/XAP4jWgbdm31ucZF+1ZI6wLNhY=
			</data>
		</dict>
		<key>wikipedia-namespaces/co.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BMMx70tmfp8l0btYqf/6gF0YNRm0ypvNacMhsBWm/KA=
			</data>
		</dict>
		<key>wikipedia-namespaces/cr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0xeL18l6R7aEFsCo+W/3xxUQkIuwbCMzhiyIHDyNBjw=
			</data>
		</dict>
		<key>wikipedia-namespaces/crh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Pk4c7OSFd2MzWUefGFH92ENE0Uzv9xGrWZfr4JbjInA=
			</data>
		</dict>
		<key>wikipedia-namespaces/cs.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3Xw0WcBSdr7TiR745OQcP4E6DLYTZB/+EhaoJ6eOd34=
			</data>
		</dict>
		<key>wikipedia-namespaces/csb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			NIAGYEqjMv/LJB+uZ/s0wahlOc3FjPdYcfkDhhgYwfA=
			</data>
		</dict>
		<key>wikipedia-namespaces/cu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cCnNLm7aaX35LiU4xjPbS5ThlwRT3THMljpn249FzLs=
			</data>
		</dict>
		<key>wikipedia-namespaces/cv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			a4LixZZV2TahuD4e7XxEUe8hZmU2EopRYhC42rka1p4=
			</data>
		</dict>
		<key>wikipedia-namespaces/cy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ff+7JO+4jSnoOWNQgB0GHM+Rwir7X1DRb4MboDD0ZRQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/da.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XqWD7LcX5pnNpdvq7QmG0opO+0xUJ/4Kxwm5yduISPM=
			</data>
		</dict>
		<key>wikipedia-namespaces/de.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lKCSPMVN+8DP41TDdTUZyNqS+NYtJx811/SBtuZG3ic=
			</data>
		</dict>
		<key>wikipedia-namespaces/din.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1imcXf6zBSB9zF7tCuCT37ivwvKFioJarrYYNFVfn6c=
			</data>
		</dict>
		<key>wikipedia-namespaces/diq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UEF6CUGhOqNkrln3uMgKcFTJmHxc2ryHA2/3hjBwZro=
			</data>
		</dict>
		<key>wikipedia-namespaces/dsb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			88GT+jWf683d8Me6Eezgq/YJ1PSLCprMgO1f1+aVL1k=
			</data>
		</dict>
		<key>wikipedia-namespaces/dty.json</key>
		<dict>
			<key>hash2</key>
			<data>
			R8exrUiLnpaO7OThru4o7Zgg8gsN9xOf+Vax+n+JHfs=
			</data>
		</dict>
		<key>wikipedia-namespaces/dv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			R/9joIw6a/FgMxEgVT5f1Jka3G/Plcwyx2lWoUI/oyc=
			</data>
		</dict>
		<key>wikipedia-namespaces/dz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			H346cgMg47k9BINfeYz8PNsAKYCiI/jRM5L1d8NLNEk=
			</data>
		</dict>
		<key>wikipedia-namespaces/ee.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fY8iokiWKxcyK11X1Cg70ltdyf0iMvu0G49I5dzTHcc=
			</data>
		</dict>
		<key>wikipedia-namespaces/el.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4PGiK/m/VznDqgvkhsr+R3sy0TFfzwR/0lsr4BBSn4M=
			</data>
		</dict>
		<key>wikipedia-namespaces/eml.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nstkjswahB6GYpvFfXsJYO5DC5CrQJR3qm/8/Oi8O2I=
			</data>
		</dict>
		<key>wikipedia-namespaces/en.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iAVcBk/ueEQX13Gc6cm6FPoxWIEgg9n1aWFh+grx5Yo=
			</data>
		</dict>
		<key>wikipedia-namespaces/eo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RyFBSw+Toxh+cEJtlX6rVjWwyQnkYzKVvBqoSFFrhOQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/es.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1U6riZyAvjnzwPjM/pLCa7GDjvAtg9wgvb4feZILco0=
			</data>
		</dict>
		<key>wikipedia-namespaces/et.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IpZTz3h/0EHI0GAeSMq/9ITAvqIuhHX3AS7FZdE8o90=
			</data>
		</dict>
		<key>wikipedia-namespaces/eu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			auYmZB3W4Q2c9oGA/AQN/nz+5nOdjURvn1CCfChrUWM=
			</data>
		</dict>
		<key>wikipedia-namespaces/ext.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yiR1592orT1EeIXuSsOG8r9SvSTg4O268jT7lYiDmQg=
			</data>
		</dict>
		<key>wikipedia-namespaces/fa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AdvO/JhN0RikgtDzDkc/wozd+E7puV1NKts/5sqw3AQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/ff.json</key>
		<dict>
			<key>hash2</key>
			<data>
			905lSbnHY2POzgaOsSZyic7LbyYdSnw+5wv7Xl9IuBg=
			</data>
		</dict>
		<key>wikipedia-namespaces/fi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KLsOmQgxS/22r8y3PzRo9nbDzpWl4zaFm5Qrm2y8yVM=
			</data>
		</dict>
		<key>wikipedia-namespaces/fiu-vro.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+dgGJSn4TZzedivGM7PWTKlY9rMinxBZlt1+JCRl1Ao=
			</data>
		</dict>
		<key>wikipedia-namespaces/fj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BDMVvTSa0fm3fRkuzhKH0h7V0pNBjO2BK9Lz3imbNE0=
			</data>
		</dict>
		<key>wikipedia-namespaces/fo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WdEg0593Y/yyIqiFpxM5mHDXO06NBi36l7k35d7ahFg=
			</data>
		</dict>
		<key>wikipedia-namespaces/fr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Y0VoSP0xyuOhTWY1nTXpmDvGGDQ+KyVGjE7ssDgQc7Y=
			</data>
		</dict>
		<key>wikipedia-namespaces/frp.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+RB+zRyjdMgZHj3AbNDyD9lAD6D/3ZqQhogPCAwJoDE=
			</data>
		</dict>
		<key>wikipedia-namespaces/frr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qIpzoiasgt0i4IGxwE6uOYXJAHQc41WBL5IHJ6otqDc=
			</data>
		</dict>
		<key>wikipedia-namespaces/fur.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BAQaKaT7znH7oCmvOXghrKazw3CPmnHHSceCJNquKdw=
			</data>
		</dict>
		<key>wikipedia-namespaces/fy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DEw+U8dqbHwwZJlscPuxYDoCw50sc4j0eLpW9vXYVbs=
			</data>
		</dict>
		<key>wikipedia-namespaces/ga.json</key>
		<dict>
			<key>hash2</key>
			<data>
			rRcHaRmi/vYglTLUNnC5fnqCC/q7MSz24qVR4mZESTA=
			</data>
		</dict>
		<key>wikipedia-namespaces/gag.json</key>
		<dict>
			<key>hash2</key>
			<data>
			wCLR/+9nZwYf9b+iTMDjO89ntQUVZYRSwap5B9r0xL4=
			</data>
		</dict>
		<key>wikipedia-namespaces/gan.json</key>
		<dict>
			<key>hash2</key>
			<data>
			sA8RwX3/qfzFSRy270iBRcxSdoF0lQ6v/OVyAc1P8gE=
			</data>
		</dict>
		<key>wikipedia-namespaces/gcr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			liZr4WuVSCJjau4efD9VlWJ1R1J9PgpLCkAkJrtqOhA=
			</data>
		</dict>
		<key>wikipedia-namespaces/gd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Aq7z5l+ILgq7bORW5yOHxDUMGJLansGI8csdhjlL78c=
			</data>
		</dict>
		<key>wikipedia-namespaces/gl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Yg09nzxYO8IXYyC7kxgEvSvZosnVOUTsw2j/xFK2xCg=
			</data>
		</dict>
		<key>wikipedia-namespaces/glk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LmUzWdVRaE3h2wd7Hs4YF78Yiq9Oh+MkLgxF7vgrOvM=
			</data>
		</dict>
		<key>wikipedia-namespaces/gn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7N8F6aJYidRcEv2p1JW/ONPbgKyi3X2tg6BMmPJPf6M=
			</data>
		</dict>
		<key>wikipedia-namespaces/gom.json</key>
		<dict>
			<key>hash2</key>
			<data>
			C/3LG3NK/uEYHBQ2zmyKhxvwdNyj73DSJS10KycugEI=
			</data>
		</dict>
		<key>wikipedia-namespaces/gor.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xKj14VogQt8xi0zWjLSt6aLa0CUtpLGuqH2ACqjTq5M=
			</data>
		</dict>
		<key>wikipedia-namespaces/got.json</key>
		<dict>
			<key>hash2</key>
			<data>
			rdi12g9yVgnOtK3JmvuYFTUO4NVOzNjGIE1SqFQnmPQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/gu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			HxA6bHJS7mIZCK/DUVjW0QCZlTW1lWirFNIwsz2j2LE=
			</data>
		</dict>
		<key>wikipedia-namespaces/gv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KAlZDmL5RtsGCzFMOKWmo4WIF1WBV1a06QiNwqKQxWA=
			</data>
		</dict>
		<key>wikipedia-namespaces/ha.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qpNVDfpnF9m2b/k3jFMp64ZzHanYF1rSbBJFR+E843I=
			</data>
		</dict>
		<key>wikipedia-namespaces/hak.json</key>
		<dict>
			<key>hash2</key>
			<data>
			MoJhFzKO9FZN10HaIejxxjeXVeAdO4/WYGXuzVeeDas=
			</data>
		</dict>
		<key>wikipedia-namespaces/haw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OE6dkLSkIMUNZbFr3SwhuaW99JTZx5ajxh4cVSKlI6Q=
			</data>
		</dict>
		<key>wikipedia-namespaces/he.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4Gzrf9e7cS3CqkEjkWSW0h2CcFDkj0T5MHnuQX3ag7k=
			</data>
		</dict>
		<key>wikipedia-namespaces/hi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6eF1UX21E88LTtT4D9ZRfqm7HUHU7hBAbK8wstzC1Ow=
			</data>
		</dict>
		<key>wikipedia-namespaces/hif.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LKWeyhyP7d35/lthgdgCkR8qD+Rr7YOUnTppMKOOFpI=
			</data>
		</dict>
		<key>wikipedia-namespaces/ho.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/hr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kg/rRKAy2LA03Z6MMh/l0MECix/yH3d9B2Yy9CRd9vk=
			</data>
		</dict>
		<key>wikipedia-namespaces/hsb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Hv6YPdULKNUDkEwWaw+osAA3bnn2UJRnYqxeaKKE/Cs=
			</data>
		</dict>
		<key>wikipedia-namespaces/ht.json</key>
		<dict>
			<key>hash2</key>
			<data>
			R4kxXtpUpWdXSP2E3IqA3393OR9z/IXddJjjDTtTImU=
			</data>
		</dict>
		<key>wikipedia-namespaces/hu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3h8gDblLUBDeE6zXwUF2uOy8S50CQh7mvtexkCLt8ow=
			</data>
		</dict>
		<key>wikipedia-namespaces/hy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Ob5CSKVyeW/aPXl3nbq0b9UL0quq6XS7zxlwpFLXhxE=
			</data>
		</dict>
		<key>wikipedia-namespaces/hyw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Ou62xtl1srXX3579AtrJrWA5JqkRmI74XH9pSg5bFMw=
			</data>
		</dict>
		<key>wikipedia-namespaces/hz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/ia.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BTW6WjFP08fgydaHuk13ZVzT+mvDKQw5//HG3Jcz4AY=
			</data>
		</dict>
		<key>wikipedia-namespaces/id.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Vo/SpwVosApiM5J/JJS01CSOe3l7jT7v0b+N7WeOxEk=
			</data>
		</dict>
		<key>wikipedia-namespaces/ie.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThT4zBRqaX9rb0VQvsxClbD4MDfR9vmqcYhfJJvOaIg=
			</data>
		</dict>
		<key>wikipedia-namespaces/ig.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LoNp5jJRPzScKb5DZCFd9LSk3o9pkMtu0qUT2+Snibg=
			</data>
		</dict>
		<key>wikipedia-namespaces/ii.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dGyMAA0W4k1OWaT26H5uMRzvtXNcQCcCu0LBkgofuCw=
			</data>
		</dict>
		<key>wikipedia-namespaces/ik.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EIcQEBTcfIY2GzY392dbjdiaRtTanPy2xjxFaH8GxLk=
			</data>
		</dict>
		<key>wikipedia-namespaces/ilo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+xZs3T/SIfpwinLIXJaL+timIHm/KvMklXRdKl4KlqQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/inh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZYVrSkFpcXZq2Rn4RF1d647mr2TRFDSuypeht2IM+sA=
			</data>
		</dict>
		<key>wikipedia-namespaces/io.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+gHu0clFOGvtRHbMA++Q7FvIwBS8DJrX/XD7PeIlqw0=
			</data>
		</dict>
		<key>wikipedia-namespaces/is.json</key>
		<dict>
			<key>hash2</key>
			<data>
			rKvhPFt0cHVyy+EdsvfBa/PIxklLJz4j9N2Fb+6oNY4=
			</data>
		</dict>
		<key>wikipedia-namespaces/it.json</key>
		<dict>
			<key>hash2</key>
			<data>
			NUKFEaEs+dVjftz50iW1Dfm0WsFsQkyytDqcBoDs0E4=
			</data>
		</dict>
		<key>wikipedia-namespaces/iu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			koKwoMU2ixLR9Lstn+T4ZE6C/01dI8skGgdqdnFMW0w=
			</data>
		</dict>
		<key>wikipedia-namespaces/ja.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZIC05BRQEsi06nDdySkX112zV5R0m1Zds55jXHbkzwk=
			</data>
		</dict>
		<key>wikipedia-namespaces/jam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZHm0CpwUCdrERAIiucHQ+AI6q5718QD/MBF+mCKUsA8=
			</data>
		</dict>
		<key>wikipedia-namespaces/jbo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jdYoJP7kjL/1ris76tqkLuiEifEPl9jSfauU9A5fDI4=
			</data>
		</dict>
		<key>wikipedia-namespaces/jv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JNOkaxMJwhdaqdbibjfn0eMfP+zSkTlKGZOz9Dds2i8=
			</data>
		</dict>
		<key>wikipedia-namespaces/ka.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uqy/zmvXxtwhgPs086oNHPLlOa3ly3dUQxEudeHCLuw=
			</data>
		</dict>
		<key>wikipedia-namespaces/kaa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			d4Wg02f4HY7tqXRcsn57i2zfPHYfWJs6OrzEjBYPKLI=
			</data>
		</dict>
		<key>wikipedia-namespaces/kab.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IPX+OtjY/gOtbTjMfFhJtqfnH7z2FHBOO/gDwO8jv3Y=
			</data>
		</dict>
		<key>wikipedia-namespaces/kbd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5pMc4aHZa0bl1jqjpRDrw3U3XNBUhAb3wMPbWUXLNVA=
			</data>
		</dict>
		<key>wikipedia-namespaces/kbp.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5prV+3rmT1XWV5+9UXds8P+D7GJhHLEuFh+SsbNSk8k=
			</data>
		</dict>
		<key>wikipedia-namespaces/kg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nscjKkf1vlhm6VsH4x94vn3XjiHyNGlcQ6+g70uGFRI=
			</data>
		</dict>
		<key>wikipedia-namespaces/ki.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/kj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/kk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OUh8krfNMYv/nZ1cR/n0AkIvhqLR5Bu37hm6HKH952I=
			</data>
		</dict>
		<key>wikipedia-namespaces/kl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bnKIIKNP4fsNjbppUdQg6bRQAPB9VZF28x7/uzei50c=
			</data>
		</dict>
		<key>wikipedia-namespaces/km.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DWwKqJ2hLkHe3aQ/TaLXa1sSmAQO3ovsKhDavhVIVxY=
			</data>
		</dict>
		<key>wikipedia-namespaces/kn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5LgRN+oJNeJbRGmBH/Sd/JMV1AVwSQSWkuDeusQUPBA=
			</data>
		</dict>
		<key>wikipedia-namespaces/ko.json</key>
		<dict>
			<key>hash2</key>
			<data>
			85NpBA1WzFBYGevhSyDT5uOjananGFP1mtCTXrGOXes=
			</data>
		</dict>
		<key>wikipedia-namespaces/koi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7CembEo5TpSMg3dgHpONRgZkjvW+eTfsSUijzzTxlZw=
			</data>
		</dict>
		<key>wikipedia-namespaces/kr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/krc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PzQC69xyrLhrt2VzqErUD9ojpfrCazEtFawwaIT8Uv8=
			</data>
		</dict>
		<key>wikipedia-namespaces/ks.json</key>
		<dict>
			<key>hash2</key>
			<data>
			41iVr6rTuFikwNUtnSCZTUJY1rNuR2azsVBljYMjE38=
			</data>
		</dict>
		<key>wikipedia-namespaces/ksh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			89mxK/9kzNIFB/Ce4XkFfThIj6qZRQh2Zv/NTiAzBqc=
			</data>
		</dict>
		<key>wikipedia-namespaces/ku.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fzVyiOeAlpoPHzjK8o0proCXZMN1idSzQ+rEdHVKwDA=
			</data>
		</dict>
		<key>wikipedia-namespaces/kv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IyBuvjof4tnYURXNlt2VurQaig0JxPTHukfp+nvdzJM=
			</data>
		</dict>
		<key>wikipedia-namespaces/kw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1Ip6ZwSNWzL9wLldzqPwpSdBvhbVAGGrAjMTbtnvPW8=
			</data>
		</dict>
		<key>wikipedia-namespaces/ky.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GOczrxaMGs+khFmFB4+ClvI3s+kbCIb5+/4m2VgQ9Pw=
			</data>
		</dict>
		<key>wikipedia-namespaces/la.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hSC+o9al9mScyUzmUSYDTCis77au8eZ6yLIt2MZAvso=
			</data>
		</dict>
		<key>wikipedia-namespaces/lad.json</key>
		<dict>
			<key>hash2</key>
			<data>
			teABA4R9n3ght7BS3sptl8e/5dYaOTbstPuDnyEFBbw=
			</data>
		</dict>
		<key>wikipedia-namespaces/lb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jmEU7uFQLPmaVfKMn0Sjswr6hFDo1ix2lnN5Zi9hXLs=
			</data>
		</dict>
		<key>wikipedia-namespaces/lbe.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kphuCMHRbtGc9bKDyUZ03/H3nOB5QswIlJTKiYKUXnA=
			</data>
		</dict>
		<key>wikipedia-namespaces/lez.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kJ8qZyga0I9b6it9SbyZoEOUf+hBqNBrrCTs6F9M+7Q=
			</data>
		</dict>
		<key>wikipedia-namespaces/lfn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			YNdGAiQey7gf2wgfof50P3npWv4/UCWzwYFkrvI5nkQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/lg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			x7JDXtlPBdgBefaqlsjY64IEpC7uTbMHKrg5446RYhk=
			</data>
		</dict>
		<key>wikipedia-namespaces/li.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1YcshA0devcy3F4pFaqoV3jm/SlvLfAtci+i7WdnMLY=
			</data>
		</dict>
		<key>wikipedia-namespaces/lij.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7xExLZSH38aWfSFYP5ASYNTbS3O1xCO6yPzQ6QISmQ8=
			</data>
		</dict>
		<key>wikipedia-namespaces/lld.json</key>
		<dict>
			<key>hash2</key>
			<data>
			wWtmfhIo9iX6ZMgh5B0dnS2zcTr8BiNEMWV8sO0b7Is=
			</data>
		</dict>
		<key>wikipedia-namespaces/lmo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jSOyPzA5ahlzumu4vzvapOpdXC2Me4jkJlW5K1QEVRY=
			</data>
		</dict>
		<key>wikipedia-namespaces/ln.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hFz6GpO2GgL0ex9bC2VNJX2gYxpF/5oBB/sbGbvgZWs=
			</data>
		</dict>
		<key>wikipedia-namespaces/lo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TcgainR8suVCA1nHG+MBCI1TUWJBYDQlWXijRRkqJDE=
			</data>
		</dict>
		<key>wikipedia-namespaces/lrc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DLiB/yS9JBW1yxm7lv/u8NraRvNrKNdyfoVyfQXaBHU=
			</data>
		</dict>
		<key>wikipedia-namespaces/lt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			A52YTCxlcGkU2ppjz/WtVLPZvBzuqO6Pz5YTaZ9gMfo=
			</data>
		</dict>
		<key>wikipedia-namespaces/ltg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hJ/+bFz6u1vEs5RSqb+3hiXHuR5C6/lBDZmJvCkuR9I=
			</data>
		</dict>
		<key>wikipedia-namespaces/lv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2iQ1v6tlYd7nbYsZ50lSFwelf/KgIkxF8iA7YbYgIRI=
			</data>
		</dict>
		<key>wikipedia-namespaces/mai.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+nC0lenFdbMODo0HTUt81j4cQwQzQgSUVhA6PsfVIwQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/map-bms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3ervYYW4czwXRFabY4645Gz/VeqxBC0owgkvV+YFYMc=
			</data>
		</dict>
		<key>wikipedia-namespaces/mdf.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jD6YGrmIx33pWtQpsvN7DkkqVO5seOtCjOyd0KS1ZZI=
			</data>
		</dict>
		<key>wikipedia-namespaces/mg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j13K/LmAM3ExFjq3ysnzjRT/NdC5gU9W9d7DGyDUUYs=
			</data>
		</dict>
		<key>wikipedia-namespaces/mh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/mhr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1Y0c53xjOe83CS2zjW9FSRBgDTaRrqQ38T8d74nupJM=
			</data>
		</dict>
		<key>wikipedia-namespaces/mi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7CVnDcKPsLD4Qs+lSGF7AKV+P6uGPJmC/Z63tNkgimU=
			</data>
		</dict>
		<key>wikipedia-namespaces/min.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dm/Bpc/TaTj87VxrIXpQMm5BWyGfg0PG/zSW+CKcKwE=
			</data>
		</dict>
		<key>wikipedia-namespaces/mk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EZpl53u0ck7eFtEfrC+PyYQjG37mxpVnfv1NIYK3ZUw=
			</data>
		</dict>
		<key>wikipedia-namespaces/ml.json</key>
		<dict>
			<key>hash2</key>
			<data>
			w3QAEDDRz+GNUCM6hwF30ufDAJf9uOdzBzTdWL2OBjE=
			</data>
		</dict>
		<key>wikipedia-namespaces/mn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			U2KpBBRVuY1LpecSniBNMWM80jQowESYerfuGU5v3GQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/mnw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vfVWJzwrjJkJGap7lSnO3XDxm/FEd2vaI+FJSO059aQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/mr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XxdBogF9DWVTCeB7dz+kSgZyskiRELr6AhhvzdWPQ0A=
			</data>
		</dict>
		<key>wikipedia-namespaces/mrj.json</key>
		<dict>
			<key>hash2</key>
			<data>
			61YvTLjNF001fDngw2ajuS3sz1FVDBflGb0vs1jsT48=
			</data>
		</dict>
		<key>wikipedia-namespaces/ms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E3COyNn4pVEHzmWeQ4Qp2uQExwJfCaFtSmqO4rZ+NI4=
			</data>
		</dict>
		<key>wikipedia-namespaces/mt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			d5MixQf6nmTFzS+CnW3vzQIFGr8XLrbH7tzyaMmaCoU=
			</data>
		</dict>
		<key>wikipedia-namespaces/mus.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/mwl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UDuV193X/XhIAchQ7237rsCexMZwWjm7UxK5B8iQgvE=
			</data>
		</dict>
		<key>wikipedia-namespaces/my.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UuMNBunR/YSJbZmBdR+nlmv8MvN6aiShrlwr9/sZWXo=
			</data>
		</dict>
		<key>wikipedia-namespaces/myv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LgrHSWhXNJXDM4MBgbTbtzRJBCPCWLdWSwsNVkGW9Xc=
			</data>
		</dict>
		<key>wikipedia-namespaces/mzn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ltgvdYsf2RfYn3iFpMJFFtlUiay7LBgIJhbS0xo9A9g=
			</data>
		</dict>
		<key>wikipedia-namespaces/na.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Zs5TpVv6kfRFirE7WCsACrifwPeU6PZ98iTuzm+WJZE=
			</data>
		</dict>
		<key>wikipedia-namespaces/nah.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j8bhsWAjhXiAcAdxjCjxr34ZzTi8Gkr7HGF3rp+A6nM=
			</data>
		</dict>
		<key>wikipedia-namespaces/nap.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hCsP/gRGYJBTtqDufTyDqHla3f1DOT/HFjMmqtPsckM=
			</data>
		</dict>
		<key>wikipedia-namespaces/nds-nl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l6Qt4wAovBtBAomNSYf5+tik12eqnL3WI/KPdqoTb1A=
			</data>
		</dict>
		<key>wikipedia-namespaces/nds.json</key>
		<dict>
			<key>hash2</key>
			<data>
			k4z+YJv3F7mf6+QRZnwugwfCaND6LDb8V2/F8uITJY0=
			</data>
		</dict>
		<key>wikipedia-namespaces/ne.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Cgkp5ET34f+jfVktk3fA6Bpnionhbz3p7a5obkvUev8=
			</data>
		</dict>
		<key>wikipedia-namespaces/new.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PrGKJjbUenO32G5mQtX6t0mKNVhoEWL4daMQSTOo0Z4=
			</data>
		</dict>
		<key>wikipedia-namespaces/ng.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/nl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kQUJ30/X0ULnyBEiK7hGshD7O78gsTr4uu+BQqr7YSE=
			</data>
		</dict>
		<key>wikipedia-namespaces/nn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s9iP/2C0FwbQepBNwzpIhNVUTUhLsariNTuRzJo6P8Y=
			</data>
		</dict>
		<key>wikipedia-namespaces/no.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1LsDNzKpu/MaLCp4wlFA9DDC357gyVetec5K9I/zuGQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/nov.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SgIMwlWm1j+Ddt2tySeSb22qcJf6RoYxdjGMGQOxGe8=
			</data>
		</dict>
		<key>wikipedia-namespaces/nqo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aTpjUZl/kIS4YG27UHFCfDFjfeD956Yi7Vj2JjQ+M2A=
			</data>
		</dict>
		<key>wikipedia-namespaces/nrm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BBFZJvbwlWbqgJzao1KfWnCkHEscj7ByUMouEcV/Bfo=
			</data>
		</dict>
		<key>wikipedia-namespaces/nso.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qaXIVn9ixCcVk+Ptc/S79LN6S8C9uUKopdQrJ2wJPTo=
			</data>
		</dict>
		<key>wikipedia-namespaces/nv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gZkaCT1Sl6GrtBYwix0Yc5XPA0FVRzJF1Q9DMK+h8LU=
			</data>
		</dict>
		<key>wikipedia-namespaces/ny.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UiYQXZXKh4dxFnjmlRhsEXwLWpl5826EOgANuUXaB4w=
			</data>
		</dict>
		<key>wikipedia-namespaces/oc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3SREAZdl2D9DOEMkDmbfYR+2+k5G9Rt5JZqVAkK5wZY=
			</data>
		</dict>
		<key>wikipedia-namespaces/olo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xLfeFbFs7h/6p+RYb5WtgPxu/CxW4QsCSTu4mtpDqXA=
			</data>
		</dict>
		<key>wikipedia-namespaces/om.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xwVpP+omh8y6wMn89XYZrXgZhB5ZtuLWahgHVlO60kA=
			</data>
		</dict>
		<key>wikipedia-namespaces/or.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bJwk/XFffsXet2RfWufq9IANkt+EOtVP99mL7GjQHAc=
			</data>
		</dict>
		<key>wikipedia-namespaces/os.json</key>
		<dict>
			<key>hash2</key>
			<data>
			N4hQcaqgZ+StOc4KUEbFPjtx8f5auKUO6TraqpLIaug=
			</data>
		</dict>
		<key>wikipedia-namespaces/pa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XM/5gaTvuww8AGXEfZCy4bZiQjVZEq5vvurTCFH35bc=
			</data>
		</dict>
		<key>wikipedia-namespaces/pag.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CAQr/vGzhBLbHaY+4RSJRz1eEKVqteudQVl6SgrnEo0=
			</data>
		</dict>
		<key>wikipedia-namespaces/pam.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Zrh3wWtqa/ANgR3NdT53I2/h0Zc2zi4kwgD4IsCmJak=
			</data>
		</dict>
		<key>wikipedia-namespaces/pap.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GUlFUb+PxhzHA8kBYeJjPG4lNJ1r6IvnORjkOeS76xQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/pcd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ogU0qy+qLhSf6XOkHfbZygghXAP5D1RKckkdzMAu/A0=
			</data>
		</dict>
		<key>wikipedia-namespaces/pdc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GqXqaVJ+6CT/ItL7UODcO1nsNtd9TzodauinnUOYlsE=
			</data>
		</dict>
		<key>wikipedia-namespaces/pfl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			o4mtZE9dAuLXBj/2exJcctckE7WWOycqQqZZzu55SeM=
			</data>
		</dict>
		<key>wikipedia-namespaces/pi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			77lN7WIr5AwlKJEoVjwG0cPeTrL7Y7IgjDFI2osWwCc=
			</data>
		</dict>
		<key>wikipedia-namespaces/pih.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PU6wc4tD7wYbzKiq744ep1Rd9edqi3+iDVJbILurv5c=
			</data>
		</dict>
		<key>wikipedia-namespaces/pl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			imKuZ3ObvoU0FgZMeUbtgMsYCcuQ2dE6Cc5TeTEsAM0=
			</data>
		</dict>
		<key>wikipedia-namespaces/pms.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8hcFgzJgiF6oLVzQz6pLAe0yyCY/9qPEQ/F5NWEhgVk=
			</data>
		</dict>
		<key>wikipedia-namespaces/pnb.json</key>
		<dict>
			<key>hash2</key>
			<data>
			If4PimugsFyMnEihR4kRkSORL+vrP5SAqhflZ4wit5Y=
			</data>
		</dict>
		<key>wikipedia-namespaces/pnt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iRrSu20eyFGMjrfQteYfTG4SV/LmFK6/5AUPrhbTjZg=
			</data>
		</dict>
		<key>wikipedia-namespaces/ps.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2qQGOd6kEwOOjSALPjwPGlwqa7PfS2UaJX9QmNVotV8=
			</data>
		</dict>
		<key>wikipedia-namespaces/pt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GIMaOLx4NJEHwd46qpIs5XgoscmTdfsYKcjLTR4g7Ic=
			</data>
		</dict>
		<key>wikipedia-namespaces/qu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Z+uiGVZY5sMt93lGIVI3ynfuSfQjJkjaX8RYPfXwgyY=
			</data>
		</dict>
		<key>wikipedia-namespaces/rm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			oDCIybSqa4VXRcVY+uTQEW3dzqa1XJUibmoJb1cOe60=
			</data>
		</dict>
		<key>wikipedia-namespaces/rmy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			B300aDmRKCKRzSO3buuCu3e7t1N2F51GyibV0PiFyIQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/rn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/ro.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Om96D6/yfu7dQS3HauSCQDzaINrV1cDdcKdNwv8B+X4=
			</data>
		</dict>
		<key>wikipedia-namespaces/roa-rup.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kRhaFwKnImFMm+MN6D5woVXA8+JSPo2tegkxHDy2sUg=
			</data>
		</dict>
		<key>wikipedia-namespaces/roa-tara.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0f5frGqoXV9JscM584b+o7vwhQ8egBXo4XlbLIpQn3s=
			</data>
		</dict>
		<key>wikipedia-namespaces/ru.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dXFHUueSYfFZF1U9/IXzVUpTEd39sU2tQRgV+ayUdoI=
			</data>
		</dict>
		<key>wikipedia-namespaces/rue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			87NqdaIgfsb7YtsRKxTssDhaHVCZinjSEACN9KSv2c0=
			</data>
		</dict>
		<key>wikipedia-namespaces/rw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nd2+dHsgqv73NsZkcXqKDefK+BZYvAio2oJZJhdMVLU=
			</data>
		</dict>
		<key>wikipedia-namespaces/sa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+F/2djQtLyDmYfkGtVaUAVkbFqndUognYKBMlMbq2q0=
			</data>
		</dict>
		<key>wikipedia-namespaces/sah.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ABF0uNXhGKUgWcsw9YmMcdKGPvRks0ZjJNprMomozJs=
			</data>
		</dict>
		<key>wikipedia-namespaces/sat.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zb5jeYGtkYnknQOemLw0Eh7tZmReYpIMj7sfhF8cYak=
			</data>
		</dict>
		<key>wikipedia-namespaces/sc.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E/Omdv2frGaxFmvOQR/rd8e+lzEbmCwqqYNggVG7w+M=
			</data>
		</dict>
		<key>wikipedia-namespaces/scn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			h364y6RdORLyNtb/IE4B9pL4sPcL5KF6HGaM6UwctSI=
			</data>
		</dict>
		<key>wikipedia-namespaces/sco.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KntQsAqNScEkna3X4f/zdoGMsxkpiZzd1Pk/cUYQdXY=
			</data>
		</dict>
		<key>wikipedia-namespaces/sd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			18xExIvh75sfcPwxArQkvdWM/yU4wZxH9zBvyuJpHdo=
			</data>
		</dict>
		<key>wikipedia-namespaces/se.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZWgvs/gPAsDXKm3LcAdC3B2fxJRxSSpvGGc1vjm23GY=
			</data>
		</dict>
		<key>wikipedia-namespaces/sg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			F2UCGuIERmhd+PgnIc/8NNv6lrai840CegZ7sTDYO48=
			</data>
		</dict>
		<key>wikipedia-namespaces/sh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zOExrRTzGfGeJ9efdfL2y87Dl7MTKWZWKVuMAUCrrMc=
			</data>
		</dict>
		<key>wikipedia-namespaces/shn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JOeQ3jbxIHlBbkc6xAfHgIFlbyhgONKwIj1pBcWeDLs=
			</data>
		</dict>
		<key>wikipedia-namespaces/si.json</key>
		<dict>
			<key>hash2</key>
			<data>
			u9as7kvk5IoeBXxS17ecItiHMnNWsBzLjXOrDzytcM4=
			</data>
		</dict>
		<key>wikipedia-namespaces/simple.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4vecvkH3t4PwyhrpyYizvV1SFynNYY02zsHH/YJWkbI=
			</data>
		</dict>
		<key>wikipedia-namespaces/sk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yOzCSsx3gW2mALdTIhlQ5V4bk0toAZM++BVKAig+wts=
			</data>
		</dict>
		<key>wikipedia-namespaces/sl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uxE7DvsXc5X/ZV9Iw+I+twxxls/9sIAFS7+V+KhrhsM=
			</data>
		</dict>
		<key>wikipedia-namespaces/sm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AfWTmuDiXk/1rdb1fv5Y2ncZAz81SEVRkeXFMayDkbc=
			</data>
		</dict>
		<key>wikipedia-namespaces/sn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uo/LzSHkYoTR0wm9yK5el8dqII9aI5yzLDX9KOFRaiU=
			</data>
		</dict>
		<key>wikipedia-namespaces/so.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hGuXhTQiXCY/gyaCvvEQeynh7ZUmgHui/K22x++sx3U=
			</data>
		</dict>
		<key>wikipedia-namespaces/sq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XC0BjSF/8qwMync/iPnffzMP144TWLZdldsNLs3O/CM=
			</data>
		</dict>
		<key>wikipedia-namespaces/sr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gPxbSpaZd9ahE6Rzkij7HN+aedtwZYZniPj2lHbnBhA=
			</data>
		</dict>
		<key>wikipedia-namespaces/srn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JAevvGMM8IX0GHm5/DRwfM8yYKjxEr9uKwn75Xd7qwE=
			</data>
		</dict>
		<key>wikipedia-namespaces/ss.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q5KUAWGQf0H6PjxrR7BOJ5g4d1MCAF1+b8ZFuNxzZcI=
			</data>
		</dict>
		<key>wikipedia-namespaces/st.json</key>
		<dict>
			<key>hash2</key>
			<data>
			w4dM+6EuucocIdK2oYjbaO8k6zEmPrmk285YeBgfsyk=
			</data>
		</dict>
		<key>wikipedia-namespaces/stq.json</key>
		<dict>
			<key>hash2</key>
			<data>
			a8LmAAfd3S/6PdT4DLR6Od2sPxonWr/DP++o/QeQ7FQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/su.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1CweziyRJQJecS6RBqGm4CUqMLCuzO1hAb4Hpwq2Q0U=
			</data>
		</dict>
		<key>wikipedia-namespaces/sv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8lOhcjaGF0252u7oTVzDCcHzq4FmtHbbiKPBvhuMhbM=
			</data>
		</dict>
		<key>wikipedia-namespaces/sw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aCs1U2V1b5K0JePO+dEBl8QaS4SJH65nOqa3W4riaq4=
			</data>
		</dict>
		<key>wikipedia-namespaces/szl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yA9Mp/ftDfZ7K/2rRDuFI/Vs+GFIdnNv6N9nR5FY67A=
			</data>
		</dict>
		<key>wikipedia-namespaces/szy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SPoAYGkP5jLvvjtIrAXOrUhcSfxDwGdLonKy6hrIIME=
			</data>
		</dict>
		<key>wikipedia-namespaces/ta.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xweXVAaT5Ga4cOxvqBjXYQFNhEfnANS9WLsjFVxu1K4=
			</data>
		</dict>
		<key>wikipedia-namespaces/tcy.json</key>
		<dict>
			<key>hash2</key>
			<data>
			S02llvq1KO87jd9QJHIiXn6G6QQtPHJog/fdRM05TBU=
			</data>
		</dict>
		<key>wikipedia-namespaces/te.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lQbhRSl1Hz7ONo3UjI7BXSzKxpUVseRlonIAOhJ1XZE=
			</data>
		</dict>
		<key>wikipedia-namespaces/test.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cGQvCpzKUQxjPK5eUugaOQnvV9WJoAdYCaF81zIurKI=
			</data>
		</dict>
		<key>wikipedia-namespaces/tet.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DPht0EOcSrtEr/t2YOf6gBOZW5xx0fFi7zujAA6+LvI=
			</data>
		</dict>
		<key>wikipedia-namespaces/tg.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j2viR1z3Yp+HRTNS8sLuT3oo3yA+BynQgRSUfrHpu/E=
			</data>
		</dict>
		<key>wikipedia-namespaces/th.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FN+624ULN4BQ4HJP0BZQItjI1ZhhsE1aVnZoZksKu5U=
			</data>
		</dict>
		<key>wikipedia-namespaces/ti.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GhMA3TpBGxexgOEFlOac8+zPpmMQsgtPz/SSRhwW6zg=
			</data>
		</dict>
		<key>wikipedia-namespaces/tk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aRfpFjIMpYeK2ySl5NuMQ4MpdD8b4IaO8XxCUjxOhNE=
			</data>
		</dict>
		<key>wikipedia-namespaces/tl.json</key>
		<dict>
			<key>hash2</key>
			<data>
			MAQzH2FSr7Gl27JxlMQaYjkGsWYcNGW53Ht0CH2ZcYQ=
			</data>
		</dict>
		<key>wikipedia-namespaces/tn.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/to.json</key>
		<dict>
			<key>hash2</key>
			<data>
			sGGaA21Qmx6rVz8H3NNGz+fBX8scdlkBp3qEgJ5hOjI=
			</data>
		</dict>
		<key>wikipedia-namespaces/tpi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LHbYJiDPn4cNK3btnEi2YRSXyFV/SDIvqoG/2xUqZdw=
			</data>
		</dict>
		<key>wikipedia-namespaces/tr.json</key>
		<dict>
			<key>hash2</key>
			<data>
			STX8TKAb52ZGNSGL3uEstApLHq6g8o9uSQmEv/xQ1y0=
			</data>
		</dict>
		<key>wikipedia-namespaces/trv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			u8K/BHUk5Ubh4mCsa11SuWDgnuBm4xgvQY/zcnYgOik=
			</data>
		</dict>
		<key>wikipedia-namespaces/ts.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RPFaOmAhiTPv1CEuMsASVpxwAi5+EB0xTK1gZ0BV90Y=
			</data>
		</dict>
		<key>wikipedia-namespaces/tt.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DIEKHA8nzollisxrcT+P+cMFQ0y88FaW0hTmBbZuJLo=
			</data>
		</dict>
		<key>wikipedia-namespaces/tum.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ThMr96OGgm16nAudq3knpbf2hXggdjazq7kxuNFGEL0=
			</data>
		</dict>
		<key>wikipedia-namespaces/tw.json</key>
		<dict>
			<key>hash2</key>
			<data>
			QUHYJi0+3mEKRXRZH7W8HIuAZnEpEsRvtGWeBr5151g=
			</data>
		</dict>
		<key>wikipedia-namespaces/ty.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BEm6bSN7I34THzZI/+sCUqzKYgzShncqAH5ZnuAZcB4=
			</data>
		</dict>
		<key>wikipedia-namespaces/tyv.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TUfCwGa67VUuj9nGPHFF8w+zXHm/NVv3Q8/M0ghSOCE=
			</data>
		</dict>
		<key>wikipedia-namespaces/udm.json</key>
		<dict>
			<key>hash2</key>
			<data>
			S/kC7w9fv4w2ZrgH4r6h6sYBr8ffVcLquBaMt/2xaeY=
			</data>
		</dict>
		<key>wikipedia-namespaces/ug.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qwsl7hqxmPmcsaBck4XjLJx+IaD8bHGBznZ7bQljM1Q=
			</data>
		</dict>
		<key>wikipedia-namespaces/uk.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EX+5CCT9JUEgXr3Qaz+n7lO21IpphUBIHtqq7akf4rg=
			</data>
		</dict>
		<key>wikipedia-namespaces/ur.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8CaUjWCWHcP0xCr61v4foR9pzxJNaH4m45y0vrVV0B0=
			</data>
		</dict>
		<key>wikipedia-namespaces/uz.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7g9k0sD8mUTbSqcq8QchhT1xygy0s89iqxSHoWXbSVY=
			</data>
		</dict>
		<key>wikipedia-namespaces/ve.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3dxQwNg12aaZyTZcMqyoRIcrJM5nY8XEbnlkxHYEir8=
			</data>
		</dict>
		<key>wikipedia-namespaces/vec.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7eLsPSlCPWfMLfFKA1o7KZPoFwtfPh1BEPbH4m9FfIk=
			</data>
		</dict>
		<key>wikipedia-namespaces/vep.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FTFEhGhrndG8U2tZr+uRz87OqDT6Q/yEz2QigYv1dKc=
			</data>
		</dict>
		<key>wikipedia-namespaces/vi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			rh6A5tAyWa/29xjKblw3wQf7i5M3dhCa/xUUrVbfMNI=
			</data>
		</dict>
		<key>wikipedia-namespaces/vls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DGiMvp2ToMrgP4Q9F5t4IJvAyAapHDLTJ9xca61OJQg=
			</data>
		</dict>
		<key>wikipedia-namespaces/vo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qmzmzpgX3uPubaoOoqk+qFgRzH3w5DAqpZAI66ckK9o=
			</data>
		</dict>
		<key>wikipedia-namespaces/wa.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xfEot004SNlFx+iCABBnzWUAQSIc17kr6pNVpEKu9dw=
			</data>
		</dict>
		<key>wikipedia-namespaces/war.json</key>
		<dict>
			<key>hash2</key>
			<data>
			X87y2G+XVZKLKEA74uW3GxzQQ59W0bVHcuIHFrRgwdY=
			</data>
		</dict>
		<key>wikipedia-namespaces/wo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hT5h8CRuu8LSij8Hgt0iMBYLfyDqpZj89wE3446VOJw=
			</data>
		</dict>
		<key>wikipedia-namespaces/wuu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xJfUbmv0qi3q23Fa6Y1SVTyKcsX66loXPmNqlCVVBtA=
			</data>
		</dict>
		<key>wikipedia-namespaces/xal.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8z6UZko/Ex0vAtRrmKYAB5thKaHoumitH+2XM//SgJk=
			</data>
		</dict>
		<key>wikipedia-namespaces/xh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			pvGXjYVWi4TVRFtClfti/CpLsBxlIY12dHroVm+6qWc=
			</data>
		</dict>
		<key>wikipedia-namespaces/xmf.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+R7IrPruurke1i3JnQXJ10ecZ3d/U5v5zVPw91ZGWCg=
			</data>
		</dict>
		<key>wikipedia-namespaces/yi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bETXwQ20ho/y+L6aG9rYxW3RW/pL/CPceaVplOAvelE=
			</data>
		</dict>
		<key>wikipedia-namespaces/yo.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XCAnt46S0rM4VfidVj09pFXczIpkVIA2eY+8hvXhuLk=
			</data>
		</dict>
		<key>wikipedia-namespaces/yue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E7K1S5irkAeiiv90redb35FXGFTv9StpSOme9y9mv+c=
			</data>
		</dict>
		<key>wikipedia-namespaces/za.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1OpfSObMYITasfB2ZWg2Q/zA9xZKHhDZDiq7+6/lZWM=
			</data>
		</dict>
		<key>wikipedia-namespaces/zea.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CCrOCwhBbaSexYrGhu8Pdy2XTATohCzA0nIJugAom5g=
			</data>
		</dict>
		<key>wikipedia-namespaces/zh-classical.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RO6+ggBECLcboMlwzTGoau0Sgw6OUls9PFyUrcaUnnc=
			</data>
		</dict>
		<key>wikipedia-namespaces/zh-min-nan.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2nzvEL3OiNRaRCGrA2zJFUIkqn5FyC3wKy2jCnUoQtc=
			</data>
		</dict>
		<key>wikipedia-namespaces/zh-yue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E7K1S5irkAeiiv90redb35FXGFTv9StpSOme9y9mv+c=
			</data>
		</dict>
		<key>wikipedia-namespaces/zh.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IlnCNkwnrhms+R3fyuC+lebm8v9MpPJOUmC53pdJ9nY=
			</data>
		</dict>
		<key>wikipedia-namespaces/zu.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RKOQ0cC5PtcJ5dA0wqc6PrGHLRJsgec0M/5B5FfNIe0=
			</data>
		</dict>
		<key>yi.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GOiEX5J8nd7K1B6YL8oqaV4V1A4/Cl19jbyuelp4aQ8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>yi.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			AIgwpb60yZwyyIZUL6izUZh3priIqUZsqgN4z84q+Tg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4+MsjPbY7c9iKEHkzZikGax84m8EfOiPlTa4eP1TCwI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			35oZLr3U9WZIIXPOUoNry94aJqQXfJgyRfQ0HlY76Wc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ixGBC8MiMj0vkCHLHNCH0Tioyrkt1DTKeCs/UonGLtI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-hant.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			rF6TZn0YojDmGJWlvhw4s27yTWPtuPIUA0PSXtNPyvQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
