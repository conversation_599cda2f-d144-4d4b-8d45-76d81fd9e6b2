.cm-mw-sepia, .cm-mw-sepia .CodeMirror {
  background-color: #F8F1E3;
  color: #222;
}

.cm-mw-sepia .CodeMirror-gutters {
  background-color: #F8F1E3;
  border-right: 4px solid #F8F1E3;
}

.cm-mw-sepia .CodeMirror-linenumber {
  color: #6C6760;
  font-size: 0.8em;
}

.cm-mw-sepia .cm-mw-template {
  color: #222; /* plain text color */
}

.cm-mw-sepia .cm-mw-link,
.cm-mw-sepia .cm-mw-link-pagename,
.cm-mw-sepia .cm-mw-link-tosection,
.cm-mw-sepia .cm-mw-link-bracket,
.cm-mw-sepia .cm-mw-link-delimiter,
.cm-mw-sepia .cm-mw-extlink,
.cm-mw-sepia .cm-mw-free-extlink,
.cm-mw-sepia .cm-mw-extlink-protocol,
.cm-mw-sepia .cm-mw-free-extlink-protocol,
.cm-mw-sepia .cm-mw-extlink-bracket,
.cm-mw-sepia .cm-mw-doubleUnderscore,
.cm-mw-sepia .cm-mw-signature,
.cm-mw-sepia .cm-mw-hr {
  color: #36C;
  font-weight: normal;
}

.cm-mw-sepia .cm-mw-mnemonic,
.cm-mw-sepia .cm-mw-exttag-name,
.cm-mw-sepia .cm-mw-exttag-bracket,
.cm-mw-sepia .cm-mw-exttag-attribute,
.cm-mw-sepia .cm-mw-htmltag-name,
.cm-mw-sepia .cm-mw-htmltag-bracket,
.cm-mw-sepia .cm-mw-htmltag-attribute {
  color: #00AF89;
  font-weight: normal;
}

.cm-mw-sepia .cm-mw-parserfunction-name,
.cm-mw-sepia .cm-mw-parserfunction-bracket,
.cm-mw-sepia .cm-mw-parserfunction-delimiter { 
  color: #B32424;
}

.cm-mw-sepia .cm-mw-table-bracket,
.cm-mw-sepia .cm-mw-table-delimiter,
.cm-mw-sepia .cm-mw-table-definition { 
  color: #F06695; 
  font-weight: normal; 
}

.cm-mw-sepia .cm-mw-template-name,
.cm-mw-sepia .cm-mw-template-argument-name,
.cm-mw-sepia .cm-mw-template-delimiter,
.cm-mw-sepia .cm-mw-template-bracket  {
  color: #97498F;
  font-weight: normal;
}

.cm-mw-sepia .cm-mw-list,
.cm-mw-sepia .cm-mw-doubleUnderscore, 
.cm-mw-sepia .cm-mw-signature, 
.cm-mw-sepia .cm-mw-hr,
.cm-mw-sepia .cm-mw-indenting,
.cm-mw-sepia .cm-mw-apostrophes-bold, 
.cm-mw-sepia .cm-mw-apostrophes-italic,
.cm-mw-sepia .cm-mw-section-header,
.cm-mw-sepia .cm-mw-templatevariable,
.cm-mw-sepia .cm-mw-templatevariable-name,
.cm-mw-sepia .cm-mw-templatevariable-bracket,
.cm-mw-sepia .cm-mw-templatevariable-delimiter,
.cm-mw-sepia .cm-mw-matching {
  color: #FF9500;
  font-weight: normal;
}

.cm-mw-sepia .cm-mw-comment,
.cm-mw-sepia .cm-mw-skipformatting {
  color: #6C6760;
}

.cm-mw-sepia .cm-searching {
  color: #000;
  background-color: rgba(255, 204, 51, 0.3);
  border-radius: 2px;
}