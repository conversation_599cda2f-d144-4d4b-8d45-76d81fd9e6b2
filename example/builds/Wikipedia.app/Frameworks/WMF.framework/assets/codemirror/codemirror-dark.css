.cm-mw-dark, .cm-mw-dark .CodeMirror {
  background-color: #2E3136;
  color: #FFF;
}

.cm-mw-dark .CodeMirror-gutters {
  background-color: #2E3136;
  border-right: 4px solid #2E3136;
}

.cm-mw-dark .CodeMirror-linenumber {
  color: #C8CCD1;
  font-size: 0.8em;
}

.cm-mw-dark .cm-mw-template {
  color: #FFF; /* plain text color */
}

.cm-mw-dark .cm-mw-link,
.cm-mw-dark .cm-mw-link-pagename,
.cm-mw-dark .cm-mw-link-tosection,
.cm-mw-dark .cm-mw-link-bracket,
.cm-mw-dark .cm-mw-link-delimiter,
.cm-mw-dark .cm-mw-extlink,
.cm-mw-dark .cm-mw-free-extlink,
.cm-mw-dark .cm-mw-extlink-protocol,
.cm-mw-dark .cm-mw-free-extlink-protocol,
.cm-mw-dark .cm-mw-extlink-bracket,
.cm-mw-dark .cm-mw-doubleUnderscore,
.cm-mw-dark .cm-mw-signature,
.cm-mw-dark .cm-mw-hr {
  color: #6699FF;
  font-weight: normal;
}

.cm-mw-dark .cm-mw-mnemonic,
.cm-mw-dark .cm-mw-exttag-name,
.cm-mw-dark .cm-mw-exttag-bracket,
.cm-mw-dark .cm-mw-exttag-attribute,
.cm-mw-dark .cm-mw-htmltag-name,
.cm-mw-dark .cm-mw-htmltag-bracket,
.cm-mw-dark .cm-mw-htmltag-attribute {
  color: #00AF89;
  font-weight: normal;
}

.cm-mw-dark .cm-mw-parserfunction-name,
.cm-mw-dark .cm-mw-parserfunction-bracket,
.cm-mw-dark .cm-mw-parserfunction-delimiter { 
  color: #FF6E6E;
}

.cm-mw-dark .cm-mw-table-bracket,
.cm-mw-dark .cm-mw-table-delimiter,
.cm-mw-dark .cm-mw-table-definition { 
  color: #F06695; 
  font-weight: normal; 
}

.cm-mw-dark .cm-mw-template-name,
.cm-mw-dark .cm-mw-template-argument-name,
.cm-mw-dark .cm-mw-template-delimiter,
.cm-mw-dark .cm-mw-template-bracket  {
  color: #C180BB;
  font-weight: normal;
}

.cm-mw-dark .cm-mw-list,
.cm-mw-dark .cm-mw-doubleUnderscore, 
.cm-mw-dark .cm-mw-signature, 
.cm-mw-dark .cm-mw-hr,
.cm-mw-dark .cm-mw-indenting,
.cm-mw-dark .cm-mw-apostrophes-bold, 
.cm-mw-dark .cm-mw-apostrophes-italic,
.cm-mw-dark .cm-mw-section-header,
.cm-mw-dark .cm-mw-templatevariable,
.cm-mw-dark .cm-mw-templatevariable-name,
.cm-mw-dark .cm-mw-templatevariable-bracket,
.cm-mw-dark .cm-mw-templatevariable-delimiter,
.cm-mw-dark .cm-mw-matching {
  color: #FF9500;
  font-weight: normal;
}

.cm-mw-dark .cm-mw-comment,
.cm-mw-dark .cm-mw-skipformatting {
  color: #C8CCD1;
}

.cm-mw-dark .cm-searching {
  color: #000;
  background-color: rgba(247, 215, 121, 0.7);
  border-radius: 2px;
}