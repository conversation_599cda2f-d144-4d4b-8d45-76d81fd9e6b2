.cm-mw-black, .cm-mw-black .CodeMirror {
  background-color: #000;
  color: #FFF;
}

.cm-mw-black .CodeMirror-gutters {
  background-color: #000;
  border-right: 4px solid #000;
}

.cm-mw-black .CodeMirror-linenumber {
  color: #C8CCD1;
  font-size: 0.8em;
}

.cm-mw-black .cm-mw-template {
  color: #FFF; /* plain text color */
}

.cm-mw-black .cm-mw-link,
.cm-mw-black .cm-mw-link-pagename,
.cm-mw-black .cm-mw-link-tosection,
.cm-mw-black .cm-mw-link-bracket,
.cm-mw-black .cm-mw-link-delimiter,
.cm-mw-black .cm-mw-extlink,
.cm-mw-black .cm-mw-free-extlink,
.cm-mw-black .cm-mw-extlink-protocol,
.cm-mw-black .cm-mw-free-extlink-protocol,
.cm-mw-black .cm-mw-extlink-bracket,
.cm-mw-black .cm-mw-doubleUnderscore,
.cm-mw-black .cm-mw-signature,
.cm-mw-black .cm-mw-hr {
  color: #6699FF;
  font-weight: normal;
}

.cm-mw-black .cm-mw-mnemonic,
.cm-mw-black .cm-mw-exttag-name,
.cm-mw-black .cm-mw-exttag-bracket,
.cm-mw-black .cm-mw-exttag-attribute,
.cm-mw-black .cm-mw-htmltag-name,
.cm-mw-black .cm-mw-htmltag-bracket,
.cm-mw-black .cm-mw-htmltag-attribute {
  color: #00AF89;
  font-weight: normal;
}

.cm-mw-black .cm-mw-parserfunction-name,
.cm-mw-black .cm-mw-parserfunction-bracket,
.cm-mw-black .cm-mw-parserfunction-delimiter { 
  color: #FF6E6E;
}

.cm-mw-black .cm-mw-table-bracket,
.cm-mw-black .cm-mw-table-delimiter,
.cm-mw-black .cm-mw-table-definition { 
  color: #F06695; 
  font-weight: normal; 
}

.cm-mw-black .cm-mw-template-name,
.cm-mw-black .cm-mw-template-argument-name,
.cm-mw-black .cm-mw-template-delimiter,
.cm-mw-black .cm-mw-template-bracket  {
  color: #C180BB;
  font-weight: normal;
}

.cm-mw-black .cm-mw-list,
.cm-mw-black .cm-mw-doubleUnderscore, 
.cm-mw-black .cm-mw-signature, 
.cm-mw-black .cm-mw-hr,
.cm-mw-black .cm-mw-indenting,
.cm-mw-black .cm-mw-apostrophes-bold, 
.cm-mw-black .cm-mw-apostrophes-italic,
.cm-mw-black .cm-mw-section-header,
.cm-mw-black .cm-mw-templatevariable,
.cm-mw-black .cm-mw-templatevariable-name,
.cm-mw-black .cm-mw-templatevariable-bracket,
.cm-mw-black .cm-mw-templatevariable-delimiter,
.cm-mw-black .cm-mw-matching {
  color: #FF9500;
  font-weight: normal;
}

.cm-mw-black .cm-mw-comment,
.cm-mw-black .cm-mw-skipformatting {
  color: #C8CCD1;
}

.cm-mw-black .cm-searching {
  color: #000;
  background-color: rgba(247, 215, 121, 0.7);
  border-radius: 2px;
}