.cm-mw-off .cm-mw-link,
.cm-mw-off .cm-mw-link-pagename,
.cm-mw-off .cm-mw-link-tosection,
.cm-mw-off .cm-mw-link-bracket,
.cm-mw-off .cm-mw-link-delimiter,
.cm-mw-off .cm-mw-extlink,
.cm-mw-off .cm-mw-free-extlink,
.cm-mw-off .cm-mw-extlink-protocol,
.cm-mw-off .cm-mw-free-extlink-protocol,
.cm-mw-off .cm-mw-extlink-bracket,
.cm-mw-off .cm-mw-doubleUnderscore,
.cm-mw-off .cm-mw-signature,
.cm-mw-off .cm-mw-hr,
.cm-mw-off .cm-mw-mnemonic,
.cm-mw-off .cm-mw-exttag-name,
.cm-mw-off .cm-mw-exttag-bracket,
.cm-mw-off .cm-mw-exttag-attribute,
.cm-mw-off .cm-mw-htmltag-name,
.cm-mw-off .cm-mw-htmltag-bracket,
.cm-mw-off .cm-mw-htmltag-attribute,
.cm-mw-off .cm-mw-parserfunction-name,
.cm-mw-off .cm-mw-parserfunction-bracket,
.cm-mw-off .cm-mw-parserfunction-delimiter,
.cm-mw-off .cm-mw-table-bracket,
.cm-mw-off .cm-mw-table-delimiter,
.cm-mw-off .cm-mw-table-definition,
.cm-mw-off .cm-mw-template-name,
.cm-mw-off .cm-mw-template-argument-name,
.cm-mw-off .cm-mw-template-delimiter,
.cm-mw-off .cm-mw-template-bracket,
.cm-mw-off .cm-mw-list,
.cm-mw-off .cm-mw-doubleUnderscore, 
.cm-mw-off .cm-mw-signature, 
.cm-mw-off .cm-mw-hr,
.cm-mw-off .cm-mw-indenting,
.cm-mw-off .cm-mw-apostrophes-bold, 
.cm-mw-off .cm-mw-apostrophes-italic,
.cm-mw-off .cm-mw-section-header,
.cm-mw-off .cm-mw-templatevariable,
.cm-mw-off .cm-mw-templatevariable-name,
.cm-mw-off .cm-mw-templatevariable-bracket,
.cm-mw-off .cm-mw-templatevariable-delimiter,
.cm-mw-off .cm-mw-matching,
.cm-mw-off .cm-mw-comment,
.cm-mw-off .cm-mw-skipformatting {
  color: inherit;
}
