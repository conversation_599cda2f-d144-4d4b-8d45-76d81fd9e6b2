.cm-mw-light, .cm-mw-light .CodeMirror {
  background-color: #FFF;
  color: #222;
}

.cm-mw-light .CodeMirror-gutters {
  background-color: #FFF;
  border-right: 4px solid #FFF;
}

.cm-mw-light .CodeMirror-linenumber {
  color: #72777D;
  font-size: 0.8em;
}

.cm-mw-light .cm-mw-template {
  color: #222; /* plain text color */
}

.cm-mw-light .cm-mw-link,
.cm-mw-light .cm-mw-link-pagename,
.cm-mw-light .cm-mw-link-tosection,
.cm-mw-light .cm-mw-link-bracket,
.cm-mw-light .cm-mw-link-delimiter,
.cm-mw-light .cm-mw-extlink,
.cm-mw-light .cm-mw-free-extlink,
.cm-mw-light .cm-mw-extlink-protocol,
.cm-mw-light .cm-mw-free-extlink-protocol,
.cm-mw-light .cm-mw-extlink-bracket,
.cm-mw-light .cm-mw-doubleUnderscore,
.cm-mw-light .cm-mw-signature,
.cm-mw-light .cm-mw-hr {
  color: #36C;
  font-weight: normal;
}

.cm-mw-light .cm-mw-mnemonic,
.cm-mw-light .cm-mw-exttag-name,
.cm-mw-light .cm-mw-exttag-bracket,
.cm-mw-light .cm-mw-exttag-attribute,
.cm-mw-light .cm-mw-htmltag-name,
.cm-mw-light .cm-mw-htmltag-bracket,
.cm-mw-light .cm-mw-htmltag-attribute {
  color: #00AF89;
  font-weight: normal;
}

.cm-mw-light .cm-mw-parserfunction-name,
.cm-mw-light .cm-mw-parserfunction-bracket,
.cm-mw-light .cm-mw-parserfunction-delimiter { 
  color: #B32424;
}

.cm-mw-light .cm-mw-table-bracket,
.cm-mw-light .cm-mw-table-delimiter,
.cm-mw-light .cm-mw-table-definition { 
  color: #E6275d; 
  font-weight: normal; 
}

.cm-mw-light .cm-mw-template-name,
.cm-mw-light .cm-mw-template-argument-name,
.cm-mw-light .cm-mw-template-delimiter,
.cm-mw-light .cm-mw-template-bracket  {
  color: #97498F;
  font-weight: normal;
}

.cm-mw-light .cm-mw-list,
.cm-mw-light .cm-mw-doubleUnderscore, 
.cm-mw-light .cm-mw-signature, 
.cm-mw-light .cm-mw-hr,
.cm-mw-light .cm-mw-indenting,
.cm-mw-light .cm-mw-apostrophes-bold, 
.cm-mw-light .cm-mw-apostrophes-italic,
.cm-mw-light .cm-mw-section-header,
.cm-mw-light .cm-mw-templatevariable,
.cm-mw-light .cm-mw-templatevariable-name,
.cm-mw-light .cm-mw-templatevariable-bracket,
.cm-mw-light .cm-mw-templatevariable-delimiter,
.cm-mw-light .cm-mw-matching {
  color: #FF9500;
  font-weight: normal;
}

.cm-mw-light .cm-mw-comment,
.cm-mw-light .cm-mw-skipformatting {
  color: #72777D;
}

.cm-mw-light .cm-searching {
  color: #000;
  background-color: rgba(255, 204, 51, 0.3);
  border-radius: 2px;
}