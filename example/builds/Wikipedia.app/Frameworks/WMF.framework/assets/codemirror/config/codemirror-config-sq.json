{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__jotp__": "notoc", "__notoc__": "notoc", "__jogaleri__": "nogallery", "__nogallery__": "nogallery", "__forcetoc__": "forcetoc", "__tp__": "toc", "__toc__": "toc", "__joredaktimseksioni__": "noeditsection", "__noeditsection__": "noeditsection", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__KATEGORIEFSHEHUR__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEX__": "index", "__NOINDEX__": "noindex", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"ns": "ns", "nse": "nse", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "lcfirst": "lcfirst", "ucfirst": "ucfirst", "lc": "lc", "uc": "uc", "urllokale": "<PERSON>url", "localurl": "<PERSON>url", "localurle": "<PERSON><PERSON><PERSON>", "urleplotë": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnum": "formatnum", "gramatika": "grammar", "grammar": "grammar", "gjinia": "gender", "gender": "gender", "shumës": "plural", "plural": "plural", "bidi": "bidi", "#gjuha": "language", "#language": "language", "padleft": "padleft", "padright": "padright", "anchorencode": "anchorencode", "filepath": "filepath", "pageid": "pageid", "int": "int", "#special": "special", "#speciale": "speciale", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#if": "if", "#ifeq": "ifeq", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "#mentor": "mentor", "articlepath": "articlepath", "serveri": "server", "server": "server", "emriiserverit": "servername", "servername": "servername", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NUMRIFAQEVE": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NUMRIIPËRDORUESVE": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NUMRIIPËRDORUESVEAKTIVË": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NUMRIIARTIKUJVE": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NUMRIISKEDAVE": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NUMRIIADMINISTRUESVE": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NUMRIREDAKTIMEVE": "numberofedits", "NUMBEROFEDITS": "numberofedits", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "MADHËSIAEFAQES": "pagesize", "PAGESIZE": "pagesize", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "TALKSPACE": "talkspace", "TALKSPACEE": "talkspacee", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "EMRIFAQES": "pagename", "PAGENAME": "pagename", "PAGENAMEE": "pagenamee", "EMRIIPLOTËIFAQES": "fullpagename", "FULLPAGENAME": "fullpagename", "EMRIIPLOTËIFAQESE": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "EMRIIFAQESBAZË": "basepagename", "BASEPAGENAME": "basepagename", "EMRIIFAQESBAZËE": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "EMRIINËNFAQES": "subpagename", "SUBPAGENAME": "subpagename", "EMRIINËNFAQESE": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "EMRIIFAQESSËDISKUTIMIT": "talkpagename", "TALKPAGENAME": "talkpagename", "EMRIIFAQESSËDISKUTIMITE": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "REVISIONID": "revisionid", "REVISIONDAY": "revisionday", "REVISIONDAY2": "revisionday2", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "REVISIONYEAR": "revisionyear", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "HAPËSIRA": "namespace", "NAMESPACE": "namespace", "DISPLAYTITLE": "displaytitle", "!": "!", "MUAJIMOMENTAL": "currentmonth", "MUAJIMOMENTAL2": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "MUAJIMOMENTAL1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "EMRIIMUAJITMOMENTAL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "DITASOT": "currentday", "CURRENTDAY": "currentday", "DITASOT2": "currentday2", "CURRENTDAY2": "currentday2", "EMRIIDITËSOT": "currentdayname", "CURRENTDAYNAME": "currentdayname", "SIVJET": "currentyear", "CURRENTYEAR": "currentyear", "KOHATANI": "currenttime", "CURRENTTIME": "currenttime", "ORATANI": "currenthour", "CURRENTHOUR": "currenthour", "MUAJILOKAL": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LOCALMONTH1": "localmonth1", "EMRIIMUAJITLOKAL": "localmonthname", "LOCALMONTHNAME": "localmonthname", "LOCALMONTHNAMEGEN": "localmonthnamegen", "LOCALMONTHABBREV": "localmonthabbrev", "DITALOKALE": "localday", "LOCALDAY": "localday", "DITALOKALE2": "localday2", "LOCALDAY2": "localday2", "EMRIIDITËSLOKALE": "localdayname", "LOCALDAYNAME": "localdayname", "VITILOKAL": "localyear", "LOCALYEAR": "localyear", "KOHALOKALE": "localtime", "LOCALTIME": "localtime", "ORALOKALE": "localhour", "LOCALHOUR": "localhour", "EMRIISAJTIT": "sitename", "SITENAME": "sitename", "JAVAMOMENTALE": "currentweek", "CURRENTWEEK": "currentweek", "CURRENTDOW": "currentdow", "LOCALWEEK": "localweek", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "CURRENTVERSION": "currentversion", "CURRENTTIMESTAMP": "currenttimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-z]+)(.*)$/sD"}}