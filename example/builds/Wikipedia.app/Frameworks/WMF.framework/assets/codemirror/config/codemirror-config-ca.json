{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "charinsert": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__captaula__": "notoc", "__notaula__": "notoc", "__notoc__": "notoc", "__capdetaula__": "notoc", "__pascapdesomari__": "notoc", "__pascapdetdm__": "notoc", "__capgaleria__": "nogallery", "__nogaleria__": "nogallery", "__nogallery__": "nogallery", "__capdegalariá__": "nogallery", "__capdegalaria__": "nogallery", "__pascapdedegalariá__": "nogallery", "__forçataula__": "forcetoc", "__forcetoc__": "forcetoc", "__forçartaula__": "forcetoc", "__forçarsomari__": "forcetoc", "__forçartdm__": "forcetoc", "__taula__": "toc", "__resum__": "toc", "__tdm__": "toc", "__toc__": "toc", "__somari__": "toc", "__secciónoeditable__": "noeditsection", "__seccionoeditable__": "noeditsection", "__noeditsection__": "noeditsection", "__seccionnoneditabla__": "noeditsection", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LIGAMSECCIONNOVÈLA__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__PASCAPDELIGAMSECCIONNOVÈLA__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__CATAMAGADA__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEX__": "index", "__CAPINDEX__": "noindex", "__NOINDEX__": "noindex", "__PASCAPDINDÈX__": "noindex", "__REDIRECCIÓESTATICA__": "staticredirect", "__REDIRECCIOESTATICA__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__REDIRECCIONESTATICA__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"espacen": "ns", "ns": "ns", "nse": "nse", "encòdaurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "initminus": "lcfirst", "lcfirst": "lcfirst", "initmajus": "ucfirst", "ucfirst": "ucfirst", "minus": "lc", "lc": "lc", "majus": "uc", "capit": "uc", "uc": "uc", "urllocala": "<PERSON>url", "localurl": "<PERSON>url", "urllocalax": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "urlcompleta": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlcompletax": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnombre": "formatnum", "formatnum": "formatnum", "gramatica": "grammar", "grammar": "grammar", "genre": "gender", "gender": "gender", "plural": "plural", "bidi": "bidi", "#idioma": "language", "#llengua": "language", "#language": "language", "#lenga": "language", "separacióesquerra": "padleft", "separacioesquerra": "padleft", "padleft": "padleft", "borratgeesquèrra": "padleft", "separaciódreta": "padright", "separaciodreta": "padright", "padright": "padright", "borratgedrecha": "padright", "encòdaancòra": "anchorencode", "anchorencode": "anchorencode", "camí": "filepath", "cami": "filepath", "filepath": "filepath", "camin": "filepath", "pageid": "pageid", "int": "int", "#especial": "special", "#special": "special", "#speciale": "speciale", "#etiqueta": "tag", "#marcador": "tag", "#tag": "tag", "#balisa": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "pagebanner": "PAGEBANNER", "#if": "if", "#ifeq": "ifeq", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "articlepath": "articlepath", "servidor": "server", "server": "server", "nomservidor": "servername", "servername": "servername", "caminescript": "scriptpath", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NOMBREPAGINAS": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NOMBREUSUARIS": "numberofusers", "NOMBRED'USUARIS": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NOMBREUTILIZAIRES": "numberofusers", "NOMBREUTILIZAIRESACTIUS": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NOMBREARTICLES": "numberofarticles", "NOMBRED'ARTICLES": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NOMBREFITXERS": "numberoffiles", "NOMBRED'ARXIUS": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NOMBREFICHIÈRS": "numberoffiles", "NOMBREADMINS": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NOMBREEDICIONS": "numberofedits", "NOMBRED'EDICIONS": "numberofedits", "NUMBEROFEDITS": "numberofedits", "NOMBREMODIFS": "numberofedits", "ORDENA": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "CLAUDETRIADA": "defaultsort", "PÀGINESENCATEGORIA": "pagesincategory", "PAGINESENCATEGORIA": "pagesincategory", "PAGINESENCAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "PAGINASDINSCAT": "pagesincategory", "MIDAPÀGINA": "pagesize", "MIDAPAGINA": "pagesize", "MIDADELAPLANA": "pagesize", "PAGESIZE": "pagesize", "TALHAPAGINA": "pagesize", "NIVELLPROTECCIÓ": "protectionlevel", "NIVELLPROTECCIO": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "NIVÈLDEPROTECCION": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "ESPACINOMENATGEX": "namespacee", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "ESPACIDISCUSSION": "talkspace", "TALKSPACE": "talkspace", "ESPACIDISCUSSIONX": "talkspacee", "TALKSPACEE": "talkspacee", "ESPACISUBJECTE": "subjectspace", "ESPACISUBJÈCTE": "subjectspace", "ESPACIARTICLE": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "ESPACISUBJECTEX": "subjectspacee", "ESPACISUBJÈCTEX": "subjectspacee", "ESPACIARTICLEX": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "NOMPÀGINA": "pagename", "NOMPAGINA": "pagename", "NOMDELAPLANA": "pagename", "PAGENAME": "pagename", "NOMPAGINAX": "pagenamee", "PAGENAMEE": "pagenamee", "NOMPAGINACOMPLET": "fullpagename", "FULLPAGENAME": "fullpagename", "NOMPAGINACOMPLETX": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "NOMBASADEPAGINA": "basepagename", "BASEPAGENAME": "basepagename", "NOMBASADEPAGINAX": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "NOMSOSPAGINA": "subpagename", "SUBPAGENAME": "subpagename", "NOMSOSPAGINAX": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "NOMPAGINADISCUSSION": "talkpagename", "TALKPAGENAME": "talkpagename", "NOMPAGINADISCUSSIONX": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NOMPAGINASUBJECTE": "subjectpagename", "NOMPAGINASUBJÈCTE": "subjectpagename", "NOMPAGINAARTICLE": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NOMPAGINASUBJECTEX": "subjectpagenamee", "NOMPAGINASUBJÈCTEX": "subjectpagenamee", "NOMPAGINAARTICLEX": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "NUMÈROVERSION": "revisionid", "REVISIONID": "revisionid", "DATAVERSION": "revisionday", "REVISIONDAY": "revisionday", "DATAVERSION2": "revisionday2", "REVISIONDAY2": "revisionday2", "MESREVISION": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "ANNADAREVISION": "revisionyear", "ANREVISION": "revisionyear", "REVISIONYEAR": "revisionyear", "ORAREVISION": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "ESPACINOMENATGE": "namespace", "NAMESPACE": "namespace", "TÍTOL": "displaytitle", "TITOL": "displaytitle", "DISPLAYTITLE": "displaytitle", "AFICHARTÍTOL": "displaytitle", "!": "!", "MESACTUAL": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "MESCORRENT": "currentmonth", "CURRENTMONTH1": "currentmonth1", "NOMMESACTUAL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "NOMMESCORRENT": "currentmonthname", "NOMGENMESACTUAL": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "NOMGENMESCORRENT": "currentmonthnamegen", "ABREVMESACTUAL": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "ABREVMESCORRENT": "<PERSON><PERSON><PERSON><PERSON>v", "DIAACTUAL": "currentday", "CURRENTDAY": "currentday", "JORNCORRENT": "currentday", "JORNACTUAL": "currentday", "DIAACTUAL2": "currentday2", "CURRENTDAY2": "currentday2", "JORNCORRENT2": "currentday2", "JORNACTUAL2": "currentday2", "NOMDIAACTUAL": "currentdayname", "CURRENTDAYNAME": "currentdayname", "NOMJORNCORRENT": "currentdayname", "NOMJORNACTUAL": "currentdayname", "ANYACTUAL": "currentyear", "CURRENTYEAR": "currentyear", "ANNADACORRENTA": "currentyear", "ANNADAACTUALA": "currentyear", "HORARICTUAL": "currenttime", "CURRENTTIME": "currenttime", "DATACORRENTA": "currenttime", "DATAACTUALA": "currenttime", "HORAACTUAL": "currenthour", "CURRENTHOUR": "currenthour", "ORACORRENTA": "currenthour", "ORAACTUALA": "currenthour", "MESLOCAL": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LOCALMONTH1": "localmonth1", "NOMMESLOCAL": "localmonthname", "LOCALMONTHNAME": "localmonthname", "NOMGENMESLOCAL": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "ABREVMESLOCAL": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "DIALOCAL": "localday", "LOCALDAY": "localday", "JORNLOCAL": "localday", "DIALOCAL2": "localday2", "LOCALDAY2": "localday2", "JORNLOCAL2": "localday2", "NOMDIALOCAL": "localdayname", "LOCALDAYNAME": "localdayname", "NOMJORNLOCAL": "localdayname", "ANYLOCAL": "localyear", "LOCALYEAR": "localyear", "ANNADALOCALA": "localyear", "HORARILOCAL": "localtime", "LOCALTIME": "localtime", "ORARILOCAL": "localtime", "HORALOCAL": "localhour", "LOCALHOUR": "localhour", "ORALOCALA": "localhour", "NOMSIT": "sitename", "NOMSITE_NOMSITI": "sitename", "SITENAME": "sitename", "SETMANACORRENTA": "currentweek", "CURRENTWEEK": "currentweek", "JDSCORRENT": "currentdow", "CURRENTDOW": "currentdow", "SETMANALOCALA": "localweek", "LOCALWEEK": "localweek", "JDSLOCAL": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "VERSIÓACTUAL": "currentversion", "VERSIOACTUAL": "currentversion", "CURRENTVERSION": "currentversion", "VERSIONACTUALA": "currentversion", "INSTANTACTUAL": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "INSTANTLOCAL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "MARCADIRECCION": "directionmark", "MARCADIR": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "IDIOMACONTINGUT": "contentlanguage", "LLENGUACONTINGUT": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "LENGACONTENGUT": "contentlanguage", "LENGCONTENGUT": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^((?:[a-zàèéíòóúç·ïü]|'(?!'))+)(.*)$/sDu"}}