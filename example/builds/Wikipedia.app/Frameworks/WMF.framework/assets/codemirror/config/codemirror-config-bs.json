{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "dynamicpagelist": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__bezsadržaja__": "notoc", "__notoc__": "notoc", "__bezgalerije__": "nogallery", "__nogallery__": "nogallery", "__forsiranisadržaj__": "forcetoc", "__forcetoc__": "forcetoc", "__sadržaj__": "toc", "__toc__": "toc", "__bez_izmjena__": "noeditsection", "__bezizmjena__": "noeditsection", "__noeditsection__": "noeditsection", "__beztc__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__bezcc__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LINKNOVESEKCIJE__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__SAKRIVENAKATEGORIJA__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__SADRZAJ__": "index", "__INDEX__": "index", "__BEZSADRZAJA__": "noindex", "__NOINDEX__": "noindex", "__STATISTICNOPREUSMJERENJE__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"ip": "ns", "ns": "ns", "nse": "nse", "dekodirajadresu": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "lcprvi": "lcfirst", "lcfirst": "lcfirst", "ucprvi": "ucfirst", "ucfirst": "ucfirst", "lc": "lc", "uc": "uc", "lokalnaadresa": "<PERSON>url", "localurl": "<PERSON>url", "lokalneadrese": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "punurl": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "punurle": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "numerickiformat": "formatnum", "formatnum": "formatnum", "gramatika": "grammar", "grammar": "grammar", "pol": "gender", "gender": "gender", "množina": "plural", "plural": "plural", "bidi": "bidi", "#jezik": "language", "#language": "language", "jastuklijevo": "padleft", "padleft": "padleft", "jastukdesno": "padright", "padright": "padright", "anchorencode": "anchorencode", "stazadatoteke": "filepath", "filepath": "filepath", "pageid": "pageid", "int": "int", "#specijalno": "special", "#special": "special", "#speciale": "speciale", "#oznaka": "tag", "#tag": "tag", "#formatdatuma": "formatdate", "#formatdate": "formatdate", "#dateformat": "formatdate", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#if": "if", "#ifeq": "ifeq", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "articlepath": "articlepath", "server": "server", "imeservera": "servername", "servername": "servername", "skripta": "scriptpath", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"BROJSTRANICA": "numberofpages", "NUMBEROFPAGES": "numberofpages", "BROJKORISNIKA": "numberofusers", "NUMBEROFUSERS": "numberofusers", "BROJAKTIVNIHKORISNIKA": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "BROJČLANAKA": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "BROJDATOTEKA": "numberoffiles", "BROJFAJLOVA": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "BROJADMINISTRATORA": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "BROJUGRUPI": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "BROJPROMJENA": "numberofedits", "NUMBEROFEDITS": "numberofedits", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "STRANICEUKATEGORIJI": "pagesincategory", "STRANICEUKAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "VELICINASTRANICE": "pagesize", "PAGESIZE": "pagesize", "NIVOZASTITE": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "IMENSKIPROSTORI": "namespacee", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "PROSTORZARAZGOVOR": "talkspace", "TALKSPACE": "talkspace", "PROSTORIZARAZGOVOR": "talkspacee", "TALKSPACEE": "talkspacee", "PROSTORSUBJEKTA": "subjectspace", "PROSTORCLANAKA": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "PROSTORISUBJEKTA": "subjectspacee", "PROSTORICLANKA": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "STRANICA": "pagename", "PAGENAME": "pagename", "STRANICE": "pagenamee", "PAGENAMEE": "pagenamee", "PUNOIMESTRANE": "fullpagename", "FULLPAGENAME": "fullpagename", "PUNOIMESTRANEE": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "IMEBAZNESTRANICE": "basepagename", "BASEPAGENAME": "basepagename", "IMENABAZNESTRANICE": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "IMEPODSTRANICE": "subpagename", "SUBPAGENAME": "subpagename", "IMENAPODSTRANICE": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "IMESTRANICERAZGOVORA": "talkpagename", "TALKPAGENAME": "talkpagename", "IMENASTRANICERAZGOVORA": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "IMESTRANICESUBKJEKTA": "subjectpagename", "IMESTRANICECLANKA": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "IMENASTRANICESUBJEKTA": "subjectpagenamee", "IMENASTRANICECLANAKA": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "IDREVIZIJE": "revisionid", "REVISIONID": "revisionid", "REVIZIJEDANA": "revisionday", "REVISIONDAY": "revisionday", "REVIZIJEDANA2": "revisionday2", "REVISIONDAY2": "revisionday2", "REVIZIJAMJESECA": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "REVIZIJAGODINE": "revisionyear", "REVISIONYEAR": "revisionyear", "REVIZIJAVREMENSKOGPECATA": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "IMENSKIPROSTOR": "namespace", "NAMESPACE": "namespace", "POKAZINASLOV": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "TRENUTNIMJESEC": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "TRENUTNIMJESEC1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "TRENUTNIMJESECIME": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "TRENUTNIMJESECROD": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "TRENUTNIMJESECSKR": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "TRENUTNIDAN": "currentday", "CURRENTDAY": "currentday", "TRENUTNIDAN2": "currentday2", "CURRENTDAY2": "currentday2", "TRENUTNIDANIME": "currentdayname", "CURRENTDAYNAME": "currentdayname", "TRENUTNAGODINA": "currentyear", "CURRENTYEAR": "currentyear", "TRENUTNOVRIJEME": "currenttime", "CURRENTTIME": "currenttime", "TRENUTNISAT": "currenthour", "CURRENTHOUR": "currenthour", "LOKALNIMJESEC": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LOKALNIMJESEC1": "localmonth1", "LOCALMONTH1": "localmonth1", "LOKALNIMJESECIME": "localmonthname", "LOCALMONTHNAME": "localmonthname", "LOKALNIMJESECIMEROD": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "LOKALNIMJESECSKR": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "LOKALNIDAN": "localday", "LOCALDAY": "localday", "LOKALNIDAN2": "localday2", "LOCALDAY2": "localday2", "LOKALNIDANIME": "localdayname", "LOCALDAYNAME": "localdayname", "LOKALNAGODINA": "localyear", "LOCALYEAR": "localyear", "LOKALNOVRIJEME": "localtime", "LOCALTIME": "localtime", "LOKALNISAT": "localhour", "LOCALHOUR": "localhour", "IMESAJTA": "sitename", "SITENAME": "sitename", "TRENUTNASEDMICA": "currentweek", "CURRENTWEEK": "currentweek", "TRENUTNIDOV": "currentdow", "CURRENTDOW": "currentdow", "LOKALNASEDMICA": "localweek", "LOCALWEEK": "localweek", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "SADASNJAVERZIJA": "currentversion", "CURRENTVERSION": "currentversion", "SADASNJIVREMENSKIPECAT": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "LOKALNIVREMENSKIPECAT": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]+)(.*)$/sDu"}}