{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__aucunsommaire__": "notoc", "__aucunetdm__": "notoc", "__notoc__": "notoc", "__aucunegalerie__": "nogallery", "__nogallery__": "nogallery", "__forcersommaire__": "forcetoc", "__forcertdm__": "forcetoc", "__forcetoc__": "forcetoc", "__sommaire__": "toc", "__tdm__": "toc", "__toc__": "toc", "__sectionnoneditable__": "noeditsection", "__noeditsection__": "noeditsection", "__sansconversiontitre__": "notitleconvert", "__sansct__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__sansconversioncontenu__": "nocontentconvert", "__sanscc__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LIENNOUVELLESECTION__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__AUCUNLIENNOUVELLESECTION__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__CATCACHEE__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEX__": "index", "__AUCUNINDEX__": "noindex", "__NOINDEX__": "noindex", "__REDIRECTIONSTATIQUE__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"espacen": "ns", "ns": "ns", "espacenx": "nse", "nse": "nse", "encodeurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "initminus": "lcfirst", "lcfirst": "lcfirst", "initmajus": "ucfirst", "initcapit": "ucfirst", "ucfirst": "ucfirst", "minus": "lc", "lc": "lc", "majus": "uc", "capit": "uc", "uc": "uc", "urllocale": "<PERSON>url", "localurl": "<PERSON>url", "urllocalex": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "urlklok": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlcomplete": "<PERSON><PERSON>l", "urlcompletex": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "urlcanonique": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "urlcanoniquex": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnombre": "formatnum", "formatnum": "formatnum", "yezhadur": "grammar", "grammar": "grammar", "grammaire": "grammar", "jener": "gender", "gender": "gender", "genre": "gender", "liester": "plural", "plural": "plural", "pluriel": "plural", "bidi": "bidi", "#yezh": "language", "#language": "language", "#langue": "language", "bourragegauche": "padleft", "bourregauche": "padleft", "padleft": "padleft", "bourragedroite": "padright", "bourredroite": "padright", "padright": "padright", "encodeancre": "anchorencode", "anchorencode": "anchorencode", "chemin": "filepath", "filepath": "filepath", "idpage": "pageid", "pageid": "pageid", "int": "int", "#dibar": "special", "#special": "special", "#spécial": "special", "#spéciale": "speciale", "#speciale": "speciale", "#balise": "tag", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#gwezennadurrummad": "categorytree", "#arbrecatégories": "categorytree", "#arbrecats": "categorytree", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#galv": "invoke", "#invoque": "invoke", "#invoke": "invoke", "#related": "related", "#si": "if", "#if": "if", "#si=": "ifeq", "#ifeq": "ifeq", "#selon": "switch", "#switch": "switch", "#siexiste": "ifexist", "#ifexist": "ifexist", "#siexpr": "ifexpr", "#ifexpr": "ifexpr", "#sierreur": "iferror", "#iferror": "iferror", "#amzer": "time", "#heure": "time", "#time": "time", "#heurel": "timel", "#timel": "timel", "#expr": "expr", "#relenabs": "rel2abs", "#rel2abs": "rel2abs", "#partiestitre": "titleparts", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "sanslienexterne": "noexternallanglinks", "noexternallanglinks": "noexternallanglinks", "#propriété": "property", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "cheminarticle": "articlepath", "articlepath": "articlepath", "servijer": "server", "server": "server", "serveur": "server", "anvservijer": "servername", "servername": "servername", "nomserveur": "servername", "cheminscript": "scriptpath", "scriptpath": "scriptpath", "cheminstyle": "stylepath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NIVERABAJENNOU": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NOMBREPAGES": "numberofpages", "NIVERAIMPLIJERIEN": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NOMBREUTILISATEURS": "numberofusers", "NIVERAIMPLIJERIENOBERIANT": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NOMBREUTILISATEURSACTIFS": "numberofactiveusers", "NIVERABENNADOU": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NOMBREARTICLES": "numberofarticles", "NIVERARESTROU": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NOMBREFICHIERS": "numberoffiles", "NOMBREADMINS": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NOMBREDANSGROUPE": "numberingroup", "NBDANSGROUPE": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NIVERAZEGASEDENNOU": "numberofedits", "NUMBEROFEDITS": "numberofedits", "NOMBREMODIFS": "numberofedits", "CLEFDETRI": "defaultsort", "CLEDETRI": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PAGESDANSCAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "MENTPAJENN": "pagesize", "PAGESIZE": "pagesize", "TAILLEPAGE": "pagesize", "NIVEAUDEPROTECTION": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "ESAOUENNANVSK": "namespacee", "NAMESPACEE": "namespacee", "ESPACENOMMAGEX": "namespacee", "NOMBREESPACENOMMAGE": "namespacenumber", "NAMESPACENUMBER": "namespacenumber", "ESPACEDISCUSSION": "talkspace", "TALKSPACE": "talkspace", "ESPACEDISCUSSIONX": "talkspacee", "TALKSPACEE": "talkspacee", "ESPACESUJET": "subjectspace", "ESPACEARTICLE": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "ESPACESUJETX": "subjectspacee", "ESPACEARTICLEX": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "ANVPAJENN": "pagename", "PAGENAME": "pagename", "NOMPAGE": "pagename", "ANVPAJENNSK": "pagenamee", "PAGENAMEE": "pagenamee", "NOMPAGEX": "pagenamee", "ANVPAJENNKLOK": "fullpagename", "FULLPAGENAME": "fullpagename", "NOMPAGECOMPLET": "fullpagename", "ANVPAJENNKLOKSK": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "NOMPAGECOMPLETX": "fullpagenamee", "NOMPAGERACINE": "rootpagename", "ROOTPAGENAME": "rootpagename", "NOMPAGERACINEX": "rootpagenamee", "ROOTPAGENAMEE": "rootpagenamee", "NOMBASEDEPAGE": "basepagename", "BASEPAGENAME": "basepagename", "NOMBASEDEPAGEX": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "ANVISPAJENN": "subpagename", "SUBPAGENAME": "subpagename", "NOMSOUSPAGE": "subpagename", "NOMSOUSPAGEX": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "NOMPAGEDISCUSSION": "talkpagename", "TALKPAGENAME": "talkpagename", "NOMPAGEDISCUSSIONX": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NOMPAGESUJET": "subjectpagename", "NOMPAGEARTICLE": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NOMPAGESUJETX": "subjectpagenamee", "NOMPAGEARTICLEX": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "IDVERSION": "revisionid", "REVISIONID": "revisionid", "JOURVERSION": "revisionday", "JOUR1VERSION": "revisionday", "REVISIONDAY": "revisionday", "JOUR2VERSION": "revisionday2", "REVISIONDAY2": "revisionday2", "MOISVERSION": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "MOISVERSION1": "revisionmonth1", "REVISIONMONTH1": "revisionmonth1", "ANNEEVERSION": "revisionyear", "REVISIONYEAR": "revisionyear", "INSTANTVERSION": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "UTILISATEURVERSION": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "ESAOUENNANV": "namespace", "NAMESPACE": "namespace", "ESPACENOMMAGE": "namespace", "AFFICHERTITRE": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "MOISACTUEL": "currentmonth", "MOIS2ACTUEL": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "MOIS1ACTUEL": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "NOMMOISACTUEL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "NOMGENMOISACTUEL": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "ABREVMOISACTUEL": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "JOURACTUEL": "currentday", "JOUR1ACTUEL": "currentday", "CURRENTDAY": "currentday", "JOUR2ACTUEL": "currentday2", "CURRENTDAY2": "currentday2", "NOMJOURACTUEL": "currentdayname", "CURRENTDAYNAME": "currentdayname", "ANNEEACTUELLE": "currentyear", "CURRENTYEAR": "currentyear", "HORAIREACTUEL": "currenttime", "CURRENTTIME": "currenttime", "HEUREACTUELLE": "currenthour", "CURRENTHOUR": "currenthour", "MOISLOCAL": "localmonth", "MOIS2LOCAL": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "MOIS1LOCAL": "localmonth1", "LOCALMONTH1": "localmonth1", "NOMMOISLOCAL": "localmonthname", "LOCALMONTHNAME": "localmonthname", "NOMGENMOISLOCAL": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "ABREVMOISLOCAL": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "JOURLOCAL": "localday", "JOUR1LOCAL": "localday", "LOCALDAY": "localday", "JOUR2LOCAL": "localday2", "LOCALDAY2": "localday2", "NOMJOURLOCAL": "localdayname", "LOCALDAYNAME": "localdayname", "ANNEELOCALE": "localyear", "LOCALYEAR": "localyear", "HORAIRELOCAL": "localtime", "LOCALTIME": "localtime", "HEURELOCALE": "localhour", "LOCALHOUR": "localhour", "ANVLEC'HIENN": "sitename", "SITENAME": "sitename", "NOMSITE": "sitename", "SEMAINEACTUELLE": "currentweek", "CURRENTWEEK": "currentweek", "JDSACTUEL": "currentdow", "CURRENTDOW": "currentdow", "SEMAINELOCALE": "localweek", "LOCALWEEK": "localweek", "JDSLOCAL": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "STUMMRED": "currentversion", "CURRENTVERSION": "currentversion", "VERSIONACTUELLE": "currentversion", "INSTANTACTUEL": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "INSTANTLOCAL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "MARQUEDIRECTION": "directionmark", "MARQUEDIR": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "LANGUECONTENU": "contentlanguage", "LANGCONTENU": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^((?:c\\'h|C\\'H|C\\'h|c’h|C’H|C’h|[a-zA-ZàâçéèêîôûäëïöüùñÇÉÂÊÎÔÛÄËÏÖÜÀÈÙÑ])+)(.*)$/sDu"}}