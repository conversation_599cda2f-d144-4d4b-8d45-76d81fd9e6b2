{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__безсъдържание__": "notoc", "__notoc__": "notoc", "__безгалерия__": "nogallery", "__nogallery__": "nogallery", "__съссъдържание__": "forcetoc", "__forcetoc__": "forcetoc", "__съдържание__": "toc", "__toc__": "toc", "__без_редактиране_на_раздели__": "noeditsection", "__noeditsection__": "noeditsection", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__ВРЪЗКА_ЗА_НОВ_РАЗДЕЛ__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__СКРИТАКАТЕГОРИЯ__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__ИНДЕКСИРАНЕ__": "index", "__INDEX__": "index", "__БЕЗИНДЕКСИРАНЕ__": "noindex", "__NOINDEX__": "noindex", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"ип": "ns", "ns": "ns", "nse": "nse", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "мбпърва": "lcfirst", "lcfirst": "lcfirst", "гбпърва": "ucfirst", "ucfirst": "ucfirst", "мб": "lc", "lc": "lc", "гб": "uc", "uc": "uc", "локаленадрес": "<PERSON>url", "localurl": "<PERSON>url", "локаленадреси": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "пълен_адрес": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "пълен_адреси": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnum": "formatnum", "граматика": "grammar", "grammar": "grammar", "пол": "gender", "gender": "gender", "мн_число": "plural", "plural": "plural", "bidi": "bidi", "#език": "language", "#language": "language", "padleft": "padleft", "padright": "padright", "anchorencode": "anchorencode", "filepath": "filepath", "pageid": "pageid", "вътр": "int", "int": "int", "#special": "special", "#speciale": "speciale", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#if": "if", "#ifeq": "ifeq", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "articlepath": "articlepath", "сървър": "server", "server": "server", "именасървъра": "servername", "servername": "servername", "пътдоскрипта": "scriptpath", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"БРОЙСТРАНИЦИ": "numberofpages", "NUMBEROFPAGES": "numberofpages", "БРОЙПОТРЕБИТЕЛИ": "numberofusers", "NUMBEROFUSERS": "numberofusers", "БРОЙАКТИВНИПОТРЕБИТЕЛИ": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "БРОЙСТАТИИ": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "БРОЙФАЙЛОВЕ": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "БРОЙАДМИНИСТРАТОРИ": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "БРОЙРЕДАКЦИИ": "numberofedits", "NUMBEROFEDITS": "numberofedits", "СОРТКАТ": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "PAGESIZE": "pagesize", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "ИМЕННОПРОСТРАНСТВОИ": "namespacee", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "TALKSPACE": "talkspace", "TALKSPACEE": "talkspacee", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "СТРАНИЦА": "pagename", "PAGENAME": "pagename", "СТРАНИЦАИ": "pagenamee", "PAGENAMEE": "pagenamee", "ПЪЛНОИМЕ_СТРАНИЦА": "fullpagename", "FULLPAGENAME": "fullpagename", "ПЪЛНОИМЕ_СТРАНИЦАИ": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "BASEPAGENAME": "basepagename", "BASEPAGENAMEE": "basepagenamee", "ИМЕ_ПОДСТРАНИЦА": "subpagename", "SUBPAGENAME": "subpagename", "ИМЕ_ПОДСТРАНИЦАИ": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "ИМЕ_БЕСЕДА": "talkpagename", "TALKPAGENAME": "talkpagename", "ИМЕ_БЕСЕДАИ": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "ИД_НА_ВЕРСИЯТА": "revisionid", "REVISIONID": "revisionid", "ДЕН_НА_ВЕРСИЯТА": "revisionday", "REVISIONDAY": "revisionday", "ДЕН_НА_ВЕРСИЯТА2": "revisionday2", "REVISIONDAY2": "revisionday2", "МЕСЕЦ_НА_ВЕРСИЯТА": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "ГОДИНА_НА_ВЕРСИЯТА": "revisionyear", "REVISIONYEAR": "revisionyear", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "ИМЕННОПРОСТРАНСТВО": "namespace", "NAMESPACE": "namespace", "ПОКАЗВ_ЗАГЛАВИЕ": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "ТЕКУЩМЕСЕЦ": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "ТЕКУЩМЕСЕЦ1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "ТЕКУЩМЕСЕЦИМЕ": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "ТЕКУЩМЕСЕЦИМЕРОД": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "ТЕКУЩМЕСЕЦСЪКР": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "ТЕКУЩДЕН": "currentday", "CURRENTDAY": "currentday", "ТЕКУЩДЕН2": "currentday2", "CURRENTDAY2": "currentday2", "ТЕКУЩДЕНИМЕ": "currentdayname", "CURRENTDAYNAME": "currentdayname", "ТЕКУЩАГОДИНА": "currentyear", "CURRENTYEAR": "currentyear", "ТЕКУЩОВРЕМЕ": "currenttime", "CURRENTTIME": "currenttime", "ТЕКУЩЧАС": "currenthour", "CURRENTHOUR": "currenthour", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LOCALMONTH1": "localmonth1", "LOCALMONTHNAME": "localmonthname", "LOCALMONTHNAMEGEN": "localmonthnamegen", "LOCALMONTHABBREV": "localmonthabbrev", "LOCALDAY": "localday", "LOCALDAY2": "localday2", "LOCALDAYNAME": "localdayname", "LOCALYEAR": "localyear", "LOCALTIME": "localtime", "LOCALHOUR": "localhour", "ИМЕНАСАЙТА": "sitename", "SITENAME": "sitename", "ТЕКУЩАСЕДМИЦА": "currentweek", "CURRENTWEEK": "currentweek", "ТЕКУЩ_ДЕН_ОТ_СЕДМИЦАТА": "currentdow", "CURRENTDOW": "currentdow", "LOCALWEEK": "localweek", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "CURRENTVERSION": "currentversion", "CURRENTTIMESTAMP": "currenttimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zабвгдежзийклмнопрстуфхцчшщъыьэюя]+)(.*)$/sDu"}}