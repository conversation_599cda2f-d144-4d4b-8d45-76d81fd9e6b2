{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__tsyasianalahatra__": "notoc", "__aucunsommaire__": "notoc", "__aucunetdm__": "notoc", "__notoc__": "notoc", "__tsyasianagallery__": "nogallery", "__aucunegalerie__": "nogallery", "__nogallery__": "nogallery", "__tereonylahatra__": "forcetoc", "__forcersommaire__": "forcetoc", "__forcertdm__": "forcetoc", "__forcetoc__": "forcetoc", "__lahatra__": "toc", "__lahat__": "toc", "__sommaire__": "toc", "__tdm__": "toc", "__toc__": "toc", "__tsyazoovaina__": "noeditsection", "__sectionnoneditable__": "noeditsection", "__noeditsection__": "noeditsection", "__sansconversiontitre__": "notitleconvert", "__sansct__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__sansconversioncontenu__": "nocontentconvert", "__sanscc__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LIENNOUVELLESECTION__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__AUCUNLIENNOUVELLESECTION__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__CATCACHEE__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEX__": "index", "__AUCUNINDEX__": "noindex", "__NOINDEX__": "noindex", "__REDIRECTIONSTATIQUE__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"espacen": "ns", "ns": "ns", "espacenx": "nse", "nse": "nse", "encodeurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "initminus": "lcfirst", "lcfirst": "lcfirst", "initmajus": "ucfirst", "initcapit": "ucfirst", "ucfirst": "ucfirst", "minus": "lc", "lc": "lc", "majus": "uc", "capit": "uc", "uc": "uc", "urllocale": "<PERSON>url", "localurl": "<PERSON>url", "urllocalex": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "urlrehetra": "<PERSON><PERSON>l", "urlcomplete": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlrehetrax": "<PERSON><PERSON><PERSON>", "urlcompletex": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "urlcanonique": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "urlcanoniquex": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnombre": "formatnum", "formatnum": "formatnum", "grammaire": "grammar", "grammar": "grammar", "genre": "gender", "gender": "gender", "pluriel": "plural", "plural": "plural", "bidi": "bidi", "#langue": "language", "#language": "language", "bourragegauche": "padleft", "bourregauche": "padleft", "padleft": "padleft", "bourragedroite": "padright", "bourredroite": "padright", "padright": "padright", "encodeancre": "anchorencode", "anchorencode": "anchorencode", "chemin": "filepath", "filepath": "filepath", "idpage": "pageid", "pageid": "pageid", "int": "int", "#spécial": "special", "#special": "special", "#spéciale": "speciale", "#speciale": "speciale", "#balise": "tag", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#karazantsokajy": "categorytree", "#categorytree": "categorytree", "#arbrecatégories": "categorytree", "#arbrecats": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoque": "invoke", "#invoke": "invoke", "#related": "related", "#raha": "if", "#if": "if", "#si": "if", "#rahamitovy": "ifeq", "#ifeq": "ifeq", "#si=": "ifeq", "#selon": "switch", "#switch": "switch", "#rahamisy": "ifexist", "#ifexist": "ifexist", "#siexiste": "ifexist", "#rahamarina": "ifexpr", "#ifexpr": "ifexpr", "#siexpr": "ifexpr", "#rahadiso": "iferror", "#iferror": "iferror", "#sierreur": "iferror", "#lera": "time", "#time": "time", "#heure": "time", "#heurel": "timel", "#timel": "timel", "#expr": "expr", "#relenabs": "rel2abs", "#rel2abs": "rel2abs", "#partiestitre": "titleparts", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "sanslienexterne": "noexternallanglinks", "noexternallanglinks": "noexternallanglinks", "#propriété": "property", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "cheminarticle": "articlepath", "articlepath": "articlepath", "serveur": "server", "server": "server", "nomserveur": "servername", "servername": "servername", "cheminscript": "scriptpath", "scriptpath": "scriptpath", "cheminstyle": "stylepath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"ISAPEJY": "numberofpages", "NOMBREPAGES": "numberofpages", "NUMBEROFPAGES": "numberofpages", "ISAMPIKAMBANA": "numberofusers", "NOMBREUTILISATEURS": "numberofusers", "NUMBEROFUSERS": "numberofusers", "ISAMPIKAMBANAMANOVA": "numberofactiveusers", "NOMBREUTILISATEURSACTIFS": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "ISALAHATSORATRA": "numberofarticles", "NOMBREARTICLES": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "ISARAKITRA": "numberoffiles", "NOMBREFICHIERS": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NOMBREADMINS": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NOMBREDANSGROUPE": "numberingroup", "NBDANSGROUPE": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "ISAFANOVANA": "numberofedits", "NOMBREMODIFS": "numberofedits", "NUMBEROFEDITS": "numberofedits", "CLEFDETRI": "defaultsort", "CLEDETRI": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PAGESDANSCAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "TAILLEPAGE": "pagesize", "PAGESIZE": "pagesize", "NIVEAUDEPROTECTION": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "ANARANTSEHATRAX": "namespacee", "ANARANASEHATRAX": "namespacee", "ESPACENOMMAGEX": "namespacee", "NAMESPACEE": "namespacee", "NOMBREESPACENOMMAGE": "namespacenumber", "NAMESPACENUMBER": "namespacenumber", "PEJINDRESAKA": "talkspace", "PEJYRESAKA": "talkspace", "DINIKA": "talkspace", "ESPACEDISCUSSION": "talkspace", "TALKSPACE": "talkspace", "PEJINDRESAKAX": "talkspacee", "PEJYRESAKAX": "talkspacee", "DINIKAX": "talkspacee", "ESPACEDISCUSSIONX": "talkspacee", "TALKSPACEE": "talkspacee", "TOERANALAHATSORATRA": "subjectspace", "ESPACESUJET": "subjectspace", "ESPACEARTICLE": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "TOERANNYLAHATSORATRA": "subjectspacee", "ESPACESUJETX": "subjectspacee", "ESPACEARTICLEX": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "ANARAMPEJY": "pagename", "ANARANAPEJY": "pagename", "NOMPAGE": "pagename", "PAGENAME": "pagename", "ANARAMPEJYX": "pagenamee", "ANARANAPEJYX": "pagenamee", "NOMPAGEX": "pagenamee", "PAGENAMEE": "pagenamee", "ANARAMPEJYFENO": "fullpagename", "ANARANAPEJYFENO": "fullpagename", "NOMPAGECOMPLET": "fullpagename", "FULLPAGENAME": "fullpagename", "ANARAMPEJYFENOX": "fullpagenamee", "ANARANAPEJYFENOX": "fullpagenamee", "NOMPAGECOMPLETX": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "NOMPAGERACINE": "rootpagename", "ROOTPAGENAME": "rootpagename", "NOMPAGERACINEX": "rootpagenamee", "ROOTPAGENAMEE": "rootpagenamee", "ANARANAFOTOPEJY": "basepagename", "ANARAMPOTOPEJY": "basepagename", "NOMBASEDEPAGE": "basepagename", "BASEPAGENAME": "basepagename", "ANARANAFOTOPEJYE": "basepagenamee", "ANARAMPOTOPEJYE": "basepagenamee", "NOMBASEDEPAGEX": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "ANARANAZANAPEJY": "subpagename", "ANARANJANAPEJY": "subpagename", "NOMSOUSPAGE": "subpagename", "SUBPAGENAME": "subpagename", "ANARANJANAPEJYX": "subpagenamee", "ANARANAZANAPEJYX": "subpagenamee", "NOMSOUSPAGEX": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "ANARAMPEJINDRESAKA": "talkpagename", "ANARANAPEJINDRESAKA": "talkpagename", "NOMPAGEDISCUSSION": "talkpagename", "TALKPAGENAME": "talkpagename", "NOMPAGEDISCUSSIONX": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NOMPAGESUJET": "subjectpagename", "NOMPAGEARTICLE": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NOMPAGESUJETX": "subjectpagenamee", "NOMPAGEARTICLEX": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "IDVERSION": "revisionid", "REVISIONID": "revisionid", "JOURVERSION": "revisionday", "JOUR1VERSION": "revisionday", "REVISIONDAY": "revisionday", "JOUR2VERSION": "revisionday2", "REVISIONDAY2": "revisionday2", "MOISVERSION": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "MOISVERSION1": "revisionmonth1", "REVISIONMONTH1": "revisionmonth1", "ANNEEVERSION": "revisionyear", "REVISIONYEAR": "revisionyear", "INSTANTVERSION": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "UTILISATEURVERSION": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "ANARANTSEHATRA": "namespace", "ANARANASEHATRA": "namespace", "ESPACENOMMAGE": "namespace", "NAMESPACE": "namespace", "ASEHOLOHATENY": "displaytitle", "AFFICHERTITRE": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "VOLANA": "currentmonth", "MOISACTUEL": "currentmonth", "MOIS2ACTUEL": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "VOLANA1": "currentmonth1", "MOIS1ACTUEL": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "ANARAMBOLANA": "currentmonthname", "NOMMOISACTUEL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthnamegen", "ANARAMBOLANAGEN": "currentmonthnamegen", "ANARANAVOLANA": "currentmonthnamegen", "NOMGENMOISACTUEL": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "ANARAMBOLANAFOHY": "<PERSON><PERSON><PERSON><PERSON>v", "ANARANAVOLANAFOHY": "<PERSON><PERSON><PERSON><PERSON>v", "ABREVMOISACTUEL": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "ANDRO": "currentday", "JOURACTUEL": "currentday", "JOUR1ACTUEL": "currentday", "CURRENTDAY": "currentday", "ANDRO2": "currentday2", "JOUR2ACTUEL": "currentday2", "CURRENTDAY2": "currentday2", "ANARANANDRO": "currentdayname", "ANARANAANDRO": "currentdayname", "NOMJOURACTUEL": "currentdayname", "CURRENTDAYNAME": "currentdayname", "TAONA": "currentyear", "ANNEEACTUELLE": "currentyear", "CURRENTYEAR": "currentyear", "LERA": "currenttime", "HORAIREACTUEL": "currenttime", "CURRENTTIME": "currenttime", "ORA": "currenthour", "HEUREACTUELLE": "currenthour", "CURRENTHOUR": "currenthour", "VOLANAANTOERANA": "localmonth", "MOISLOCAL": "localmonth", "MOIS2LOCAL": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "VOLANAANTOERANA1": "localmonth1", "MOIS1LOCAL": "localmonth1", "LOCALMONTH1": "localmonth1", "ANARAMBOLANAANTOERANA": "localmonthname", "NOMMOISLOCAL": "localmonthname", "LOCALMONTHNAME": "localmonthname", "ANARAMBOLANAANTOERANAGEN": "localmonthnamegen", "NOMGENMOISLOCAL": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "ANARAMBOLANAANTOERANAFOHY": "localmonthabbrev", "ABREVMOISLOCAL": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "ANDROANTOERANA": "localday", "JOURLOCAL": "localday", "JOUR1LOCAL": "localday", "LOCALDAY": "localday", "ANDROANTOERANA2": "localday2", "JOUR2LOCAL": "localday2", "LOCALDAY2": "localday2", "ANARANANDROANTOERANA": "localdayname", "NOMJOURLOCAL": "localdayname", "LOCALDAYNAME": "localdayname", "TAONAANTOERANA": "localyear", "ANNEELOCALE": "localyear", "LOCALYEAR": "localyear", "LERAANTOERANA": "localtime", "HORAIRELOCAL": "localtime", "LOCALTIME": "localtime", "ORAANTOERANA": "localhour", "HEURELOCALE": "localhour", "LOCALHOUR": "localhour", "NOMSITE": "sitename", "SITENAME": "sitename", "HERINANDRO": "currentweek", "SEMAINEACTUELLE": "currentweek", "CURRENTWEEK": "currentweek", "ALAHADY": "currentdow", "JDSACTUEL": "currentdow", "CURRENTDOW": "currentdow", "HERINANDROANTOERANA": "localweek", "SEMAINELOCALE": "localweek", "LOCALWEEK": "localweek", "ALAHADYANTOERANA": "localdow", "JDSLOCAL": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "VERSIONACTUELLE": "currentversion", "CURRENTVERSION": "currentversion", "INSTANTACTUEL": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "INSTANTLOCAL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "MARQUEDIRECTION": "directionmark", "MARQUEDIR": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "LANGUECONTENU": "contentlanguage", "LANGCONTENU": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zàâçéèêîôûäëïöüùÇÉÂÊÎÔÛÄËÏÖÜÀÈÙ]+)(.*)$/sDu"}}