{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "quiz": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__ingeninnehållsförteckning__": "notoc", "__notoc__": "notoc", "__ingetgalleri__": "nogallery", "__nogallery__": "nogallery", "__alltidinnehållsförteckning__": "forcetoc", "__forcetoc__": "forcetoc", "__innehållsförteckning__": "toc", "__toc__": "toc", "__interedigerasektion__": "noeditsection", "__noeditsection__": "noeditsection", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__NYTTAVSNITTLÄNK__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__DOLDKAT__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEXERA__": "index", "__INDEX__": "index", "__INTEINDEXERA_": "noindex", "__NOINDEX__": "noindex", "__STATISKOMDIRIGERING__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"nr": "ns", "ns": "ns", "nse": "nse", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "lbförst": "lcfirst", "lcfirst": "lcfirst", "ucfirst": "ucfirst", "sbförst": "ucfirst", "lb": "lc", "lc": "lc", "sb": "uc", "uc": "uc", "lokalurl": "<PERSON>url", "localurl": "<PERSON>url", "lokalurle": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "fullturl": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "fullturle": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formateranum": "formatnum", "formateratal": "formatnum", "formatnum": "formatnum", "grammatik": "grammar", "grammar": "grammar", "kön": "gender", "gender": "gender", "plural": "plural", "bidi": "bidi", "#språk": "language", "#language": "language", "padleft": "padleft", "padright": "padright", "anchorencode": "anchorencode", "filsökväg": "filepath", "filepath": "filepath", "pageid": "pageid", "int": "int", "#special": "special", "#speciale": "speciale", "#tagg": "tag", "#tag": "tag", "#formateradatum": "formatdate", "#datumformat": "formatdate", "#formatdate": "formatdate", "#dateformat": "formatdate", "#kategoriträd": "categorytree", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#om": "if", "#if": "if", "#omlika": "ifeq", "#ifeq": "ifeq", "#växel": "switch", "#switch": "switch", "#omfinns": "ifexist", "#ifexist": "ifexist", "#omutr": "ifexpr", "#ifexpr": "ifexpr", "#omfel": "iferror", "#iferror": "iferror", "#tid": "time", "#time": "time", "#tidl": "timel", "#timel": "timel", "#utr": "expr", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "#mentor": "mentor", "articlepath": "articlepath", "server": "server", "servernamn": "servername", "servername": "servername", "skriptsökväg": "scriptpath", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"ANTALSIDOR": "numberofpages", "NUMBEROFPAGES": "numberofpages", "ANTALANVÄNDARE": "numberofusers", "NUMBEROFUSERS": "numberofusers", "ANTALAKTIVAANVÄNDARE": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "ANTALARTIKLAR": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "ANTALFILER": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "ANTALADMINS": "numberofadmins", "ANTALADMINISTRATÖRER": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "ANTALIGRUPP": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "ANTALREDIGERINGAR": "numberofedits", "NUMBEROFEDITS": "numberofedits", "STANDARDSORTERING": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "SIDORIKATEGORI": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "SIDSTORLEK": "pagesize", "PAGESIZE": "pagesize", "SKYDDSNIVÅ": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "NAMNRYMDE": "namespacee", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "DISKUSSIONSRYMD": "talkspace", "TALKSPACE": "talkspace", "DISKUSSIONSRYMDE": "talkspacee", "TALKSPACEE": "talkspacee", "ARTIKELRYMD": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "ARTIKELRYMDE": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "SIDNAMN": "pagename", "PAGENAME": "pagename", "SIDNAMNE": "pagenamee", "PAGENAMEE": "pagenamee", "HELASIDNAMNET": "fullpagename", "FULLPAGENAME": "fullpagename", "HELASIDNAMNETE": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "GRUNDSIDNAMN": "basepagename", "BASEPAGENAME": "basepagename", "GRUNDSIDNAMNE": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "UNDERSIDNAMN": "subpagename", "SUBPAGENAME": "subpagename", "UNDERSIDNAMNE": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "DISKUSSIONSSIDNAMN": "talkpagename", "TALKPAGENAME": "talkpagename", "DISKUSSIONSSIDNAMNE": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "REVISIONSID": "revisionid", "REVISIONID": "revisionid", "REVISIONSDAG": "revisionday", "REVISIONDAY": "revisionday", "REVISIONSDAG2": "revisionday2", "REVISIONDAY2": "revisionday2", "REVISIONSMÅNAD": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "REVISIONSÅR": "revisionyear", "REVISIONYEAR": "revisionyear", "REVISIONSTIDSSTÄMPEL": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONSANVÄNDARE": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "NAMNRYMD": "namespace", "NAMESPACE": "namespace", "VISATITEL": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "NUVARANDEMÅNAD": "currentmonth", "NUMÅNAD": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "NUVARANDEMÅNAD1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "NUVARANDEMÅNADSNAMN": "currentmonthname", "NUMÅNADSNAMN": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "NUVARANDEMÅNADKORT": "<PERSON><PERSON><PERSON><PERSON>v", "NUMÅNADKORT": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "NUVARANDEDAG": "currentday", "NUDAG": "currentday", "CURRENTDAY": "currentday", "NUVARANDEDAG2": "currentday2", "NUDAG2": "currentday2", "CURRENTDAY2": "currentday2", "NUVARANDEDAGSNAMN": "currentdayname", "NUDAGSNAMN": "currentdayname", "CURRENTDAYNAME": "currentdayname", "NUVARANDEÅR": "currentyear", "NUÅR": "currentyear", "CURRENTYEAR": "currentyear", "NUVARANDETID": "currenttime", "NUTID": "currenttime", "CURRENTTIME": "currenttime", "NUVARANDETIMME": "currenthour", "NUTIMME": "currenthour", "CURRENTHOUR": "currenthour", "LOKALMÅNAD": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LOKALMÅNAD1": "localmonth1", "LOCALMONTH1": "localmonth1", "LOKALMÅNADSNAMN": "localmonthname", "LOCALMONTHNAME": "localmonthname", "LOCALMONTHNAMEGEN": "localmonthnamegen", "LOKALMÅNADKORT": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "LOKALDAG": "localday", "LOCALDAY": "localday", "LOKALDAG2": "localday2", "LOCALDAY2": "localday2", "LOKALDAGSNAMN": "localdayname", "LOCALDAYNAME": "localdayname", "LOKALTÅR": "localyear", "LOCALYEAR": "localyear", "LOKALTID": "localtime", "LOCALTIME": "localtime", "LOKALTIMME": "localhour", "LOCALHOUR": "localhour", "SAJTNAMN": "sitename", "SITENAMN": "sitename", "SITENAME": "sitename", "NUVARANDEVECKA": "currentweek", "NUVECKA": "currentweek", "CURRENTWEEK": "currentweek", "NUVARANDEVECKODAG": "currentdow", "CURRENTDOW": "currentdow", "LOKALVECKA": "localweek", "LOCALWEEK": "localweek", "LOKALVECKODAG": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "NUVARANDEVERSION": "currentversion", "NUVERSION": "currentversion", "CURRENTVERSION": "currentversion", "NUTIDSTÄMPEL": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "LOKALTIDSTÄMPEL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "INNEHÅLLSSPRÅK": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zå<PERSON>öéÅÄÖÉ]+)(.*)$/sDu"}}