{"wgPageParseReport":{"limitreport":{"cputime":"0.421","walltime":"0.440","ppvisitednodes":{"value":13033,"limit":1000000},"postexpandincludesize":{"value":8571,"limit":2097152},"templateargumentsize":{"value":438,"limit":2097152},"expansiondepth":{"value":10,"limit":40},"expensivefunctioncount":{"value":1,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":0,"limit":5000000},"entityaccesscount":{"value":0,"limit":400},"timingprofile":["100.00%  419.133      1 Template:Test_wiki","100.00%  419.133      1 -total"," 99.05%  415.148      1 Template:Test_wiki/core"," 87.05%  364.853      2 Template:Test_wiki/validcode","  2.74%   11.476      2 Template:Test_wiki/639-3"]},"scribunto":{"limitreport-timeusage":{"value":"0.005","limit":"10.000"},"limitreport-memusage":{"value":549585,"limit":52428800}},"cachereport":{"origin":"mw1329","timestamp":"20210414005654","ttl":2592000,"transientcontent":false}}});mw.config.set({"wgBackendResponseTime":141,"wgHostname":"mw1399"}