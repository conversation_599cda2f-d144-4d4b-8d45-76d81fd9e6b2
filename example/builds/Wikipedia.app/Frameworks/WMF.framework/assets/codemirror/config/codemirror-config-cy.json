{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__dimtaflencynnwys__": "notoc", "__dimrhestrgynnwys__": "notoc", "__dimrhg__": "notoc", "__notoc__": "notoc", "__nogallery__": "nogallery", "__forcetoc__": "forcetoc", "__toc__": "toc", "__dimadrangolygu__": "noeditsection", "__dimgolyguadran__": "noeditsection", "__noeditsection__": "noeditsection", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"_NEWSECTIONLINK_": "newsectionlink", "_CYSWLLTADRANNEWYDD_": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "_HIDDENCAT_": "hiddencat", "_CATCUDD_": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEX__": "index", "__NOINDEX__": "noindex", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"ns": "ns", "nse": "nse", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "lcfirst": "lcfirst", "ucfirst": "ucfirst", "lc": "lc", "uc": "uc", "localurl": "<PERSON>url", "localurle": "<PERSON><PERSON><PERSON>", "urlllawn": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlllawne": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "fformatiorhif": "formatnum", "formatnum": "formatnum", "grammar": "grammar", "gramadeg": "grammar", "gender": "gender", "lluosog": "plural", "plural": "plural", "bidi": "bidi", "#iaith": "language", "#language": "language", "padleft": "padleft", "padright": "padright", "anchorencode": "anchorencode", "filepath": "filepath", "pageid": "pageid", "int": "int", "#arbennig": "special", "#special": "special", "#speciale": "speciale", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#if": "if", "#ifeq": "ifeq", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "articlepath": "articlepath", "gweinydd": "server", "server": "server", "enw'rgweinydd": "servername", "servername": "servername", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NUMBEROFPAGES": "numberofpages", "NIFERYDEFNYDDWYR": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NIFEROERTHYGLAU": "numberofarticles", "NIFERYRERTHYGLAU": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NIFERYFFEILIAU": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NIFERYGWEINYDDWYR": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NIFERYGOLYGIADAU": "numberofedits", "NUMBEROFEDITS": "numberofedits", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "TUDALENNAUYNYCAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "MAINTTUD": "pagesize", "PAGESIZE": "pagesize", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "NAMESPACE": "namespacee", "PARTHE": "namespacee", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "TALKSPACE": "talkspace", "TALKSPACEE": "talkspacee", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "ENWTUDALEN": "pagename", "PAGENAME": "pagename", "ENWTUDALENE": "pagenamee", "PAGENAMEE": "pagenamee", "ENWLLAWNTUDALEN": "fullpagename", "FULLPAGENAME": "fullpagename", "ENWLLAWNTUDALENE": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "BASEPAGENAME": "basepagename", "BASEPAGENAMEE": "basepagenamee", "ENWISDUDALEN": "subpagename", "SUBPAGENAME": "subpagename", "ENWISDUDALENE": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "ENWTUDALENSGWRS": "talkpagename", "TALKPAGENAME": "talkpagename", "ENWTUDALENSGWRSE": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "IDYGOLYGIAD": "revisionid", "REVISIONID": "revisionid", "DIWRNODYGOLYGIAD": "revisionday", "REVISIONDAY": "revisionday", "DIWRNODYGOLYGIAD2": "revisionday2", "REVISIONDAY2": "revisionday2", "MISYGOLYGIAD": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "BLWYDDYNYGOLYGIAD": "revisionyear", "REVISIONYEAR": "revisionyear", "STAMPAMSERYGOLYGIAD": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "PARTH": "namespace", "DISPLAYTITLE": "displaytitle", "!": "!", "MISCYFOES": "currentmonth", "MISCYFREDOL": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "CURRENTMONTH1": "currentmonth1", "ENWMISCYFOES": "currentmonthname", "ENWMISCYFREDOL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "GENENWMISCYFOES": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "DYDDIADCYFOES": "currentday", "DYDDCYFREDOL": "currentday", "CURRENTDAY": "currentday", "CURRENTDAY2": "currentday2", "ENWDYDDCYFOES": "currentdayname", "ENWDYDDCYFREDOL": "currentdayname", "CURRENTDAYNAME": "currentdayname", "FLWYDDYNCYFOES": "currentyear", "BLWYDDYNGYFREDOL": "currentyear", "CURRENTYEAR": "currentyear", "AMSERCYFOES": "currenttime", "AMSERCYFREDOL": "currenttime", "CURRENTTIME": "currenttime", "AWRGYFREDOL": "currenthour", "CURRENTHOUR": "currenthour", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LOCALMONTH1": "localmonth1", "LOCALMONTHNAME": "localmonthname", "LOCALMONTHNAMEGEN": "localmonthnamegen", "LOCALMONTHABBREV": "localmonthabbrev", "LOCALDAY": "localday", "LOCALDAY2": "localday2", "LOCALDAYNAME": "localdayname", "LOCALYEAR": "localyear", "LOCALTIME": "localtime", "LOCALHOUR": "localhour", "SITENAME": "sitename", "WYTHNOSGYFREDOL": "currentweek", "CURRENTWEEK": "currentweek", "CURRENTDOW": "currentdow", "LOCALWEEK": "localweek", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "GOLYGIADCYFREDOL": "currentversion", "CURRENTVERSION": "currentversion", "STAMPAMSERCYFREDOL": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "STAMPAMSERLLEOL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "IAITHYCYNNWYS": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([àáâèéêìíîïòóôûŵŷa-z]+)(.*)$/sDu"}}