{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__χωρισππ__": "notoc", "__χωρισπινακαπεριεχομενων__": "notoc", "__notoc__": "notoc", "__χωρισπινακοθηκη__": "nogallery", "__nogallery__": "nogallery", "__μεππ__": "forcetoc", "__μεπινακαπεριεχομενων__": "forcetoc", "__forcetoc__": "forcetoc", "__ππ__": "toc", "__πινακασπεριεχομενων__": "toc", "__toc__": "toc", "__χωρισεπεξενοτ__": "noeditsection", "__χωρισεπεξεργασιαενοτητων__": "noeditsection", "__noeditsection__": "noeditsection", "__χωρισμετατροπητιτλου__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__χωρισμετατροπηπεριχομενου__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__ΔΕΣΜΟΣΝΕΑΣΕΝΟΤΗΤΑΣ__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__ΚΡΥΦΗΚΑΤΗΓΟΡΙΑ__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__ΕΥΡΕΤΗΡΙΟ__": "index", "__INDEX__": "index", "__ΧΩΡΙΣΕΥΡΕΤΗΡΙΟ__": "noindex", "__NOINDEX__": "noindex", "__ΣΤΑΤΙΚΗΑΝΑΚΑΤΕΥΘΥΝΣΗ__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"χο": "ns", "χωροσονοματων": "ns", "οχ": "ns", "ονοματοχωροσ": "ns", "ns": "ns", "nse": "nse", "κωδικοποιησηurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "πρωτοπεζο": "lcfirst", "lcfirst": "lcfirst", "πρωτοκεφαλαιο": "ucfirst", "ucfirst": "ucfirst", "πεζα": "lc", "lc": "lc", "κεφαλαια": "uc", "uc": "uc", "τοπικοurl": "<PERSON>url", "localurl": "<PERSON>url", "τοπικοurlκ": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "πληρεσurl": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "πληρεσurlκ": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "κανονικοurl": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "κανονικοurlκ": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "μορφοποιησηαριθμου": "formatnum", "formatnum": "formatnum", "γραμματικη": "grammar", "grammar": "grammar", "φυλο": "gender", "gender": "gender", "πληθυντικοσ": "plural", "plural": "plural", "bidi": "bidi", "#γλωσσα": "language", "#language": "language", "αριστεροπαραγεμισμα": "padleft", "padleft": "padleft", "δεξιπαραγεμισμα": "padright", "padright": "padright", "κωδικοποιησηαγκυρασ": "anchorencode", "anchorencode": "anchorencode", "διαδρομηαρχειου": "filepath", "filepath": "filepath", "pageid": "pageid", "εσωτ": "int", "int": "int", "#λειτουργία": "special", "#special": "special", "#speciale": "speciale", "#ετικέτα": "tag", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#if": "if", "#ifeq": "ifeq", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "articlepath": "articlepath", "εξυπηρετητησ": "server", "server": "server", "ονομαεξυπηρετητη": "servername", "servername": "servername", "διαδρομηπρογραμματοσ": "scriptpath", "scriptpath": "scriptpath", "διαδρομηστυλ": "stylepath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"ΑΡΙΘΜΟΣΣΕΛΙΔΩΝ": "numberofpages", "NUMBEROFPAGES": "numberofpages", "ΑΡΙΘΜΟΣΧΡΗΣΤΩΝ": "numberofusers", "NUMBEROFUSERS": "numberofusers", "ΕΝΕΡΓΟΙΧΡΗΣΤΕΣ": "numberofactiveusers", "ΑΡΙΘΜΟΣΕΝΕΡΓΩΝΧΡΗΣΤΩΝ": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "ΑΡΙΘΜΟΣΑΡΘΡΩΝ": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "ΑΡΙΘΜΟΣΑΡΧΕΙΩΝ": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "ΑΡΙΘΜΟΣΔΙΑΧΕΙΡΙΣΤΩΝ": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "ΟΜΑΔΑΑΡΙΘΜΗΣΗΣ": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "ΑΡΙΘΜΟΣΕΠΕΞΕΡΓΑΣΙΩΝ": "numberofedits", "NUMBEROFEDITS": "numberofedits", "ΠΡΟΚΑΘΟΡΙΣΜΕΝΗΤΑΞΙΝΟΜΗΣΗ": "defaultsort", "ΚΛΕΙΔΙΠΡΟΚΑΘΟΡΙΣΜΕΝΗΣΤΑΞΙΝΟΜΗΣΗΣ": "defaultsort", "ΠΡΟΚΑΘΟΡΙΣΜΕΝΗΤΑΞΙΝΟΜΗΣΗΚΑΤΗΓΟΡΙΑΣ": "defaultsort", "ΠΡΟΚΤΑΞ": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "ΣΕΛΙΔΕΣΣΤΗΝΚΑΤΗΓΟΡΙΑ": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "ΜΕΓΕΘΟΣΣΕΛΙΔΑΣ": "pagesize", "PAGESIZE": "pagesize", "ΕΠΙΠΕΔΟΠΡΟΣΤΑΣΙΑΣ": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "ΠΕΡΙΟΧΗΚ": "namespacee", "NAMESPACEE": "namespacee", "ΑΡΙΘΜΟΣΟΝΟΜΑΤΟΣΧΩΡΟΥ": "namespacenumber", "ΑΡΙΘΜΟΣΟΝΟΜΑΤΟΧΩΡΟΥ": "namespacenumber", "NAMESPACENUMBER": "namespacenumber", "ΠΕΡΙΟΧΗΣΥΖΗΤΗΣΕΩΝ": "talkspace", "TALKSPACE": "talkspace", "ΠΕΡΙΟΧΗΣΥΖΗΤΗΣΕΩΝΚ": "talkspacee", "TALKSPACEE": "talkspacee", "ΠΕΡΙΟΧΗΘΕΜΑΤΩΝ": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "ΠΕΡΙΟΧΗΘΕΜΑΤΩΝΚ": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "ΟΝΟΜΑΣΕΛΙΔΑΣ": "pagename", "PAGENAME": "pagename", "ΟΝΟΜΑΣΕΛΙΔΑΣΚ": "pagenamee", "PAGENAMEE": "pagenamee", "ΠΛΗΡΕΣΟΝΟΜΑΣΕΛΙΔΑΣ": "fullpagename", "FULLPAGENAME": "fullpagename", "ΠΛΗΡΕΣΟΝΟΜΑΣΕΛΙΔΑΣΚ": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "ΒΑΣΗΟΝΟΜΑΤΟΣΣΕΛΙΔΑΣ": "basepagename", "BASEPAGENAME": "basepagename", "ΒΑΣΗΟΝΟΜΑΤΟΣΣΕΛΙΔΑΣΚ": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "ΟΝΟΜΑΥΠΟΣΕΛΙΔΑΣ": "subpagename", "SUBPAGENAME": "subpagename", "ΟΝΟΜΑΥΠΟΣΕΛΙΔΑΣΚ": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "ΟΝΟΜΑΣΕΛΙΔΑΣΣΥΖΗΤΗΣΕΩΝ": "talkpagename", "TALKPAGENAME": "talkpagename", "ΟΝΟΜΑΣΕΛΙΔΑΣΣΥΖΗΤΗΣΕΩΝΚ": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "ΟΝΟΜΑΣΕΛΙΔΑΣΘΕΜΑΤΟΣ": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "ΟΝΟΜΑΣΕΛΙΔΑΣΘΕΜΑΤΟΣΚ": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "ΚΩΔΙΚΟΣΑΛΛΑΓΗΣ": "revisionid", "REVISIONID": "revisionid", "ΜΕΡΑΑΛΛΑΓΗΣ": "revisionday", "REVISIONDAY": "revisionday", "ΜΕΡΑΑΛΛΑΓΗΣ2": "revisionday2", "REVISIONDAY2": "revisionday2", "ΜΗΝΑΣΑΛΛΑΓΗΣ": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "ΜΗΝΑΣΑΝΑΘΕΩΡΗΣΗΣ1": "revisionmonth1", "REVISIONMONTH1": "revisionmonth1", "ΕΤΟΣΑΛΛΑΓΗΣ": "revisionyear", "REVISIONYEAR": "revisionyear", "ΧΡΟΝΟΣΗΜΑΝΣΗΑΛΛΑΓΗΣ": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "ΧΡΗΣΤΗΣΑΝΑΘΕΩΡΗΣΗΣ": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "ΠΕΡΙΟΧΗ": "namespace", "NAMESPACE": "namespace", "ΔΕΙΞΕΤΙΤΛΟ": "displaytitle", "ΠΡΟΒΟΛΗΤΙΤΛΟΥ": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "ΤΡΕΧΩΝΜΗΝΑΣ": "currentmonth", "ΤΡΕΧΩΝΜΗΝΑΣ2": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "ΤΡΕΧΩΝΜΗΝΑΣ1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "ΤΡΕΧΩΝΜΗΝΑΣΟΝΟΜΑ": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "ΤΡΕΧΩΝΜΗΝΑΣΓΕΝΙΚΗ": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "ΤΡΕΧΩΝΜΗΝΑΣΣΥΝΤ": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "ΤΡΕΧΟΥΣΑΜΕΡΑ": "currentday", "CURRENTDAY": "currentday", "ΤΡΕΧΟΥΣΑΜΕΡΑ2": "currentday2", "CURRENTDAY2": "currentday2", "ΤΡΕΧΟΥΣΑΜΕΡΑΟΝΟΜΑ": "currentdayname", "CURRENTDAYNAME": "currentdayname", "ΤΡΕΧΟΝΕΤΟΣ": "currentyear", "CURRENTYEAR": "currentyear", "ΤΡΕΧΩΝΧΡΟΝΟΣ": "currenttime", "CURRENTTIME": "currenttime", "ΤΡΕΧΟΥΣΑΩΡΑ": "currenthour", "CURRENTHOUR": "currenthour", "ΤΟΠΙΚΟΣΜΗΝΑΣ": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "ΤΟΠΙΚΟΣΜΗΝΑΣ1": "localmonth1", "LOCALMONTH1": "localmonth1", "ΤΟΠΙΚΟΣΜΗΝΑΣΟΝΟΜΑ": "localmonthname", "LOCALMONTHNAME": "localmonthname", "ΤΟΠΙΚΟΣΜΗΝΑΣΓΕΝΙΚΗ": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "ΤΟΠΙΚΟΣΜΗΝΑΣΣΥΝΤ": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "ΤΟΠΙΚΗΜΕΡΑ": "localday", "LOCALDAY": "localday", "ΤΟΠΙΚΗΜΕΡΑ2": "localday2", "LOCALDAY2": "localday2", "ΤΟΠΙΚΗΜΕΡΑΟΝΟΜΑ": "localdayname", "LOCALDAYNAME": "localdayname", "ΤΟΠΙΚΟΕΤΟΣ": "localyear", "LOCALYEAR": "localyear", "ΤΟΠΙΚΟΣΧΡΟΝΟΣ": "localtime", "LOCALTIME": "localtime", "ΤΟΠΙΚΗΩΡΑ": "localhour", "LOCALHOUR": "localhour", "ΙΣΤΟΧΩΡΟΣ": "sitename", "SITENAME": "sitename", "ΤΡΕΧΟΥΣΑΕΒΔΟΜΑΔΑ": "currentweek", "CURRENTWEEK": "currentweek", "ΤΡΕΧΟΥΣΑΜΕΡΑΕΒΔΟΜΑΔΑΣ": "currentdow", "CURRENTDOW": "currentdow", "ΤΟΠΙΚΗΕΒΔΟΜΑΔΑ": "localweek", "LOCALWEEK": "localweek", "ΤΟΠΙΚΗΜΕΡΑΕΒΔΟΜΑΔΑΣ": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "ΤΡΕΧΟΥΣΑΕΚΔΟΣΗ": "currentversion", "CURRENTVERSION": "currentversion", "ΤΡΕΧΟΥΣΑΧΡΟΝΟΣΗΜΑΝΣΗ": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "ΤΟΠΙΚΗΧΡΟΝΟΣΗΜΑΝΣΗ": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "ΚΩΔΙΚΟΣΦΟΡΑΣ": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "ΓΛΩΣΣΑΠΕΡΙΕΧΟΜΕΝΟΥ": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zαβγδεζηθικλμνξοπρστυφχψωςΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩάέήίόύώϊϋΐΰΆΈΉΊΌΎΏΪΫ]+)(.*)$/sDu"}}