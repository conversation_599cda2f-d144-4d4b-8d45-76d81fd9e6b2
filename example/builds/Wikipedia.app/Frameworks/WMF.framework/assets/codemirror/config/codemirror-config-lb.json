{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__kein_inhaltsverzeichnis__": "notoc", "__keininhaltsverzeichnis__": "notoc", "__notoc__": "notoc", "__keine_galerie__": "nogallery", "__keinegalerie__": "nogallery", "__nogallery__": "nogallery", "__inhaltsverzeichnis_erzwingen__": "forcetoc", "__forcetoc__": "forcetoc", "__inhaltsverzeichnis__": "toc", "__toc__": "toc", "__abschnitte_nicht_bearbeiten__": "noeditsection", "__noeditsection__": "noeditsection", "__keine_titelkonvertierung__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__keine_inhaltskonvertierung__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__NEUER_ABSCHNITTSLINK__": "newsectionlink", "__PLUS_LINK__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__KEIN_NEUER_ABSCHNITTSLINK__": "nonewsectionlink", "__KEIN_PLUS_LINK__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__VERSTOPPT_KATEGORIE__": "hiddencat", "__VERSTECKTE_KATEGORIE__": "hiddencat", "__WARTUNGSKATEGORIE__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEXIEREN__": "index", "__INDIZIEREN__": "index", "__INDEX__": "index", "__NICHT_INDEXIEREN__": "noindex", "__KEIN_INDEX__": "noindex", "__NICHT_INDIZIEREN__": "noindex", "__NOINDEX__": "noindex", "__PERMANENTE_WEITERLEITUNG__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"nr": "ns", "ns": "ns", "nr_url": "nse", "nse": "nse", "urlenkodiert": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "initial_klein": "lcfirst", "lcfirst": "lcfirst", "initial_gross": "ucfirst", "ucfirst": "ucfirst", "klein": "lc", "lc": "lc", "gross": "uc", "uc": "uc", "lokale_url": "<PERSON>url", "localurl": "<PERSON>url", "lokale_url_c": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "vollständige_url": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "vollständige_url_c": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "kanonische_url": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "kanonische_url_c": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "zueleformat": "formatnum", "zahlenformat": "formatnum", "formatnum": "formatnum", "grammaire": "grammar", "grammatik": "grammar", "grammar": "grammar", "geschlecht": "gender", "gender": "gender", "plural": "plural", "bidi": "bidi", "#sprooch": "language", "#sprache": "language", "#language": "language", "füllenlinks": "padleft", "padleft": "padleft", "füllenrechts": "padright", "padright": "padright", "ankerenkodiert": "anchorencode", "sprungmarkeenkodiert": "anchorencode", "anchorencode": "anchorencode", "dateipfad": "filepath", "filepath": "filepath", "seitenid": "pageid", "seitenkennung": "pageid", "pageid": "pageid", "nachricht": "int", "int": "int", "#spezial": "special", "#special": "special", "#speziale": "speciale", "#speciale": "speciale", "#erweiterung": "tag", "#tag": "tag", "#datumsformat": "formatdate", "#formatdate": "formatdate", "#dateformat": "formatdate", "#kategorienbaum": "categorytree", "#kategoriebaum": "categorytree", "#categorytree": "categorytree", "#ziel": "target", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#aufrufen": "invoke", "#invoke": "invoke", "#related": "related", "#if": "if", "#ifeq": "ifeq", "#wechsle": "switch", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#abschnitt": "lst", "#lstx": "lstx", "#section-x": "lstx", "#abschnitt-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "keineexternensprachlinks": "noexternallanglinks", "keine_externen_sprachlinks": "noexternallanglinks", "noexternallanglinks": "noexternallanglinks", "#eegeschaft": "property", "#eigenschaft": "property", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "artikelpfad": "articlepath", "articlepath": "articlepath", "server": "server", "servername": "servername", "skriptpfad": "scriptpath", "scriptpath": "scriptpath", "stilpfad": "stylepath", "stylepfad": "stylepath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbrepositoriumsname": "wbreponame", "wbreponame": "wbreponame"}, {"Säitenzuel": "numberofpages", "SEITENANZAHL": "numberofpages", "NUMBEROFPAGES": "numberofpages", "Benotzerzuel": "numberofusers", "BENUTZERANZAHL": "numberofusers", "NUMBEROFUSERS": "numberofusers", "Aktiv_Benotzer": "numberofactiveusers", "AKTIVE_BENUTZER": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "Artikelen": "numberofarticles", "ARTIKELANZAHL": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "Zuel_vu_Fichieren": "numberoffiles", "DATEIANZAHL": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "ADMINANZAHL": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "BENUTZER_IN_GRUPPE": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "BEARBEITUNGSANZAHL": "numberofedits", "NUMBEROFEDITS": "numberofedits", "SORTIERUNG": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "SEITEN_IN_KATEGORIE": "pagesincategory", "SEITEN_KAT": "pagesincategory", "SEITENINKAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "SEITENGRÖSSE": "pagesize", "PAGESIZE": "pagesize", "SCHUTZSTATUS": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "NAMENSRAUM_URL": "namespacee", "NAMESPACEE": "namespacee", "NAMENSRAUMNUMMER": "namespacenumber", "NAMESPACENUMBER": "namespacenumber", "DISKUSSIONSNAMENSRAUM": "talkspace", "DISK_NR": "talkspace", "TALKSPACE": "talkspace", "DISKUSSIONSNAMENSRAUM_URL": "talkspacee", "DISK_NR_URL": "talkspacee", "TALKSPACEE": "talkspacee", "Haaptnummraum": "subjectspace", "HAUPTNAMENSRAUM": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "HAUPTNAMENSRAUM_URL": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "Säitennumm": "pagename", "SEITENNAME": "pagename", "PAGENAME": "pagename", "SEITENNAME_URL": "pagenamee", "PAGENAMEE": "pagenamee", "VOLLER_SEITENNAME": "fullpagename", "FULLPAGENAME": "fullpagename", "VOLLER_SEITENNAME_URL": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "STAMMSEITE": "rootpagename", "ROOTPAGENAME": "rootpagename", "STAMMSEITE_URL": "rootpagenamee", "ROOTPAGENAMEE": "rootpagenamee", "OBERSEITE": "basepagename", "BASEPAGENAME": "basepagename", "OBERSEITE_URL": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "UNTERSEITE": "subpagename", "SUBPAGENAME": "subpagename", "UNTERSEITE_URL": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "DISKUSSIONSSEITE": "talkpagename", "DISK": "talkpagename", "TALKPAGENAME": "talkpagename", "DISKUSSIONSSEITE_URL": "talkpagenamee", "DISK_URL": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "Haaptsäit": "subjectpagename", "HAUPTSEITE": "subjectpagename", "HAUPTSEITENNAME": "subjectpagename", "VORDERSEITE": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "HAUPTSEITENNAME_URL": "subjectpagenamee", "VORDERSEITE_URL": "subjectpagenamee", "HAUPTSEITE_URL": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "REVISIONSID": "revisionid", "VERSIONSID": "revisionid", "REVISIONID": "revisionid", "REVISIONSTAG": "revisionday", "VERSIONSTAG": "revisionday", "REVISIONDAY": "revisionday", "REVISIONSTAG2": "revisionday2", "VERSIONSTAG2": "revisionday2", "REVISIONDAY2": "revisionday2", "REVISIONSMONAT": "<PERSON><PERSON><PERSON>", "VERSIONSMONAT": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONSMONAT1": "revisionmonth1", "VERSIONSMONAT1": "revisionmonth1", "REVISIONMONTH1": "revisionmonth1", "REVISIONSJAHR": "revisionyear", "VERSIONSJAHR": "revisionyear", "REVISIONYEAR": "revisionyear", "REVISIONSZEITSTEMPEL": "revisiontimestamp", "VERSIONSZEITSTEMPEL": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONSBENUTZER": "revisionuser", "VERSIONSBENUTZER": "revisionuser", "REVISIONUSER": "revisionuser", "KASKADENQUELLEN": "cascadingsources", "CASCADINGSOURCES": "cascadingsources", "Nummraum": "namespace", "NAMENSRAUM": "namespace", "NAMESPACE": "namespace", "SEITENTITEL": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "JETZIGER_MONAT": "currentmonth", "JETZIGER_MONAT_2": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "JETZIGER_MONAT_1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "JETZIGER_MONATSNAME": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "JETZIGER_MONATSNAME_GENITIV": "currentmonthnamegen", "JETZIGER_MONATSNAME_GEN": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "JETZIGER_MONATSNAME_KURZ": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "JETZIGER_KALENDERTAG": "currentday", "JETZIGER_TAG": "currentday", "CURRENTDAY": "currentday", "JETZIGER_KALENDERTAG_2": "currentday2", "JETZIGER_TAG_2": "currentday2", "CURRENTDAY2": "currentday2", "JETZIGER_WOCHENTAG": "currentdayname", "CURRENTDAYNAME": "currentdayname", "JETZIGES_JAHR": "currentyear", "CURRENTYEAR": "currentyear", "JETZIGE_UHRZEIT": "currenttime", "CURRENTTIME": "currenttime", "JETZIGE_STUNDE": "currenthour", "CURRENTHOUR": "currenthour", "LOKALER_MONAT": "localmonth", "LOKALER_MONAT_2": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LOKALER_MONAT_1": "localmonth1", "LOCALMONTH1": "localmonth1", "LOKALER_MONATSNAME": "localmonthname", "LOCALMONTHNAME": "localmonthname", "LOKALER_MONATSNAME_GENITIV": "localmonthnamegen", "LOKALER_MONATSNAME_GEN": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "LOKALER_MONATSNAME_KURZ": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "LOKALER_KALENDERTAG": "localday", "LOKALER_TAG": "localday", "LOCALDAY": "localday", "LOKALER_KALENDERTAG_2": "localday2", "LOKALER_TAG_2": "localday2", "LOCALDAY2": "localday2", "LOKALER_WOCHENTAG": "localdayname", "LOCALDAYNAME": "localdayname", "LOKALES_JAHR": "localyear", "LOCALYEAR": "localyear", "LOKALE_UHRZEIT": "localtime", "LOCALTIME": "localtime", "LOKALE_STUNDE": "localhour", "LOCALHOUR": "localhour", "PROJEKTNAME": "sitename", "SITENAME": "sitename", "JETZIGE_KALENDERWOCHE": "currentweek", "JETZIGE_WOCHE": "currentweek", "CURRENTWEEK": "currentweek", "JETZIGER_WOCHENTAG_ZAHL": "currentdow", "CURRENTDOW": "currentdow", "LOKALE_KALENDERWOCHE": "localweek", "LOKALE_WOCHE": "localweek", "LOCALWEEK": "localweek", "LOKALER_WOCHENTAG_ZAHL": "localdow", "LOCALDOW": "localdow", "VERSIONSGRÖSSE": "revisionsize", "REVISIONSIZE": "revisionsize", "AKTUELL_VERSIOUN": "currentversion", "JETZIGE_VERSION": "currentversion", "CURRENTVERSION": "currentversion", "JETZIGER_ZEITSTEMPEL": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "LOKALER_ZEITSTEMPEL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "TEXTAUSRICHTUNG": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "INHALTSSPRACHE": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([äöüßa-z]+)(.*)$/sDu"}}