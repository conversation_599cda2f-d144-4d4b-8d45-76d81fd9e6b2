{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__seníndice__": "notoc", "__semtdc__": "notoc", "__semsumário__": "notoc", "__notoc__": "notoc", "__sengalería__": "nogallery", "__semgaleria__": "nogallery", "__nogallery__": "nogallery", "__forzaroíndice__": "forcetoc", "__forcartdc__": "forcetoc", "__forcarsumario__": "forcetoc", "__forçartdc__": "forcetoc", "__forçarsumário__": "forcetoc", "__forcetoc__": "forcetoc", "__índice__": "toc", "__tdc__": "toc", "__sumário__": "toc", "__sumario__": "toc", "__toc__": "toc", "__secciónsnoneditables__": "noeditsection", "__nãoeditarseção__": "noeditsection", "__semeditarseção__": "noeditsection", "__naoeditarsecao__": "noeditsection", "__semeditarsecao__": "noeditsection", "__noeditsection__": "noeditsection", "__semconvertertitulo__": "notitleconvert", "__semconvertertítulo__": "notitleconvert", "__semct__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__semconverterconteudo__": "nocontentconvert", "__semconverterconteúdo__": "nocontentconvert", "__semcc__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LIGAZÓNDANOVASECCIÓN__": "newsectionlink", "__LINKDENOVASECAO__": "newsectionlink", "__LINKDENOVASEÇÃO__": "newsectionlink", "__LIGACAODENOVASECAO__": "newsectionlink", "__LIGAÇÃODENOVASEÇÃO__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__SEMLINKDENOVASECAO__": "nonewsectionlink", "__SEMLINKDENOVASEÇÃO__": "nonewsectionlink", "__SEMLIGACAODENOVASECAO__": "nonewsectionlink", "__SEMLIGAÇÃODENOVASEÇÃO__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__CATEGORÍAOCULTA__": "hiddencat", "__CATEGORIAOCULTA__": "hiddencat", "__CATOCULTA__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEXAR__": "index", "__INDEX__": "index", "__NONINDEXAR__": "noindex", "__NAOINDEXAR__": "noindex", "__NÃOINDEXAR__": "noindex", "__NOINDEX__": "noindex", "__REDIRECCIÓNESTÁTICA__": "staticredirect", "__REDIRECCIONESTATICA__": "staticredirect", "__REDIRECIONAMENTOESTATICO__": "staticredirect", "__REDIRECIONAMENTOESTÁTICO__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__HOMONIMOS__": "disambiguation", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"ns": "ns", "nse": "nse", "codificaurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "primeiraminúscula": "lcfirst", "primeiraminuscula": "lcfirst", "lcfirst": "lcfirst", "primeiramaiúscula": "ucfirst", "primeiramaiuscula": "ucfirst", "ucfirst": "ucfirst", "minúscula": "lc", "minuscula": "lc", "minusculas": "lc", "minúsculas": "lc", "lc": "lc", "maiúscula": "uc", "maiuscula": "uc", "maiusculas": "uc", "maiúsculas": "uc", "uc": "uc", "urllocal": "<PERSON>url", "localurl": "<PERSON>url", "localurle": "<PERSON><PERSON><PERSON>", "urlcompleto": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlcompletoc": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "urlcanónico": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnum": "formatnum", "gramática": "grammar", "grammar": "grammar", "sexo": "gender", "genero": "gender", "gênero": "gender", "gender": "gender", "plural": "plural", "bidi": "bidi", "#lingua": "language", "#idioma": "language", "#language": "language", "padleft": "padleft", "padright": "padright", "codificaancora": "anchorencode", "codificaâncora": "anchorencode", "anchorencode": "anchorencode", "caminhodoarquivo": "filepath", "filepath": "filepath", "iddapáxina": "pageid", "pageid": "pageid", "int": "int", "#especial": "special", "#special": "special", "#speciale": "speciale", "#etiqueta": "tag", "#tag": "tag", "#formatodadata": "formatdate", "#formateardata": "formatdate", "#formatdate": "formatdate", "#dateformat": "formatdate", "#árboredecategorías": "categorytree", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#chamar": "invoke", "#invoke": "invoke", "#related": "related", "pagebanner": "PAGEBANNER", "#se": "if", "#if": "if", "#seigual": "ifeq", "#ifeq": "ifeq", "#switch": "switch", "#seexiste": "ifexist", "#ifexist": "ifexist", "#seexpr": "ifexpr", "#ifexpr": "ifexpr", "#seerro": "iferror", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#partesdotítulo": "titleparts", "#partesdotitulo": "titleparts", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#trecho": "lst", "#lstx": "lstx", "#section-x": "lstx", "#trecho-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#propriedade": "property", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "rutadoartigo": "articlepath", "articlepath": "articlepath", "servidor": "server", "server": "server", "nomedoservidor": "servername", "servername": "servername", "rutadaescritura": "scriptpath", "caminhodoscript": "scriptpath", "scriptpath": "scriptpath", "rutadoestilo": "stylepath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NÚMERODEPÁXINAS": "numberofpages", "NUMERODEPAGINAS": "numberofpages", "NÚMERODEPÁGINAS": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NÚMERODEUSUARIOS": "numberofusers", "NUMERODEUSUARIOS": "numberofusers", "NÚMERODEUSUÁRIOS": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NÚMERODEUSUARIOSACTIVOS": "numberofactiveusers", "NUMERODEUSUARIOSATIVOS": "numberofactiveusers", "NÚMERODEUSUÁRIOSATIVOS": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NÚMERODEARTIGOS": "numberofarticles", "NUMERODEARTIGOS": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NÚMERODEFICHEIROS": "numberoffiles", "NUMERODEARQUIVOS": "numberoffiles", "NÚMERODEARQUIVOS": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NÚMERODEADMINISTRADORES": "numberofadmins", "NUMERODEADMINISTRADORES": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NÚMEROENGRUPO": "numberingroup", "NUMEROENGRUPO": "numberingroup", "NUMERONOGRUPO": "numberingroup", "NÚMERONOGRUPO": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NÚMERODEEDICIÓNS": "numberofedits", "NUMERODEEDICOES": "numberofedits", "NÚMERODEEDIÇÕES": "numberofedits", "NUMBEROFEDITS": "numberofedits", "ORDENAR": "defaultsort", "ORDENACAOPADRAO": "defaultsort", "ORDENAÇÃOPADRÃO": "defaultsort", "ORDEMPADRAO": "defaultsort", "ORDEMPADRÃO": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PÁXINASNACATEGORÍA": "pagesincategory", "PAXINASNACATEGORIA": "pagesincategory", "PAGINASNACATEGORIA": "pagesincategory", "PÁGINASNACATEGORIA": "pagesincategory", "PAGINASNACAT": "pagesincategory", "PÁGINASNACAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "TAMAÑODAPÁXINA": "pagesize", "TAMAÑODAPAXINA": "pagesize", "TAMANHODAPAGINA": "pagesize", "TAMANHODAPÁGINA": "pagesize", "PAGESIZE": "pagesize", "NIVELDEPROTECCIÓN": "protectionlevel", "NIVELDEPROTECCION": "protectionlevel", "NIVELDEPROTECAO": "protectionlevel", "NÍVELDEPROTEÇÃO": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "DOMINIOC": "namespacee", "DOMÍNIOC": "namespacee", "ESPACONOMINALC": "namespacee", "ESPAÇONOMINALC": "namespacee", "NAMESPACEE": "namespacee", "NÚMERODOESPAZODENOMES": "namespacenumber", "NAMESPACENUMBER": "namespacenumber", "ESPAZODECONVERSA": "talkspace", "PAGINADEDISCUSSAO": "talkspace", "PÁGINADEDISCUSSÃO": "talkspace", "TALKSPACE": "talkspace", "PAGINADEDISCUSSAOC": "talkspacee", "PÁGINADEDISCUSSÃOC": "talkspacee", "TALKSPACEE": "talkspacee", "ESPAZODECONTIDO": "subjectspace", "PAGINADECONTEUDO": "subjectspace", "PAGINADECONTEÚDO": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "PAGINADECONTEUDOC": "subjectspacee", "PAGINADECONTEÚDOC": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "NOMEDAPÁXINA": "pagename", "NOMEDAPAGINA": "pagename", "NOMEDAPÁGINA": "pagename", "PAGENAME": "pagename", "NOMEDAPAGINAC": "pagenamee", "NOMEDAPÁGINAC": "pagenamee", "PAGENAMEE": "pagenamee", "NOMECOMPLETODAPÁXINA": "fullpagename", "NOMECOMPLETODAPAGINA": "fullpagename", "NOMECOMPLETODAPÁGINA": "fullpagename", "FULLPAGENAME": "fullpagename", "NOMECOMPLETODAPAGINAC": "fullpagenamee", "NOMECOMPLETODAPÁGINAC": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "NOMEDAPÁXINARAÍZ": "rootpagename", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "NOMEDAPÁXINABASE": "basepagename", "NOMEDAPAGINABASE": "basepagename", "NOMEDAPÁGINABASE": "basepagename", "BASEPAGENAME": "basepagename", "NOMEDAPAGINABASEC": "basepagenamee", "NOMEDAPÁGINABASEC": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "NOMEDASUBPÁXINA": "subpagename", "NOMEDASUBPAGINA": "subpagename", "NOMEDASUBPÁGINA": "subpagename", "SUBPAGENAME": "subpagename", "NOMEDASUBPAGINAC": "subpagenamee", "NOMEDASUBPÁGINAC": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "NOMEDAPÁXINADECONVERSA": "talkpagename", "NOMEDAPAGINADEDISCUSSAO": "talkpagename", "NOMEDAPÁGINADEDISCUSSÃO": "talkpagename", "TALKPAGENAME": "talkpagename", "NOMEDAPAGINADEDISCUSSAOC": "talkpagenamee", "NOMEDAPÁGINADEDISCUSSÃOC": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NOMEDAPÁXINADECONTIDO": "subjectpagename", "NOMEDAPAGINADECONTEUDO": "subjectpagename", "NOMEDAPÁGINADECONTEÚDO": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NOMEDAPAGINADECONTEUDOC": "subjectpagenamee", "NOMEDAPÁGINADECONTEÚDOC": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "IDDAREVISIÓN": "revisionid", "IDDAREVISAO": "revisionid", "IDDAREVISÃO": "revisionid", "REVISIONID": "revisionid", "DÍADAREVISIÓN": "revisionday", "DIADAREVISAO": "revisionday", "DIADAREVISÃO": "revisionday", "REVISIONDAY": "revisionday", "DÍADAREVISIÓN2": "revisionday2", "DIADAREVISAO2": "revisionday2", "DIADAREVISÃO2": "revisionday2", "REVISIONDAY2": "revisionday2", "MESDAREVISIÓN": "<PERSON><PERSON><PERSON>", "MESDAREVISAO": "<PERSON><PERSON><PERSON>", "MÊSDAREVISÃO": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "MESDAREVISIÓN1": "revisionmonth1", "REVISIONMONTH1": "revisionmonth1", "ANODAREVISIÓN": "revisionyear", "ANODAREVISAO": "revisionyear", "ANODAREVISÃO": "revisionyear", "REVISIONYEAR": "revisionyear", "DATAEHORADAREVISIÓN": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "USUARIODAREVISIÓN": "revisionuser", "USUARIODAREVISAO": "revisionuser", "USUÁRIODAREVISÃO": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "ESPAZODENOMES": "namespace", "DOMINIO": "namespace", "DOMÍNIO": "namespace", "ESPACONOMINAL": "namespace", "ESPAÇONOMINAL": "namespace", "NAMESPACE": "namespace", "AMOSAROTÍTULO": "displaytitle", "MOSTRAROTÍTULO": "displaytitle", "EXIBETITULO": "displaytitle", "EXIBETÍTULO": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "MESACTUAL": "currentmonth", "MESACTUAL2": "currentmonth", "MESATUAL": "currentmonth", "MESATUAL2": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "MESACTUAL1": "currentmonth1", "MESATUAL1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "NOMEDOMESACTUAL": "currentmonthname", "NOMEDOMESATUAL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "ABREVIATURADOMESACTUAL": "<PERSON><PERSON><PERSON><PERSON>v", "MESATUALABREV": "<PERSON><PERSON><PERSON><PERSON>v", "MESATUALABREVIADO": "<PERSON><PERSON><PERSON><PERSON>v", "ABREVIATURADOMESATUAL": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "DÍAACTUAL": "currentday", "DIAATUAL": "currentday", "CURRENTDAY": "currentday", "DÍAACTUAL2": "currentday2", "DIAATUAL2": "currentday2", "CURRENTDAY2": "currentday2", "NOMEDODÍAACTUAL": "currentdayname", "NOMEDODIAATUAL": "currentdayname", "CURRENTDAYNAME": "currentdayname", "ANOACTUAL": "currentyear", "ANOATUAL": "currentyear", "CURRENTYEAR": "currentyear", "DATAEHORAACTUAIS": "currenttime", "HORARIOATUAL": "currenttime", "CURRENTTIME": "currenttime", "HORAACTUAL": "currenthour", "HORAATUAL": "currenthour", "CURRENTHOUR": "currenthour", "MESLOCAL": "localmonth", "MESLOCAL2": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "MESLOCAL1": "localmonth1", "LOCALMONTH1": "localmonth1", "NOMEDOMESLOCAL": "localmonthname", "LOCALMONTHNAME": "localmonthname", "LOCALMONTHNAMEGEN": "localmonthnamegen", "ABREVIATURADOMESLOCAL": "localmonthabbrev", "MESLOCALABREV": "localmonthabbrev", "MESLOCALABREVIADO": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "DÍALOCAL": "localday", "DIALOCAL": "localday", "LOCALDAY": "localday", "DÍALOCAL2": "localday2", "DIALOCAL2": "localday2", "LOCALDAY2": "localday2", "NOMEDODÍALOCAL": "localdayname", "NOMEDODIALOCAL": "localdayname", "LOCALDAYNAME": "localdayname", "ANOLOCAL": "localyear", "LOCALYEAR": "localyear", "DATAEHORALOCAIS": "localtime", "HORARIOLOCAL": "localtime", "LOCALTIME": "localtime", "HORALOCAL": "localhour", "LOCALHOUR": "localhour", "NOMEDOSITIO": "sitename", "NOMEDOSITE": "sitename", "NOMEDOSÍTIO": "sitename", "SITENAME": "sitename", "SEMANAACTUAL": "currentweek", "SEMANAATUAL": "currentweek", "CURRENTWEEK": "currentweek", "DIADASEMANAATUAL": "currentdow", "CURRENTDOW": "currentdow", "SEMANALOCAL": "localweek", "LOCALWEEK": "localweek", "DIADASEMANALOCAL": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "VERSIÓNACTUAL": "currentversion", "REVISAOATUAL": "currentversion", "REVISÃOATUAL": "currentversion", "CURRENTVERSION": "currentversion", "CURRENTTIMESTAMP": "currenttimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "LINGUADOCONTIDO": "contentlanguage", "IDIOMADOCONTIDO": "contentlanguage", "IDIOMADOCONTEUDO": "contentlanguage", "IDIOMADOCONTEÚDO": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([áâãàéêẽçíòóôõq̃úüűũa-z]+)(.*)$/sDu"}}