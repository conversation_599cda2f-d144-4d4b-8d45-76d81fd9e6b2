{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__notoc__": "notoc", "__nogallery__": "nogallery", "__forcetoc__": "forcetoc", "__toc__": "toc", "__noeditsection__": "noeditsection", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDICE__": "index", "__INDEX__": "index", "__NOINDICE__": "noindex", "__NOINDEX__": "noindex", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"ns": "ns", "nse": "nse", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "lcfirst": "lcfirst", "ucfirst": "ucfirst", "lc": "lc", "uc": "uc", "localurl": "<PERSON>url", "localurle": "<PERSON><PERSON><PERSON>", "fullurl": "<PERSON><PERSON>l", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnum": "formatnum", "grammar": "grammar", "genere": "gender", "gender": "gender", "plurale": "plural", "plural": "plural", "bidi": "bidi", "#lingua": "language", "#language": "language", "padleft": "padleft", "padright": "padright", "anchorencode": "anchorencode", "filepath": "filepath", "pageid": "pageid", "int": "int", "#special": "special", "#speciale": "speciale", "#etichetta": "tag", "#tag": "tag", "#formatodata": "formatdate", "#formatdate": "formatdate", "#dateformat": "formatdate", "#alberocategorie": "categorytree", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#se": "if", "#if": "if", "#seeq": "ifeq", "#ifeq": "ifeq", "#switch": "switch", "#seesiste": "ifexist", "#ifexist": "ifexist", "#seespr": "ifexpr", "#ifexpr": "ifexpr", "#seerrore": "iferror", "#iferror": "iferror", "#tempo": "time", "#time": "time", "#timel": "timel", "#espr": "expr", "#expr": "expr", "#rel2abs": "rel2abs", "#patititolo": "titleparts", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "articlepath": "articlepath", "server": "server", "nomeserver": "servername", "servername": "servername", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NUMEROPAGINE": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NUMEROUTENTI": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NUMEROUTENTIATTIVI": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NUMEROVOCI": "numberofarticles", "NUMEROARTICOLI": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NUMEROFILE": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NUMEROADMIN": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NUMEROMODIFICHE": "numberofedits", "NUMEROEDIT": "numberofedits", "NUMBEROFEDITS": "numberofedits", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PAGINEINCAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "DIMENSIONEPAGINA": "pagesize", "PESOPAGINA": "pagesize", "PAGESIZE": "pagesize", "LIVELLOPROTEZIONE": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "TALKSPACE": "talkspace", "TALKSPACEE": "talkspacee", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "TITOLOPAGINA": "pagename", "PAGENAME": "pagename", "TITOLOPAGINAE": "pagenamee", "PAGENAMEE": "pagenamee", "FULLPAGENAME": "fullpagename", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "BASEPAGENAME": "basepagename", "BASEPAGENAMEE": "basepagenamee", "NOMESOTTOPAGINA": "subpagename", "SUBPAGENAME": "subpagename", "NOMESOTTOPAGINAE": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "TALKPAGENAME": "talkpagename", "TALKPAGENAMEE": "talkpagenamee", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "REVISIONID": "revisionid", "REVISIONDAY": "revisionday", "REVISIONDAY2": "revisionday2", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "REVISIONYEAR": "revisionyear", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "NAMESPACE": "namespace", "MOSTRATITOLO": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "MESEATTUALE": "currentmonth", "MESECORRENTE": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "CURRENTMONTH1": "currentmonth1", "NOMEMESEATTUALE": "currentmonthname", "NOMEMESECORRENTE": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "NOMEMESEATTUALEGEN": "currentmonthnamegen", "NOMEMESECORRENTEGEN": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "MESEATTUALEABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "MESECORRENTEABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "GIORNOATTUALE": "currentday", "GIORNOCORRENTE": "currentday", "CURRENTDAY": "currentday", "GIORNOATTUALE2": "currentday2", "GIORNOCORRENTE2": "currentday2", "CURRENTDAY2": "currentday2", "NOMEGIORNOATTUALE": "currentdayname", "NOMEGIORNOCORRENTE": "currentdayname", "CURRENTDAYNAME": "currentdayname", "ANNOATTUALE": "currentyear", "ANNOCORRENTE": "currentyear", "CURRENTYEAR": "currentyear", "ORARIOATTUALE": "currenttime", "CURRENTTIME": "currenttime", "ORAATTUALE": "currenthour", "ORACORRENTE": "currenthour", "CURRENTHOUR": "currenthour", "MESELOCALE": "localmonth", "MESELOCALE2": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "MESELOCALE1": "localmonth1", "LOCALMONTH1": "localmonth1", "NOMEMESELOCALE": "localmonthname", "LOCALMONTHNAME": "localmonthname", "NOMEMESELOCALEGEN": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "MESELOCALEABBREV": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "GIORNOLOCALE": "localday", "LOCALDAY": "localday", "GIORNOLOCALE2": "localday2", "LOCALDAY2": "localday2", "NOMEGIORNOLOCALE": "localdayname", "LOCALDAYNAME": "localdayname", "ANNOLOCALE": "localyear", "LOCALYEAR": "localyear", "ORARIOLOCALE": "localtime", "LOCALTIME": "localtime", "ORALOCALE": "localhour", "LOCALHOUR": "localhour", "NOMESITO": "sitename", "SITENAME": "sitename", "SETTIMANACORRENTE": "currentweek", "CURRENTWEEK": "currentweek", "CURRENTDOW": "currentdow", "SETTIMANALOCALE": "localweek", "LOCALWEEK": "localweek", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "CURRENTVERSION": "currentversion", "CURRENTTIMESTAMP": "currenttimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zàéèíîìóòúù]+)(.*)$/sDu"}}