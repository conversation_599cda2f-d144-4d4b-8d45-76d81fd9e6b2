{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__faracuprins__": "notoc", "__notoc__": "notoc", "__faragalerie__": "nogallery", "__nogallery__": "nogallery", "__forteazacuprins__": "forcetoc", "__forcetoc__": "forcetoc", "__cuprins__": "toc", "__toc__": "toc", "__faraeditsectiune__": "noeditsection", "__noeditsection__": "noeditsection", "__faraconvertiretitlu__": "notitleconvert", "__fct__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__faraconvertirecontinut__": "nocontentconvert", "__fcc__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LEGATURASECTIUNENOUA__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__FARALEGATURASECTIUNENOUA__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__ASCUNDECAT__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEX__": "index", "__FARAINDEX__": "noindex", "__NOINDEX__": "noindex", "__REDIRECTIONARESTATICA__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"sn": "ns", "ns": "ns", "nse": "nse", "codificareurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "minusculaprima": "lcfirst", "lcfirst": "lcfirst", "majusculaprima": "ucfirst", "ucfirst": "ucfirst", "minuscula": "lc", "lc": "lc", "majuscula": "uc", "uc": "uc", "urllocal": "<PERSON>url", "localurl": "<PERSON>url", "urllocale": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "urlcomplet": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlcomplete": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnr": "formatnum", "formatnum": "formatnum", "gramatica": "grammar", "grammar": "grammar", "gen": "gender", "gender": "gender", "plural": "plural", "bidi": "bidi", "#limba": "language", "#language": "language", "padleft": "padleft", "padright": "padright", "codificareancora": "anchorencode", "anchorencode": "anchorencode", "caleafisierului": "filepath", "filepath": "filepath", "pageid": "pageid", "int": "int", "#special": "special", "#speciale": "speciale", "#eticheta": "tag", "#tag": "tag", "#formatdata": "formatdate", "#dataformat": "formatdate", "#formatdate": "formatdate", "#dateformat": "formatdate", "#arborecategorie": "categorytree", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#if": "if", "#ifeq": "ifeq", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "#mentor": "mentor", "articlepath": "articlepath", "server": "server", "numeserver": "servername", "servername": "servername", "calescript": "scriptpath", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NUMARDEPAGINI": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NUMARDEUTILIZATORI": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NUMARDEUTILIZATORIACTIVI": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NUMARDEARTICOLE": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NUMARDEFISIERE": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NUMARADMINI": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NUMARINGRUP": "numberingroup", "NUMINGRUP": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NUMARDEMODIFICARI": "numberofedits", "NUMBEROFEDITS": "numberofedits", "SORTAREIMPLICITA": "defaultsort", "CHEIESORTAREIMPLICITA": "defaultsort", "CATEGORIESORTAREIMPLICITA": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PAGINIINCATEGORIE": "pagesincategory", "PAGINIINCAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "MARIMEPAGINA": "pagesize", "PAGESIZE": "pagesize", "NIVELPROTECTIE": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "SPATIUUDENUME": "namespacee", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "SPATIUDEDISCUTIE": "talkspace", "TALKSPACE": "talkspace", "SPATIUUDEDISCUTIE": "talkspacee", "TALKSPACEE": "talkspacee", "SPATIUSUBIECT": "subjectspace", "SPATIUARTICOL": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "SPATIUUSUBIECT": "subjectspacee", "SPATIUUARTICOL": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "NUMEPAGINA": "pagename", "PAGENAME": "pagename", "NUMEEPAGINA": "pagenamee", "PAGENAMEE": "pagenamee", "NUMEPAGINACOMPLET": "fullpagename", "FULLPAGENAME": "fullpagename", "NUMEEPAGINACOMPLET": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "NUMEDEBAZAPAGINA": "basepagename", "BASEPAGENAME": "basepagename", "NUMEEDEBAZAPAGINA": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "NUMESUBPAGINA": "subpagename", "SUBPAGENAME": "subpagename", "NUMEESUBPAGINA": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "NUMEPAGINADEDISCUTIE": "talkpagename", "TALKPAGENAME": "talkpagename", "NUMEEPAGINADEDISCUTIE": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NUMEPAGINASUBIECT": "subjectpagename", "NUMEPAGINAARTICOL": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NUMEEPAGINASUBIECT": "subjectpagenamee", "NUMEEPAGINAARTICOL": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "IDREVIZIE": "revisionid", "REVISIONID": "revisionid", "ZIREVIZIE": "revisionday", "REVISIONDAY": "revisionday", "ZIREVIZIE2": "revisionday2", "REVISIONDAY2": "revisionday2", "LUNAREVIZIE": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "ANREVIZIE": "revisionyear", "REVISIONYEAR": "revisionyear", "STAMPILATIMPREVIZIE": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "UTILIZATORREVIZIE": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "SPATIUDENUME": "namespace", "NAMESPACE": "namespace", "ARATATITLU": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "NUMARLUNACURENTA": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "LUNACURENTA1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "NUMELUNACURENTA": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "NUMELUNACURENTAGEN": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "LUNACURENTAABREV": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "NUMARZIUACURENTA": "currentday", "CURRENTDAY": "currentday", "NUMARZIUACURENTA2": "currentday2", "CURRENTDAY2": "currentday2", "NUMEZIUACURENTA": "currentdayname", "CURRENTDAYNAME": "currentdayname", "ANULCURENT": "currentyear", "CURRENTYEAR": "currentyear", "TIMPULCURENT": "currenttime", "CURRENTTIME": "currenttime", "ORACURENTA": "currenthour", "CURRENTHOUR": "currenthour", "LUNALOCALA": "localmonth", "LUNALOCALA2": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LUNALOCALA1": "localmonth1", "LOCALMONTH1": "localmonth1", "NUMELUNALOCALA": "localmonthname", "LOCALMONTHNAME": "localmonthname", "NUMELUNALOCALAGEN": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "LUNALOCALAABREV": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "ZIUALOCALA": "localday", "LOCALDAY": "localday", "ZIUALOCALA2": "localday2", "LOCALDAY2": "localday2", "NUMEZIUALOCALA": "localdayname", "LOCALDAYNAME": "localdayname", "ANULLOCAL": "localyear", "LOCALYEAR": "localyear", "TIMPULLOCAL": "localtime", "LOCALTIME": "localtime", "ORALOCALA": "localhour", "LOCALHOUR": "localhour", "NUMESITE": "sitename", "SITENAME": "sitename", "SAPTAMANACURENTA": "currentweek", "CURRENTWEEK": "currentweek", "CURRENTDOW": "currentdow", "SAPTAMANALOCALA": "localweek", "LOCALWEEK": "localweek", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "VERSIUNECURENTA": "currentversion", "CURRENTVERSION": "currentversion", "STAMPILATIMPCURENT": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "STAMPILATIMPLOCAL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "SEMNDIRECTIE": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "LIMBACONTINUT": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zăâîşţșțĂÂÎŞŢȘȚ]+)(.*)$/sDu"}}