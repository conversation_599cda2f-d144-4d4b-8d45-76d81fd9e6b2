{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__notoc__": "notoc", "__nogallery__": "nogallery", "__forcetoc__": "forcetoc", "__toc__": "toc", "__noeditsection__": "noeditsection", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEX__": "index", "__NOINDEX__": "noindex", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"ns": "ns", "nse": "nse", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "lcfirst": "lcfirst", "ucfirst": "ucfirst", "lc": "lc", "uc": "uc", "localurl": "<PERSON>url", "localurle": "<PERSON><PERSON><PERSON>", "fullurl": "<PERSON><PERSON>l", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnum": "formatnum", "grammar": "grammar", "gender": "gender", "பன்மை": "plural", "plural": "plural", "bidi": "bidi", "#language": "language", "padleft": "padleft", "padright": "padright", "anchorencode": "anchorencode", "filepath": "filepath", "pageid": "pageid", "int": "int", "#சிறப்பு": "special", "#special": "special", "#speciale": "speciale", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#if": "if", "#ifeq": "ifeq", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "#mentor": "mentor", "articlepath": "articlepath", "server": "server", "servername": "servername", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NUMBEROFPAGES": "numberofpages", "NUMBEROFUSERS": "numberofusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NUMBEROFARTICLES": "numberofarticles", "NUMBEROFFILES": "numberoffiles", "NUMBEROFADMINS": "numberofadmins", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NUMBEROFEDITS": "numberofedits", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "பக்க_அளவு": "pagesize", "PAGESIZE": "pagesize", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "TALKSPACE": "talkspace", "TALKSPACEE": "talkspacee", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "PAGENAME": "pagename", "PAGENAMEE": "pagenamee", "FULLPAGENAME": "fullpagename", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "BASEPAGENAME": "basepagename", "BASEPAGENAMEE": "basepagenamee", "SUBPAGENAME": "subpagename", "SUBPAGENAMEE": "subpagenamee", "TALKPAGENAME": "talkpagename", "TALKPAGENAMEE": "talkpagenamee", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "REVISIONID": "revisionid", "REVISIONDAY": "revisionday", "REVISIONDAY2": "revisionday2", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "REVISIONYEAR": "revisionyear", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "NAMESPACE": "namespace", "DISPLAYTITLE": "displaytitle", "!": "!", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "CURRENTMONTH1": "currentmonth1", "CURRENTMONTHNAME": "currentmonthname", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTDAY": "currentday", "CURRENTDAY2": "currentday2", "CURRENTDAYNAME": "currentdayname", "CURRENTYEAR": "currentyear", "CURRENTTIME": "currenttime", "CURRENTHOUR": "currenthour", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LOCALMONTH1": "localmonth1", "LOCALMONTHNAME": "localmonthname", "LOCALMONTHNAMEGEN": "localmonthnamegen", "LOCALMONTHABBREV": "localmonthabbrev", "LOCALDAY": "localday", "LOCALDAY2": "localday2", "LOCALDAYNAME": "localdayname", "LOCALYEAR": "localyear", "LOCALTIME": "localtime", "LOCALHOUR": "localhour", "SITENAME": "sitename", "CURRENTWEEK": "currentweek", "CURRENTDOW": "currentdow", "LOCALWEEK": "localweek", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "CURRENTVERSION": "currentversion", "CURRENTTIMESTAMP": "currenttimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([஀-௿]+)(.*)$/sDu"}}