{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__semtdc__": "notoc", "__semsumário__": "notoc", "__notoc__": "notoc", "__semgaleria__": "nogallery", "__nogallery__": "nogallery", "__forcartdc__": "forcetoc", "__forcarsumario__": "forcetoc", "__forçartdc__": "forcetoc", "__forçarsumário__": "forcetoc", "__forcetoc__": "forcetoc", "__tdc__": "toc", "__sumário__": "toc", "__sumario__": "toc", "__toc__": "toc", "__nãoeditarseção__": "noeditsection", "__semeditarseção__": "noeditsection", "__naoeditarsecao__": "noeditsection", "__semeditarsecao__": "noeditsection", "__noeditsection__": "noeditsection", "__semconvertertitulo__": "notitleconvert", "__semconvertertítulo__": "notitleconvert", "__semct__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__semconverterconteudo__": "nocontentconvert", "__semconverterconteúdo__": "nocontentconvert", "__semcc__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LINKDENOVASECAO__": "newsectionlink", "__LINKDENOVASEÇÃO__": "newsectionlink", "__LIGACAODENOVASECAO__": "newsectionlink", "__LIGAÇÃODENOVASEÇÃO__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__SEMLINKDENOVASECAO__": "nonewsectionlink", "__SEMLINKDENOVASEÇÃO__": "nonewsectionlink", "__SEMLIGACAODENOVASECAO__": "nonewsectionlink", "__SEMLIGAÇÃODENOVASEÇÃO__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__CATEGORIAOCULTA__": "hiddencat", "__CATOCULTA__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEXAR__": "index", "__INDEX__": "index", "__NAOINDEXAR__": "noindex", "__NÃOINDEXAR__": "noindex", "__NOINDEX__": "noindex", "_ANCAMINARSTATICO_": "staticredirect", "__REDIRECIONAMENTOESTATICO__": "staticredirect", "__REDIRECIONAMENTOESTÁTICO__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"ns": "ns", "nse": "nse", "codificaurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "primeiraminuscula": "lcfirst", "primeiraminúscula": "lcfirst", "lcfirst": "lcfirst", "primeiramaiuscula": "ucfirst", "primeiramaiúscula": "ucfirst", "ucfirst": "ucfirst", "minuscula": "lc", "minúscula": "lc", "minusculas": "lc", "minúsculas": "lc", "lc": "lc", "maiuscula": "uc", "maiúscula": "uc", "maiusculas": "uc", "maiúsculas": "uc", "uc": "uc", "localurl": "<PERSON>url", "localurle": "<PERSON><PERSON><PERSON>", "urlcompleto": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlcompletoc": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnum": "formatnum", "grammar": "grammar", "genero": "gender", "gênero": "gender", "gender": "gender", "plural": "plural", "bidi": "bidi", "#lhengua": "language", "#idioma": "language", "#language": "language", "padleft": "padleft", "padright": "padright", "codificaancora": "anchorencode", "codificaâncora": "anchorencode", "anchorencode": "anchorencode", "caminofexeiro": "filepath", "caminhodoarquivo": "filepath", "filepath": "filepath", "pageid": "pageid", "int": "int", "#special": "special", "#speciale": "speciale", "#eitiqueta": "tag", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#se": "if", "#if": "if", "#seigual": "ifeq", "#ifeq": "ifeq", "#switch": "switch", "#seexiste": "ifexist", "#ifexist": "ifexist", "#seexpr": "ifexpr", "#ifexpr": "ifexpr", "#seerro": "iferror", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#partesdotítulo": "titleparts", "#partesdotitulo": "titleparts", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#trecho": "lst", "#lstx": "lstx", "#section-x": "lstx", "#trecho-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#propriedade": "property", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "articlepath": "articlepath", "servidor": "server", "server": "server", "nomedoservidor": "servername", "servername": "servername", "caminhodoscript": "scriptpath", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NUMERODEPAGINAS": "numberofpages", "NÚMERODEPÁGINAS": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NUMERODEUSUARIOS": "numberofusers", "NÚMERODEUSUÁRIOS": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NUMERODEUSUARIOSATIVOS": "numberofactiveusers", "NÚMERODEUSUÁRIOSATIVOS": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NUMERODEARTIGOS": "numberofarticles", "NÚMERODEARTIGOS": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NUMERODEARQUIVOS": "numberoffiles", "NÚMERODEARQUIVOS": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NUMERODEADMINISTRADORES": "numberofadmins", "NÚMERODEADMINISTRADORES": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NUMERONOGRUPO": "numberingroup", "NÚMERONOGRUPO": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NUMERODEEDICOES": "numberofedits", "NÚMERODEEDIÇÕES": "numberofedits", "NUMBEROFEDITS": "numberofedits", "ORDENACAOPADRAO": "defaultsort", "ORDENAÇÃOPADRÃO": "defaultsort", "ORDEMPADRAO": "defaultsort", "ORDEMPADRÃO": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PAGINASNACATEGORIA": "pagesincategory", "PÁGINASNACATEGORIA": "pagesincategory", "PAGINASNACAT": "pagesincategory", "PÁGINASNACAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "TAMANHOFEXEIRO": "pagesize", "TAMANHODAPAGINA": "pagesize", "TAMANHODAPÁGINA": "pagesize", "PAGESIZE": "pagesize", "NIVELDEPROTECAO": "protectionlevel", "NÍVELDEPROTEÇÃO": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "DOMINIOC": "namespacee", "DOMÍNIOC": "namespacee", "ESPACONOMINALC": "namespacee", "ESPAÇONOMINALC": "namespacee", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "PAGINADEDISCUSSAO": "talkspace", "PÁGINADEDISCUSSÃO": "talkspace", "TALKSPACE": "talkspace", "PAGINADEDISCUSSAOC": "talkspacee", "PÁGINADEDISCUSSÃOC": "talkspacee", "TALKSPACEE": "talkspacee", "PAGINADECONTEUDO": "subjectspace", "PAGINADECONTEÚDO": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "PAGINADECONTEUDOC": "subjectspacee", "PAGINADECONTEÚDOC": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "NOMEDAPAGINA": "pagename", "NOMEDAPÁGINA": "pagename", "PAGENAME": "pagename", "NOMEDAPAGINAC": "pagenamee", "NOMEDAPÁGINAC": "pagenamee", "PAGENAMEE": "pagenamee", "NOMECOMPLETODAPAGINA": "fullpagename", "NOMECOMPLETODAPÁGINA": "fullpagename", "FULLPAGENAME": "fullpagename", "NOMECOMPLETODAPAGINAC": "fullpagenamee", "NOMECOMPLETODAPÁGINAC": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "NOMEDAPAGINABASE": "basepagename", "NOMEDAPÁGINABASE": "basepagename", "BASEPAGENAME": "basepagename", "NOMEDAPAGINABASEC": "basepagenamee", "NOMEDAPÁGINABASEC": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "NOMEDASUBPAGINA": "subpagename", "NOMEDASUBPÁGINA": "subpagename", "SUBPAGENAME": "subpagename", "NOMEDASUBPAGINAC": "subpagenamee", "NOMEDASUBPÁGINAC": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "NOMEDAPAGINADEDISCUSSAO": "talkpagename", "NOMEDAPÁGINADEDISCUSSÃO": "talkpagename", "TALKPAGENAME": "talkpagename", "NOMEDAPAGINADEDISCUSSAOC": "talkpagenamee", "NOMEDAPÁGINADEDISCUSSÃOC": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NOMEDAPAGINADECONTEUDO": "subjectpagename", "NOMEDAPÁGINADECONTEÚDO": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NOMEDAPAGINADECONTEUDOC": "subjectpagenamee", "NOMEDAPÁGINADECONTEÚDOC": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "IDDAREVISAO": "revisionid", "IDDAREVISÃO": "revisionid", "REVISIONID": "revisionid", "DIADAREVISAO": "revisionday", "DIADAREVISÃO": "revisionday", "REVISIONDAY": "revisionday", "DIADAREVISAO2": "revisionday2", "DIADAREVISÃO2": "revisionday2", "REVISIONDAY2": "revisionday2", "MESDAREVISAO": "<PERSON><PERSON><PERSON>", "MÊSDAREVISÃO": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "ANODAREVISAO": "revisionyear", "ANODAREVISÃO": "revisionyear", "REVISIONYEAR": "revisionyear", "REVISIONTIMESTAMP": "revisiontimestamp", "USUARIODAREVISAO": "revisionuser", "USUÁRIODAREVISÃO": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "DOMINIO": "namespace", "DOMÍNIO": "namespace", "ESPACONOMINAL": "namespace", "ESPAÇONOMINAL": "namespace", "NAMESPACE": "namespace", "EXIBETITULO": "displaytitle", "EXIBETÍTULO": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "MESATUAL": "currentmonth", "MESATUAL2": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "MESATUAL1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "NOMEDOMESATUAL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "MESATUALABREV": "<PERSON><PERSON><PERSON><PERSON>v", "MESATUALABREVIADO": "<PERSON><PERSON><PERSON><PERSON>v", "ABREVIATURADOMESATUAL": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "DIAATUAL": "currentday", "CURRENTDAY": "currentday", "DIAATUAL2": "currentday2", "CURRENTDAY2": "currentday2", "NOMEDODIAATUAL": "currentdayname", "CURRENTDAYNAME": "currentdayname", "ANOATUAL": "currentyear", "CURRENTYEAR": "currentyear", "HORARIOATUAL": "currenttime", "CURRENTTIME": "currenttime", "HORAATUAL": "currenthour", "CURRENTHOUR": "currenthour", "MESLOCAL": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "MESLOCAL1": "localmonth1", "LOCALMONTH1": "localmonth1", "NOMEDOMESLOCAL": "localmonthname", "LOCALMONTHNAME": "localmonthname", "LOCALMONTHNAMEGEN": "localmonthnamegen", "MESLOCALABREV": "localmonthabbrev", "MESLOCALABREVIADO": "localmonthabbrev", "ABREVIATURADOMESLOCAL": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "DIALOCAL": "localday", "LOCALDAY": "localday", "DIALOCAL2": "localday2", "LOCALDAY2": "localday2", "NOMEDODIALOCAL": "localdayname", "LOCALDAYNAME": "localdayname", "ANOLOCAL": "localyear", "LOCALYEAR": "localyear", "HORARIOLOCAL": "localtime", "LOCALTIME": "localtime", "HORALOCAL": "localhour", "LOCALHOUR": "localhour", "NOMEDOSITE": "sitename", "NOMEDOSÍTIO": "sitename", "NOMEDOSITIO": "sitename", "SITENAME": "sitename", "SEMANAATUAL": "currentweek", "CURRENTWEEK": "currentweek", "DIADASEMANAATUAL": "currentdow", "CURRENTDOW": "currentdow", "SEMANALOCAL": "localweek", "LOCALWEEK": "localweek", "DIADASEMANALOCAL": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "REVISAOATUAL": "currentversion", "REVISÃOATUAL": "currentversion", "CURRENTVERSION": "currentversion", "CURRENTTIMESTAMP": "currenttimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "IDIOMADOCONTEUDO": "contentlanguage", "IDIOMADOCONTEÚDO": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([áâãàéêẽçíòóôõq̃úüűũa-z]+)(.*)$/sDu"}}