{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__bezspisu__": "notoc", "__notoc__": "notoc", "__bezgalerii__": "nogallery", "__nogallery__": "nogallery", "__zespisem__": "forcetoc", "__wymuśspis__": "forcetoc", "__forcetoc__": "forcetoc", "__spis__": "toc", "__toc__": "toc", "__bezedycjisekcji__": "noeditsection", "__noeditsection__": "noeditsection", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LINKNOWEJSEKCJI__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__KATEGORIAUKRYTA__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEKSUJ__": "index", "__INDEX__": "index", "__NIEINDEKSUJ__": "noindex", "__NOINDEX__": "noindex", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"pn": "ns", "ns": "ns", "nse": "nse", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "zmałej": "lcfirst", "odmałej": "lcfirst", "lcfirst": "lcfirst", "zwielkiej": "ucfirst", "zdużej": "ucfirst", "odwielkiej": "ucfirst", "oddużej": "ucfirst", "ucfirst": "ucfirst", "małe": "lc", "lc": "lc", "wielkie": "uc", "duże": "uc", "uc": "uc", "localurl": "<PERSON>url", "localurle": "<PERSON><PERSON><PERSON>", "pełnyurl": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnum": "formatnum", "odmiana": "grammar", "grammar": "grammar", "płeć": "gender", "gender": "gender", "mnoga": "plural", "plural": "plural", "bidi": "bidi", "#język": "language", "#language": "language", "dolewej": "padleft", "padleft": "padleft", "doprawej": "padright", "padright": "padright", "anchorencode": "anchorencode", "ścieżkapliku": "filepath", "filepath": "filepath", "pageid": "pageid", "int": "int", "#specjalna": "special", "#special": "special", "#speciale": "speciale", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#drzewokategorii": "categorytree", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#if": "if", "#ifeq": "ifeq", "#switch": "switch", "#ifexist": "ifexist", "#ifexpr": "ifexpr", "#iferror": "iferror", "#time": "time", "#timel": "timel", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#właściwość": "property", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "#mentor": "mentor", "ścieżkaartykułów": "articlepath", "articlepath": "articlepath", "serwer": "server", "server": "server", "nazwaserwera": "servername", "servername": "servername", "ścieżkaskryptu": "scriptpath", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"STRON": "numberofpages", "NUMBEROFPAGES": "numberofpages", "UŻYTKOWNIKÓW": "numberofusers", "NUMBEROFUSERS": "numberofusers", "LICZBAAKTYWNYCHUŻYTKOWNIKÓW": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "ARTYKUŁÓW": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "PLIKÓW": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "ADMINISTRATORÓW": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "EDYCJI": "numberofedits", "NUMBEROFEDITS": "numberofedits", "SORTUJ": "defaultsort", "DOMYŚLNIESORTUJ": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "STRONYWKATEGORII": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "ROZMIARSTRONY": "pagesize", "PAGESIZE": "pagesize", "__POZIOMZABEZPIECZEŃ__": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "DYSKUSJA": "talkspace", "TALKSPACE": "talkspace", "TALKSPACEE": "talkspacee", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "NAZWASTRONY": "pagename", "PAGENAME": "pagename", "PAGENAMEE": "pagenamee", "PELNANAZWASTRONY": "fullpagename", "FULLPAGENAME": "fullpagename", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "BAZOWANAZWASTRONY": "basepagename", "BASEPAGENAME": "basepagename", "BASEPAGENAMEE": "basepagenamee", "NAZWAPODSTRONY": "subpagename", "SUBPAGENAME": "subpagename", "SUBPAGENAMEE": "subpagenamee", "NAZWASTRONYDYSKUSJI": "talkpagename", "TALKPAGENAME": "talkpagename", "TALKPAGENAMEE": "talkpagenamee", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "REVISIONID": "revisionid", "REVISIONDAY": "revisionday", "REVISIONDAY2": "revisionday2", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "REVISIONYEAR": "revisionyear", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "NAZWAPRZESTRZENI": "namespace", "NAMESPACE": "namespace", "WYŚWIETLANYTYTUŁ": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "CURRENTMONTH1": "currentmonth1", "CURRENTMONTHNAME": "currentmonthname", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "AKTUALNYDZIEŃ": "currentday", "CURRENTDAY": "currentday", "CURRENTDAY2": "currentday2", "NAZWADNIA": "currentdayname", "CURRENTDAYNAME": "currentdayname", "AKTUALNYROK": "currentyear", "CURRENTYEAR": "currentyear", "AKTUALNYCZAS": "currenttime", "CURRENTTIME": "currenttime", "AKTUALNAGODZINA": "currenthour", "CURRENTHOUR": "currenthour", "MIESIĄC": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "LOCALMONTH1": "localmonth1", "MIESIĄCNAZWA": "localmonthname", "LOCALMONTHNAME": "localmonthname", "MIESIĄCNAZWAD": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "MIESIĄCNAZWASKR": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "DZIEŃ": "localday", "LOCALDAY": "localday", "DZIEŃ2": "localday2", "LOCALDAY2": "localday2", "DZIEŃTYGODNIA": "localdayname", "LOCALDAYNAME": "localdayname", "ROK": "localyear", "LOCALYEAR": "localyear", "CZAS": "localtime", "LOCALTIME": "localtime", "GODZINA": "localhour", "LOCALHOUR": "localhour", "PROJEKT": "sitename", "SITENAME": "sitename", "AKTUALNYTYDZIEŃ": "currentweek", "CURRENTWEEK": "currentweek", "CURRENTDOW": "currentdow", "TYDZIEŃROKU": "localweek", "LOCALWEEK": "localweek", "DZIEŃTYGODNIANR": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "AKTUALNAWERSJA": "currentversion", "CURRENTVERSION": "currentversion", "CURRENTTIMESTAMP": "currenttimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zęóąśłżźćńĘÓĄŚŁŻŹĆŃ]+)(.*)$/sDu"}}