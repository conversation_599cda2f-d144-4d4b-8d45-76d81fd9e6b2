{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__aucunsommaire__": "notoc", "__aucunetdm__": "notoc", "__notoc__": "notoc", "__sin_tdc__": "notoc", "__notdc__": "notoc", "__без_оглавления__": "notoc", "__без_огл__": "notoc", "__aucunegalerie__": "nogallery", "__nogallery__": "nogallery", "__sin_galería__": "nogallery", "__nogalería__": "nogallery", "__nogaleria__": "nogallery", "__без_галереи__": "nogallery", "__forcersommaire__": "forcetoc", "__forcertdm__": "forcetoc", "__forcetoc__": "forcetoc", "__forzar_tdc__": "forcetoc", "__forzartdc__": "forcetoc", "__forzartoc__": "forcetoc", "__обязательное_оглавление__": "forcetoc", "__обяз_огл__": "forcetoc", "__sommaire__": "toc", "__tdm__": "toc", "__toc__": "toc", "__tdc__": "toc", "__оглавление__": "toc", "__огл__": "toc", "__sectionnoneditable__": "noeditsection", "__noeditsection__": "noeditsection", "__no_editar_sección__": "noeditsection", "__noeditarsección__": "noeditsection", "__noeditarseccion__": "noeditsection", "__без_редактирования_раздела__": "noeditsection", "__sansconversiontitre__": "notitleconvert", "__sansct__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__noconvertirtitulo__": "notitleconvert", "__noconvertirtítulo__": "notitleconvert", "__noct___": "notitleconvert", "__без_преобразования_заголовка__": "notitleconvert", "__sansconversioncontenu__": "nocontentconvert", "__sanscc__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert", "__noconvertircontenido__": "nocontentconvert", "__nocc___": "nocontentconvert", "__без_преобразования_текста__": "nocontentconvert"}, {"__LIENNOUVELLESECTION__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__VINCULARANUEVASECCION__": "newsectionlink", "__ENLACECREARSECCIÓN__": "newsectionlink", "__ССЫЛКА_НА_НОВЫЙ_РАЗДЕЛ__": "newsectionlink", "__AUCUNLIENNOUVELLESECTION__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__NOVINCULARANUEVASECCION__": "nonewsectionlink", "__SINENLACECREARSECCIÓN__": "nonewsectionlink", "__БЕЗ_ССЫЛКИ_НА_НОВЫЙ_РАЗДЕЛ__": "nonewsectionlink", "__CATCACHEE__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__CATEGORÍAOCULTA__": "hiddencat", "__СКРЫТАЯ_КАТЕГОРИЯ__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEXAR__": "index", "__INDEX__": "index", "__ИНДЕКС__": "index", "__AUCUNINDEX__": "noindex", "__NOINDEX__": "noindex", "__NOINDEXAR__": "noindex", "__БЕЗ_ИНДЕКСА__": "noindex", "__REDIRECTIONSTATIQUE__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__REDIRECCIÓNESTÁTICA__": "staticredirect", "__REDIRECCIONESTATICA__": "staticredirect", "__СТАТИЧЕСКОЕ_ПЕРЕНАПРАВЛЕНИЕ__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIGUACION__": "disambiguation", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"espacen": "ns", "ns": "ns", "en": "ns", "пи": "ns", "espacenx": "nse", "nse": "nse", "пик": "nse", "encodeurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "codificarurl": "<PERSON><PERSON><PERSON><PERSON>", "закодированный_адрес": "<PERSON><PERSON><PERSON><PERSON>", "initminus": "lcfirst", "lcfirst": "lcfirst", "primerominus": "lcfirst", "primerominús": "lcfirst", "первая_буква_маленькая": "lcfirst", "initmajus": "ucfirst", "initcapit": "ucfirst", "ucfirst": "ucfirst", "primeromayus": "ucfirst", "primeromayús": "ucfirst", "первая_буква_большая": "ucfirst", "minus": "lc", "lc": "lc", "minús": "lc", "маленькими_буквами": "lc", "majus": "uc", "capit": "uc", "uc": "uc", "mayus": "uc", "mayús": "uc", "большими_буквами": "uc", "urllocale": "<PERSON>url", "localurl": "<PERSON>url", "urllocal": "<PERSON>url", "локальный_адрес": "<PERSON>url", "urllocalex": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "urllocalc": "<PERSON><PERSON><PERSON>", "локальный_адрес_2": "<PERSON><PERSON><PERSON>", "urlcomplete": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlcompleta": "<PERSON><PERSON>l", "полный_адрес": "<PERSON><PERSON>l", "urlcompletex": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "urlcompletac": "<PERSON><PERSON><PERSON>", "полный_адрес_2": "<PERSON><PERSON><PERSON>", "urlcanonique": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "urlcanonica": "<PERSON><PERSON><PERSON>", "urlcanoniquex": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "urlcanonicac": "<PERSON><PERSON><PERSON>", "formatnombre": "formatnum", "formatnum": "formatnum", "formatonúmero": "formatnum", "formatonumero": "formatnum", "форматировать_число": "formatnum", "grammaire": "grammar", "grammar": "grammar", "gramatica": "grammar", "gramática": "grammar", "падеж": "grammar", "genre": "gender", "gender": "gender", "género": "gender", "genero": "gender", "пол": "gender", "pluriel": "plural", "plural": "plural", "множественное_число": "plural", "bidi": "bidi", "#langue": "language", "#language": "language", "#idioma": "language", "#язык": "language", "bourragegauche": "padleft", "bourregauche": "padleft", "padleft": "padleft", "rellenarizquierda": "padleft", "rellenarizq": "padleft", "заполнить_слева": "padleft", "bourragedroite": "padright", "bourredroite": "padright", "padright": "padright", "rellenarderecha": "padright", "rellenarder": "padright", "заполнить_справа": "padright", "encodeancre": "anchorencode", "anchorencode": "anchorencode", "кодировать_метку": "anchorencode", "chemin": "filepath", "filepath": "filepath", "rutaarchivo": "filepath", "rutarchivo": "filepath", "rutadearchivo": "filepath", "путь_к_файлу": "filepath", "idpage": "pageid", "pageid": "pageid", "iddepágina": "pageid", "idpágina": "pageid", "iddepagina": "pageid", "idpagina": "pageid", "идентификатор_страницы": "pageid", "внутр": "int", "int": "int", "#spécial": "special", "#special": "special", "#especial": "special", "#служебная": "special", "#spéciale": "speciale", "#speciale": "speciale", "#especialc": "speciale", "#balise": "tag", "#tag": "tag", "#etiqueta": "tag", "#метка": "tag", "#тег": "tag", "#тэг": "tag", "#formatodefecha": "formatdate", "#formatearfecha": "formatdate", "#formatdate": "formatdate", "#dateformat": "formatdate", "#форматдаты": "formatdate", "#arbrecatégories": "categorytree", "#arbrecats": "categorytree", "#árboldecategorías": "categorytree", "#arboldecategorias": "categorytree", "#деревокатегорий": "categorytree", "#categorytree": "categorytree", "#destino": "target", "#target": "target", "#babel": "babel", "#вавилон": "babel", "#coordinates": "coordinates", "#invoque": "invoke", "#invocar": "invoke", "#вызвать": "invoke", "#invoke": "invoke", "#related": "related", "#si": "if", "#если": "if", "#if": "if", "#si=": "ifeq", "#siigual": "ifeq", "#ifeq": "ifeq", "#selon": "switch", "#según": "switch", "#переключатель": "switch", "#switch": "switch", "#siexiste": "ifexist", "#ifexist": "ifexist", "#siexpr": "ifexpr", "#ifexpr": "ifexpr", "#sierreur": "iferror", "#sierror": "iferror", "#еслиошибка": "iferror", "#iferror": "iferror", "#heure": "time", "#tiempo": "time", "#время": "time", "#time": "time", "#heurel": "timel", "#tiempol": "timel", "#мвремя": "timel", "#timel": "timel", "#expr": "expr", "#relenabs": "rel2abs", "#rel2abs": "rel2abs", "#partiestitre": "titleparts", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "sanslienexterne": "noexternallanglinks", "nointerwikis": "noexternallanglinks", "noexternallanglinks": "noexternallanglinks", "#propriété": "property", "#propiedad": "property", "#свойство": "property", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "cheminarticle": "articlepath", "articlepath": "articlepath", "rutaartículo": "articlepath", "rutaarticulo": "articlepath", "путь_к_статье": "articlepath", "serveur": "server", "server": "server", "servidor": "server", "сервер": "server", "nomserveur": "servername", "servername": "servername", "nombreservidor": "servername", "название_сервера": "servername", "cheminscript": "scriptpath", "scriptpath": "scriptpath", "rutascript": "scriptpath", "rutadescript": "scriptpath", "путь_к_скрипту": "scriptpath", "cheminstyle": "stylepath", "stylepath": "stylepath", "rutaestilo": "stylepath", "rutadeestilo": "stylepath", "путь_к_стилю": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NOMBREPAGES": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NÚMERODEPÁGINAS": "numberofpages", "NUMERODEPAGINAS": "numberofpages", "КОЛИЧЕСТВО_СТРАНИЦ": "numberofpages", "NOMBREUTILISATEURS": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NÚMERODEUSUARIOS": "numberofusers", "NUMERODEUSUARIOS": "numberofusers", "КОЛИЧЕСТВО_УЧАСТНИКОВ": "numberofusers", "NOMBREUTILISATEURSACTIFS": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NÚMERODEUSUARIOSACTIVOS": "numberofactiveusers", "NUMERODEUSUARIOSACTIVOS": "numberofactiveusers", "КОЛИЧЕСТВО_АКТИВНЫХ_УЧАСТНИКОВ": "numberofactiveusers", "NOMBREARTICLES": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NÚMERODEARTÍCULOS": "numberofarticles", "NUMERODEARTICULOS": "numberofarticles", "КОЛИЧЕСТВО_СТАТЕЙ": "numberofarticles", "NOMBREFICHIERS": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NÚMERODEARCHIVOS": "numberoffiles", "NUMERODEARCHIVOS": "numberoffiles", "КОЛИЧЕСТВО_ФАЙЛОВ": "numberoffiles", "NOMBREADMINS": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NÚMEROADMINIISTRADORES": "numberofadmins", "NÚMEROADMINS": "numberofadmins", "NUMEROADMINS": "numberofadmins", "NUMEROADMINISTRADORES": "numberofadmins", "NUMERODEADMINISTRADORES": "numberofadmins", "NUMERODEADMINS": "numberofadmins", "NÚMERODEADMINISTRADORES": "numberofadmins", "NÚMERODEADMINS": "numberofadmins", "КОЛИЧЕСТВО_АДМИНИСТРАТОРОВ": "numberofadmins", "NOMBREDANSGROUPE": "numberingroup", "NBDANSGROUPE": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NÚMEROENGRUPO": "numberingroup", "NUMEROENGRUPO": "numberingroup", "NUMENGRUPO": "numberingroup", "NÚMENGRUPO": "numberingroup", "ЧИСЛО_В_ГРУППЕ": "numberingroup", "NOMBREMODIFS": "numberofedits", "NUMBEROFEDITS": "numberofedits", "NÚMERODEEDICIONES": "numberofedits", "NUMERODEEDICIONES": "numberofedits", "КОЛИЧЕСТВО_ПРАВОК": "numberofedits", "CLEFDETRI": "defaultsort", "CLEDETRI": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "ORDENAR": "defaultsort", "CLAVEDEORDENPREDETERMINADO": "defaultsort", "ORDENDECATEGORIAPREDETERMINADO": "defaultsort", "ORDENDECATEGORÍAPREDETERMINADO": "defaultsort", "СОРТИРОВКА_ПО_УМОЛЧАНИЮ": "defaultsort", "КЛЮЧ_СОРТИРОВКИ": "defaultsort", "PAGESDANSCAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "PÁGINASENCATEGORÍA": "pagesincategory", "PÁGINASENCAT": "pagesincategory", "PAGSENCAT": "pagesincategory", "PAGINASENCATEGORIA": "pagesincategory", "PAGINASENCAT": "pagesincategory", "СТРАНИЦ_В_КАТЕГОРИИ": "pagesincategory", "TAILLEPAGE": "pagesize", "PAGESIZE": "pagesize", "TAMAÑOPÁGINA": "pagesize", "TAMAÑODEPÁGINA": "pagesize", "TAMAÑOPAGINA": "pagesize", "TAMAÑODEPAGINA": "pagesize", "РАЗМЕР_СТРАНИЦЫ": "pagesize", "NIVEAUDEPROTECTION": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "NIVELDEPROTECCIÓN": "protectionlevel", "NIVELDEPROTECCION": "protectionlevel", "УРОВЕНЬ_ЗАЩИТЫ": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "ESPACENOMMAGEX": "namespacee", "NAMESPACEE": "namespacee", "ESPACIODENOMBREC": "namespacee", "ПРОСТРАНСТВО_ИМЁН_2": "namespacee", "NOMBREESPACENOMMAGE": "namespacenumber", "NAMESPACENUMBER": "namespacenumber", "NÚMERODELESPACIO": "namespacenumber", "НОМЕР_ПРОСТРАНСТВА_ИМЁН": "namespacenumber", "ESPACEDISCUSSION": "talkspace", "TALKSPACE": "talkspace", "ESPACIODEDISCUSION": "talkspace", "ESPACIODEDISCUSIÓN": "talkspace", "ПРОСТРАНСТВО_ОБСУЖДЕНИЙ": "talkspace", "ESPACEDISCUSSIONX": "talkspacee", "TALKSPACEE": "talkspacee", "ESPACIODEDISCUSIONC": "talkspacee", "ПРОСТРАНСТВО_ОБСУЖДЕНИЙ_2": "talkspacee", "ESPACESUJET": "subjectspace", "ESPACEARTICLE": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "ESPACIODEASUNTO": "subjectspace", "ESPACIODETEMA": "subjectspace", "ESPACIODEARTÍCULO": "subjectspace", "ESPACIODEARTICULO": "subjectspace", "ПРОСТРАНСТВО_СТАТЕЙ": "subjectspace", "ESPACESUJETX": "subjectspacee", "ESPACEARTICLEX": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "ESPACIODETEMAC": "subjectspacee", "ESPACIODEASUNTOC": "subjectspacee", "ESPACIODEARTICULOC": "subjectspacee", "ESPACIODEARTÍCULOC": "subjectspacee", "ПРОСТРАНСТВО_СТАТЕЙ_2": "subjectspacee", "NOMPAGE": "pagename", "PAGENAME": "pagename", "NOMBREDEPAGINA": "pagename", "NOMBREDEPÁGINA": "pagename", "НАЗВАНИЕ_СТРАНИЦЫ": "pagename", "NOMPAGEX": "pagenamee", "PAGENAMEE": "pagenamee", "NOMBREDEPAGINAC": "pagenamee", "NOMBREDEPÁGINAC": "pagenamee", "НАЗВАНИЕ_СТРАНИЦЫ_2": "pagenamee", "NOMPAGECOMPLET": "fullpagename", "FULLPAGENAME": "fullpagename", "NOMBRECOMPLETODEPÁGINA": "fullpagename", "NOMBRECOMPLETODEPAGINA": "fullpagename", "ПОЛНОЕ_НАЗВАНИЕ_СТРАНИЦЫ": "fullpagename", "NOMPAGECOMPLETX": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "NOMBRECOMPLETODEPAGINAC": "fullpagenamee", "NOMBRECOMPLETODEPÁGINAC": "fullpagenamee", "ПОЛНОЕ_НАЗВАНИЕ_СТРАНИЦЫ_2": "fullpagenamee", "NOMPAGERACINE": "rootpagename", "ROOTPAGENAME": "rootpagename", "NOMBREDEPAGINARAIZ": "rootpagename", "NOMBREDEPÁGINARAÍZ": "rootpagename", "NOMPAGERACINEX": "rootpagenamee", "ROOTPAGENAMEE": "rootpagenamee", "NOMBREDEPAGINARAIZC": "rootpagenamee", "NOMBREDEPÁGINARAÍZC": "rootpagenamee", "NOMBASEDEPAGE": "basepagename", "BASEPAGENAME": "basepagename", "NOMBREDEPAGINABASE": "basepagename", "NOMBREDEPÁGINABASE": "basepagename", "ОСНОВА_НАЗВАНИЯ_СТРАНИЦЫ": "basepagename", "NOMBASEDEPAGEX": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "NOMBREDEPAGINABASEC": "basepagenamee", "NOMBREDEPÁGINABASEC": "basepagenamee", "ОСНОВА_НАЗВАНИЯ_СТРАНИЦЫ_2": "basepagenamee", "NOMSOUSPAGE": "subpagename", "SUBPAGENAME": "subpagename", "NOMBREDESUBPAGINA": "subpagename", "NOMBREDESUBPÁGINA": "subpagename", "НАЗВАНИЕ_ПОДСТРАНИЦЫ": "subpagename", "NOMSOUSPAGEX": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "NOMBREDESUBPAGINAC": "subpagenamee", "NOMBREDESUBPÁGINAC": "subpagenamee", "НАЗВАНИЕ_ПОДСТРАНИЦЫ_2": "subpagenamee", "NOMPAGEDISCUSSION": "talkpagename", "TALKPAGENAME": "talkpagename", "NOMBREDEPÁGINADEDISCUSIÓN": "talkpagename", "NOMBREDEPAGINADEDISCUSION": "talkpagename", "NOMBREDEPAGINADISCUSION": "talkpagename", "NOMBREDEPÁGINADISCUSIÓN": "talkpagename", "НАЗВАНИЕ_СТРАНИЦЫ_ОБСУЖДЕНИЯ": "talkpagename", "NOMPAGEDISCUSSIONX": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NOMBREDEPÁGINADEDISCUSIÓNC": "talkpagenamee", "NOMBREDEPAGINADEDISCUSIONC": "talkpagenamee", "NOMBREDEPAGINADISCUSIONC": "talkpagenamee", "NOMBREDEPÁGINADISCUSIÓNC": "talkpagenamee", "НАЗВАНИЕ_СТРАНИЦЫ_ОБСУЖДЕНИЯ_2": "talkpagenamee", "NOMPAGESUJET": "subjectpagename", "NOMPAGEARTICLE": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NOMBREDEPAGINADETEMA": "subjectpagename", "NOMBREDEPÁGINADETEMA": "subjectpagename", "NOMBREDEPÁGINADEASUNTO": "subjectpagename", "NOMBREDEPAGINADEASUNTO": "subjectpagename", "NOMBREDEPAGINADEARTICULO": "subjectpagename", "NOMBREDEPÁGINADEARTÍCULO": "subjectpagename", "НАЗВАНИЕ_СТРАНИЦЫ_СТАТЬИ": "subjectpagename", "NOMPAGESUJETX": "subjectpagenamee", "NOMPAGEARTICLEX": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "NOMBREDEPAGINADETEMAC": "subjectpagenamee", "NOMBREDEPÁGINADETEMAC": "subjectpagenamee", "NOMBREDEPÁGINADEASUNTOC": "subjectpagenamee", "NOMBREDEPAGINADEASUNTOC": "subjectpagenamee", "NOMBREDEPAGINADEARTICULOC": "subjectpagenamee", "NOMBREDEPÁGINADEARTÍCULOC": "subjectpagenamee", "НАЗВАНИЕ_СТРАНИЦЫ_СТАТЬИ_2": "subjectpagenamee", "IDVERSION": "revisionid", "REVISIONID": "revisionid", "IDDEREVISION": "revisionid", "IDREVISION": "revisionid", "IDDEREVISIÓN": "revisionid", "IDREVISIÓN": "revisionid", "ИД_ВЕРСИИ": "revisionid", "JOURVERSION": "revisionday", "JOUR1VERSION": "revisionday", "REVISIONDAY": "revisionday", "DIADEREVISION": "revisionday", "DIAREVISION": "revisionday", "DÍADEREVISIÓN": "revisionday", "DÍAREVISIÓN": "revisionday", "ДЕНЬ_ВЕРСИИ": "revisionday", "JOUR2VERSION": "revisionday2", "REVISIONDAY2": "revisionday2", "DIADEREVISION2": "revisionday2", "DIAREVISION2": "revisionday2", "DÍADEREVISIÓN2": "revisionday2", "DÍAREVISIÓN2": "revisionday2", "ДЕНЬ_ВЕРСИИ_2": "revisionday2", "MOISVERSION": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "MESDEREVISION": "<PERSON><PERSON><PERSON>", "MESDEREVISIÓN": "<PERSON><PERSON><PERSON>", "MESREVISION": "<PERSON><PERSON><PERSON>", "MESREVISIÓN": "<PERSON><PERSON><PERSON>", "МЕСЯЦ_ВЕРСИИ": "<PERSON><PERSON><PERSON>", "MOISVERSION1": "revisionmonth1", "REVISIONMONTH1": "revisionmonth1", "MESDEREVISION1": "revisionmonth1", "MESDEREVISIÓN1": "revisionmonth1", "MESREVISION1": "revisionmonth1", "MESREVISIÓN1": "revisionmonth1", "МЕСЯЦ_ВЕРСИИ_1": "revisionmonth1", "ANNEEVERSION": "revisionyear", "REVISIONYEAR": "revisionyear", "AÑODEREVISION": "revisionyear", "AÑODEREVISIÓN": "revisionyear", "AÑOREVISION": "revisionyear", "AÑOREVISIÓN": "revisionyear", "ГОД_ВЕРСИИ": "revisionyear", "INSTANTVERSION": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "MARCADEHORADEREVISION": "revisiontimestamp", "MARCADEHORADEREVISIÓN": "revisiontimestamp", "ОТМЕТКА_ВРЕМЕНИ_ВЕРСИИ": "revisiontimestamp", "UTILISATEURVERSION": "revisionuser", "REVISIONUSER": "revisionuser", "USUARIODEREVISION": "revisionuser", "USUARIODEREVISIÓN": "revisionuser", "ВЕРСИЯ_УЧАСТНИКА": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "ESPACENOMMAGE": "namespace", "NAMESPACE": "namespace", "ESPACIODENOMBRE": "namespace", "ПРОСТРАНСТВО_ИМЁН": "namespace", "AFFICHERTITRE": "displaytitle", "DISPLAYTITLE": "displaytitle", "MOSTRARTÍTULO": "displaytitle", "MOSTRARTITULO": "displaytitle", "ПОКАЗАТЬ_ЗАГОЛОВОК": "displaytitle", "!": "!", "MOISACTUEL": "currentmonth", "MOIS2ACTUEL": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "MESACTUAL": "currentmonth", "MESACTUAL2": "currentmonth", "ТЕКУЩИЙ_МЕСЯЦ": "currentmonth", "ТЕКУЩИЙ_МЕСЯЦ_2": "currentmonth", "MOIS1ACTUEL": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "MESACTUAL1": "currentmonth1", "ТЕКУЩИЙ_МЕСЯЦ_1": "currentmonth1", "NOMMOISACTUEL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "MESACTUALCOMPLETO": "currentmonthname", "NOMBREMESACTUAL": "currentmonthname", "НАЗВАНИЕ_ТЕКУЩЕГО_МЕСЯЦА": "currentmonthname", "NOMGENMOISACTUEL": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "MESACTUALGENITIVO": "currentmonthnamegen", "НАЗВАНИЕ_ТЕКУЩЕГО_МЕСЯЦА_РОД": "currentmonthnamegen", "ABREVMOISACTUEL": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "MESACTUALABREVIADO": "<PERSON><PERSON><PERSON><PERSON>v", "НАЗВАНИЕ_ТЕКУЩЕГО_МЕСЯЦА_АБР": "<PERSON><PERSON><PERSON><PERSON>v", "JOURACTUEL": "currentday", "JOUR1ACTUEL": "currentday", "CURRENTDAY": "currentday", "DÍAACTUAL": "currentday", "DIAACTUAL": "currentday", "DÍA_ACTUAL": "currentday", "DIA_ACTUAL": "currentday", "ТЕКУЩИЙ_ДЕНЬ": "currentday", "JOUR2ACTUEL": "currentday2", "CURRENTDAY2": "currentday2", "DÍAACTUAL2": "currentday2", "DIAACTUAL2": "currentday2", "DÍA_ACTUAL2": "currentday2", "DIA_ACTUAL2": "currentday2", "ТЕКУЩИЙ_ДЕНЬ_2": "currentday2", "NOMJOURACTUEL": "currentdayname", "CURRENTDAYNAME": "currentdayname", "NOMBREDÍAACTUAL": "currentdayname", "NOMBREDIAACTUAL": "currentdayname", "НАЗВАНИЕ_ТЕКУЩЕГО_ДНЯ": "currentdayname", "ANNEEACTUELLE": "currentyear", "CURRENTYEAR": "currentyear", "AÑOACTUAL": "currentyear", "AÑO_ACTUAL": "currentyear", "ТЕКУЩИЙ_ГОД": "currentyear", "HORAIREACTUEL": "currenttime", "CURRENTTIME": "currenttime", "HORA_MINUTOS_ACTUAL": "currenttime", "HORAMINUTOSACTUAL": "currenttime", "TIEMPOACTUAL": "currenttime", "ТЕКУЩЕЕ_ВРЕМЯ": "currenttime", "HEUREACTUELLE": "currenthour", "CURRENTHOUR": "currenthour", "HORAACTUAL": "currenthour", "HORA_ACTUAL": "currenthour", "ТЕКУЩИЙ_ЧАС": "currenthour", "MOISLOCAL": "localmonth", "MOIS2LOCAL": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "MESLOCAL": "localmonth", "MESLOCAL2": "localmonth", "МЕСТНЫЙ_МЕСЯЦ": "localmonth", "МЕСТНЫЙ_МЕСЯЦ_2": "localmonth", "MOIS1LOCAL": "localmonth1", "LOCALMONTH1": "localmonth1", "MESLOCAL1": "localmonth1", "МЕСТНЫЙ_МЕСЯЦ_1": "localmonth1", "NOMMOISLOCAL": "localmonthname", "LOCALMONTHNAME": "localmonthname", "MESLOCALCOMPLETO": "localmonthname", "NOMBREMESLOCAL": "localmonthname", "НАЗВАНИЕ_МЕСТНОГО_МЕСЯЦА": "localmonthname", "NOMGENMOISLOCAL": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "MESLOCALGENITIVO": "localmonthnamegen", "НАЗВАНИЕ_МЕСТНОГО_МЕСЯЦА_РОД": "localmonthnamegen", "ABREVMOISLOCAL": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "MESLOCALABREVIADO": "localmonthabbrev", "НАЗВАНИЕ_МЕСТНОГО_МЕСЯЦА_АБР": "localmonthabbrev", "JOURLOCAL": "localday", "JOUR1LOCAL": "localday", "LOCALDAY": "localday", "DÍALOCAL": "localday", "DIALOCAL": "localday", "МЕСТНЫЙ_ДЕНЬ": "localday", "JOUR2LOCAL": "localday2", "LOCALDAY2": "localday2", "DIALOCAL2": "localday2", "DÍALOCAL2": "localday2", "МЕСТНЫЙ_ДЕНЬ_2": "localday2", "NOMJOURLOCAL": "localdayname", "LOCALDAYNAME": "localdayname", "NOMBREDIALOCAL": "localdayname", "NOMBREDÍALOCAL": "localdayname", "НАЗВАНИЕ_МЕСТНОГО_ДНЯ": "localdayname", "ANNEELOCALE": "localyear", "LOCALYEAR": "localyear", "AÑOLOCAL": "localyear", "МЕСТНЫЙ_ГОД": "localyear", "HORAIRELOCAL": "localtime", "LOCALTIME": "localtime", "HORAMINUTOSLOCAL": "localtime", "TIEMPOLOCAL": "localtime", "МЕСТНОЕ_ВРЕМЯ": "localtime", "HEURELOCALE": "localhour", "LOCALHOUR": "localhour", "HORALOCAL": "localhour", "МЕСТНЫЙ_ЧАС": "localhour", "NOMSITE": "sitename", "SITENAME": "sitename", "NOMBREDELSITIO": "sitename", "НАЗВАНИЕ_САЙТА": "sitename", "SEMAINEACTUELLE": "currentweek", "CURRENTWEEK": "currentweek", "SEMANAACTUAL": "currentweek", "ТЕКУЩАЯ_НЕДЕЛЯ": "currentweek", "JDSACTUEL": "currentdow", "CURRENTDOW": "currentdow", "DDSACTUAL": "currentdow", "DIADESEMANAACTUAL": "currentdow", "DÍADESEMANAACTUAL": "currentdow", "ТЕКУЩИЙ_ДЕНЬ_НЕДЕЛИ": "currentdow", "SEMAINELOCALE": "localweek", "LOCALWEEK": "localweek", "SEMANALOCAL": "localweek", "МЕСТНАЯ_НЕДЕЛЯ": "localweek", "JDSLOCAL": "localdow", "LOCALDOW": "localdow", "DDSLOCAL": "localdow", "DIADESEMANALOCAL": "localdow", "DÍADESEMANALOCAL": "localdow", "МЕСТНЫЙ_ДЕНЬ_НЕДЕЛИ": "localdow", "TAMAÑODEREVISIÓN": "revisionsize", "TAMAÑODEREVISION": "revisionsize", "REVISIONSIZE": "revisionsize", "VERSIONACTUELLE": "currentversion", "CURRENTVERSION": "currentversion", "VERSIONACTUAL": "currentversion", "VERSIÓNACTUAL": "currentversion", "ТЕКУЩАЯ_ВЕРСИЯ": "currentversion", "INSTANTACTUEL": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "MARCADEHORAACTUAL": "currenttimestamp", "ОТМЕТКА_ТЕКУЩЕГО_ВРЕМЕНИ": "currenttimestamp", "INSTANTLOCAL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "MARCADEHORALOCAL": "localtimestamp", "ОТМЕТКА_МЕСТНОГО_ВРЕМЕНИ": "localtimestamp", "MARQUEDIRECTION": "directionmark", "MARQUEDIR": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "НАПРАВЛЕНИЕ_ПИСЬМА": "directionmark", "LANGUECONTENU": "contentlanguage", "LANGCONTENU": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "IDIOMADELCONTENIDO": "contentlanguage", "IDIOMADELCONT": "contentlanguage", "ЯЗЫК_СОДЕРЖАНИЯ": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zàâçéèêîôûäëïöüùÇÉÂÊÎÔÛÄËÏÖÜÀÈÙ]+)(.*)$/sDu"}}