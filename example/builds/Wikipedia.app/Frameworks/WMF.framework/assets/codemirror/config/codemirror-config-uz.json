{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__ichidagilaryoq__": "notoc", "__notoc__": "notoc", "__galereyayoq__": "nogallery", "__nogallery__": "nogallery", "__ichidagilarmajburiy__": "forcetoc", "__forcetoc__": "forcetoc", "__ichidagilari__": "toc", "__ichidagilar__": "toc", "__toc__": "toc", "__tahriryoq__": "noeditsection", "__tartiblashyoq__": "noeditsection", "__noeditsection__": "noeditsection", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__NEWSECTIONLINK__": "newsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__YASHIRINTURKUM__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEKS__": "index", "__INDEX__": "index", "__INDEKSYOQ__": "noindex", "__NOINDEX__": "noindex", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"ns": "ns", "nse": "nse", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "lcfirst": "lcfirst", "ucfirst": "ucfirst", "lc": "lc", "uc": "uc", "localurl": "<PERSON>url", "localurle": "<PERSON><PERSON><PERSON>", "fullurl": "<PERSON><PERSON>l", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnum": "formatnum", "grammar": "grammar", "jins": "gender", "gender": "gender", "plural": "plural", "bidi": "bidi", "#til": "language", "#language": "language", "padleft": "padleft", "padright": "padright", "anchorencode": "anchorencode", "filepath": "filepath", "pageid": "pageid", "int": "int", "#maxsus": "special", "#special": "special", "#speciale": "speciale", "#yorliq": "tag", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#turkumiyerarxiyasi": "categorytree", "#turkumdaraxti": "categorytree", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoke": "invoke", "#related": "related", "#agar": "if", "#if": "if", "#agarteng": "ifeq", "#ifeq": "ifeq", "#tanlov": "switch", "#switch": "switch", "#agarbor": "ifexist", "#ifexist": "ifexist", "#agarifoda": "ifexpr", "#ifexpr": "ifexpr", "#agarxato": "iferror", "#iferror": "iferror", "#vaqt": "time", "#time": "time", "#timel": "timel", "#ifoda": "expr", "#expr": "expr", "#rel2abs": "rel2abs", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "articlepath": "articlepath", "server": "server", "servername": "servername", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"SAHIFASONI": "numberofpages", "NUMBEROFPAGES": "numberofpages", "FOYDALANUVCHISONI": "numberofusers", "NUMBEROFUSERS": "numberofusers", "FAOLFOYDALANUVCHISONI": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "MAQOLASONI": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "FAYLSONI": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "ADMINISTRATORSONI": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "OZGARISHSONI": "numberofedits", "NUMBEROFEDITS": "numberofedits", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "TURKUMDAGISAHIFALAR": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "SAHIFAHAJMI": "pagesize", "PAGESIZE": "pagesize", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "TALKSPACE": "talkspace", "TALKSPACEE": "talkspacee", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "SAHIFANOMI": "pagename", "PAGENAME": "pagename", "PAGENAMEE": "pagenamee", "FULLPAGENAME": "fullpagename", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "BASEPAGENAME": "basepagename", "BASEPAGENAMEE": "basepagenamee", "SUBPAGENAME": "subpagename", "SUBPAGENAMEE": "subpagenamee", "TALKPAGENAME": "talkpagename", "TALKPAGENAMEE": "talkpagenamee", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "REVISIONID": "revisionid", "REVISIONDAY": "revisionday", "REVISIONDAY2": "revisionday2", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "REVISIONMONTH1": "revisionmonth1", "REVISIONYEAR": "revisionyear", "REVISIONTIMESTAMP": "revisiontimestamp", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "NOMFAZO": "namespace", "NAMESPACE": "namespace", "DISPLAYTITLE": "displaytitle", "!": "!", "JORIYOY": "currentmonth", "JORIYOY2": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "JORIYOY1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "JORIYOYNOMI": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "JORIYOYNOMIQARATQICH": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "JORIYOYQISQARTMASI": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "JORIYKUN": "currentday", "CURRENTDAY": "currentday", "JORIYKUN2": "currentday2", "CURRENTDAY2": "currentday2", "JORIYKUNNOMI": "currentdayname", "CURRENTDAYNAME": "currentdayname", "JORIYYIL": "currentyear", "CURRENTYEAR": "currentyear", "JORIYVAQT": "currenttime", "CURRENTTIME": "currenttime", "JORIYSOAT": "currenthour", "CURRENTHOUR": "currenthour", "MAHALLIYOY": "localmonth", "MAHALLIYOY2": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "MAHALLIYOY1": "localmonth1", "LOCALMONTH1": "localmonth1", "MAHALLIYOYNOMI": "localmonthname", "LOCALMONTHNAME": "localmonthname", "MAHALLIYOYQARATQICH": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "MAHALLIYOYQISQARTMASI": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "MAHALLIYKUN": "localday", "LOCALDAY": "localday", "MAHALLIYKUN2": "localday2", "LOCALDAY2": "localday2", "MAHALLIYKUNNOMI": "localdayname", "LOCALDAYNAME": "localdayname", "MAHALLIYYIL": "localyear", "LOCALYEAR": "localyear", "MAHALLIYVAQT": "localtime", "LOCALTIME": "localtime", "MAHALLIYSOAT": "localhour", "LOCALHOUR": "localhour", "SITENAME": "sitename", "JORIYHAFTA": "currentweek", "CURRENTWEEK": "currentweek", "CURRENTDOW": "currentdow", "LOCALWEEK": "localweek", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "CURRENTVERSION": "currentversion", "CURRENTTIMESTAMP": "currenttimestamp", "LOCALTIMESTAMP": "localtimestamp", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zʻʼ“»]+)(.*)$/sDu"}}