{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__aucunsommaire__": "notoc", "__aucunetdm__": "notoc", "__notoc__": "notoc", "__aucunegalerie__": "nogallery", "__nogallery__": "nogallery", "__forcersommaire__": "forcetoc", "__forcertdm__": "forcetoc", "__forcetoc__": "forcetoc", "__sommaire__": "toc", "__tdm__": "toc", "__toc__": "toc", "__sectionnoneditable__": "noeditsection", "__noeditsection__": "noeditsection", "__sansconversiontitre__": "notitleconvert", "__sansct__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__sansconversioncontenu__": "nocontentconvert", "__sanscc__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LIENNOUVELLESECTION__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__AUCUNLIENNOUVELLESECTION__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__CATCACHEE__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEX__": "index", "__AUCUNINDEX__": "noindex", "__NOINDEX__": "noindex", "__REDIRECTIONSTATIQUE__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"espacen": "ns", "ns": "ns", "espacenx": "nse", "nse": "nse", "encodeurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "initminus": "lcfirst", "lcfirst": "lcfirst", "initmajus": "ucfirst", "initcapit": "ucfirst", "ucfirst": "ucfirst", "minus": "lc", "lc": "lc", "majus": "uc", "capit": "uc", "uc": "uc", "urllocale": "<PERSON>url", "localurl": "<PERSON>url", "urllocalex": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "urlcomplete": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlcompletex": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "urlcanonique": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "urlcanoniquex": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnombre": "formatnum", "formatnum": "formatnum", "grammaire": "grammar", "grammar": "grammar", "genre": "gender", "gender": "gender", "pluriel": "plural", "plural": "plural", "bidi": "bidi", "#langue": "language", "#language": "language", "bourragegauche": "padleft", "bourregauche": "padleft", "padleft": "padleft", "bourragedroite": "padright", "bourredroite": "padright", "padright": "padright", "encodeancre": "anchorencode", "anchorencode": "anchorencode", "chemin": "filepath", "filepath": "filepath", "idpage": "pageid", "pageid": "pageid", "int": "int", "#spécial": "special", "#special": "special", "#spéciale": "speciale", "#speciale": "speciale", "#balise": "tag", "#tag": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#arbrecatégories": "categorytree", "#arbrecats": "categorytree", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoque": "invoke", "#invoke": "invoke", "#related": "related", "#si": "if", "#if": "if", "#si=": "ifeq", "#ifeq": "ifeq", "#selon": "switch", "#switch": "switch", "#siexiste": "ifexist", "#ifexist": "ifexist", "#siexpr": "ifexpr", "#ifexpr": "ifexpr", "#sierreur": "iferror", "#iferror": "iferror", "#heure": "time", "#time": "time", "#heurel": "timel", "#timel": "timel", "#expr": "expr", "#relenabs": "rel2abs", "#rel2abs": "rel2abs", "#partiestitre": "titleparts", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "sanslienexterne": "noexternallanglinks", "noexternallanglinks": "noexternallanglinks", "#propriété": "property", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "cheminarticle": "articlepath", "articlepath": "articlepath", "serveur": "server", "server": "server", "nomserveur": "servername", "servername": "servername", "cheminscript": "scriptpath", "scriptpath": "scriptpath", "cheminstyle": "stylepath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NOMBREPAGES": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NOMBREUTILISATEURS": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NOMBREUTILISATEURSACTIFS": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NOMBREARTICLES": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NOMBREFICHIERS": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NOMBREADMINS": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NOMBREDANSGROUPE": "numberingroup", "NBDANSGROUPE": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NOMBREMODIFS": "numberofedits", "NUMBEROFEDITS": "numberofedits", "CLEFDETRI": "defaultsort", "CLEDETRI": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "PAGESDANSCAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "TAILLEPAGE": "pagesize", "PAGESIZE": "pagesize", "NIVEAUDEPROTECTION": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "ESPACENOMMAGEX": "namespacee", "NAMESPACEE": "namespacee", "NOMBREESPACENOMMAGE": "namespacenumber", "NAMESPACENUMBER": "namespacenumber", "ESPACEDISCUSSION": "talkspace", "TALKSPACE": "talkspace", "ESPACEDISCUSSIONX": "talkspacee", "TALKSPACEE": "talkspacee", "ESPACESUJET": "subjectspace", "ESPACEARTICLE": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "ESPACESUJETX": "subjectspacee", "ESPACEARTICLEX": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "NOMPAGE": "pagename", "PAGENAME": "pagename", "NOMPAGEX": "pagenamee", "PAGENAMEE": "pagenamee", "NOMPAGECOMPLET": "fullpagename", "FULLPAGENAME": "fullpagename", "NOMPAGECOMPLETX": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "NOMPAGERACINE": "rootpagename", "ROOTPAGENAME": "rootpagename", "NOMPAGERACINEX": "rootpagenamee", "ROOTPAGENAMEE": "rootpagenamee", "NOMBASEDEPAGE": "basepagename", "BASEPAGENAME": "basepagename", "NOMBASEDEPAGEX": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "NOMSOUSPAGE": "subpagename", "SUBPAGENAME": "subpagename", "NOMSOUSPAGEX": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "NOMPAGEDISCUSSION": "talkpagename", "TALKPAGENAME": "talkpagename", "NOMPAGEDISCUSSIONX": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NOMPAGESUJET": "subjectpagename", "NOMPAGEARTICLE": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NOMPAGESUJETX": "subjectpagenamee", "NOMPAGEARTICLEX": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "IDVERSION": "revisionid", "REVISIONID": "revisionid", "JOURVERSION": "revisionday", "JOUR1VERSION": "revisionday", "REVISIONDAY": "revisionday", "JOUR2VERSION": "revisionday2", "REVISIONDAY2": "revisionday2", "MOISVERSION": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "MOISVERSION1": "revisionmonth1", "REVISIONMONTH1": "revisionmonth1", "ANNEEVERSION": "revisionyear", "REVISIONYEAR": "revisionyear", "INSTANTVERSION": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "UTILISATEURVERSION": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "ESPACENOMMAGE": "namespace", "NAMESPACE": "namespace", "AFFICHERTITRE": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "MOISACTUEL": "currentmonth", "MOIS2ACTUEL": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "MOIS1ACTUEL": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "NOMMOISACTUEL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "NOMGENMOISACTUEL": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "ABREVMOISACTUEL": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "JOURACTUEL": "currentday", "JOUR1ACTUEL": "currentday", "CURRENTDAY": "currentday", "JOUR2ACTUEL": "currentday2", "CURRENTDAY2": "currentday2", "NOMJOURACTUEL": "currentdayname", "CURRENTDAYNAME": "currentdayname", "ANNEEACTUELLE": "currentyear", "CURRENTYEAR": "currentyear", "HORAIREACTUEL": "currenttime", "CURRENTTIME": "currenttime", "HEUREACTUELLE": "currenthour", "CURRENTHOUR": "currenthour", "MOISLOCAL": "localmonth", "MOIS2LOCAL": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "MOIS1LOCAL": "localmonth1", "LOCALMONTH1": "localmonth1", "NOMMOISLOCAL": "localmonthname", "LOCALMONTHNAME": "localmonthname", "NOMGENMOISLOCAL": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "ABREVMOISLOCAL": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "JOURLOCAL": "localday", "JOUR1LOCAL": "localday", "LOCALDAY": "localday", "JOUR2LOCAL": "localday2", "LOCALDAY2": "localday2", "NOMJOURLOCAL": "localdayname", "LOCALDAYNAME": "localdayname", "ANNEELOCALE": "localyear", "LOCALYEAR": "localyear", "HORAIRELOCAL": "localtime", "LOCALTIME": "localtime", "HEURELOCALE": "localhour", "LOCALHOUR": "localhour", "NOMSITE": "sitename", "SITENAME": "sitename", "SEMAINEACTUELLE": "currentweek", "CURRENTWEEK": "currentweek", "JDSACTUEL": "currentdow", "CURRENTDOW": "currentdow", "SEMAINELOCALE": "localweek", "LOCALWEEK": "localweek", "JDSLOCAL": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "VERSIONACTUELLE": "currentversion", "CURRENTVERSION": "currentversion", "INSTANTACTUEL": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "INSTANTLOCAL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "MARQUEDIRECTION": "directionmark", "MARQUEDIR": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "LANGUECONTENU": "contentlanguage", "LANGCONTENU": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zàâçéèêîôûäëïöüùÇÉÂÊÎÔÛÄËÏÖÜÀÈÙ]+)(.*)$/sDu"}}