{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__tanpadaftarisi__": "notoc", "__nirdasi__": "notoc", "__notoc__": "notoc", "__tanpagaleri__": "nogallery", "__nirgal__": "nogallery", "__nogallery__": "nogallery", "__paksadaftarisi__": "forcetoc", "__paksadasi__": "forcetoc", "__forcetoc__": "forcetoc", "__daftarisi__": "toc", "__dasi__": "toc", "__toc__": "toc", "__tanpasuntinganbagian__": "noeditsection", "__nirsuba__": "noeditsection", "__noeditsection__": "noeditsection", "__tanpakonversijudul__": "notitleconvert", "__nirkodul__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__tanpakonversiisi__": "nocontentconvert", "__nirkosi__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__PRANALABAGIANBARU__": "newsectionlink", "__PRABABA__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "_TANPAPRANALABAGIANBARU__": "nonewsectionlink", "__NIRPRABABA__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__KATEGORITERSEMBUNYI__": "hiddencat", "__KATSEM__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEKS__": "index", "__INDEX__": "index", "__TANPAINDEKS__": "noindex", "__NIRDEKS__": "noindex", "__NOINDEX__": "noindex", "__PENGALIHANSTATIK__": "staticredirect", "__PENGALIHANSTATIS__": "staticredirect", "__PETIK__": "staticredirect", "__PETIS__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"rn": "ns", "runam": "ns", "ns": "ns", "nse": "nse", "kodeurl": "<PERSON><PERSON><PERSON><PERSON>", "kodu": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "akc": "lcfirst", "awalkecil": "lcfirst", "lcfirst": "lcfirst", "abs": "ucfirst", "awalbesar": "ucfirst", "ucfirst": "ucfirst", "kc": "lc", "kecil": "lc", "hurufkecil": "lc", "lc": "lc", "bs": "uc", "besar": "uc", "hurufbesar": "uc", "uc": "uc", "urllokal": "<PERSON>url", "localurl": "<PERSON>url", "urllokale": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "urllengkap": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urllengkape": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatangka": "formatnum", "forang": "formatnum", "formatnum": "formatnum", "tatabahasa": "grammar", "tasa": "grammar", "grammar": "grammar", "jantina": "gender", "gender": "gender", "jamak": "plural", "plural": "plural", "bidi": "bidi", "#bahasa": "language", "#bhs": "language", "#language": "language", "isikiri": "padleft", "iki": "padleft", "padleft": "padleft", "isikanan": "padright", "ika": "padright", "padright": "padright", "kodejangkar": "anchorencode", "kojang": "anchorencode", "anchorencode": "anchorencode", "lokasiberkas": "filepath", "lober": "filepath", "filepath": "filepath", "pageid": "pageid", "int": "int", "#istimewa": "special", "#spesial": "special", "#special": "special", "#speciale": "speciale", "#kata_kunci": "tag", "#takun": "tag", "#tag": "tag", "#formattanggal": "formatdate", "#formatdate": "formatdate", "#dateformat": "formatdate", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#pinta": "invoke", "#invoke": "invoke", "#related": "related", "#jika": "if", "#if": "if", "#jikasama": "ifeq", "#ifeq": "ifeq", "#pilih": "switch", "#switch": "switch", "#jikaada": "ifexist", "#ifexist": "ifexist", "#jikahitung": "ifexpr", "#ifexpr": "ifexpr", "#jikasalah": "iferror", "#iferror": "iferror", "#waktu": "time", "#time": "time", "#waktu1": "timel", "#timel": "timel", "#hitung": "expr", "#expr": "expr", "#rel2abs": "rel2abs", "#bagianjudul": "titleparts", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "noexternallanglinks": "noexternallanglinks", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "articlepath": "articlepath", "peladen": "server", "server": "server", "namapeladen": "servername", "namaserver": "servername", "nampel": "servername", "servername": "servername", "lokasiskrip": "scriptpath", "scriptpath": "scriptpath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"JUMLAHHALAMAN": "numberofpages", "JUMMAN": "numberofpages", "NUMBEROFPAGES": "numberofpages", "JUMLAHPENGGUNA": "numberofusers", "JUMPENG": "numberofusers", "NUMBEROFUSERS": "numberofusers", "JUMLAHPENGGUNAAKTIF": "numberofactiveusers", "JUMPENGTIF": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "JUMLAHARTIKEL": "numberofarticles", "JUMKEL": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "JUMLAHBERKAS": "numberoffiles", "JUMKAS": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "JUMLAHADMIN": "numberofadmins", "JUMLAHPENGURUS": "numberofadmins", "JUMAD": "numberofadmins", "JURUS": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "JUMLAHDIKELOMPOK": "numberingroup", "JULDIPOK": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "JUMLAHSUNTINGAN": "numberofedits", "JUMTING": "numberofedits", "NUMBEROFEDITS": "numberofedits", "URUTANBAKU": "defaultsort", "UBUR": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "HALAMANDIKATEGORI": "pagesincategory", "HALDIKAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "BESARHALAMAN": "pagesize", "BESMAN": "pagesize", "PAGESIZE": "pagesize", "TINGKATPERLINDUNGAN": "protectionlevel", "TIPER": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "RUANGNAMAE": "namespacee", "RUNAME": "namespacee", "NAMESPACEE": "namespacee", "NAMESPACENUMBER": "namespacenumber", "RUANGBICARA": "talkspace", "RUBIR": "talkspace", "TALKSPACE": "talkspace", "RUANGBICARAE": "talkspacee", "RUBIRE": "talkspacee", "TALKSPACEE": "talkspacee", "RUANGUTAMA": "subjectspace", "RUANGARTIKEL": "subjectspace", "RUTAMA": "subjectspace", "RUTIKEL": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "RUANGUTAMAE": "subjectspacee", "RUANGARTIKELE": "subjectspacee", "RUTAMAE": "subjectspacee", "RUKELE": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "NAMAHALAMAN": "pagename", "NAMMAN": "pagename", "PAGENAME": "pagename", "NAMAHALAMANE": "pagenamee", "NAMMANE": "pagenamee", "PAGENAMEE": "pagenamee", "NAMAHALAMANLENGKAP": "fullpagename", "NAMALENGKAPHALAMAN": "fullpagename", "NAMMANKAP": "fullpagename", "FULLPAGENAME": "fullpagename", "AMAHALAMANLENGKAPE": "fullpagenamee", "NAMALENGKAPHALAMANE": "fullpagenamee", "NAMMANKAPE": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "ROOTPAGENAME": "rootpagename", "ROOTPAGENAMEE": "rootpagenamee", "NAMAHALAMANDASAR": "basepagename", "NAMADASARHALAMAN": "basepagename", "NAMMANSAR": "basepagename", "BASEPAGENAME": "basepagename", "NAMAHALAMANDASARE": "basepagenamee", "NAMADASARHALAMANE": "basepagenamee", "NAMMANSARE": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "NAMASUBHALAMAN": "subpagename", "NAMAUPAHALAMAN": "subpagename", "NAMUMAN": "subpagename", "SUBPAGENAME": "subpagename", "NAMASUBHALAMANE": "subpagenamee", "NAMAUPAHALAMANE": "subpagenamee", "NAMUMANE": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "NAMAHALAMANBICARA": "talkpagename", "NAMMANBIR": "talkpagename", "TALKPAGENAME": "talkpagename", "NAMAHALAMANBICARAE": "talkpagenamee", "NAMMANBIRE": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NAMAHALAMANUTAMA": "subjectpagename", "NAMAHALAMANARTIKEL": "subjectpagename", "NAMMANTAMA": "subjectpagename", "NAMMANTIKEL": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NAMAHALAMANUTAMAE": "subjectpagenamee", "NAMAHALAMANARTIKELE": "subjectpagenamee", "NAMMANTAMAE": "subjectpagenamee", "NAMMANTIKELE": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "IDREVISI": "revisionid", "IREV": "revisionid", "REVISIONID": "revisionid", "HARIREVISI": "revisionday", "HAREV": "revisionday", "REVISIONDAY": "revisionday", "HARIREVISI2": "revisionday2", "HAREV2": "revisionday2", "REVISIONDAY2": "revisionday2", "BULANREVISI": "<PERSON><PERSON><PERSON>", "BUREV": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "BULANREVISI1": "revisionmonth1", "REVISIONMONTH1": "revisionmonth1", "TAHUNREVISI": "revisionyear", "TAREV": "revisionyear", "REVISIONYEAR": "revisionyear", "STEMPELWAKTUREVISI": "revisiontimestamp", "REKAMWAKTUREVISI": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "PENGGUNAREVISI": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "RUANGNAMA": "namespace", "RUNAM": "namespace", "NAMESPACE": "namespace", "JUDULTAMPILAN": "displaytitle", "JUTAM": "displaytitle", "DISPLAYTITLE": "displaytitle", "!": "!", "BULANKINI": "currentmonth", "BULANKINI2": "currentmonth", "BUKIN": "currentmonth", "BUKIN2": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "BULANKINI1": "currentmonth1", "BUKIN1": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "NAMABULANKINI": "currentmonthname", "NAMBUKIN": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "NAMAJENDERBULANKINI": "currentmonthnamegen", "NAMJENBUKIN": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "NAMASINGKATBULANKINI": "<PERSON><PERSON><PERSON><PERSON>v", "BULANINISINGKAT": "<PERSON><PERSON><PERSON><PERSON>v", "NAMSINGBUKIN": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "HARIKINI": "currentday", "HARKIN": "currentday", "CURRENTDAY": "currentday", "HARIKINI2": "currentday2", "HARKIN2": "currentday2", "CURRENTDAY2": "currentday2", "NAMAHARIKINI": "currentdayname", "NAMHARKIN": "currentdayname", "CURRENTDAYNAME": "currentdayname", "TAHUNKINI": "currentyear", "TAKIN": "currentyear", "CURRENTYEAR": "currentyear", "WAKTUKINI": "currenttime", "WAKIN": "currenttime", "CURRENTTIME": "currenttime", "JAMKINI": "currenthour", "JAKIN": "currenthour", "CURRENTHOUR": "currenthour", "BULANLOKAL": "localmonth", "BULANLOKAL2": "localmonth", "BULOK": "localmonth", "BULOK2": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "BULANLOKAL1": "localmonth1", "BULOK1": "localmonth1", "LOCALMONTH1": "localmonth1", "NAMABULANLOKAL": "localmonthname", "NAMBULOK": "localmonthname", "LOCALMONTHNAME": "localmonthname", "NAMAJENDERBULANLOKAL": "localmonthnamegen", "NAMJENBULOK": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "NAMASINGKATBULANLOKAL": "localmonthabbrev", "NAMSINGBULOK": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "HARILOKAL": "localday", "HALOK": "localday", "LOCALDAY": "localday", "HARILOKAL2": "localday2", "HALOK2": "localday2", "LOCALDAY2": "localday2", "NAMAHARILOKAL": "localdayname", "NAMHALOK": "localdayname", "LOCALDAYNAME": "localdayname", "TAHUNLOKAL": "localyear", "TALOK": "localyear", "LOCALYEAR": "localyear", "WAKTULOKAL": "localtime", "WALOK": "localtime", "LOCALTIME": "localtime", "JAMLOKAL": "localhour", "JALOK": "localhour", "LOCALHOUR": "localhour", "NAMASITUS": "sitename", "NAMSIT": "sitename", "SITENAME": "sitename", "MINGGUKINI": "currentweek", "MIKIN": "currentweek", "CURRENTWEEK": "currentweek", "HARIDALAMMINGGU": "currentdow", "HADAMI": "currentdow", "CURRENTDOW": "currentdow", "MINGGULOKAL": "localweek", "MIKAL": "localweek", "LOCALWEEK": "localweek", "HARIDALAMMINGGULOKAL": "localdow", "HADAMIKAL": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "VERSIKINI": "currentversion", "VERKIN": "currentversion", "CURRENTVERSION": "currentversion", "STEMPELWAKTUKINI": "currenttimestamp", "STEMWAKIN": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "STEMPELWAKTULOKAL": "localtimestamp", "STEMWAKAL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "MARKAARAH": "directionmark", "MARRAH": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "BAHASAISI": "contentlanguage", "BHSISI": "contentlanguage", "BASI": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-z]+)(.*)$/sD"}}