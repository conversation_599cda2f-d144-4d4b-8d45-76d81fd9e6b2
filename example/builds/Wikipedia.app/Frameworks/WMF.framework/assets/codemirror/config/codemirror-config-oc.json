{"extCodeMirrorConfig": {"pluginModules": ["ext.CodeMirror.addons"], "tagModes": {"ref": "text/mediawiki", "pre": "mw-tag-pre", "nowiki": "mw-tag-nowiki"}, "tags": {"pre": true, "nowiki": true, "gallery": true, "indicator": true, "langconvert": true, "timeline": true, "hiero": true, "charinsert": true, "inputbox": true, "imagemap": true, "source": true, "syntaxhighlight": true, "poem": true, "categorytree": true, "score": true, "templatestyles": true, "templatedata": true, "math": true, "ce": true, "chem": true, "graph": true, "maplink": true, "mapframe": true, "ref": true, "references": true, "section": true}, "doubleUnderscore": [{"__capdetaula__": "notoc", "__pascapdesomari__": "notoc", "__pascapdetdm__": "notoc", "__notoc__": "notoc", "__captaula__": "notoc", "__notaula__": "notoc", "__aucunsommaire__": "notoc", "__aucunetdm__": "notoc", "__capdegalariá__": "nogallery", "__capdegalaria__": "nogallery", "__pascapdedegalariá__": "nogallery", "__nogallery__": "nogallery", "__capgaleria__": "nogallery", "__nogaleria__": "nogallery", "__aucunegalerie__": "nogallery", "__forçartaula__": "forcetoc", "__forçarsomari__": "forcetoc", "__forçartdm__": "forcetoc", "__forcetoc__": "forcetoc", "__forçataula__": "forcetoc", "__forcersommaire__": "forcetoc", "__forcertdm__": "forcetoc", "__taula__": "toc", "__somari__": "toc", "__tdm__": "toc", "__toc__": "toc", "__resum__": "toc", "__sommaire__": "toc", "__seccionnoneditabla__": "noeditsection", "__noeditsection__": "noeditsection", "__secciónoeditable__": "noeditsection", "__seccionoeditable__": "noeditsection", "__sectionnoneditable__": "noeditsection", "__sansconversiontitre__": "notitleconvert", "__sansct__": "notitleconvert", "__notitleconvert__": "notitleconvert", "__notc__": "notitleconvert", "__sansconversioncontenu__": "nocontentconvert", "__sanscc__": "nocontentconvert", "__nocontentconvert__": "nocontentconvert", "__nocc__": "nocontentconvert"}, {"__LIGAMSECCIONNOVÈLA__": "newsectionlink", "__NEWSECTIONLINK__": "newsectionlink", "__LIENNOUVELLESECTION__": "newsectionlink", "__PASCAPDELIGAMSECCIONNOVÈLA__": "nonewsectionlink", "__NONEWSECTIONLINK__": "nonewsectionlink", "__AUCUNLIENNOUVELLESECTION__": "nonewsectionlink", "__CATAMAGADA__": "hiddencat", "__HIDDENCAT__": "hiddencat", "__CATCACHEE__": "hiddencat", "__EXPECTUNUSEDCATEGORY__": "expectunusedcategory", "__INDEX__": "index", "__PASCAPDINDÈX__": "noindex", "__NOINDEX__": "noindex", "__CAPINDEX__": "noindex", "__AUCUNINDEX__": "noindex", "__REDIRECCIONESTATICA__": "staticredirect", "__STATICREDIRECT__": "staticredirect", "__REDIRECCIÓESTATICA__": "staticredirect", "__REDIRECCIOESTATICA__": "staticredirect", "__REDIRECTIONSTATIQUE__": "staticredirect", "__NOGLOBAL__": "noglobal", "__DISAMBIG__": "disambiguation"}], "functionSynonyms": [{"espacen": "ns", "ns": "ns", "espacenx": "nse", "nse": "nse", "encòdaurl": "<PERSON><PERSON><PERSON><PERSON>", "urlencode": "<PERSON><PERSON><PERSON><PERSON>", "encodeurl": "<PERSON><PERSON><PERSON><PERSON>", "initminus": "lcfirst", "lcfirst": "lcfirst", "initmajus": "ucfirst", "ucfirst": "ucfirst", "initcapit": "ucfirst", "minus": "lc", "lc": "lc", "majus": "uc", "capit": "uc", "uc": "uc", "urllocala": "<PERSON>url", "localurl": "<PERSON>url", "urllocale": "<PERSON>url", "urllocalax": "<PERSON><PERSON><PERSON>", "localurle": "<PERSON><PERSON><PERSON>", "urllocalex": "<PERSON><PERSON><PERSON>", "urlcompleta": "<PERSON><PERSON>l", "fullurl": "<PERSON><PERSON>l", "urlcomplete": "<PERSON><PERSON>l", "urlcompletax": "<PERSON><PERSON><PERSON>", "fullurle": "<PERSON><PERSON><PERSON>", "urlcompletex": "<PERSON><PERSON><PERSON>", "urlcanonique": "<PERSON><PERSON><PERSON>", "canonicalurl": "<PERSON><PERSON><PERSON>", "urlcanoniquex": "<PERSON><PERSON><PERSON>", "canonicalurle": "<PERSON><PERSON><PERSON>", "formatnombre": "formatnum", "formatnum": "formatnum", "gramatica": "grammar", "grammar": "grammar", "grammaire": "grammar", "genre": "gender", "gender": "gender", "pluriel": "plural", "plural": "plural", "bidi": "bidi", "#lenga": "language", "#language": "language", "#idioma": "language", "#llengua": "language", "#langue": "language", "borratgeesquèrra": "padleft", "padleft": "padleft", "separacióesquerra": "padleft", "separacioesquerra": "padleft", "bourragegauche": "padleft", "bourregauche": "padleft", "borratgedrecha": "padright", "padright": "padright", "separaciódreta": "padright", "separaciodreta": "padright", "bourragedroite": "padright", "bourredroite": "padright", "encòdaancòra": "anchorencode", "anchorencode": "anchorencode", "encodeancre": "anchorencode", "camin": "filepath", "filepath": "filepath", "camí": "filepath", "cami": "filepath", "chemin": "filepath", "idpage": "pageid", "pageid": "pageid", "int": "int", "#especial": "special", "#special": "special", "#spécial": "special", "#spéciale": "speciale", "#speciale": "speciale", "#balisa": "tag", "#tag": "tag", "#etiqueta": "tag", "#marcador": "tag", "#balise": "tag", "#formatdate": "formatdate", "#dateformat": "formatdate", "#arbrecatégories": "categorytree", "#arbrecats": "categorytree", "#categorytree": "categorytree", "#target": "target", "#babel": "babel", "#coordinates": "coordinates", "#invoque": "invoke", "#invoke": "invoke", "#related": "related", "#si": "if", "#if": "if", "#si=": "ifeq", "#ifeq": "ifeq", "#selon": "switch", "#switch": "switch", "#siexiste": "ifexist", "#ifexist": "ifexist", "#siexpr": "ifexpr", "#ifexpr": "ifexpr", "#sierreur": "iferror", "#iferror": "iferror", "#heure": "time", "#time": "time", "#heurel": "timel", "#timel": "timel", "#expr": "expr", "#relenabs": "rel2abs", "#rel2abs": "rel2abs", "#partiestitre": "titleparts", "#titleparts": "titleparts", "#lst": "lst", "#section": "lst", "#lstx": "lstx", "#section-x": "lstx", "#lsth": "lsth", "#section-h": "lsth", "sanslienexterne": "noexternallanglinks", "noexternallanglinks": "noexternallanglinks", "#propriété": "property", "#property": "property", "#statements": "statements", "#commaseparatedlist": "commaSeparatedList", "cheminarticle": "articlepath", "articlepath": "articlepath", "servidor": "server", "server": "server", "serveur": "server", "nomservidor": "servername", "servername": "servername", "nomserveur": "servername", "caminescript": "scriptpath", "scriptpath": "scriptpath", "cheminscript": "scriptpath", "cheminstyle": "stylepath", "stylepath": "stylepath", "numberofwikis": "<PERSON><PERSON><PERSON><PERSON>", "wbreponame": "wbreponame"}, {"NOMBREPAGINAS": "numberofpages", "NUMBEROFPAGES": "numberofpages", "NOMBREPAGES": "numberofpages", "NOMBREUTILIZAIRES": "numberofusers", "NUMBEROFUSERS": "numberofusers", "NOMBREUSUARIS": "numberofusers", "NOMBRED'USUARIS": "numberofusers", "NOMBREUTILISATEURS": "numberofusers", "NOMBREUTILIZAIRESACTIUS": "numberofactiveusers", "NUMBEROFACTIVEUSERS": "numberofactiveusers", "NOMBREUTILISATEURSACTIFS": "numberofactiveusers", "NOMBREARTICLES": "numberofarticles", "NUMBEROFARTICLES": "numberofarticles", "NOMBRED'ARTICLES": "numberofarticles", "NOMBREFICHIÈRS": "numberoffiles", "NUMBEROFFILES": "numberoffiles", "NOMBREFITXERS": "numberoffiles", "NOMBRED'ARXIUS": "numberoffiles", "NOMBREFICHIERS": "numberoffiles", "NOMBREADMINS": "numberofadmins", "NUMBEROFADMINS": "numberofadmins", "NOMBREDANSGROUPE": "numberingroup", "NBDANSGROUPE": "numberingroup", "NUMBERINGROUP": "numberingroup", "NUMINGROUP": "numberingroup", "NOMBREEDICIONS": "numberofedits", "NOMBREMODIFS": "numberofedits", "NUMBEROFEDITS": "numberofedits", "NOMBRED'EDICIONS": "numberofedits", "ORDENA": "defaultsort", "CLAUDETRIADA": "defaultsort", "DEFAULTSORT": "defaultsort", "DEFAULTSORTKEY": "defaultsort", "DEFAULTCATEGORYSORT": "defaultsort", "CLEFDETRI": "defaultsort", "CLEDETRI": "defaultsort", "PAGINASDINSCAT": "pagesincategory", "PAGESINCATEGORY": "pagesincategory", "PAGESINCAT": "pagesincategory", "PÀGINESENCATEGORIA": "pagesincategory", "PAGINESENCATEGORIA": "pagesincategory", "PAGINESENCAT": "pagesincategory", "PAGESDANSCAT": "pagesincategory", "TALHAPAGINA": "pagesize", "PAGESIZE": "pagesize", "MIDAPÀGINA": "pagesize", "MIDAPAGINA": "pagesize", "MIDADELAPLANA": "pagesize", "TAILLEPAGE": "pagesize", "NIVÈLDEPROTECCION": "protectionlevel", "PROTECTIONLEVEL": "protectionlevel", "NIVELLPROTECCIÓ": "protectionlevel", "NIVELLPROTECCIO": "protectionlevel", "NIVEAUDEPROTECTION": "protectionlevel", "PROTECTIONEXPIRY": "protectionexpiry", "ESPACINOMENATGEX": "namespacee", "NAMESPACEE": "namespacee", "ESPACENOMMAGEX": "namespacee", "NOMBREESPACENOMMAGE": "namespacenumber", "NAMESPACENUMBER": "namespacenumber", "ESPACIDISCUSSION": "talkspace", "TALKSPACE": "talkspace", "ESPACEDISCUSSION": "talkspace", "ESPACIDISCUSSIONX": "talkspacee", "TALKSPACEE": "talkspacee", "ESPACEDISCUSSIONX": "talkspacee", "ESPACISUBJECTE": "subjectspace", "ESPACISUBJÈCTE": "subjectspace", "ESPACIARTICLE": "subjectspace", "SUBJECTSPACE": "subjectspace", "ARTICLESPACE": "subjectspace", "ESPACESUJET": "subjectspace", "ESPACEARTICLE": "subjectspace", "ESPACISUBJECTEX": "subjectspacee", "ESPACISUBJÈCTEX": "subjectspacee", "ESPACIARTICLEX": "subjectspacee", "SUBJECTSPACEE": "subjectspacee", "ARTICLESPACEE": "subjectspacee", "ESPACESUJETX": "subjectspacee", "ESPACEARTICLEX": "subjectspacee", "NOMPAGINA": "pagename", "PAGENAME": "pagename", "NOMPÀGINA": "pagename", "NOMDELAPLANA": "pagename", "NOMPAGE": "pagename", "NOMPAGINAX": "pagenamee", "PAGENAMEE": "pagenamee", "NOMPAGEX": "pagenamee", "NOMPAGINACOMPLET": "fullpagename", "FULLPAGENAME": "fullpagename", "NOMPAGECOMPLET": "fullpagename", "NOMPAGINACOMPLETX": "fullpagenamee", "FULLPAGENAMEE": "fullpagenamee", "NOMPAGECOMPLETX": "fullpagenamee", "NOMPAGERACINE": "rootpagename", "ROOTPAGENAME": "rootpagename", "NOMPAGERACINEX": "rootpagenamee", "ROOTPAGENAMEE": "rootpagenamee", "NOMBASADEPAGINA": "basepagename", "BASEPAGENAME": "basepagename", "NOMBASEDEPAGE": "basepagename", "NOMBASADEPAGINAX": "basepagenamee", "BASEPAGENAMEE": "basepagenamee", "NOMBASEDEPAGEX": "basepagenamee", "NOMSOSPAGINA": "subpagename", "SUBPAGENAME": "subpagename", "NOMSOUSPAGE": "subpagename", "NOMSOSPAGINAX": "subpagenamee", "SUBPAGENAMEE": "subpagenamee", "NOMSOUSPAGEX": "subpagenamee", "NOMPAGINADISCUSSION": "talkpagename", "TALKPAGENAME": "talkpagename", "NOMPAGEDISCUSSION": "talkpagename", "NOMPAGINADISCUSSIONX": "talkpagenamee", "TALKPAGENAMEE": "talkpagenamee", "NOMPAGEDISCUSSIONX": "talkpagenamee", "NOMPAGINASUBJECTE": "subjectpagename", "NOMPAGINASUBJÈCTE": "subjectpagename", "NOMPAGINAARTICLE": "subjectpagename", "SUBJECTPAGENAME": "subjectpagename", "ARTICLEPAGENAME": "subjectpagename", "NOMPAGESUJET": "subjectpagename", "NOMPAGEARTICLE": "subjectpagename", "NOMPAGINASUBJECTEX": "subjectpagenamee", "NOMPAGINASUBJÈCTEX": "subjectpagenamee", "NOMPAGINAARTICLEX": "subjectpagenamee", "SUBJECTPAGENAMEE": "subjectpagenamee", "ARTICLEPAGENAMEE": "subjectpagenamee", "NOMPAGESUJETX": "subjectpagenamee", "NOMPAGEARTICLEX": "subjectpagenamee", "NUMÈROVERSION": "revisionid", "REVISIONID": "revisionid", "IDVERSION": "revisionid", "DATAVERSION": "revisionday", "REVISIONDAY": "revisionday", "JOURVERSION": "revisionday", "JOUR1VERSION": "revisionday", "DATAVERSION2": "revisionday2", "REVISIONDAY2": "revisionday2", "JOUR2VERSION": "revisionday2", "MESREVISION": "<PERSON><PERSON><PERSON>", "REVISIONMONTH": "<PERSON><PERSON><PERSON>", "MOISVERSION": "<PERSON><PERSON><PERSON>", "MOISVERSION1": "revisionmonth1", "REVISIONMONTH1": "revisionmonth1", "ANNADAREVISION": "revisionyear", "ANREVISION": "revisionyear", "REVISIONYEAR": "revisionyear", "ANNEEVERSION": "revisionyear", "ORAREVISION": "revisiontimestamp", "REVISIONTIMESTAMP": "revisiontimestamp", "INSTANTVERSION": "revisiontimestamp", "UTILISATEURVERSION": "revisionuser", "REVISIONUSER": "revisionuser", "CASCADINGSOURCES": "cascadingsources", "ESPACINOMENATGE": "namespace", "NAMESPACE": "namespace", "ESPACENOMMAGE": "namespace", "AFICHARTÍTOL": "displaytitle", "DISPLAYTITLE": "displaytitle", "TÍTOL": "displaytitle", "TITOL": "displaytitle", "AFFICHERTITRE": "displaytitle", "!": "!", "MESCORRENT": "currentmonth", "MESACTUAL": "currentmonth", "CURRENTMONTH": "currentmonth", "CURRENTMONTH2": "currentmonth", "MOISACTUEL": "currentmonth", "MOIS2ACTUEL": "currentmonth", "MOIS1ACTUEL": "currentmonth1", "CURRENTMONTH1": "currentmonth1", "NOMMESCORRENT": "currentmonthname", "NOMMESACTUAL": "currentmonthname", "CURRENTMONTHNAME": "currentmonthname", "NOMMOISACTUEL": "currentmonthname", "NOMGENMESCORRENT": "currentmonthnamegen", "NOMGENMESACTUAL": "currentmonthnamegen", "CURRENTMONTHNAMEGEN": "currentmonthnamegen", "NOMGENMOISACTUEL": "currentmonthnamegen", "ABREVMESCORRENT": "<PERSON><PERSON><PERSON><PERSON>v", "ABREVMESACTUAL": "<PERSON><PERSON><PERSON><PERSON>v", "CURRENTMONTHABBREV": "<PERSON><PERSON><PERSON><PERSON>v", "ABREVMOISACTUEL": "<PERSON><PERSON><PERSON><PERSON>v", "JORNCORRENT": "currentday", "JORNACTUAL": "currentday", "CURRENTDAY": "currentday", "DIAACTUAL": "currentday", "JOURACTUEL": "currentday", "JOUR1ACTUEL": "currentday", "JORNCORRENT2": "currentday2", "JORNACTUAL2": "currentday2", "CURRENTDAY2": "currentday2", "DIAACTUAL2": "currentday2", "JOUR2ACTUEL": "currentday2", "NOMJORNCORRENT": "currentdayname", "NOMJORNACTUAL": "currentdayname", "CURRENTDAYNAME": "currentdayname", "NOMDIAACTUAL": "currentdayname", "NOMJOURACTUEL": "currentdayname", "ANNADACORRENTA": "currentyear", "ANNADAACTUALA": "currentyear", "CURRENTYEAR": "currentyear", "ANYACTUAL": "currentyear", "ANNEEACTUELLE": "currentyear", "DATACORRENTA": "currenttime", "DATAACTUALA": "currenttime", "CURRENTTIME": "currenttime", "HORARICTUAL": "currenttime", "HORAIREACTUEL": "currenttime", "ORACORRENTA": "currenthour", "ORAACTUALA": "currenthour", "CURRENTHOUR": "currenthour", "HORAACTUAL": "currenthour", "HEUREACTUELLE": "currenthour", "MESLOCAL": "localmonth", "LOCALMONTH": "localmonth", "LOCALMONTH2": "localmonth", "MOISLOCAL": "localmonth", "MOIS2LOCAL": "localmonth", "MOIS1LOCAL": "localmonth1", "LOCALMONTH1": "localmonth1", "NOMMESLOCAL": "localmonthname", "LOCALMONTHNAME": "localmonthname", "NOMMOISLOCAL": "localmonthname", "NOMGENMESLOCAL": "localmonthnamegen", "LOCALMONTHNAMEGEN": "localmonthnamegen", "NOMGENMOISLOCAL": "localmonthnamegen", "ABREVMESLOCAL": "localmonthabbrev", "LOCALMONTHABBREV": "localmonthabbrev", "ABREVMOISLOCAL": "localmonthabbrev", "JORNLOCAL": "localday", "LOCALDAY": "localday", "DIALOCAL": "localday", "JOURLOCAL": "localday", "JOUR1LOCAL": "localday", "JORNLOCAL2": "localday2", "LOCALDAY2": "localday2", "DIALOCAL2": "localday2", "JOUR2LOCAL": "localday2", "NOMJORNLOCAL": "localdayname", "LOCALDAYNAME": "localdayname", "NOMDIALOCAL": "localdayname", "NOMJOURLOCAL": "localdayname", "ANNADALOCALA": "localyear", "LOCALYEAR": "localyear", "ANYLOCAL": "localyear", "ANNEELOCALE": "localyear", "ORARILOCAL": "localtime", "LOCALTIME": "localtime", "HORARILOCAL": "localtime", "HORAIRELOCAL": "localtime", "ORALOCALA": "localhour", "LOCALHOUR": "localhour", "HORALOCAL": "localhour", "HEURELOCALE": "localhour", "NOMSIT": "sitename", "NOMSITE_NOMSITI": "sitename", "SITENAME": "sitename", "NOMSITE": "sitename", "SETMANACORRENTA": "currentweek", "CURRENTWEEK": "currentweek", "SEMAINEACTUELLE": "currentweek", "JDSCORRENT": "currentdow", "CURRENTDOW": "currentdow", "JDSACTUEL": "currentdow", "SETMANALOCALA": "localweek", "LOCALWEEK": "localweek", "SEMAINELOCALE": "localweek", "JDSLOCAL": "localdow", "LOCALDOW": "localdow", "REVISIONSIZE": "revisionsize", "VERSIONACTUALA": "currentversion", "CURRENTVERSION": "currentversion", "VERSIÓACTUAL": "currentversion", "VERSIOACTUAL": "currentversion", "VERSIONACTUELLE": "currentversion", "INSTANTACTUAL": "currenttimestamp", "CURRENTTIMESTAMP": "currenttimestamp", "INSTANTACTUEL": "currenttimestamp", "INSTANTLOCAL": "localtimestamp", "LOCALTIMESTAMP": "localtimestamp", "MARCADIRECCION": "directionmark", "MARCADIR": "directionmark", "DIRECTIONMARK": "directionmark", "DIRMARK": "directionmark", "MARQUEDIRECTION": "directionmark", "MARQUEDIR": "directionmark", "LENGACONTENGUT": "contentlanguage", "LENGCONTENGUT": "contentlanguage", "CONTENTLANGUAGE": "contentlanguage", "CONTENTLANG": "contentlanguage", "IDIOMACONTINGUT": "contentlanguage", "LLENGUACONTINGUT": "contentlanguage", "LANGUECONTENU": "contentlanguage", "LANGCONTENU": "contentlanguage", "PAGELANGUAGE": "pagelanguage"}], "urlProtocols": "bitcoin\\:|ftp\\:\\/\\/|ftps\\:\\/\\/|geo\\:|git\\:\\/\\/|gopher\\:\\/\\/|http\\:\\/\\/|https\\:\\/\\/|irc\\:\\/\\/|ircs\\:\\/\\/|magnet\\:|mailto\\:|mms\\:\\/\\/|news\\:|nntp\\:\\/\\/|redis\\:\\/\\/|sftp\\:\\/\\/|sip\\:|sips\\:|sms\\:|ssh\\:\\/\\/|svn\\:\\/\\/|tel\\:|telnet\\:\\/\\/|urn\\:|worldwind\\:\\/\\/|xmpp\\:|\\/\\/", "linkTrailCharacters": "/^([a-zàâçéèêîôû]+)(.*)$/sDu"}}