<!DOCTYPE html>
<html>

<head>
  <meta charset=utf8>
  <meta name="viewport" id="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0">
  <script src="resources/lib/codemirror/lib/codemirror.js"></script>
  <link rel="stylesheet" type="text/css" href="resources/lib/codemirror/lib/codemirror.css">
  <script src="resources/mode/mediawiki/mediawiki.js"></script>
  <link rel="stylesheet" type="text/css" href="resources/mode/mediawiki/mediawiki.css">
  <link rel="stylesheet" type="text/css" href="codemirror-common.css">
  <link rel="stylesheet" type="text/css" href="codemirror-light.css">
  <link rel="stylesheet" type="text/css" href="codemirror-sepia.css">
  <link rel="stylesheet" type="text/css" href="codemirror-dark.css">
  <link rel="stylesheet" type="text/css" href="codemirror-black.css">
  <link rel="stylesheet" type="text/css" href="codemirror-syntax-highlighting-off.css">
  <script src="resources/addon/search/search.js"></script>
  <script src="resources/addon/search/searchcursor.js"></script>
  <script src="resources/addon/search/match-highlighter.js"></script>

  <script src="codemirror-editTextSelection.js"></script>
  <script src="codemirror-range-determination-bundle.js"></script>

</head>


<body class="cm-mw-light">
  <script>        
    let editor

    const rangesIntersect = (range1, range2) => {
      if (range1.start === range1.end) { // treat a zero length range inside of another range as intersecting
        return range1.start > range2.start && range1.start < range2.end
      } else if (range2.start === range2.end) {
        return range2.start > range1.start && range2.start < range1.end
      } else {
        return Math.max(range1.start, range2.start) < Math.min(range1.end, range2.end)
      }
    }

    const getSelectionRange = (doc) => {
      const fromCursor = doc.getCursor('from')
      const toCursor = doc.getCursor('to')

      const start = fromCursor.ch
      const end = toCursor.ch
      const isSingleLine = (fromCursor.line === toCursor.line)
      const line = fromCursor.line
      const isRangeSelected = !isSingleLine || (end - start) > 0

      return {
        start,
        end,
        isSingleLine,
        line,
        isRangeSelected
      }
    }
    
    const tokensIntersectingSelection = (selectionRange, lineTokens) => {
      return lineTokens
        .filter(token => {
          return rangesIntersect(selectionRange, token)
        })
    }

    const buttonPayload = (buttonName, buttonInfoDict) => {
      return {button: buttonName, info: buttonInfoDict}
    } 

    // returns zero if `string` is not comprised only of `char`.
    // else returns length of `string`
    const charCountInString = (string, char) => {
      const matches = string.match(`^${char}+$`)
      return (matches === null) ? 0 : string.length
    }

    const numberFromPrefixedKey = (array, prefix) => {
      const s = array.find(s => s.startsWith(prefix))
      if (!s) {
        return null
      }
      return parseInt(s.substring(prefix.length))
    }

    // The wikitext parser doesn't add a useful classes for some wikitext constructs, so supplement 
    // these here. Makes it easier to determine when the cursor sits in such wikitext (or it's
    // selected) with the same mechanism (token 'type' inspection).
    const enrichedLineTokens = (doc, line) => {
      const lineTokens = doc.getLineTokens(line, true)

      enrichHeadingTokens(lineTokens)      
      enrichIndentTokens(lineTokens)
      enrichSignatureTokens(lineTokens)
      
      return lineTokens
    }

    const tokenIncludesType = (token, type) => {
      return (token.type === null) ? false : token.type.split(' ').includes(type)
    }

    const indexOfFirstLineTokenWithType = (lineTokens, type) => {
      return lineTokens.findIndex(token => {
        return tokenIncludesType(token, type)
      })
    }

    // Adds missing classes to heading contents.
    const enrichHeadingTokens = (lineTokens) => {
      const headingTokenIndex = indexOfFirstLineTokenWithType(lineTokens, 'mw-section-header')
      const headingContentsTokenIndex = headingTokenIndex + 1
      const headingContentsTokenExists = headingContentsTokenIndex <= (lineTokens.length - 1)
      if (headingTokenIndex < 0 || !headingContentsTokenExists) {
        return
      }
      const headingToken = lineTokens[headingTokenIndex]
      const headingContentsToken = lineTokens[headingContentsTokenIndex]
      if (headingToken.end === headingContentsToken.start) {
        addTypeToToken(headingContentsToken, 'mw-section-header-text')
        addTypeToToken(headingContentsToken, `mw-section-header-depth-${headingContentsToken.start}`)
      }
    }

    const addTypeToToken = (token, type) => {
      if (token.type === null) {
        token.type = type
      } else {
        let types = token.type.split(' ')
        types.push(type)
        token.type = types.join(' ')
      }
    }

    const lineTokensFromTokenAtIndex = (lineTokens, index) => {
      return lineTokens.filter((token, thisIndex) => thisIndex >= index)
    }
    
    const enrichIndentTokens = (lineTokens) => {
      const soughtTokenIndex = indexOfFirstLineTokenWithType(lineTokens, 'mw-indenting')
      if (soughtTokenIndex < 0) {
        return
      }      
      const soughtToken = lineTokens[soughtTokenIndex]
      const supplementToken = (token, index) => {
        if (index > 0) {
          addTypeToToken(token, `mw-indenting-contents`)
        }
        addTypeToToken(token, `mw-indenting-depth-${soughtToken.end}`)
      }
      lineTokensFromTokenAtIndex(lineTokens, soughtTokenIndex)
        .forEach(supplementToken)
    }

    const enrichSignatureTokens = (lineTokens) => {
      const supplementToken = (token) => {
        addTypeToToken(token, `mw-signature-depth-${token.end - token.start}`)
      }
      lineTokens
        .filter(token => {
          return tokenIncludesType(token, 'mw-signature')
        })
        .forEach(supplementToken)
    }


    // Individual words within a tag are tokenized separately, this method 
    // gives us the overall range for the entire tag's contents. Makes it
    // easier see what tags the current selection intersects and also makes
    // it easier to expand selection later to encompass entire tag contents.
    /*
      - loops through line tokens
      - each time it encounters a tag it's not already tracking, it records start
      - until it encounters token w/o that tracked tag, at which point it records end and stops tracking that tag
      - end result will be array of ranges for all tags encountered (can be more that one for a given tag)
      - returns array similar to:
        [
          sup: {start: 12, end: 25},
          small: {start: 32, end: 98},
          large: {start: 12, end: 108},
          small: {start: 100, end: 102}
        ]
      - This is *vastly* simpler to use than large numbers of line tokens.
      - then we can easily set this all encompassing range in the button payload for tags
        (will just need to loop through this array with selection range )
    */
    const completeTagRangesForLineTokens = (lineTokens) => {
     
      let trackedTags = new Set()
      let tagRanges = []
      
      const startAndStopTrackingTagRangesInToken = (token, index, tokens) => {
        const tags = new Set(token.state.InHtmlTag)
        
        // Fix for tags like 'ref', which mediawiki parsing curiously 
        // doesn't treat like other tags.
        if (token.state.extName !== false) {
          tags.add(token.state.extName)
        }
        
        // Fix for nested tags.
        if (token.state.extState !== false) {
          token.state.extState.InHtmlTag.forEach(tags.add, tags)
        }

        const isNotAlreadyTrackingTag = (tag) => {
          return !trackedTags.has(tag)
        }
        
        // Add tag item to tagRanges (with `start` value and placeholder `end` value) 
        // when we first encounter one. Also adds tag to trackedTags.
        const startTrackingTag = (tag) => {
          trackedTags.add(tag)
          tagRanges.push({tag: tag, start: token.start + 1, end: -1})
        }
        
        [...tags]
          .filter(isNotAlreadyTrackingTag)
          .forEach(startTrackingTag)

        let tagsToStopTracking = new Set()
        
        // Update tagRange `end` when we're no longer part of a trackedTag
        // (also removes tag from trackedTags)
        const stopTrackingTag = (tag) => {
          const prevToken = tokens[index - 1]
          const end = prevToken.end
          let existingRange = tagRanges.find(tagRange => {
            return tagRange.tag === tag && tagRange.end === -1
          })
          existingRange.end = end
          tagsToStopTracking.add(tag)
        }
        
        const shouldStopTrackingTag = (tag) => {
          return !tags.has(tag)
        }
        
        [...trackedTags]
          .filter(shouldStopTrackingTag)
          .forEach(stopTrackingTag)
        
        tagsToStopTracking.forEach(trackedTags.delete, trackedTags)
      }
      
      lineTokens.forEach(startAndStopTrackingTagRangesInToken)
      
      const isTagRangeComplete = (tagRange) => tagRange.end !== -1
      
      return tagRanges.filter(isTagRangeComplete)
    }

    const selectedButtons = (doc) => {  
      const selection = doc.getSelection()
      const selectionRange = getSelectionRange(doc)
      const lineTokens = enrichedLineTokens(doc, selectionRange.line)
      let result = []

      // Determine which html tag buttons are selected.
      
      const tagRanges = !selectionRange.isSingleLine ? [] : completeTagRangesForLineTokens(lineTokens)
      const tagRangesIntersectingSelectionRange = tagRanges.filter(tagRange => rangesIntersect(selectionRange, tagRange))
      tagRangesIntersectingSelectionRange.forEach(tagRange => {
        const buttonInfoDict = {start: tagRange.start, end: tagRange.end}
        if (tagRange.tag === 'u') {
          result.push(buttonPayload('underline', buttonInfoDict))
        }
        if (tagRange.tag === 's') {
          result.push(buttonPayload('strikethrough', buttonInfoDict))
        }
        if (tagRange.tag === 'sup') {
          result.push(buttonPayload('superscript', buttonInfoDict))
        }
        if (tagRange.tag === 'sub') {
          result.push(buttonPayload('subscript', buttonInfoDict))
        }
        if (tagRange.tag === 'small' || tagRange.tag === 'big') {
          buttonInfoDict.size = tagRange.tag 
          result.push(buttonPayload('textSize', buttonInfoDict))
        }
        if (tagRange.tag === 'ref') {
          result.push(buttonPayload('reference', buttonInfoDict))
        }
      })

      
      
      // Determine which wikitext buttons are selected.

      // For now only supports single line selection ranges.
      const intersectingTokens = selectionRange.isSingleLine ? tokensIntersectingSelection(selectionRange, lineTokens) : []
      const typesArray = intersectingTokens.map(token => token.type)
        .join(' ')
        .split(' ')
        .filter(s => {
          return s.trim().length > 0
        })
      
      
      if (typesArray.includes('strong')) {
        result.push(buttonPayload('bold'))
      }
      if (typesArray.includes('em')) {
        result.push(buttonPayload('italic'))
      }
      if (typesArray.includes('mw-template-ground')) {
        result.push(buttonPayload('template'))
      }
      if (typesArray.includes('mw-comment')) {
        result.push(buttonPayload('comment'))
      }
      if (typesArray.includes('mw-section-header-text')) {
        const depth = numberFromPrefixedKey(typesArray, 'mw-section-header-depth-')
        result.push(buttonPayload('heading', {depth: depth}))
      }

      if (typesArray.includes('mw-indenting') || typesArray.includes('mw-indenting-contents')) {
        const depth = numberFromPrefixedKey(typesArray, 'mw-indenting-depth-')
        result.push(buttonPayload('indent', {depth: depth}))
      }
      if (typesArray.includes('mw-signature')/* || typesArray.includes('mw-signature-contents')*/) {
        const depth = numberFromPrefixedKey(typesArray, 'mw-signature-depth-')
        result.push(buttonPayload('signature', {depth: depth}))
      }
      const listItemLineCallback = (item) => {
        result.push(buttonPayload('li', {depth: item.depth, ordered: item.isOrdered}))
        return false // No need to continue (only need to send one 'li' message)
      }
      performCallBackForSelectedListItemLines(doc, listItemLineCallback)

      return result
    }

    const disabledButtons = (doc, selectedButtons) => {
      let buttonsToDisable = []
      
      const listPayload = selectionPayloadForButton('li', selectedButtons)
      const selectionContainsList = listPayload !== null
      if (!selectionContainsList || listPayload.info.depth <= 1) {
        buttonsToDisable.push(buttonPayload('decreaseIndentDepth'))
      }
      if (!selectionContainsList) {
        buttonsToDisable.push(buttonPayload('increaseIndentDepth'))
      }

      const historySize = doc.historySize()
      if (historySize.undo < 2) {
        buttonsToDisable.push(buttonPayload('undo'))  
        buttonsToDisable.push(buttonPayload('progress'))
      }
      if (historySize.redo < 1) {
        buttonsToDisable.push(buttonPayload('redo'))
      }

      if (shouldDisableClearFormattingButton(doc)) {
        buttonsToDisable.push(buttonPayload('clearFormatting'))
      }
      const selectionRange = getItemRangeFromSelection(editor)
      const allMarkupItems = markupItemsForItemRangeLines(editor, selectionRange)
      const markupItems = getMarkupItemsIntersectingSelection(editor, allMarkupItems, selectionRange)
      if (markupItems.length !== 0) {
        buttonsToDisable.push(buttonPayload('media'))
      }

      if (!getLink(markupItems)) {
        buttonsToDisable.push(buttonPayload('link'))
      }

      return buttonsToDisable
    }

    const shouldDisableClearFormattingButton = (doc) => {
      return !canClearFormatting(editor)
    }

    const sendNativeMessages = (doc) => {
      const buttonsToSelect = selectedButtons(doc)
      const nativeMessages = {
        // message to native land that selection has changed,
        // upon receipt native land should de-select and re-enable all buttons.
        // (`selectionChanged` should be processed before the other messages)
        selectionChanged: getSelectionRange(doc).isRangeSelected,
        // message to native land about which buttons should be selected,
        // native land should assume only these buttons should appear selected
        highlightTheseButtons: buttonsToSelect,
        // message to native land about which buttons should be disabled,
        // native land should assume only these buttons should be disabled
        disableTheseButtons: disabledButtons(doc, buttonsToSelect)
      }
      window.webkit.messageHandlers.codeMirrorMessage.postMessage(nativeMessages)
    }
        
    const applyTheme = (themeName) => {
      var elements = document.body.className.split(" ")
      const themeClass = `cm-mw-${themeName}`
      if (elements.length > 0) {
        elements[0] = themeClass
      } else {
        elements.push(themeClass)
      }
      document.body.className = elements.join(" ")
    }
  
    const toggleSyntaxColors = () => {
      var elements = document.body.className.split(" ")
      if (elements.length == 1) {
        document.body.className = elements[0] + " cm-mw-off"
      } else {
        document.body.className = elements[0]
      }
    }
  
    const scaleBodyText = (textSizeAdjustment) => {
      document.body.style['font-size'] = (textSizeAdjustment/100.0) + 'em'
    }
    
    const setupCodemirrorWithSettings = (codeMirrorSettings, callback) => {
      editor = CodeMirror(document.body, codeMirrorSettings)
      if (callback) {
        callback()
      }
      editor.on('cursorActivity', notifyNativeAboutChangesAndScrollIfNeeded)
    }

    const notifyNativeAboutChangesAndScrollIfNeeded = (doc) => {
      if (!doc.isReplacing) {
          sendNativeMessages(doc)
          scrollCursorIntoViewIfNeeded()        
      }
    }

    const setupCodemirror = (language, direction, themeName, textSizeAdjustment, isSyntaxHighlighted, callback) => {
      applyTheme(themeName)
      if (!isSyntaxHighlighted) {
          toggleSyntaxColors()
      }
      scaleBodyText(textSizeAdjustment)
      let xhr = new XMLHttpRequest()
      let url = `config/codemirror-config-${language}.json`
      xhr.open('GET', url, true)
      xhr.onload = () => {
        let object = JSON.parse(xhr.responseText)
        let mwConfig = object['extCodeMirrorConfig']
        let codeMirrorSettings = {
          mwConfig: mwConfig,
          lineWrapping: true,
          lineNumbers: true,
          mode: "text/mediawiki",
          matchBrackets: true,
          extraKeys: {
            // t.b.d.
          }, 
          inputStyle: 'contenteditable',
          direction: direction,
          autocorrect: true,
          autocapitalize: true,
          spellcheck: false,
          preventDefaultOnKeyPress: false,
          viewportMargin: Infinity
        }
        setupCodemirrorWithSettings(codeMirrorSettings, callback)
      };
      xhr.send()
    }
      
    const wrapSelectionWith = (openingMarkup, closingMarkup) => {
      if (closingMarkup === undefined) {
        closingMarkup = openingMarkup
      }
      var selectedText = editor.getSelection()
      if (!selectedText || selectedText.length == 0) {
        selectedText = ' '
      }
      editor.replaceSelection(openingMarkup + selectedText + closingMarkup, 'around')
      const selections = editor.listSelections()
      if (selections.length > 0) {
        const openingMarkupLength = openingMarkup.length
        const closingMarkupLength = closingMarkup.length
        const selection = selections[0]
        const fromPos = selection.from()
        const toPos = selection.to()
        const from = {line: fromPos.line, ch: fromPos.ch + openingMarkupLength, sticky: fromPos.sticky}
        const to = {line: toPos.line, ch: toPos.ch - closingMarkupLength, sticky: toPos.sticky}
        editor.setSelection(from, to)
      }
    }
    
    const reverseString = (string) => {
      return string.split('').reverse().join('')
    }
    
    const selectionWrapInfo = (openingMarkup, closingMarkup) => {
      if (closingMarkup === undefined) {
        closingMarkup = openingMarkup
      }
      const selections = editor.listSelections()
      if (selections.length < 1) {
        return {isWrapped: false, selectionLength: 0, openDistance: -1, closeDistance: -1}
      }
      const selection = selections[0]
      const fromPos = selection.from()
      const toPos = selection.to()
      if (fromPos.line != toPos.line) {
        return {isWrapped: false, selectionLength: 0, openDistance: -1, closeDistance: -1}
      }
      const lineIndex = fromPos.line
      const line = editor.getLine(lineIndex)
      var lastOpenMatchBeforeSelection = {distance: -1}
      var firstOpenMatchAfterSelection = {distance: -1}
      var firstOpenMatchWithinSelection = {distance: -1}
	    var openMatchEncompassingStartOfSelection = {}
      var openMatchEncompassingEndOfSelection = {}
      line.replace(new RegExp(openingMarkup, 'g'), (match, index) => {
        const endIndex = index + match.length
        if (index <= fromPos.ch && endIndex >= fromPos.ch) {
          openMatchEncompassingStartOfSelection = {match: match, offset: index - fromPos.ch}
        }
        if (endIndex <= fromPos.ch) {
          lastOpenMatchBeforeSelection = {match: match, distance: fromPos.ch - endIndex}
        }
        if (index >= fromPos.ch && endIndex <= toPos.ch && firstOpenMatchWithinSelection.distance === -1) {
          firstOpenMatchWithinSelection = {match: match, distance: index - fromPos.ch}
        }
        if (index >= toPos.ch && firstOpenMatchAfterSelection.distance === -1) {
          firstOpenMatchAfterSelection = {match: match, distance: index - toPos.ch}	
        }
        if (index <= toPos.ch && endIndex >= toPos.ch) {
          openMatchEncompassingEndOfSelection = {match: match, offset: index - toPos.ch}
        }
      })
	  
      var lastCloseMatchBeforeSelection = {distance: -1}
      var firstCloseMatchAfterSelection = {distance: -1}
      var firstCloseMatchWithinSelection = {distance: -1}
	    var closeMatchEncompassingStartOfSelection = {}
      var closeMatchEncompassingEndOfSelection = {}
      line.replace(new RegExp(closingMarkup, 'g'), (match, index) => {
        const endIndex = index + match.length
        if (index <= fromPos.ch && endIndex >= fromPos.ch) {
          closeMatchEncompassingStartOfSelection = {match: match, offset: index - fromPos.ch}
        }
        if (endIndex <= fromPos.ch) {
          lastCloseMatchBeforeSelection = {match: match, distance: fromPos.ch - endIndex}
        }
        if (index >= fromPos.ch && endIndex <= toPos.ch && firstCloseMatchWithinSelection.distance === -1) {
          firstCloseMatchWithinSelection = {match: match, distance: toPos.ch - endIndex}
        }
        if (index >= toPos.ch && firstCloseMatchAfterSelection.distance === -1) {
          firstCloseMatchAfterSelection = {match: match, distance: index - toPos.ch}	
        }
        if (index <= toPos.ch && endIndex >= toPos.ch) {
          closeMatchEncompassingEndOfSelection = {match: match, offset: index - toPos.ch}
        }
      })
      const isWrapped = lastOpenMatchBeforeSelection.distance !== -1 && (lastCloseMatchBeforeSelection.distance === -1 || lastOpenMatchBeforeSelection.distance < lastCloseMatchBeforeSelection.distance) // exclude equal distances to rule out self-contained tags, for example <ref name=blah/>
      return {
        isWrapped: isWrapped,
        selectionLength: toPos.ch - fromPos.ch,
        openMatch: lastOpenMatchBeforeSelection.match,
        closeMatch: firstCloseMatchAfterSelection.match,
        openDistance: lastOpenMatchBeforeSelection.distance,
        closeDistance: firstCloseMatchAfterSelection.distance,
        priorCloseDistance: lastCloseMatchBeforeSelection.distance,
        nextOpenDistance: firstOpenMatchAfterSelection.distance,
        priorCloseMatch: lastCloseMatchBeforeSelection.match,
        nextOpenMatch: firstOpenMatchAfterSelection.match,
        openWithinDistance: firstOpenMatchWithinSelection.distance,
        closeWithinDistance: firstCloseMatchWithinSelection.distance,
        openMatchEncompassingStartOffset: openMatchEncompassingStartOfSelection.offset,
        openMatchEncompassingStart: openMatchEncompassingStartOfSelection.match,
        openMatchEncompassingEndOffset: openMatchEncompassingEndOfSelection.offset,
        openMatchEncompassingEnd: openMatchEncompassingEndOfSelection.match,
        closeMatchEncompassingStartOffset: closeMatchEncompassingStartOfSelection.offset,
        closeMatchEncompassingStart: closeMatchEncompassingStartOfSelection.match,
        closeMatchEncompassingEndOffset: closeMatchEncompassingEndOfSelection.offset,
        closeMatchEncompassingEnd: closeMatchEncompassingEndOfSelection.match
      }
    }
    
    const unwrapSelectionWith = (openingMarkup, closingMarkup) => {
      if (closingMarkup === undefined) {
        closingMarkup = openingMarkup
      }
      const selectedText = editor.getSelection()
      if (selectedText === ' ') { // remove single whitespace that was likely inserted by wrapSelection
        editor.replaceSelection('', 'start')
      }
      const selections = editor.listSelections()
      if (selections.length > 0) {
        const selection = selections[0]
        const fromPos = selection.from()
        const toPos = selection.to()
        const lineIndex = fromPos.line
        const line = editor.getLine(lineIndex)
        const before = line.substring(0, fromPos.ch)
        const after = line.substring(fromPos.ch, line.length)
        const adjustedBefore = reverseString(reverseString(before).replace(reverseString(openingMarkup), ''))
        const adjustedAfter = after.replace(closingMarkup, '')
        const adjustedLine = adjustedBefore + adjustedAfter
        if (line.length != adjustedLine.length) {
          editor.replaceRange(adjustedLine, {line: lineIndex, ch: 0}, {line: lineIndex, ch: line.length})
          const openingMarkupLength = openingMarkup.length
          const from = {line: fromPos.line, ch: fromPos.ch - openingMarkupLength, sticky: fromPos.sticky}
          const to = {line: toPos.line, ch: toPos.ch - openingMarkupLength, sticky: toPos.sticky}
          editor.setSelection(from, to)
        }
      }
    }
  
    const identicalMarkupSelectionWrapInfo = (markup) => {
      const selections = editor.listSelections()
      if (selections.length < 1) {
        return {selectionLength: 0, beforeIndex: -1, afterIndex: -1}
      }
      const selection = selections[0]
      const fromPos = selection.from()
      const toPos = selection.to()
      if (fromPos.line != toPos.line) {
        return {selectionLength: 0, beforeIndex: -1, afterIndex: -1}
      }
      const lineIndex = fromPos.line
      const line = editor.getLine(lineIndex)
      const before = line.substring(0, fromPos.ch)
      const after = line.substring(toPos.ch, line.length)
      const beforeIndex = reverseString(before).indexOf(reverseString(markup))
      const afterIndex = after.indexOf(markup)
      return {selectionLength: toPos.ch - fromPos.ch, beforeIndex: beforeIndex, afterIndex: afterIndex}
    }
  
    const isSelectionWrappedWithIdenticalMarkup = (markup) => {
      const info = identicalMarkupSelectionWrapInfo(markup)
      return info.selectionLength == 0 ? info.beforeIndex >= 0 && info.afterIndex >= 0 : info.beforeIndex == 0 && info.afterIndex == 0
    }
    
    const slideMarkupLeft = (markup) => {
      const selectedText = editor.getSelection()
      const selectionRange = getSelectionRange(editor)
      const replacement = `${markup}${selectedText}`
      const line = selectionRange.line
      const from = { line: line, ch: selectionRange.start }
      const to = { line: line, ch: selectionRange.end + markup.length }
      editor.replaceRange(replacement, from, to)
    }
    
    const slideMarkupRight = (markup) => {
      const selectedText = editor.getSelection()
      const selectionRange = getSelectionRange(editor)
      const replacement = `${selectedText}${markup}`
      const line = selectionRange.line
      const from = { line: line, ch: selectionRange.start - markup.length }
      const to = { line: line, ch: selectionRange.end }
      editor.replaceRange(replacement, from, to)
    }
  
    const toggleIdenticalMarkup = (button, markup) => {
      const info = identicalMarkupSelectionWrapInfo(markup)
      if (info.beforeIndex >= 0 && info.afterIndex >= 0) {
        if (info.selectionLength == 0 || (info.beforeIndex == 0 && info.afterIndex == 0)) {
          unwrapSelectionWith(markup, markup)
        } else if (info.beforeIndex == 0 && info.afterIndex > 0) { // slide opening markup to the right
          slideMarkupRight(markup)
        } else if (info.beforeIndex > 0 && info.afterIndex == 0) { // slide closing markup to the left
          slideMarkupLeft(markup)
        } else {
          wrapSelectionWith(markup, markup)
        }
      } else {
        if (info.selectionLength == 0) {
          wrapSelectionWith(markup, markup)
        } else if (info.afterIndex == 0) {
          slideMarkupLeft(markup)
        } else if (info.beforeIndex == 0) {
          slideMarkupRight(markup)
        } else {
          wrapSelectionWith(markup, markup)
        }
      }
    }

    class LinkElementPosition {
      /**
      * LinkElementPosition constructor.
      * @param {!{line: number, ch: number}} from
      * @param {!{line: number, ch: number}} to
      */
      constructor(from, to) {
        this.from = from
        this.to = to
      }
    }

    class Link {
      /**
      * Link constructor.
      * @param {!string} page
      * @param {?string} label
      * @param {!boolean} hasMarkup
      * @param {!LinkElementPosition} innerPosition
      */
      constructor(page, label, hasMarkup, innerPosition) {
        this.page = page
        this.label = label
        this.hasMarkup = hasMarkup
        this.innerPosition = innerPosition
        if (hasMarkup) {
          this.outerPosition = new LinkElementPosition({ line: innerPosition.from.line, ch: innerPosition.from.ch - anchorLength }, { line: innerPosition.to.line, ch: innerPosition.to.ch + anchorLength })
        }
        if (label) {
          this.pageWithPipePosition = new LinkElementPosition({ line: innerPosition.from.line, ch: innerPosition.from.ch }, { line: innerPosition.from.line, ch: innerPosition.from.ch + page.length + 1 }) // add pipe
        }
      }
    }

    const linkFromTextBetweenAnchorMarkup = (text, innerPosition) => {
      if (!text) {
        return null
      }
      if (!innerPosition) {
        console.warn("Mising innerPosition in linkFromTextBetweenAnchorMarkup; innerPosition must be defined to construct a new Link")
        return null
      }
      const textSplitByPipe = text.split("|")
      if (textSplitByPipe.length == 0 || textSplitByPipe.length > 2) {
        return null
      }
      
      const page = textSplitByPipe[0]
      const label = textSplitByPipe[1]

      return new Link(page, label, true, innerPosition)
    }

    const linkFromWord = () => {
      const word = editor.findWordAt(editor.getCursor())
      const text = editor.getRange(word.anchor, word.head)
      return new Link(text, null, false, new LinkElementPosition(word.anchor, word.head))
    }

    const linkFromSelectionWrapInfo = () => {
      const info = selectionWrapInfo(openAnchorRegex, closeAnchorRegex)
      const anchor = editor.getCursor('anchor')
      const head = editor.getCursor('head')

      if (info.openDistance === 0 && info.closeDistance === 0) {
        const innerPosition = new LinkElementPosition(anchor, head)
        return linkFromTextBetweenAnchorMarkup(editor.getSelection(), innerPosition)
      } else {
        let from;
        let to;

        if (info.openMatchEncompassingStartOffset !== undefined && (info.closeMatchEncompassingEndOffset || info.closeDistance !== -1) && info.closeWithinDistance <= 0) {
          let fromCh
          if (info.openMatchEncompassingStartOffset === 0) {
            fromCh = anchor.ch + anchorLength
          } else if (info.openMatchEncompassingStartOffset + anchorLength === 0) {
            fromCh = anchor.ch
          } else {
            fromCh = anchor.ch - info.openMatchEncompassingStartOffset
          }
          from = { line: anchor.line, ch: fromCh }
          const toCh = head.ch + (info.closeMatchEncompassingEndOffset || info.closeDistance)
          to = { line: head.line, ch: toCh }
        } else if (info.closeMatchEncompassingEndOffset !== undefined && (info.openMatchEncompassingStartOffset || info.openDistance !== -1) && info.openWithinDistance <= 0) {
          const fromCh = anchor.ch - (info.openMatchEncompassingStartOffset || info.openDistance)
          from = { line: anchor.line, ch: fromCh }
          let toCh
          if (info.closeMatchEncompassingEndOffset === 0) {
            toCh = head.ch
          } else if (info.closeMatchEncompassingEndOffset + anchorLength === 0) {
            toCh = head.ch - anchorLength
          } else {
            toCh = head.ch + info.closeMatchEncompassingEndOffset
          }
          to = { line: head.line, ch: toCh }
        } else if (info.isWrapped && info.closeDistance !== -1 && info.openDistance !== -1 && (info.nextOpenDistance >= info.closeDistance) || info.nextOpenDistance === -1) {
          const fromCh = anchor.ch - info.openDistance
          from = { line: anchor.line, ch: fromCh }
          const toCh = head.ch + info.closeDistance
          to = { line: head.line, ch: toCh }
        } else if (info.openWithinDistance !== -1 && info.closeWithinDistance !== -1) {
          const fromCh = anchor.ch + info.openWithinDistance + anchorLength
          from = { line: anchor.line, ch: fromCh }
          const toCh = head.ch - info.closeWithinDistance - anchorLength
          to = { line: head.line, ch: toCh }
        }

        if (from && to) {
          const maybePipedText = editor.getRange(from, to)
          const innerPosition = new LinkElementPosition(from, to)
          return linkFromTextBetweenAnchorMarkup(maybePipedText, innerPosition)
        } else {
          return null
        }
      }
    }

    const getLink = (markupItems, keepInStateStore = true) => {
      const selection = editor.getSelection()
      const buttonNames = getButtonNamesFromMarkupItems(markupItems)
      let link
      if (selection.length === 0) {
        if (buttonNames.includes("link")) {
          link = linkFromSelectionWrapInfo()
        } else {
          const leading = {
            from: { line: editor.getCursor().line, ch: editor.getCursor().ch },
            to: { line: editor.getCursor().line, ch: editor.getCursor().ch + anchorLength }
          }
          const trailing = {
            from: { line: editor.getCursor().line, ch: editor.getCursor().ch - anchorLength },
            to: { line: editor.getCursor().line, ch: editor.getCursor().ch }
          }
          const leadingText = editor.getRange(leading.from, leading.to)
          const trailingText = editor.getRange(trailing.from, trailing.to)
          const isMarkup = (text) => text === "]]" || text === "[["
          if (isMarkup(leadingText) || isMarkup(trailingText)) {
            link = linkFromSelectionWrapInfo()
          } else {
            link = linkFromWord()
          }
        }
      } else {
        if (buttonNames.length === 0) {
          const innerPosition = {from: editor.getCursor('anchor'), to: editor.getCursor('head')}
          link = new Link(selection, null, false, innerPosition)
        } else if (buttonNames.length === 1 && buttonNames.includes('link')) {
          link = linkFromSelectionWrapInfo()
        }
      }
      stateStore.link = link
      return link
    }
    
    const toggleMarkup = (button, openingMarkup, closingMarkup, defaultOpeningMarkup, defaultClosingMarkup) => {
      if (closingMarkup === undefined) {
        closingMarkup = openingMarkup
      }
      if (defaultOpeningMarkup == undefined) {
        defaultOpeningMarkup = openingMarkup
      }
      if (defaultClosingMarkup == undefined) {
        defaultClosingMarkup = closingMarkup
      }
      const info = selectionWrapInfo(openingMarkup, closingMarkup)
      if (info.isWrapped) {
        if ((info.openDistance == 0 && info.closeDistance == 0) || info.selectionLength == 0) {
          unwrapSelectionWith(info.openMatch, info.closeMatch)
        } else if (info.openDistance == 0 && info.closeDistance > 0) { // slide opening markup to the right
          slideMarkupRight(info.openMatch)
        } else if (info.openDistance > 0 && info.closeDistance == 0) { // slide closing markup to the left
          slideMarkupLeft(info.closeMatch)
        } else {
          wrapSelectionWith(defaultClosingMarkup, defaultOpeningMarkup)
        }
      } else { // not wrapped with markup
        if (info.selectionLength == 0) {
          wrapSelectionWith(defaultOpeningMarkup, defaultClosingMarkup)
        } else if (info.priorCloseDistance == 0 && info.nextOpenDistance == 0) {
          unwrapSelectionWith(info.priorCloseMatch, info.nextOpenMatch)
        } else if (info.nextOpenDistance == 0) {
          slideMarkupLeft(info.nextOpenMatch)
        } else if (info.priorCloseDistance == 0) {
          slideMarkupRight(info.priorCloseMatch)
        } else {
          wrapSelectionWith(defaultOpeningMarkup, defaultClosingMarkup)
        }
      }
    }

    const stateStore = {} // local state object to avoid relying on cm
    
    const wmf = {}
    
    wmf.getWikitext = () => editor.getValue()
    wmf.setWikitext = (wikitext, completion) => {
      editor.startOperation()
      editor.setValue(wikitext)
      editor.endOperation()
      if (!completion) {
        return
      }
      requestAnimationFrame(() => {
        completion()
      })
    }
    wmf.setup = (language, direction, themeName, textSizeAdjustment, isSyntaxHighlighted, callback) => setupCodemirror(language, direction, themeName, textSizeAdjustment, isSyntaxHighlighted, callback)
    wmf.applyTheme = (themeName) => applyTheme(themeName)
    wmf.highlightAndScrollToWikitextForSelectedAndAdjacentText = highlightAndScrollToWikitextForSelectedAndAdjacentText

    const italicMarkup = "''"
    const boldMarkup = "'''"


    // Method for easier mining of button payload information for current selection. 
    // For example, if you want to know if the current selection is a heading, and if so how many
    // '=' does it use, this will return a button payload IF the current selection has been 
    // determined to be a button. This is the same payload we send to native land to tell it which
    // buttons are selected and any associated relevant properties - in the case of a heading the
    // payload's info.depth tells us how many '=' the currently selected heading has.
    const selectionPayloadForButton = (buttonType, selectedButtonsArray) => {
      if (selectedButtonsArray === undefined) {
        selectedButtonsArray = selectedButtons(editor)
      }
      const payloadsForButton = selectedButtonsArray.filter((item) => {return item.button === buttonType})
      if (payloadsForButton.length === 0) {
        return null
      }
      return payloadsForButton[0]
    }

    const toggleListForSelectedLines = (char) => {
      const listPayload = selectionPayloadForButton('li')
      const isExistingListSelected = listPayload !== null
      if (isExistingListSelected) {
        delistifySelectedLines()
        if (char === '#' && listPayload.info.ordered) {
          return
        }
        if (char === '*' && !listPayload.info.ordered) {
          return
        }
      }
      convertSelectedLinesToList(char)
    }

    class LineListItem {
      constructor(entireLine, leadingCharsAndWhitespace, char, depth) {
        this.entireLine = entireLine
        this.leadingCharsAndWhitespace = leadingCharsAndWhitespace
        this.char = char
        this.depth = depth
        this.isOrdered = (char === '#')
      }
    }

    const leadingListItemRepeatingCharactersAndWhitespace = (entireLine) => {
      const leadingCharsAndWhitespace = entireLine.match(new RegExp(`^(?:(\\*+|#+))\\s*`))
      if (!leadingCharsAndWhitespace) {
        return null
      }
      return new LineListItem(entireLine, leadingCharsAndWhitespace[0], leadingCharsAndWhitespace[1].charAt(0), leadingCharsAndWhitespace[1].length)
    } 

    const delistifySelectedLines = () => {
      const listItemLineCallback = (item, line) => {
        editor.replaceRange('', {line: line, ch: 0}, {line: line, ch: item.leadingCharsAndWhitespace.length})
        return true
      }
      performCallBackForSelectedListItemLines(editor, listItemLineCallback)
    }

    const performCallBackForSelectedListItemLines = (doc, callback) => {
      let toLine = doc.getCursor('to').line
      let fromLine = doc.getCursor('from').line
      for (let line = toLine; line >= fromLine; line--) {
        const thisLineText = doc.getLine(line)
        const lineListItem = leadingListItemRepeatingCharactersAndWhitespace(thisLineText)
        if (lineListItem) {
          const shouldContinue = callback(lineListItem, line)
          if (!shouldContinue) {
            return
          }
        }
      }
    }

    const expandSelectionToEntireLines = () => {
      const newFrom = {line: editor.getCursor('from').line, ch: 0}
      const currentTo = editor.getCursor('to')
      const newTo = {line: currentTo.line, ch: editor.getLine(currentTo.line).length}
      editor.setSelection(newFrom, newTo)
    }

    const convertSelectedLinesToList = (char) => {      
      const selectionRange = getSelectionRange(editor)
      if (selectionRange.isSingleLine) {
        editor.replaceRange(`${char} `, {line: selectionRange.line, ch: 0})
        return
      }
      // Ensure start of selection range is at beginning of line.
      expandSelectionToEntireLines()
      // Now can add characters - the 'around' option preserves selection.
      editor.replaceSelection(`${char} ${editor.getSelection().replace(/\n/gi, `\n${char} `)}`, 'around')
    }

    const verticalOffsetNeededToScrollRangeIntoView = (from, to) => {
      const fromTopOfWebView = locationOffsetFromTopOfWebView(from)
      if (fromTopOfWebView < 0) {
        return fromTopOfWebView
      }
      const fromTopOfKeyboard = locationOffsetFromTopOfKeyboard(to)
      if (fromTopOfKeyboard > 0) {
        return fromTopOfKeyboard
      }
      return 0
    }

    let adjustedContentInset = {top: 0, left: 0, bottom: 0, right: 0}
    const webViewHeightNotCoveredByKeyboard = () => document.documentElement.clientHeight - adjustedContentInset.bottom
    const locationOffsetFromTopOfWebView = (location) => editor.charCoords(location, 'window').top
    const locationOffsetFromTopOfKeyboard = (location) => editor.charCoords(location, 'window').bottom - webViewHeightNotCoveredByKeyboard()

    const scrollCursorIntoViewIfNeeded = () => {
      scrollRangeIntoViewIfNeeded(editor.getCursor('from'), editor.getCursor('to'))
    }

    let scrollTimer
    const scrollRangeIntoViewIfNeeded = (from, to) => {
      clearTimeout(scrollTimer)
      scrollTimer = setTimeout(() => {
        const offset = verticalOffsetNeededToScrollRangeIntoView(from, to)
        if (offset === 0) {
          return
        }
        // WKWebView doesn't currently support `window.scrollTo` with `behavior: smooth`,
        // so let native land do the scrolling.
        window.webkit.messageHandlers.smoothScrollToYOffsetMessage.postMessage(offset)
      }, 250)
    }

    const presentReplaceAllCount = () => {
      var replaceAllCount = editor.state.replaceAllCount;
      window.webkit.messageHandlers.replaceAllCountMessage.postMessage(replaceAllCount);
      editor.state.replaceAllCount = null;
    }

    let resizeTimer
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimer)
      resizeTimer = setTimeout(() => {
        scrollCursorIntoViewIfNeeded()        
      }, 250)
    }, true)

    let previousAdjustedContentInsetBottom = 0
    const adjustedContentInsetBottomChanged = () => {
      if (adjustedContentInset.bottom == previousAdjustedContentInsetBottom) {
        return false
      }
      previousAdjustedContentInsetBottom = adjustedContentInset.bottom
      return true  
    }
	

    const openReferenceRegex = /<ref[\s]*[^>]*>/
    const closeReferenceRegex = /<(?:\/[\s]*ref[\s]*|ref[\s]*[^>]*\/)>/
	
    const openAnchorRegex = /\[\[/
    const closeAnchorRegex = /\]\]/
    const anchorLength = 2

    const openTemplateRegex = /\{\{/
    const closeTemplateRegex = /\}\}/
  
    wmf.commands = {
      adjustedContentInsetChanged: (newInset) => {
        adjustedContentInset = newInset
        if (adjustedContentInsetBottomChanged()) {
          scrollCursorIntoViewIfNeeded()
        }
      },
      focus: () => {
        editor.focus()
      },
      focusWithoutScroll: () => {
        const scrollY = window.scrollY
        editor.focus()
        window.scrollTo(window.scrollX, scrollY)
      },
      selectLastSelection: () => {
          const from = editor.getCursor('from')
          const to = editor.getCursor('to')
          editor.setSelection(from, to, {scroll: false})
      },
      selectLastFocusedMatch: () => {
        const state = editor.state.search
        if (!state) {
          return
        }
        const from = state.posFrom
        const to = state.posTo
        if (!from || !to) {
          return
        }
        editor.setSelection(from, to, {scroll: false})
      },
      undo: () => {
        editor.undo()

        // Hack to fix cursor *appearing* to be above actual line after undo.  
        requestAnimationFrame(() => {
          const from = editor.getCursor('from')
          const to = editor.getCursor('to')
          const wasRangeSelected = (from.line !== to.line) || (to.ch - from.ch) > 0

          editor.setCursor(from.line + 1)
          editor.setCursor(from)
          if (wasRangeSelected) {
            editor.setSelection(from, to)
          }
        })
        
       notifyNativeAboutChangesAndScrollIfNeeded(editor)
      },
      redo: () => editor.redo(),
      cursorUp: () => editor.execCommand('goLineUp'),
      cursorDown: () => editor.execCommand('goLineDown'),
      cursorLeft: () => editor.execCommand('goCharLeft'),
      cursorRight: () => editor.execCommand('goCharRight'),
      bold: () => {
        toggleIdenticalMarkup('bold', boldMarkup)
      },
      italic: () => {
        if (isSelectionWrappedWithIdenticalMarkup(boldMarkup + italicMarkup)) {
          unwrapSelectionWith(italicMarkup)
        } else if (isSelectionWrappedWithIdenticalMarkup(boldMarkup)) {
          wrapSelectionWith(italicMarkup)
        } else {
          toggleIdenticalMarkup('italic', italicMarkup)
        }
      },
      reference: () => {
        toggleMarkup('reference', openReferenceRegex, closeReferenceRegex, '<ref>', '</ref>')
      },
      template: () => {
        toggleMarkup('template', openTemplateRegex, closeTemplateRegex, '{{', '}}')
      },
      comment: () => {
        toggleMarkup('comment', '<!--', '-->')
      },
      indent: () => {
        console.log('indent')
      },
      signature: () => {
        console.log('signature')
      },
      unorderedList: () => {
        toggleListForSelectedLines('*')
      },
      orderedList: () => {
        toggleListForSelectedLines('#')
      },
      superscript: () => {
        toggleMarkup('superscript', '<sup>', '</sup>')
      },
      subscript: () => {
        toggleMarkup('subscript', '<sub>', '</sub>')
      },
      underline: () => {
        toggleMarkup('underline', '<u>', '</u>')
      },
      strikethrough: () => {
        toggleMarkup('strikethrough', '<s>', '</s>')
      },
      heading: (depth) => {
        const selectionRange = getSelectionRange(editor)
        if (!selectionRange.isSingleLine) {
          return
        }

        // Attempt to get details about any currenly selected heading.
        const headingPayload = selectionPayloadForButton('heading')

        // If cursor is in existing heading we'll have a payload telling us now many '=' it uses.
        const isExistingHeadingSelected = headingPayload !== null
        
        // First toggle off existing selection heading.
        if (isExistingHeadingSelected) {
          // Expand selection to encompass current heading (you can't nest headings so this is better).
          const lineTokens = enrichedLineTokens(editor, selectionRange.line)
          const headerTextTokenIndex = indexOfFirstLineTokenWithType(lineTokens, 'mw-section-header-text')
          if (headerTextTokenIndex) {
            const headerTextToken = lineTokens[headerTextTokenIndex]
            editor.setSelection({line: selectionRange.line, ch: headerTextToken.start}, {line: selectionRange.line, ch: headerTextToken.end})
          }
          
          // Now remove the existing heading equals so the toggle at the end of the method will add the new amount of equals.
          const currentDepth = headingPayload.info.depth
          // But don't remove the existing heading equals if new depth is same as old.
          // This way the toggle at the end of the method will remove all equals when we try to set the same number of equals that are already present.
          if (currentDepth !== depth) {
            const currentEquals = '='.repeat(currentDepth)
            toggleMarkup('heading', currentEquals, currentEquals)
          }          
        }

        // If a heading isn't already selected, move the selection to its own line (and re-select it).
        if (!isExistingHeadingSelected) {
          const startOfSelectionIsNotStartOfLine = selectionRange.start !== 0
          const endOfSelectionIsNotEndOfLine = selectionRange.end !== editor.getLine(selectionRange.line).length
          if (startOfSelectionIsNotStartOfLine || endOfSelectionIsNotEndOfLine) {
            const leadingBreak = startOfSelectionIsNotStartOfLine ? '\n' : ''
            const trailingBreak = endOfSelectionIsNotEndOfLine ? '\n' : ''
          
            // Add leading or trailing line breaks as needed (heading must be on own line).
            let from = {line: selectionRange.line, ch: selectionRange.start}
            let to = {line: selectionRange.line, ch: selectionRange.end}
            editor.replaceRange(`${leadingBreak}${editor.getSelection()}${trailingBreak}`, from, to)
            
            // Re-select selection after addition of leading and/or trailing line breaks.
            const lineDelta = startOfSelectionIsNotStartOfLine ? 1 : 0
            from = {line: selectionRange.line + lineDelta, ch: 0}
            to = {line: selectionRange.line + lineDelta, ch: selectionRange.end - selectionRange.start}
            editor.setSelection(from, to)
          }
        }

        // Now actually toggle heading for selected depth.
        const equals = '='.repeat(depth)
        toggleMarkup('heading', equals, equals)
      },
      increaseIndentDepth: () => {
        const listItemLineCallback = (item, line) => {
          editor.replaceRange(item.char, {line: line, ch: 0})
          return true
        }
        performCallBackForSelectedListItemLines(editor, listItemLineCallback)
      },
      decreaseIndentDepth: () => {
        const listItemLineCallback = (item, line) => {
          if (item.depth === 1) {
            return true
          }
          editor.replaceRange('', {line: line, ch: 0}, {line: line, ch: 1})
          return true
        }
        performCallBackForSelectedListItemLines(editor, listItemLineCallback)
      },
      selectAll: () => editor.execCommand('selectAll'),
      highlighting: () => {
        editor.setOption('maxHighlightLength', editor.getOption('maxHighlightLength') === -1 ? 10000 : -1)
        editor.refresh()
      },
      lineNumbers: () => {
          editor.setOption('lineNumbers', editor.getOption('lineNumbers') == true ? false : true)
          editor.refresh()
      },
      theme: (theme) => {
          applyTheme(theme)
      },
      syntaxColors: () => {
          toggleSyntaxColors()
      },
      scaleBodyText: (textSizeAdjustment) => {
          scaleBodyText(textSizeAdjustment)
      },
      find: (text) => {
        editor.state.query = text
        editor.execCommand('find')
      },
      findNext: () => editor.execCommand('findNext'),
      findPrevious: () => editor.execCommand('findPrev'),
      clearSearch: () => editor.execCommand('clearSearch'),
      replaceAll: (text) => {
        editor.state.replaceText = text
        editor.execCommand('replaceAllWithoutDialogs')
        presentReplaceAllCount()
        editor.state.replaceText = null
        editor.state.replaceAllNum = null
      },
      replaceSingle: (text) => {
          editor.state.replaceText = text
          editor.execCommand('replaceSingleWithoutDialogs')
      },
      clearFormatting: () => {
        clearFormatting(editor)
      },
      replaceSelection: (text) => {
        editor.replaceSelection(text)
      },
      replaceSelection: (text) => editor.replaceSelection(text),
      lineInfo: () => {
        const cursorPosition = editor.getCursor('anchor')
        return {
          hasLineTokens: editor.getLineTokens(cursorPosition.line).length > 0,
          isAtLineEnd: editor.getLine(cursorPosition.line).length === cursorPosition.ch
        }
      },
      newlineAndIndent: () => editor.execCommand('newlineAndIndent'),
      getLink: () => stateStore.link,
      insertOrEditLink: (page, label) => {
        const link = stateStore.link
        if (!link) {
          console.warn("Attempting to insert or edit link with no link in stateStore")
          return
        }
        if (link.hasMarkup) { // existing link
          if (link.label) {
            if (label) { // modify an exsiting label
              const pageWithLabel = `${page}|${label}`
              editor.replaceRange(pageWithLabel, link.innerPosition.from, link.innerPosition.to) 
            } else { // modify an existing link with a label
              const pageWithExistingLabel = `${page}|${link.label}`
              editor.replaceRange(pageWithExistingLabel, link.innerPosition.from, link.innerPosition.to)
            }
          } else {
            if (label) { // add a label to an existing link with no label
              const pageWithLabel = `${page}|${label}`
              editor.replaceRange(pageWithLabel, link.innerPosition.from, link.innerPosition.to)
            } else { // modify existing link
              editor.replaceRange(page, link.innerPosition.from, link.innerPosition.to)
            }
          }
        } else { // new link
          if (label) {
            const newPageWithLabel = `[[${page}|${label}]]`
            editor.replaceRange(newPageWithLabel, link.innerPosition.from, link.innerPosition.to)
          } else {
            const shouldAddLabel = (link.page.length > 0) && (link.page.toLowerCase() !== page.toLowerCase())
            let newPageWithMaybeLabel
            if (shouldAddLabel) {
              newPageWithMaybeLabel = `[[${page}|${link.page}]]`
            } else {
              newPageWithMaybeLabel = link.page.length > 0 ? `[[${link.page}]]` : `[[${page}]]`
            }
            editor.replaceRange(newPageWithMaybeLabel, link.innerPosition.from, link.innerPosition.to)
          }
        }
      },
      removeLink: () => {
        const link = stateStore.link
        if (!link) {
          console.warn("Attempting to remove link with no link in stateStore")
          return
        }
        const innerPosition = link.innerPosition
        const outerPosition = link.outerPosition
        if (!innerPosition || !outerPosition) {
          console.warn("Attempting to remove link with no innerPosition or outerPosition")
          return
        }
        const removePageWithPipeIfPresent = () => {
          if (link.pageWithPipePosition) {
            const from = { line: link.pageWithPipePosition.from.line, ch: link.pageWithPipePosition.from.ch - anchorLength }
            const to = { line: link.pageWithPipePosition.to.line, ch: link.pageWithPipePosition.to.ch - anchorLength }
            editor.replaceRange("", from, to)
          }
        }
        const openingMarkupPosition = new LinkElementPosition({ line: outerPosition.from.line, ch: outerPosition.from.ch }, { line: innerPosition.from.line, ch: innerPosition.from.ch })
        const closingMarkupPosition = new LinkElementPosition({ line: outerPosition.to.line, ch: outerPosition.to.ch - (anchorLength * 2) }, { line: innerPosition.to.line, ch: innerPosition.to.ch })
        const removePageAndMarkup = () => {
          editor.replaceRange("", openingMarkupPosition.from, openingMarkupPosition.to)
          editor.replaceRange("", closingMarkupPosition.from, closingMarkupPosition.to)
          removePageWithPipeIfPresent()
        }
        editor.operation(removePageAndMarkup)
      }
    }

    // TODO: Import js/elementLocation ?
    wmf.elementLocation = {}
    wmf.elementLocation.getElementRect = element => {
      const rect = element.getBoundingClientRect()
      return {
        Y: rect.top,
        X: rect.left,
        Width: rect.width,
        Height: rect.height
      }
    }
    
    window.wmf = wmf

  </script>
</body>

<html>
