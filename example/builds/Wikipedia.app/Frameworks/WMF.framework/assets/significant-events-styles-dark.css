
div#significant-changes-container{
    background-color:#43464A;
    box-shadow: none;
}
div#significant-changes-inner-container{
    background-color:#2E3136;
    box-shadow: none;
}
div#significant-changes-skeleton{
    background: #27292D;
    background: linear-gradient(to right, #27292D 0%, #101418 20%, #27292D 80%);
}
h4#significant-changes-header{
    color:#C8CCD1;
}
ul#significant-changes-list li:before{
    background-color:#00af89;
}
ul#significant-changes-list li:after{
    background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' aria-hidden='true' viewBox='0 0 32 32' focusable='false'%3E%3Ccircle stroke='none' fill='%2300af89' cx='16' cy='16' r='10'%3E%3C/circle%3E%3C/svg%3E");
}
p.significant-changes-timestamp{
    color:#00af89
}
p.significant-changes-userInfo{
    color:#C8CCD1
}
p.significant-changes-description{
    color:#F8F9FA
}
hr#significant-changes-hr{
    color:#C8CCD1
}
div#new-changes-container {
    padding: .3em 0em .5em 0em;
}
p#significant-changes-read-more{
    color:#36c;
}
div#significant-changes-top-skeleton{
    background: #27292D;
    background: linear-gradient(to right, #27292D 0%, #101418 20%, #27292D 80%);
}
div#significant-changes-top-inner-container-new-changes {
    background-color: #00af89;
}
div#significant-changes-top-inner-container-last-updated {
    background-color: #27292D;
}
span#significant-changes-top-dot {
    background-color: #fff;
}
span#significant-changes-top-text-new-changes {
    color: #fff;
}
span#significant-changes-top-text-last-updated {
    color: #C8CCD1;
}
span#significant-changes-top-timestamp {
    color: #C8CCD1;
}
