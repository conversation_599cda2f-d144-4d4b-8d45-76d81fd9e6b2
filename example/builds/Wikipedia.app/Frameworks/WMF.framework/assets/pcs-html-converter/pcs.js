!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.pcs=t():e.pcs=t()}(this,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(r,a,function(t){return e[t]}.bind(null,a));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=40)}([function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={LABEL:"aria-label",LABELED_BY:"aria-labelledby"},a=function(e){switch(e){case"'":return"&#039;";case'"':return"&quot;";case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";default:return""}};t.b={escape:function(e){return e&&e.replace(/['"<>&]/g,a)}}},function(e,t,n){"use strict";n.r(t);var r="undefined"!=typeof window&&window.CustomEvent||function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{bubbles:!1,cancelable:!1,detail:void 0},n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n};t.default={matchesSelector:function(e,t){return e.matches?e.matches(t):e.matchesSelector?e.matchesSelector(t):!!e.webkitMatchesSelector&&e.webkitMatchesSelector(t)},querySelectorAll:function(e,t){return Array.prototype.slice.call(e.querySelectorAll(t))},CustomEvent:r}},function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t){function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}e.exports=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}},function(e,t,n){"use strict";n.r(t);var r=n(1),a=function(e,t){var n;for(n=e.parentElement;n&&!r.default.matchesSelector(n,t);n=n.parentElement);return n};t.default={findClosestAncestor:a,isNestedInTable:function(e){return Boolean(a(e,"table"))},closestInlineStyle:function(e,t,n){for(var r=e;r;r=r.parentElement){var a=void 0;try{a=r.style[t]}catch(e){continue}if(a){if(void 0===n)return r;if(n===a)return r}}},isVisible:function(e){return Boolean(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},copyAttributesToDataAttributes:function(e,t,n){n.filter((function(t){return e.hasAttribute(t)})).forEach((function(n){return t.setAttribute("data-".concat(n),e.getAttribute(n))}))},copyDataAttributesToAttributes:function(e,t,n){n.filter((function(t){return e.hasAttribute("data-".concat(t))})).forEach((function(n){return t.setAttribute(n,e.getAttribute("data-".concat(n)))}))}}},function(e,t,n){"use strict";n(30);var r=n(20).default,a=n(4).default,i=n(1).default,o={PLACEHOLDER_CLASS:"pcs-lazy-load-placeholder",PLACEHOLDER_PENDING_CLASS:"pcs-lazy-load-placeholder-pending",PLACEHOLDER_LOADING_CLASS:"pcs-lazy-load-placeholder-loading",PLACEHOLDER_ERROR_CLASS:"pcs-lazy-load-placeholder-error",IMAGE_LOADING_CLASS:"pcs-lazy-load-image-loading",IMAGE_LOADED_CLASS:"pcs-lazy-load-image-loaded"},u=["class","style","src","srcset","width","height","alt","usemap","data-file-width","data-file-height","data-image-gallery"],c={px:50,ex:10,em:5},d=function(e,t){var n=e.createElement("span");t.hasAttribute("class")&&n.setAttribute("class",t.getAttribute("class")||""),n.classList.add("pcs-lazy-load-placeholder"),n.classList.add("pcs-lazy-load-placeholder-pending");var i=r.from(t);i.width&&n.style.setProperty("width","".concat(i.width)),a.copyAttributesToDataAttributes(t,n,u);var o=e.createElement("span");if(i.width&&i.height){var c=i.heightValue/i.widthValue;o.style.setProperty("padding-top","".concat(100*c,"%"))}return n.appendChild(o),t.parentNode&&t.parentNode.replaceChild(n,t),n},s=function(e){var t=r.from(e);if(!t.width||!t.height)return!0;var n=c[t.widthUnit]||1/0,a=c[t.heightUnit]||1/0;return t.widthValue>=n&&t.heightValue>=a};t.a={CLASSES:o,PLACEHOLDER_CLASS:"pcs-lazy-load-placeholder",isLazyLoadable:s,queryLazyLoadableImages:function(e){return i.querySelectorAll(e,"img").filter((function(e){return s(e)}))},convertImagesToPlaceholders:function(e,t){return t.map((function(t){return d(e,t)}))},convertImageToPlaceholder:d,loadPlaceholder:function(e,t){t.classList.add("pcs-lazy-load-placeholder-loading"),t.classList.remove("pcs-lazy-load-placeholder-pending");var n=e.createElement("img"),r=function(e){n.setAttribute("src",n.getAttribute("src")||""),e.stopPropagation(),e.preventDefault()};return n.addEventListener("load",(function(){t.removeEventListener("click",r),t.parentNode&&t.parentNode.replaceChild(n,t),n.classList.add("pcs-lazy-load-image-loaded"),n.classList.remove("pcs-lazy-load-image-loading")}),{once:!0}),n.addEventListener("error",(function(){t.classList.add("pcs-lazy-load-placeholder-error"),t.classList.remove("pcs-lazy-load-placeholder-loading"),t.addEventListener("click",r)}),{once:!0}),a.copyDataAttributesToAttributes(t,n,u),n.classList.add("pcs-lazy-load-image-loading"),n}}},function(e,t,n){"use strict";var r=n(0),a=n(1).default,i=function(e){return!!e&&!("SECTION"!==e.tagName||!e.getAttribute("data-mw-section-id"))},o={BASE:"pcs-section-control",SHOW:"pcs-section-control-show",HIDE:"pcs-section-control-hide"},u={HIDE:"pcs-section-hidden"},c={HIDEABLE:"pcs-section-hideable-header"},d={CONTENT:"pcs-section-content-",CONTROL:"pcs-section-control-"},s="pcs-section-aria-collapse",l="pcs-section-aria-expand",f=function(e){return d.CONTROL+e},p=function(e){return d.CONTENT+e},h=function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=f(t),i=p(t),c=e.getElementById(a),d=e.getElementById(i);if(c&&d){n?(c.classList.remove(o.HIDE),c.classList.add(o.SHOW),d.classList.add(u.HIDE),c.setAttribute(r.a.LABELED_BY,l)):(c.classList.remove(o.SHOW),c.classList.add(o.HIDE),d.classList.remove(u.HIDE),c.setAttribute(r.a.LABELED_BY,s));var h=c.parentElement;h&&h.setAttribute("onclick","pcs.c1.Sections.setHidden('".concat(t,"', ").concat(!n,");"))}};t.a={createFoldHR:function(e,t){if(t.parentElement){var n=e.createElement("hr");n.classList.add("pcs-fold-hr"),t.parentElement.insertBefore(n,t)}},expandCollapsedSectionIfItContainsElement:function(e,t){var n=function(e){for(var t=e;t=t.parentElement;)if("SECTION"===t.tagName&&t.parentElement&&"pcs"===t.parentElement.id){var n=t.getAttribute("data-mw-section-id");if(n)return n}}(t);n&&h(e,n,!1)},getSectionIDOfElement:function(e){var t=function(e){for(var t=e;t;){if(i(t))return t;t=t.parentElement}return null}(e);return t&&t.getAttribute("data-mw-section-id")},getLeadParagraphText:function(e){var t=e.querySelector("#content-block-0>p");return t&&t.innerText||""},getSectionOffsets:function(e){return{sections:a.querySelectorAll(e,"section").reduce((function(e,t){var n=t.getAttribute("data-mw-section-id"),r=t&&t.firstElementChild&&t.firstElementChild.querySelector(".pcs-edit-section-title");return n&&parseInt(n)>=1&&e.push({heading:r&&r.innerHTML,id:parseInt(n),yOffset:t.offsetTop}),e}),[])}},prepareForHiding:function(e,t,n,a,i,d,h){var m=function(e,t){var n=e.createElement("span");return n.id=f(t),n.classList.add(o.BASE),n.classList.add(o.SHOW),n}(e,t);if(null===e.getElementById(l)){var g=e.createElement("span");g.setAttribute("id",l),g.setAttribute(r.a.LABEL,d),m.appendChild(g)}if(null===e.getElementById(s)){var v=e.createElement("span");v.setAttribute("id",s),v.setAttribute(r.a.LABEL,h),m.appendChild(v)}m.setAttribute("role","button"),m.setAttribute(r.a.LABELED_BY,l),a&&m&&(a.appendChild(m),a.classList.add(c.HIDEABLE),a.setAttribute("onclick","pcs.c1.Sections.setHidden('".concat(t,"', false);")));for(var b=n.firstElementChild,E=e.createElement("div");b;){var y=b;b=b.nextElementSibling,y!==i&&(n.removeChild(y),E.appendChild(y))}E.id=p(t),E.classList.add(u.HIDE),n.appendChild(E)},setHidden:h,getControlIdForSectionId:f,isMediaWikiSectionElement:i}},function(e,t,n){"use strict";var r={ELEMENT_NODE:1,TEXT_NODE:3};t.a={isNodeTypeElementOrText:function(e){return e.nodeType===r.ELEMENT_NODE||e.nodeType===r.TEXT_NODE},getBoundingClientRectAsPlainObject:function(e){var t=e.getBoundingClientRect();return{top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.width,height:t.height,x:t.x,y:t.y}},NODE_TYPE:r}},function(e,t,n){"use strict";n(28);var r=n(4),a=n(7),i=n(1),o=n(6),u=n(0),c=a.a.NODE_TYPE,d={ICON:"pcs-collapse-table-icon",CONTAINER:"pcs-collapse-table-container",CONTENT:"pcs-collapse-table-content",COLLAPSED_CONTAINER:"pcs-collapse-table-collapsed-container",COLLAPSED:"pcs-collapse-table-collapsed",COLLAPSED_BOTTOM:"pcs-collapse-table-collapsed-bottom",COLLAPSE_TEXT:"pcs-collapse-table-collapse-text",EXPANDED:"pcs-collapse-table-expanded",TABLE_INFOBOX:"pcs-table-infobox",TABLE_OTHER:"pcs-table-other",TABLE:"pcs-collapse-table"},s="pcs-collapse-table-aria-collapse",l="pcs-collapse-table-aria-expand",f=function(e){return i.default.querySelectorAll(e,"a").length<3},p=function(e){return e&&e.replace(/[\s0-9]/g,"").length>0},h=function(e){var t=e.match(/\w+/);if(t)return t[0]},m=function(e,t){var n=h(t),r=h(e.textContent);return!(!n||!r)&&n.toLowerCase()===r.toLowerCase()},g=function(e){return e.trim().replace(/\s/g," ")},v=function(e,t){t.parentNode.replaceChild(e.createTextNode(" "),t)},b=function(e,t,n){if(!f(t))return null;var r=e.createDocumentFragment();r.appendChild(t.cloneNode(!0));var o=r.querySelector("th");i.default.querySelectorAll(o,".geo, .coordinates, sup.mw-ref, ol, ul, style, script").forEach((function(e){return e.remove()}));for(var u,d=o.lastChild;d;)n&&a.a.isNodeTypeElementOrText(d)&&m(d,n)?d.previousSibling?(d=d.previousSibling).nextSibling.remove():(d.remove(),d=void 0):(u=d).nodeType===c.ELEMENT_NODE&&"BR"===u.tagName?(v(e,d),d=d.previousSibling):d=d.previousSibling;var s=o.textContent;return p(s)?g(s):null},E=function(e,t,n){for(var r=[],a=e.createTreeWalker(t),i=a.nextNode();i;)if("TH"===i.tagName){var o=b(e,i,n);if(o&&-1===r.indexOf(o)&&(r.push(o),2===r.length))break;i=a.nextNode()}else i=a.nextNode();return r},y=function(e,t,n){var r=e.children[0],a=e.children[1],i=e.children[2],o=r.querySelector(".pcs-collapse-table-aria"),c="none"!==a.style.display;return c?(a.style.display="none",r.classList.remove(d.COLLAPSED),r.classList.remove(d.ICON),r.classList.add(d.EXPANDED),o&&o.setAttribute(u.a.LABELED_BY,l),i.style.display="none",t===i&&n&&n(e)):(a.style.display="block",r.classList.remove(d.EXPANDED),r.classList.add(d.COLLAPSED),r.classList.add(d.ICON),o&&o.setAttribute(u.a.LABELED_BY,s),i.style.display="block"),c},A=function(e){var t=this.parentNode;return y(t,this,e)},T=function(e){var t,n=["navbox","vertical-navbox","navbox-inner","metadata","mbox-small"].some((function(t){return e.classList.contains(t)}));try{t="none"===e.style.display}catch(e){t=!0}return!t&&!n},I=function(e){return e.classList.contains("infobox")||e.classList.contains("infobox_v3")},L=function(e,t){var n=e.createElement("div");return n.classList.add(d.COLLAPSED_CONTAINER),n.classList.add(d.EXPANDED),n.appendChild(t),n},S=function(e,t){var n=e.createElement("div");return n.classList.add(d.COLLAPSED_BOTTOM),n.classList.add(d.ICON),n.textContent=t||"",n},w=function(e,t,n,r,a,i){var o=e.createDocumentFragment(),c=e.createElement("strong");c.textContent=t,c.classList.add(n),o.appendChild(c);var f=e.createElement("span");f.classList.add(d.COLLAPSE_TEXT),r.length>0&&f.appendChild(e.createTextNode(": ".concat(r[0]))),r.length>1&&f.appendChild(e.createTextNode(", ".concat(r[1]))),r.length>0&&f.appendChild(e.createTextNode(" ...")),o.appendChild(f);var p=e.createElement("span");if(p.classList.add("pcs-collapse-table-aria"),p.setAttribute(u.a.LABELED_BY,l),p.setAttribute("role","button"),p.setAttribute("display","none"),p.appendChild(e.createTextNode("")),null===e.getElementById(l)){var h=e.createElement("span");h.setAttribute("id",l),h.setAttribute(u.a.LABEL,i),p.appendChild(h)}if(null===e.getElementById(s)){var m=e.createElement("span");m.setAttribute("id",s),m.setAttribute(u.a.LABEL,a),p.appendChild(m)}return o.appendChild(p),o},C=function(e,t,n,r,a,i,u,c,s){var l=w(t,r,a,i,c,s),f=t.createElement("div");f.className=d.CONTAINER,function(e,t){if(e&&t){var n=e,r=e.parentNode;if(r){for(var a=!1;r;){if(o.a.isMediaWikiSectionElement(r)){a=!0;break}n=r,r=r.parentNode}a||(n=e,r=e.parentNode),r.insertBefore(t,n),r.removeChild(n)}}}(e,f),e.classList.add(d.TABLE);var p=L(t,l);p.style.display="block";var h=S(t,u);h.style.display="none",f.appendChild(p);var m=t.createElement("div");m.className=d.CONTENT,m.appendChild(e),f.appendChild(m),f.appendChild(h),m.style.display="none"},k=function(e,t,n,a,i){for(var o=e.querySelectorAll("table, .infobox_v3"),u=0;u<o.length;++u){var c=o[u];if(!r.default.findClosestAncestor(c,".".concat(d.CONTAINER))&&T(c)){var s=I(c),l=E(e,c,t);if(l.length||s)C(c,e,0,s?n:a,s?d.TABLE_INFOBOX:d.TABLE_OTHER,l,i)}}},_=function(e){i.default.querySelectorAll(e,".".concat(d.CONTAINER)).forEach((function(e){y(e)}))},N=function(e,t,n,r){var a=function(t){return e.dispatchEvent(new i.default.CustomEvent("section-toggled",{collapsed:t}))};i.default.querySelectorAll(t,".".concat(d.COLLAPSED_CONTAINER)).forEach((function(e){e.onclick=function(){var t=A.bind(e)();a(t)}})),i.default.querySelectorAll(t,".".concat(d.COLLAPSED_BOTTOM)).forEach((function(e){e.onclick=function(){var t=A.bind(e,r)();a(t)}})),n||_(t)},O=function(e,t,n,r,a,i,o,u,c){r||(k(t,n,i,o,u),N(e,t,a,c))};t.a={CLASS:d,SECTION_TOGGLED_EVENT_TYPE:"section-toggled",toggleCollapsedForAll:_,toggleCollapseClickCallback:A,collapseTables:function(e,t,n,r,a,i,o,u){O(e,t,n,r,!0,a,i,o,u)},getTableHeaderTextArray:E,adjustTables:O,prepareTables:k,prepareTable:C,setupEventHandling:N,expandCollapsedTableIfItContainsElement:function(e){if(e){var t='[class*="'.concat(d.CONTAINER,'"]'),n=r.default.findClosestAncestor(e,t);if(n){var a=n.firstElementChild;a&&a.classList.contains(d.EXPANDED)&&a.click()}}},test:{extractEligibleHeaderText:b,firstWordFromString:h,shouldTableBeCollapsed:T,isHeaderEligible:f,isHeaderTextEligible:p,isInfobox:I,newCollapsedHeaderDiv:L,newCollapsedFooterDiv:S,newCaptionFragment:w,isNodeTextContentSimilarToPageTitle:m,stringWithNormalizedWhitespace:g,replaceNodeWithBreakingSpaceTextNode:v,getTableHeaderTextArray:E}}},function(e,t,n){"use strict";var r=n(2),a=n.n(r),i=n(3),o=n.n(i),u=(n(26),n(11)),c=n(0),d={lastEdited:"lastEdited",pageIssues:"pageIssues",disambiguation:"disambiguation",coordinate:"coordinate",talkPage:"talkPage"},s=function(){function e(t,n,r,i){a()(this,e),this.title=t,this.subtitle=n,this.itemType=r,this.clickHandler=i,this.payload=[]}return o()(e,[{key:"iconClass",value:function(){switch(this.itemType){case d.lastEdited:return"pcs-footer-menu-icon-last-edited";case d.talkPage:return"pcs-footer-menu-icon-talk-page";case d.pageIssues:return"pcs-footer-menu-icon-page-issues";case d.disambiguation:return"pcs-footer-menu-icon-disambiguation";case d.coordinate:return"pcs-footer-menu-icon-coordinate";default:return""}}},{key:"payloadExtractor",value:function(){switch(this.itemType){case d.pageIssues:return u.a.collectPageIssues;case d.disambiguation:return u.a.collectHatnotes;default:return}}}]),e}();t.a={MenuItemType:d,setHeading:function(e,t,n){var r=n.getElementById(t);r.textContent=e,r.title=c.b.escape(e)},maybeAddItem:function(e,t,n,r,a,i){if(""!==e){var o=new s(e,t,n,a),u=o.payloadExtractor();u&&(o.payload=u(i),0===o.payload.length)||function(e,t,n){n.getElementById(t).appendChild(function(e,t){var n=t.createElement("div");n.className="pcs-footer-menu-item",n.role="menuitem";var r=t.createElement("a");if(r.addEventListener("click",(function(){e.clickHandler(e.payload)})),n.appendChild(r),e.title){var a=t.createElement("div");a.className="pcs-footer-menu-item-title",a.textContent=e.title,r.title=e.title,r.appendChild(a)}if(e.subtitle){var i=t.createElement("div");i.className="pcs-footer-menu-item-subtitle",i.textContent=e.subtitle,r.appendChild(i)}var o=e.iconClass();return o&&n.classList.add(o),t.createDocumentFragment().appendChild(n)}(e,n))}(o,r,i)}}}},function(e,t,n){"use strict";n(29);var r=n(0),a={SECTION_HEADER:"pcs-edit-section-header",TITLE:"pcs-edit-section-title",LINK_CONTAINER:"pcs-edit-section-link-container",LINK:"pcs-edit-section-link",PROTECTION:{UNPROTECTED:"",PROTECTED:"page-protected",FORBIDDEN:"no-editing"}},i={TITLE_DESCRIPTION:"pcs-edit-section-title-description",ADD_TITLE_DESCRIPTION:"pcs-edit-section-add-title-description",DIVIDER:"pcs-edit-section-divider",PRONUNCIATION:"pcs-edit-section-title-pronunciation",ARIA_EDIT_PROTECTED:"pcs-edit-section-aria-protected",ARIA_EDIT_NORMAL:"pcs-edit-section-aria-normal"},o={SECTION_INDEX:"data-id",ACTION:"data-action",PRONUNCIATION_URL:"data-pronunciation-url",DESCRIPTION_SOURCE:"data-description-source",WIKIDATA_ENTITY_ID:"data-wikdata-entity-id"},u=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",u=e.createElement("a");return u.href=n,u.setAttribute(o.SECTION_INDEX,t),u.setAttribute(o.ACTION,"edit_section"),u.setAttribute(r.a.LABELED_BY,i.ARIA_EDIT_NORMAL),u.classList.add(a.LINK),u},c=function(e,t){var n=e.createElement("div");return n.classList.add(a.SECTION_HEADER),n.classList.add("v2"),n},d=function(e,t){t.classList.add(a.TITLE),e.appendChild(t)},s=function(e,t,n,r){var a=c(e),i=e.createElement("h".concat(n));return i.innerHTML=r||"",i.setAttribute(o.SECTION_INDEX,t),d(a,i),a};t.a={appendEditSectionHeader:d,CLASS:a,IDS:i,DATA_ATTRIBUTE:o,setEditButtons:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e.documentElement.classList;t?r.remove(a.PROTECTION.FORBIDDEN):r.add(a.PROTECTION.FORBIDDEN),n?r.add(a.PROTECTION.PROTECTED):r.remove(a.PROTECTION.PROTECTED)},setARIAEditButtons:function(e){e.documentElement.classList.contains(a.PROTECTION.PROTECTED)&&Array.from(e.getElementsByClassName(a.LINK)).forEach((function(e){return e.setAttribute(r.a.LABELED_BY,i.ARIA_EDIT_PROTECTED)}))},newEditSectionHeader:s,newEditSectionButton:function(e,t,n,o,c){var d=e.createElement("span");if(d.classList.add(a.LINK_CONTAINER),null===e.getElementById(i.ARIA_EDIT_NORMAL)&&o){var s=e.createElement("span");s.setAttribute("id",i.ARIA_EDIT_NORMAL),s.setAttribute(r.a.LABEL,o),d.appendChild(s)}if(null===e.getElementById(i.ARIA_EDIT_PROTECTED)&&c){var l=e.createElement("span");l.setAttribute("id",i.ARIA_EDIT_PROTECTED),l.setAttribute(r.a.LABEL,c),d.appendChild(l)}var f=n;return f||(f=u(e,t)),d.appendChild(f),d},newEditSectionWrapper:c,newEditSectionLink:u,newPageHeader:function(e,t,n,r,a,u,c,d){var l=e.createDocumentFragment(),f=s(e,0,1,t);if(d){var p=e.createElement("a");p.setAttribute(o.ACTION,"title_pronunciation"),p.setAttribute(o.PRONUNCIATION_URL,d),p.id=i.PRONUNCIATION,f.querySelector("h1").appendChild(p)}l.appendChild(f);var h=function(e,t,n,r,a,u){if(void 0!==t&&t.length>0){var c=e.createElement("p");return c.setAttribute(o.DESCRIPTION_SOURCE,n),c.setAttribute(o.WIKIDATA_ENTITY_ID,r),c.id=i.TITLE_DESCRIPTION,c.innerHTML=t,c}if(u){var d=e.createElement("a");d.href="#",d.setAttribute(o.ACTION,"add_title_description");var s=e.createElement("p");return s.id=i.ADD_TITLE_DESCRIPTION,s.innerHTML=a,d.appendChild(s),d}return null}(e,n,r,a,u,c);h&&l.appendChild(h);var m=e.createElement("hr");return m.id=i.DIVIDER,l.appendChild(m),l}}},function(e,t,n){"use strict";var r=n(1),a=function(e){return e?r.default.querySelectorAll(e,".mbox-text-span").map((function(e){return r.default.querySelectorAll(e,".hide-when-compact, .collapsed").forEach((function(e){return e.remove()})),e})):[]},i=function(e){var t=e.closest("section[data-mw-section-id]"),n=t&&t.querySelector("h1,h2,h3,h4,h5,h6");return{id:t&&parseInt(t.getAttribute("data-mw-section-id"),10),title:n&&n.innerHTML.trim(),anchor:n&&n.getAttribute("id")}};t.a={collectHatnotes:function(e){return e?r.default.querySelectorAll(e,"div.hatnote").map((function(e){var t=r.default.querySelectorAll(e,'div.hatnote a[href]:not([href=""]):not([redlink="1"])').map((function(e){return e.href}));return{html:e.innerHTML.trim(),links:t,section:i(e)}})):[]},collectPageIssues:function(e){return a(e).map((function(e){return{html:e.innerHTML.trim(),section:i(e)}}))},test:{collectPageIssueElements:a}}},function(e,t,n){"use strict";var r={ANDROID:"".concat("pcs-platform-","android"),IOS:"".concat("pcs-platform-","ios")};t.a={CLASS:r,CLASS_PREFIX:"pcs-platform-",classify:function(e){var t=e.document.documentElement;(function(e){return/android/i.test(e.navigator.userAgent)})(e)&&t.classList.add(r.ANDROID),function(e){return/ipad|iphone|ipod/i.test(e.navigator.userAgent)}(e)&&t.classList.add(r.IOS)},setPlatform:function(e,t){e&&e.documentElement&&e.documentElement.classList.add(t)},setVersion:function(e,t){if(e&&e.documentElement)for(var n=t||1,r=1;r<=2&&(e.documentElement.classList.add("pcs-v"+r),r!==n);r++);}}},function(e,t,n){"use strict";n(32);var r={DEFAULT:"".concat("pcs-theme-","default"),DARK:"".concat("pcs-theme-","dark"),SEPIA:"".concat("pcs-theme-","sepia"),BLACK:"".concat("pcs-theme-","black")},a=function(e,t){if(e)for(var n in e.classList.add(t),r)Object.prototype.hasOwnProperty.call(r,n)&&r[n]!==t&&e.classList.remove(r[n])};t.a={THEME:r,CLASS_PREFIX:"pcs-theme-",setTheme:function(e,t){var n=e.body;a(n,t);var r=e.getElementById("pcs");a(r,t)}}},function(e,t,n){"use strict";var r=n(2),a=n.n(r),i=n(4),o=n(7),u=n(1),c=function(e,t,n){var r=decodeURIComponent(e),a=decodeURIComponent(t);if(void 0!==n){var i=decodeURIComponent(n),o="./".concat(i);return 0===r.indexOf(o)&&e.indexOf(a)===o.length}return r.indexOf(a)>-1},d=function(e,t){return c(e,"#cite_note-",t)},s=function(e){return Boolean(e)&&e.nodeType===Node.TEXT_NODE&&Boolean(e.textContent.match(/^\s+$/))},l=function(e){var t=e.querySelector("a");return t&&d(t.hash)},f=function(e,t){var n=t.querySelector("A").getAttribute("href").split("#")[1];return e.getElementById(n)||e.getElementById(decodeURIComponent(n))},p=function(e,t){var n=f(e,t);if(!n)return"";var r=n.querySelector("span.mw-reference-text");return r?r.innerHTML.trim():""},h=function(e){return u.default.matchesSelector(e,".reference, .mw-ref")?e:i.default.findClosestAncestor(e,".reference, .mw-ref")},m=function e(t,n,r,i,o){a()(this,e),this.id=t,this.rect=n,this.text=r,this.html=i,this.href=o},g=function e(t,n){a()(this,e),this.href=t,this.text=n},v=function e(t,n){a()(this,e),this.selectedIndex=t,this.referencesGroup=n},b=function(e,t){var n=e;do{n=t(n)}while(s(n));return n},E=function(e,t,n){for(var r=e;(r=b(r,t))&&r.nodeType===Node.ELEMENT_NODE&&l(r);)n(r)},y=function(e){return e.previousSibling},A=function(e){return e.nextSibling},T=function(e){var t=[e];return E(e,y,(function(e){return t.unshift(e)})),E(e,A,(function(e){return t.push(e)})),t},I=function(e,t){var n=T(t),r=n.indexOf(t),a=n.map((function(t){return function(e,t){return new m(h(t).id,o.a.getBoundingClientRectAsPlainObject(t),t.textContent,p(e,t),t.querySelector("A").getAttribute("href"))}(e,t)}));return new v(r,a)};t.a={collectNearbyReferences:function(e,t){var n=t.parentElement;return I(e,n)},collectNearbyReferencesAsText:function(e,t){var n=t.parentElement,r=T(n),a=r.indexOf(n),i=r.map((function(e){return function(e,t){return new g(t.querySelector("A").getAttribute("href"),t.textContent)}(0,e)}));return new v(a,i)},collectReferencesForBackLink:function(e,t,n){var r=function(e){var t=e.getAttribute("pcs-back-links");return t?JSON.parse(t):[]}(t);if(!r||0===r.length)return{};for(var a,i=n.split("#pcs-ref-back-link-")[1],o=[],u=r[0],c=0;c<r.length;c++){var d=r[c].split("#")[1],s=e.getElementById(d);s&&(a||(a=s.textContent.trim()),o.push({id:d}))}return{referenceId:i,referenceText:a,backLinks:o,href:u}},isBackLink:function(e,t){return c(e,"#pcs-ref-back-link-",t)},isCitation:d,CLASS:{BACK_LINK_ANCHOR:"pcs-ref-back-link",BACK_LINK_CONTAINER:"pcs-ref-backlink-container",BODY:"pcs-ref-body",BODY_HEADER:"pcs-ref-body-header",BODY_CONTENT:"pcs-ref-body-content",REF:"pcs-ref"},BACK_LINK_FRAGMENT_PREFIX:"#pcs-ref-back-link-",BACK_LINK_ATTRIBUTE:"pcs-back-links",test:{adjacentNonWhitespaceNode:b,closestReferenceClassElement:h,collectAdjacentReferenceNodes:E,collectNearbyReferenceNodes:T,collectRefText:p,getRefTextContainer:f,hasCitationLink:l,isWhitespaceTextNode:s,nextSiblingGetter:A,prevSiblingGetter:y}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var r=n(2),a=n.n(r),i=n(3),o=n.n(i),u=function(){function e(t,n,r){a()(this,e),this._window=t,this._period=n,this._function=r,this._context=void 0,this._arguments=void 0,this._result=void 0,this._timeout=0,this._timestamp=0}return o()(e,null,[{key:"wrap",value:function(t,n,r){var a=new e(t,n,r),i=function(){return a.queue(this,arguments)};return i.result=function(){return a.result},i.pending=function(){return a.pending()},i.delay=function(){return a.delay()},i.cancel=function(){return a.cancel()},i.reset=function(){return a.reset()},i}}]),o()(e,[{key:"queue",value:function(e,t){var n=this;return this._context=e,this._arguments=t,this.pending()||(this._timeout=this._window.setTimeout((function(){n._timeout=0,n._timestamp=Date.now(),n._result=n._function.apply(n._context,n._arguments)}),this.delay())),this.result}},{key:"pending",value:function(){return Boolean(this._timeout)}},{key:"delay",value:function(){return this._timestamp?Math.max(0,this._period-(Date.now()-this._timestamp)):0}},{key:"cancel",value:function(){this._timeout&&this._window.clearTimeout(this._timeout),this._timeout=0}},{key:"reset",value:function(){this.cancel(),this._result=void 0,this._timestamp=0}},{key:"result",get:function(){return this._result}}]),e}()},function(e,t,n){"use strict";n(24);t.a={containerFragment:function(e){var t=e.createDocumentFragment(),n=e.createElement("section");n.id="pcs-footer-container-menu",n.className="pcs-footer-section",n.innerHTML="<h2 id='pcs-footer-container-menu-heading'></h2>\n   <div id='pcs-footer-container-menu-items'></div>",t.appendChild(n);var r=e.createElement("section");r.id="pcs-footer-container-readmore",r.className="pcs-footer-section",r.style.display="none",r.innerHTML="<h2 id='pcs-footer-container-readmore-heading'></h2>\n   <div id='pcs-footer-container-readmore-pages'></div>",t.appendChild(r);var a=e.createElement("section");return a.id="pcs-footer-container-legal",t.appendChild(a),t},isContainerAttached:function(e){return Boolean(e.querySelector("#pcs-footer-container"))}}},function(e,t,n){"use strict";t.a={setPercentage:function(e,t){if(t){var n=(100*(Number(t.slice(0,-1))/100*.95)).toString()+"%";e.style["font-size"]=n}}}},function(e,t,n){"use strict";t.a={setMargins:function(e,t){void 0!==t.top&&(e.style.marginTop=t.top),void 0!==t.right&&(e.style.marginRight=t.right),void 0!==t.bottom&&(e.style.marginBottom=t.bottom),void 0!==t.left&&(e.style.marginLeft=t.left)},setPadding:function(e,t){void 0!==t.top&&(e.style.paddingTop=t.top),void 0!==t.right&&(e.style.paddingRight=t.right),void 0!==t.bottom&&(e.style.paddingBottom=t.bottom),void 0!==t.left&&(e.style.paddingLeft=t.left)}}},function(e,t,n){"use strict";n(31);var r="pcs-dim-images",a=function(e,t){e.body.classList[t?"add":"remove"](r)},i=function(e){return e.body.classList.contains(r)};t.a={CLASS:r,dim:function(e,t){return a(e.document,t)},isDim:function(e){return i(e.document)},dimImages:a,areImagesDimmed:i}},function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return d}));var r=n(2),a=n.n(r),i=n(3),o=n.n(i),u=/(-?\d*\.?\d*)(\D+)?/,c=function(){function e(t,n){a()(this,e),this._value=Number(t),this._unit=n||"px"}return o()(e,null,[{key:"fromElement",value:function(t,n){return t.style.getPropertyValue(n)&&e.fromStyle(t.style.getPropertyValue(n))||t.hasAttribute(n)&&new e(t.getAttribute(n))||void 0}},{key:"fromStyle",value:function(t){var n=t.match(u)||[];return new e(n[1],n[2])}}]),o()(e,[{key:"toString",value:function(){return isNaN(this.value)?"":"".concat(this.value).concat(this.unit)}},{key:"value",get:function(){return this._value}},{key:"unit",get:function(){return this._unit}}]),e}(),d=function(){function e(t,n){a()(this,e),this._width=t,this._height=n}return o()(e,null,[{key:"from",value:function(t){return new e(c.fromElement(t,"width"),c.fromElement(t,"height"))}}]),o()(e,[{key:"width",get:function(){return this._width}},{key:"widthValue",get:function(){return this._width&&!isNaN(this._width.value)?this._width.value:NaN}},{key:"widthUnit",get:function(){return this._width&&this._width.unit||"px"}},{key:"height",get:function(){return this._height}},{key:"heightValue",get:function(){return this._height&&!isNaN(this._height.value)?this._height.value:NaN}},{key:"heightUnit",get:function(){return this._height&&this._height.unit||"px"}}]),e}()},function(e,t,n){"use strict";n(25);var r=n(0);t.a={add:function(e,t,n,a,i,o){var u=e.querySelector("#".concat(a));u.innerHTML="<div class='pcs-footer-legal-contents'>\n    <hr class='pcs-footer-legal-divider'>\n    <span class='pcs-footer-legal-license'>\n    ".concat(function(e,t){var n=e.split("$1");return"".concat(r.b.escape(n[0]),'<a class="external text" rel="mw:ExtLink" href="https://creativecommons.org/licenses/by-sa/3.0/">').concat(r.b.escape(t),"</a>").concat(r.b.escape(n[1]))}(t,n),"\n    <br>\n      <div class=\"pcs-footer-browser\">\n        <a class='pcs-footer-browser-link' href='N/A'>\n          ").concat(r.b.escape(i),"\n        </a>\n      </div>\n    </span>\n  </div>"),u.querySelector(".pcs-footer-browser-link").addEventListener("click",(function(){o()}))}}},function(e,t,n){"use strict";var r=n(2),a=n.n(r),i=(n(27),n(0)),o=function(e,t,n){var r=new RegExp("\\s?[".concat(t,"][^").concat(t).concat(n,"]+[").concat(n,"]"),"g"),a=0,i=e,o="";do{o=i,i=i.replace(r,""),a++}while(o!==i&&a<30);return i},u=function(e){var t=e;return t=o(t,"(",")"),t=o(t,"/","/")},c=function e(t,n,r,i,o){a()(this,e),this.title=t,this.displayTitle=n,this.thumbnail=r,this.description=i,this.extract=o},d=/^[a-z]+:/,s=function(e,t,n,r,a,i){var o=[],s=i.getElementById(n),f=i.getElementById(r);l(t,"pcs-footer-container-readmore-heading",i),e.forEach((function(e,t){var n=e.titles.normalized;o.push(n);var r=function(e,t,n){var r=n.createElement("a");r.id=t,r.className="pcs-footer-readmore-page";var a=!n.pcsSetupSettings||n.pcsSetupSettings.loadImages;if(e.thumbnail&&e.thumbnail.source&&a){var i=n.createElement("div");i.style.backgroundImage="url(".concat(e.thumbnail.source.replace(d,""),")"),i.classList.add("pcs-footer-readmore-page-image"),r.appendChild(i)}var o,c,s=n.createElement("div");if(s.classList.add("pcs-footer-readmore-page-container"),r.appendChild(s),r.setAttribute("title",e.title),r.setAttribute("data-pcs-source","read-more"),r.href="./".concat(encodeURI(e.title)),e.displayTitle?o=e.displayTitle:e.title&&(o=e.title),o){var l=n.createElement("div");l.id=t,l.className="pcs-footer-readmore-page-title",l.innerHTML=o.replace(/_/g," "),r.title=e.title.replace(/_/g," "),s.appendChild(l)}if(e.description&&(c=e.description),(!c||c.length<10)&&e.extract&&(c=u(e.extract)),c){var f=n.createElement("div");f.id=t,f.className="pcs-footer-readmore-page-description",f.innerHTML=c,s.appendChild(f)}return n.createDocumentFragment().appendChild(r)}(new c(n,e.titles.display,e.thumbnail,e.description,e.extract),t,i);f.appendChild(r)})),a(o),s.style.display="block"},l=function(e,t,n){var r=n.getElementById(t);r.textContent=e,r.title=i.b.escape(e)};t.a={fetchAndAdd:function(e,t,n,r,a,i,o,u){var c=new XMLHttpRequest;c.open("GET",function(e,t,n){return"".concat(n||"","/page/related/").concat(e)}(e,0,i),!0),c.onload=function(){var e;try{e=JSON.parse(c.responseText).pages}catch(e){}if(e&&e.length){var i;if(e.length>n){var d=Math.floor(Math.random()*Math.floor(e.length-n));i=e.slice(d,d+n)}else i=e;s(i,t,r,a,o,u)}},c.send()},setHeading:l,test:{cleanExtract:u,safelyRemoveEnclosures:o}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var r=n(2),a=n.n(r),i=n(3),o=n.n(i),u=n(8),c=n(4),d=n(5),s=n(1),l=n(15),f=["scroll","resize",u.a.SECTION_TOGGLED_EVENT_TYPE],p=function(){function e(t,n){var r=this;a()(this,e),this._window=t,this._loadDistanceMultiplier=n,this._placeholders=[],this._registered=!1,this._throttledLoadPlaceholders=l.a.wrap(t,100,(function(){return r._loadPlaceholders()}))}return o()(e,[{key:"convertImagesToPlaceholders",value:function(e){var t=d.a.queryLazyLoadableImages(e),n=d.a.convertImagesToPlaceholders(this._window.document,t);this._placeholders=this._placeholders.concat(n),this._register()}},{key:"collectExistingPlaceholders",value:function(e){var t=s.default.querySelectorAll(e,".".concat(d.a.PLACEHOLDER_CLASS));this._placeholders=this._placeholders.concat(t),this._register()}},{key:"loadPlaceholders",value:function(){this._throttledLoadPlaceholders()}},{key:"deregister",value:function(){var e=this;this._registered&&(f.forEach((function(t){return e._window.removeEventListener(t,e._throttledLoadPlaceholders)})),this._throttledLoadPlaceholders.reset(),this._placeholders=[],this._registered=!1)}},{key:"_register",value:function(){var e=this;!this._registered&&this._placeholders.length&&(this._registered=!0,f.forEach((function(t){return e._window.addEventListener(t,e._throttledLoadPlaceholders)})))}},{key:"_loadPlaceholders",value:function(){var e=this;this._placeholders=this._placeholders.filter((function(t){var n=!0;return e._isPlaceholderEligibleToLoad(t)&&(d.a.loadPlaceholder(e._window.document,t),n=!1),n})),0===this._placeholders.length&&this.deregister()}},{key:"_isPlaceholderEligibleToLoad",value:function(e){return c.default.isVisible(e)&&this._isPlaceholderWithinLoadDistance(e)}},{key:"_isPlaceholderWithinLoadDistance",value:function(e){var t=e.getBoundingClientRect(),n=this._window.innerHeight*this._loadDistanceMultiplier;return!(t.top>n||t.bottom<-n)}}]),e}()},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){},function(e,t,n){e.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(r,a,function(t){return e[t]}.bind(null,a));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=4)}([function(e){e.exports={ar:"٠١٢٣٤٥٦٧٨٩",fa:"۰۱۲۳۴۵۶۷۸۹",ml:"൦൧൨൩൪൫൬൭൮൯",kn:"೦೧೨೩೪೫೬೭೮೯",lo:"໐໑໒໓໔໕໖໗໘໙",or:"୦୧୨୩୪୫୬୭୮୯",kh:"០១២៣៤៥៦៧៨៩",nqo:"߀߁߂߃߄߅߆߇߈߉",pa:"੦੧੨੩੪੫੬੭੮੯",gu:"૦૧૨૩૪૫૬૭૮૯",hi:"०१२३४५६७८९",my:"၀၁၂၃၄၅၆၇၈၉",ta:"௦௧௨௩௪௫௬௭௮௯",te:"౦౧౨౩౪౫౬౭౮౯",th:"๐๑๒๓๔๕๖๗๘๙",bo:"༠༡༢༣༤༥༦༧༨༩"}},function(e){e.exports={ak:{one:"n = 0..1"},am:{one:"i = 0 or n = 1"},ar:{zero:"n = 0",one:"n = 1",two:"n = 2",few:"n % 100 = 3..10",many:"n % 100 = 11..99"},ars:{zero:"n = 0",one:"n = 1",two:"n = 2",few:"n % 100 = 3..10",many:"n % 100 = 11..99"},as:{one:"i = 0 or n = 1"},be:{one:"n % 10 = 1 and n % 100 != 11",few:"n % 10 = 2..4 and n % 100 != 12..14",many:"n % 10 = 0 or n % 10 = 5..9 or n % 100 = 11..14"},bh:{one:"n = 0..1"},bn:{one:"i = 0 or n = 1"},br:{one:"n % 10 = 1 and n % 100 != 11,71,91",two:"n % 10 = 2 and n % 100 != 12,72,92",few:"n % 10 = 3..4,9 and n % 100 != 10..19,70..79,90..99",many:"n != 0 and n % 1000000 = 0"},bs:{one:"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11",few:"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14"},cs:{one:"i = 1 and v = 0",few:"i = 2..4 and v = 0",many:"v != 0"},cy:{zero:"n = 0",one:"n = 1",two:"n = 2",few:"n = 3",many:"n = 6"},da:{one:"n = 1 or t != 0 and i = 0,1"},dsb:{one:"v = 0 and i % 100 = 1 or f % 100 = 1",two:"v = 0 and i % 100 = 2 or f % 100 = 2",few:"v = 0 and i % 100 = 3..4 or f % 100 = 3..4"},fa:{one:"i = 0 or n = 1"},ff:{one:"i = 0,1"},fil:{one:"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9"},fr:{one:"i = 0,1"},ga:{one:"n = 1",two:"n = 2",few:"n = 3..6",many:"n = 7..10"},gd:{one:"n = 1,11",two:"n = 2,12",few:"n = 3..10,13..19"},gu:{one:"i = 0 or n = 1"},guw:{one:"n = 0..1"},gv:{one:"v = 0 and i % 10 = 1",two:"v = 0 and i % 10 = 2",few:"v = 0 and i % 100 = 0,20,40,60,80",many:"v != 0"},he:{one:"i = 1 and v = 0",two:"i = 2 and v = 0",many:"v = 0 and n != 0..10 and n % 10 = 0"},hi:{one:"i = 0 or n = 1"},hr:{one:"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11",few:"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14"},hsb:{one:"v = 0 and i % 100 = 1 or f % 100 = 1",two:"v = 0 and i % 100 = 2 or f % 100 = 2",few:"v = 0 and i % 100 = 3..4 or f % 100 = 3..4"},hy:{one:"i = 0,1"},is:{one:"t = 0 and i % 10 = 1 and i % 100 != 11 or t != 0"},iu:{one:"n = 1",two:"n = 2"},iw:{one:"i = 1 and v = 0",two:"i = 2 and v = 0",many:"v = 0 and n != 0..10 and n % 10 = 0"},kab:{one:"i = 0,1"},kn:{one:"i = 0 or n = 1"},kw:{one:"n = 1",two:"n = 2"},lag:{zero:"n = 0",one:"i = 0,1 and n != 0"},ln:{one:"n = 0..1"},lt:{one:"n % 10 = 1 and n % 100 != 11..19",few:"n % 10 = 2..9 and n % 100 != 11..19",many:"f != 0"},lv:{zero:"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19",one:"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1"},mg:{one:"n = 0..1"},mk:{one:"v = 0 and i % 10 = 1 or f % 10 = 1"},mo:{one:"i = 1 and v = 0",few:"v != 0 or n = 0 or n != 1 and n % 100 = 1..19"},mr:{one:"i = 0 or n = 1"},mt:{one:"n = 1",few:"n = 0 or n % 100 = 2..10",many:"n % 100 = 11..19"},naq:{one:"n = 1",two:"n = 2"},nso:{one:"n = 0..1"},pa:{one:"n = 0..1"},pl:{one:"i = 1 and v = 0",few:"v = 0 and i % 10 = 2..4 and i % 100 != 12..14",many:"v = 0 and i != 1 and i % 10 = 0..1 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 12..14"},prg:{zero:"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19",one:"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1"},pt:{one:"i = 0..1"},ro:{one:"i = 1 and v = 0",few:"v != 0 or n = 0 or n != 1 and n % 100 = 1..19"},ru:{one:"v = 0 and i % 10 = 1 and i % 100 != 11",few:"v = 0 and i % 10 = 2..4 and i % 100 != 12..14",many:"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14"},se:{one:"n = 1",two:"n = 2"},sh:{one:"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11",few:"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14"},shi:{one:"i = 0 or n = 1",few:"n = 2..10"},si:{one:"n = 0,1 or i = 0 and f = 1"},sk:{one:"i = 1 and v = 0",few:"i = 2..4 and v = 0",many:"v != 0"},sl:{one:"v = 0 and i % 100 = 1",two:"v = 0 and i % 100 = 2",few:"v = 0 and i % 100 = 3..4 or v != 0"},sma:{one:"n = 1",two:"n = 2"},smi:{one:"n = 1",two:"n = 2"},smj:{one:"n = 1",two:"n = 2"},smn:{one:"n = 1",two:"n = 2"},sms:{one:"n = 1",two:"n = 2"},sr:{one:"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11",few:"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14"},ti:{one:"n = 0..1"},tl:{one:"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9"},tzm:{one:"n = 0..1 or n = 11..99"},uk:{one:"v = 0 and i % 10 = 1 and i % 100 != 11",few:"v = 0 and i % 10 = 2..4 and i % 100 != 12..14",many:"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14"},wa:{one:"n = 0..1"},zu:{one:"i = 0 or n = 1"}}},function(e,t,n){var r,a;void 0===(a="function"==typeof(r=function(){return function(e,t){"use strict";if(!(e=e.split("@")[0].replace(/^\s*/,"").replace(/\s*$/,"")).length)return!0;var n,r,a,i,o=0,u=D(/^\s+/),c=D(/^\d+/),d=O("n"),s=O("i"),l=O("f"),f=O("t"),p=O("v"),h=O("w"),m=O("is"),g=O("is not"),v=O("!="),b=O("="),E=O("mod"),y=O("%"),A=O("not"),T=O("in"),I=O("within"),L=O(".."),S=O(","),w=O("or"),C=O("and");function k(e){return function(){var t,n;for(t=0;t<e.length;t++)if(null!==(n=e[t]()))return n;return null}}function _(e){var t,n,r=o,a=[];for(t=0;t<e.length;t++){if(null===(n=e[t]()))return o=r,null;a.push(n)}return a}function N(e,t){return function(){for(var n=o,r=[],a=t();null!==a;)r.push(a),a=t();return r.length<e?(o=n,null):r}}function O(t){var n=t.length;return function(){var r=null;return e.substr(o,n)===t&&(r=t,o+=n),r}}function D(t){return function(){var n=e.substr(o).match(t);return null===n?null:(o+=n[0].length,n[0])}}function x(){var e=_([u,A]);return null===e?null:e[1]}function P(){var e=_([k([B,c]),N(0,R)]),t=[];return null!==e?(t=t.concat(e[0]),e[1][0]&&(t=t.concat(e[1][0])),t):null}function R(){var e=_([S,P]);return null!==e?e[1]:null}function B(){var e,t,n,r,a=_([c,L,c]);if(null!==a){for(t=[],n=parseInt(a[0],10),r=parseInt(a[2],10),e=n;e<=r;e++)t.push(e);return t}return null}function H(){var e,t=_([a,N(0,M)]);if(t){if(!t[0])return!1;for(e=0;e<t[1].length;e++)if(!t[1][e])return!1;return!0}return null}function M(){var e=_([u,C,u,a]);return null!==e?e[3]:null}function z(){var e=_([u,w,u,H]);return null!==e?(e[3],e[3]):null}if(n=k([function(){var e=d();return null===e?e:e=parseFloat(t,10)},function(){var e=s();return null===e?(parseInt(t,10),e):e=parseInt(t,10)},function(){var e=l();return null===e?e:e=(t+".").split(".")[1]||0},function(){var e=f();return null===e?e:e=(t+".").split(".")[1].replace(/0$/,"")||0},function(){var e=p();return null===e?e:e=(t+".").split(".")[1].length||0},function(){var e=h();return null===e?e:e=(t+".").split(".")[1].replace(/0$/,"").length||0}]),r=k([function(){var e=_([n,u,k([E,y]),u,c]);return null===e?null:(parseInt(e[0],10),e[2],parseInt(e[4],10),parseFloat(e[0])%parseInt(e[4],10))},n]),a=k([function(){var e=_([r,u,k([m]),u,c]);return null!==e?(e[0],parseInt(e[4],10),e[0]===parseInt(e[4],10)):null},function(){var e,t,n=_([r,u,v,u,P]);if(null!==n){for(n[0],n[4],t=n[4],e=0;e<t.length;e++)if(parseInt(t[e],10)===parseInt(n[0],10))return!1;return!0}return null},function(){var e=_([r,u,k([g,v]),u,c]);return null!==e?(e[0],parseInt(e[4],10),e[0]!==parseInt(e[4],10)):null},function(){var e,t,n;if(null!==(e=_([r,N(0,x),u,k([T,b]),u,P]))){for(t=e[5],n=0;n<t.length;n++)if(parseInt(t[n],10)===parseFloat(e[0]))return"not"!==e[1][0];return"not"===e[1][0]}return null},function(){var e,t;return null!==(t=_([r,N(0,x),u,I,u,P]))?(e=t[5],t[0]>=parseInt(e[0],10)&&t[0]<parseInt(e[e.length-1],10)?"not"!==t[1][0]:"not"===t[1][0]):null}]),null===(i=function(){var e,t=_([H,N(0,z)]);if(t){for(e=0;e<t[1].length;e++)if(t[1][e])return!0;return t[0]}return!1}()))throw new Error("Parse error at position "+o.toString()+" for rule: "+e);return o!==e.length&&e.substr(0,o),i}})?r.call(t,n,t,e):r)||(e.exports=a)},function(e){e.exports={ab:["ru"],ace:["id"],aln:["sq"],als:["gsw","de"],an:["es"],anp:["hi"],arn:["es"],arz:["ar"],av:["ru"],ay:["es"],ba:["ru"],bar:["de"],"bat-smg":["sgs","lt"],bcc:["fa"],"be-x-old":["be-tarask"],bh:["bho"],bjn:["id"],bm:["fr"],bpy:["bn"],bqi:["fa"],bug:["id"],"cbk-zam":["es"],ce:["ru"],crh:["crh-latn"],"crh-cyrl":["ru"],csb:["pl"],cv:["ru"],"de-at":["de"],"de-ch":["de"],"de-formal":["de"],dsb:["de"],dtp:["ms"],egl:["it"],eml:["it"],ff:["fr"],fit:["fi"],"fiu-vro":["vro","et"],frc:["fr"],frp:["fr"],frr:["de"],fur:["it"],gag:["tr"],gan:["gan-hant","zh-hant","zh-hans"],"gan-hans":["zh-hans"],"gan-hant":["zh-hant","zh-hans"],gl:["pt"],glk:["fa"],gn:["es"],gsw:["de"],hif:["hif-latn"],hsb:["de"],ht:["fr"],ii:["zh-cn","zh-hans"],inh:["ru"],iu:["ike-cans"],jut:["da"],jv:["id"],kaa:["kk-latn","kk-cyrl"],kbd:["kbd-cyrl"],khw:["ur"],kiu:["tr"],kk:["kk-cyrl"],"kk-arab":["kk-cyrl"],"kk-latn":["kk-cyrl"],"kk-cn":["kk-arab","kk-cyrl"],"kk-kz":["kk-cyrl"],"kk-tr":["kk-latn","kk-cyrl"],kl:["da"],"ko-kp":["ko"],koi:["ru"],krc:["ru"],ks:["ks-arab"],ksh:["de"],ku:["ku-latn"],"ku-arab":["ckb"],kv:["ru"],lad:["es"],lb:["de"],lbe:["ru"],lez:["ru"],li:["nl"],lij:["it"],liv:["et"],lmo:["it"],ln:["fr"],ltg:["lv"],lzz:["tr"],mai:["hi"],"map-bms":["jv","id"],mg:["fr"],mhr:["ru"],min:["id"],mo:["ro"],mrj:["ru"],mwl:["pt"],myv:["ru"],mzn:["fa"],nah:["es"],nap:["it"],nds:["de"],"nds-nl":["nl"],"nl-informal":["nl"],no:["nb"],os:["ru"],pcd:["fr"],pdc:["de"],pdt:["de"],pfl:["de"],pms:["it"],pt:["pt-br"],"pt-br":["pt"],qu:["es"],qug:["qu","es"],rgn:["it"],rmy:["ro"],"roa-rup":["rup"],rue:["uk","ru"],ruq:["ruq-latn","ro"],"ruq-cyrl":["mk"],"ruq-latn":["ro"],sa:["hi"],sah:["ru"],scn:["it"],sg:["fr"],sgs:["lt"],sli:["de"],sr:["sr-ec"],srn:["nl"],stq:["de"],su:["id"],szl:["pl"],tcy:["kn"],tg:["tg-cyrl"],tt:["tt-cyrl","ru"],"tt-cyrl":["ru"],ty:["fr"],udm:["ru"],ug:["ug-arab"],uk:["ru"],vec:["it"],vep:["et"],vls:["nl"],vmf:["de"],vot:["fi"],vro:["et"],wa:["fr"],wo:["fr"],wuu:["zh-hans"],xal:["ru"],xmf:["ka"],yi:["he"],za:["zh-hans"],zea:["nl"],zh:["zh-hans"],"zh-classical":["lzh"],"zh-cn":["zh-hans"],"zh-hant":["zh-hans"],"zh-hk":["zh-hant","zh-hans"],"zh-min-nan":["nan"],"zh-mo":["zh-hk","zh-hant","zh-hans"],"zh-my":["zh-sg","zh-hans"],"zh-sg":["zh-hans"],"zh-tw":["zh-hant","zh-hans"],"zh-yue":["yue"]}},function(e,t,n){"use strict";n.r(t);var r=n(1),a=n(0),i=n(2),o=n.n(i);class u{constructor(e){this.locale=e}convertPlural(e,t){var n=new RegExp("\\d+=","i");if(!t||0===t.length)return"";for(let r=0;r<t.length;r++){let a=t[r];if(n.test(a)){if(parseInt(a.slice(0,a.indexOf("=")),10)===e)return a.slice(a.indexOf("=")+1);t[r]=void 0}}t=t.filter(e=>!!e);let a=r[this.locale];if(!a)return 1===e?t[0]:t[1];let i=this.getPluralForm(e,a);return t[i=Math.min(i,t.length-1)]}getPluralForm(e,t){const n=["zero","one","two","few","many","other"];let r=0;for(let a=0;a<n.length;a++)if(t[n[a]]){if(o()(t[n[a]],e))return r;r++}return r}convertNumber(e,t){let n=this.digitTransformTable(this.locale),r=String(e),a="";if(!n)return e;if(t){if(parseFloat(e,10)===e)return e;let t=[];for(let e in n)t[n[e]]=e;n=t}for(let e=0;e<r.length;e++)n[r[e]]?a+=n[r[e]]:a+=r[e];return t?parseFloat(a,10):a}convertGrammar(e,t){return e}gender(e,t){if(!t||0===t.length)return"";for(;t.length<2;)t.push(t[t.length-1]);return"male"===e?t[0]:"female"===e?t[1]:3===t.length?t[2]:t[0]}digitTransformTable(e){return!!a[e]&&a[e].split("")}}var c={bs:class extends u{convertGrammar(e,t){switch(t){case"instrumental":e="s "+e;break;case"lokativ":e="o "+e}return e}},default:u,dsb:class extends u{convertGrammar(e,t){switch(t){case"instrumental":e="z "+e;break;case"lokatiw":e="wo "+e}return e}},fi:class extends u{convertGrammar(e,t){let n=e.match(/[aou][^äöy]*$/i),r=e;switch(e.match(/wiki$/i)&&(n=!1),e.match(/[bcdfghjklmnpqrstvwxz]$/i)&&(e+="i"),t){case"genitive":e+="n";break;case"elative":e+=n?"sta":"stä";break;case"partitive":e+=n?"a":"ä";break;case"illative":e+=e.slice(-1)+"n";break;case"inessive":e+=n?"ssa":"ssä";break;default:e=r}return e}},ga:class extends u{convertGrammar(e,t){if("ainmlae"===t)switch(e){case"an Domhnach":e="Dé Domhnaigh";break;case"an Luan":e="Dé Luain";break;case"an Mháirt":e="Dé Mháirt";break;case"an Chéadaoin":e="Dé Chéadaoin";break;case"an Déardaoin":e="Déardaoin";break;case"an Aoine":e="Dé hAoine";break;case"an Satharn":e="Dé Sathairn"}return e}},he:class extends u{convertGrammar(e,t){switch(t){case"prefixed":case"תחילית":"ו"===e.slice(0,1)&&"וו"!==e.slice(0,2)&&(e="ו"+e),"ה"===e.slice(0,1)&&(e=e.slice(1)),(e.slice(0,1)<"א"||e.slice(0,1)>"ת")&&(e="־"+e)}return e}},hsb:class extends u{convertGrammar(e,t){switch(t){case"instrumental":e="z "+e;break;case"lokatiw":e="wo "+e}return e}},hu:class extends u{convertGrammar(e,t){switch(t){case"rol":e+="ról";break;case"ba":e+="ba";break;case"k":e+="k"}return e}},hy:class extends u{convertGrammar(e,t){return"genitive"===t&&("ա"===e.slice(-1)?e=e.slice(0,-1)+"այի":"ո"===e.slice(-1)?e=e.slice(0,-1)+"ոյի":"գիրք"===e.slice(-4)?e=e.slice(0,-4)+"գրքի":e+="ի"),e}},la:class extends u{convertGrammar(e,t){switch(t){case"genitive":e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/u[ms]$/i,"i")).replace(/ommunia$/i,"ommunium")).replace(/a$/i,"ae")).replace(/libri$/i,"librorum")).replace(/nuntii$/i,"nuntiorum")).replace(/tio$/i,"tionis")).replace(/ns$/i,"ntis")).replace(/as$/i,"atis")).replace(/es$/i,"ei");break;case"accusative":e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/u[ms]$/i,"um")).replace(/ommunia$/i,"am")).replace(/a$/i,"ommunia")).replace(/libri$/i,"libros")).replace(/nuntii$/i,"nuntios")).replace(/tio$/i,"tionem")).replace(/ns$/i,"ntem")).replace(/as$/i,"atem")).replace(/es$/i,"em");break;case"ablative":e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/u[ms]$/i,"o")).replace(/ommunia$/i,"ommunibus")).replace(/a$/i,"a")).replace(/libri$/i,"libris")).replace(/nuntii$/i,"nuntiis")).replace(/tio$/i,"tione")).replace(/ns$/i,"nte")).replace(/as$/i,"ate")).replace(/es$/i,"e")}return e}},os:class extends u{convertGrammar(e,t){var n,r,a,i;switch(n="мæ",r="",a="",i="",e.match(/тæ$/i)?(e=e.slice(0,-1),n="æм"):e.match(/[аæеёиоыэюя]$/i)?r="й":e.match(/у$/i)?e.slice(-2,-1).match(/[аæеёиоыэюя]$/i)||(r="й"):e.match(/[бвгджзйклмнопрстфхцчшщьъ]$/i)||(a="-"),t){case"genitive":i=a+r+"ы";break;case"dative":i=a+r+"æн";break;case"allative":i=a+n;break;case"ablative":i="й"===r?a+r+"æ":a+r+"æй";break;case"superessive":i=a+r+"ыл";break;case"equative":i=a+r+"ау";break;case"comitative":i=a+"имæ"}return e+i}},ru:class extends u{convertGrammar(e,t){return"genitive"===t&&("ь"===e.slice(-1)?e=e.slice(0,-1)+"я":"ия"===e.slice(-2)?e=e.slice(0,-2)+"ии":"ка"===e.slice(-2)?e=e.slice(0,-2)+"ки":"ти"===e.slice(-2)?e=e.slice(0,-2)+"тей":"ды"===e.slice(-2)?e=e.slice(0,-2)+"дов":"ник"===e.slice(-3)&&(e=e.slice(0,-3)+"ника")),e}},sl:class extends u{convertGrammar(e,t){switch(t){case"mestnik":e="o "+e;break;case"orodnik":e="z "+e}return e}},uk:class extends u{convertGrammar(e,t){switch(t){case"genitive":"ь"===e.slice(-1)?e=e.slice(0,-1)+"я":"ія"===e.slice(-2)?e=e.slice(0,-2)+"ії":"ка"===e.slice(-2)?e=e.slice(0,-2)+"ки":"ти"===e.slice(-2)?e=e.slice(0,-2)+"тей":"ды"===e.slice(-2)?e=e.slice(0,-2)+"дов":"ник"===e.slice(-3)&&(e=e.slice(0,-3)+"ника");break;case"accusative":"ія"===e.slice(-2)&&(e=e.slice(0,-2)+"ію")}return e}}};const d=new RegExp("(?:([A-Za-zªµºÀ-ÖØ-öø-ʸʻ-ˁːˑˠ-ˤˮͰ-ͳͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-҂Ҋ-ԯԱ-Ֆՙ-՟ա-և։ः-हऻऽ-ीॉ-ौॎ-ॐक़-ॡ।-ঀংঃঅ-ঌএঐও-নপ-রলশ-হঽ-ীেৈোৌৎৗড়ঢ়য়-ৡ০-ৱ৴-৺ਃਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਾ-ੀਖ਼-ੜਫ਼੦-੯ੲ-ੴઃઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽ-ીૉોૌૐૠૡ૦-૰ૹଂଃଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽାୀେୈୋୌୗଡ଼ଢ଼ୟ-ୡ୦-୷ஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹாிுூெ-ைொ-ௌௐௗ௦-௲ఁ-ఃఅ-ఌఎ-ఐఒ-నప-హఽు-ౄౘ-ౚౠౡ౦-౯౿ಂಃಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽ-ೄೆ-ೈೊೋೕೖೞೠೡ೦-೯ೱೲംഃഅ-ഌഎ-ഐഒ-ഺഽ-ീെ-ൈൊ-ൌൎൗൟ-ൡ൦-൵൹-ൿංඃඅ-ඖක-නඳ-රලව-ෆා-ෑෘ-ෟ෦-෯ෲ-෴ก-ะาำเ-ๆ๏-๛ກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ະາຳຽເ-ໄໆ໐-໙ໜ-ໟༀ-༗༚-༴༶༸༾-ཇཉ-ཬཿ྅ྈ-ྌ྾-࿅࿇-࿌࿎-࿚က-ာေးျြဿ-ၗၚ-ၝၡ-ၰၵ-ႁႃႄႇ-ႌႎ-ႜ႞-ჅჇჍა-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚ፠-፼ᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙿᚁ-ᚚᚠ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱ᜵᜶ᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳាើ-ៅះៈ។-៚ៜ០-៩᠐-᠙ᠠ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᤣ-ᤦᤩ-ᤫᤰᤱᤳ-ᤸ᥆-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉ᧐-᧚ᨀ-ᨖᨙᨚ᨞-ᩕᩗᩡᩣᩤᩭ-ᩲ᪀-᪉᪐-᪙᪠-᪭ᬄ-ᬳᬵᬻᬽ-ᭁᭃ-ᭋ᭐-᭪᭴-᭼ᮂ-ᮡᮦᮧ᮪ᮮ-ᯥᯧᯪ-ᯬᯮ᯲᯳᯼-ᰫᰴᰵ᰻-᱉ᱍ-᱿᳀-᳇᳓᳡ᳩ-ᳬᳮ-ᳳᳵᳶᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼ‎ⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎ⅏Ⅰ-ↈ⌶-⍺⎕⒜-ⓩ⚬⠀-⣿Ⰰ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯ⵰ⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〮〯〱-〵〸-〼ぁ-ゖゝ-ゟァ-ヺー-ヿㄅ-ㄭㄱ-ㆎ㆐-ㆺㇰ-㈜㈠-㉏㉠-㉻㉿-㊰㋀-㋋㋐-㋾㌀-㍶㍻-㏝㏠-㏾㐀-䶵一-鿕ꀀ-ꒌꓐ-ꘌꘐ-ꘫꙀ-ꙮꚀ-ꚝꚠ-ꛯ꛲-꛷Ꜣ-ꞇ꞉-ꞭꞰ-ꞷꟷ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠤꠧ꠰-꠷ꡀ-ꡳꢀ-ꣃ꣎-꣙ꣲ-ꣽ꤀-ꤥ꤮-ꥆꥒ꥓꥟-ꥼꦃ-ꦲꦴꦵꦺꦻꦽ-꧍ꧏ-꧙꧞-ꧤꧦ-ꧾꨀ-ꨨꨯꨰꨳꨴꩀ-ꩂꩄ-ꩋꩍ꩐-꩙꩜-ꩻꩽ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫫꫮ-ꫵꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭥꭰ-ꯤꯦꯧꯩ-꯬꯰-꯹가-힣ힰ-ퟆퟋ-ퟻ-舘並-龎ﬀ-ﬆﬓ-ﬗＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ]|\ud800[\udc00-\udc0b]|\ud800[\udc0d-\udc26]|\ud800[\udc28-\udc3a]|𐀼|𐀽|\ud800[\udc3f-\udc4d]|\ud800[\udc50-\udc5d]|\ud800[\udc80-\udcfa]|𐄀|𐄂|\ud800[\udd07-\udd33]|\ud800[\udd37-\udd3f]|\ud800[\uddd0-\uddfc]|\ud800[\ude80-\ude9c]|\ud800[\udea0-\uded0]|\ud800[\udf00-\udf23]|\ud800[\udf30-\udf4a]|\ud800[\udf50-\udf75]|\ud800[\udf80-\udf9d]|\ud800[\udf9f-\udfc3]|\ud800[\udfc8-\udfd5]|\ud801[\udc00-\udc9d]|\ud801[\udca0-\udca9]|\ud801[\udd00-\udd27]|\ud801[\udd30-\udd63]|𐕯|\ud801[\ude00-\udf36]|\ud801[\udf40-\udf55]|\ud801[\udf60-\udf67]|𑀀|\ud804[\udc02-\udc37]|\ud804[\udc47-\udc4d]|\ud804[\udc66-\udc6f]|\ud804[\udc82-\udcb2]|𑂷|𑂸|\ud804[\udcbb-\udcc1]|\ud804[\udcd0-\udce8]|\ud804[\udcf0-\udcf9]|\ud804[\udd03-\udd26]|𑄬|\ud804[\udd36-\udd43]|\ud804[\udd50-\udd72]|\ud804[\udd74-\udd76]|\ud804[\udd82-\uddb5]|\ud804[\uddbf-\uddc9]|𑇍|\ud804[\uddd0-\udddf]|\ud804[\udde1-\uddf4]|\ud804[\ude00-\ude11]|\ud804[\ude13-\ude2e]|𑈲|𑈳|𑈵|\ud804[\ude38-\ude3d]|\ud804[\ude80-\ude86]|𑊈|\ud804[\ude8a-\ude8d]|\ud804[\ude8f-\ude9d]|\ud804[\ude9f-\udea9]|\ud804[\udeb0-\udede]|\ud804[\udee0-\udee2]|\ud804[\udef0-\udef9]|𑌂|𑌃|\ud804[\udf05-\udf0c]|𑌏|𑌐|\ud804[\udf13-\udf28]|\ud804[\udf2a-\udf30]|𑌲|𑌳|\ud804[\udf35-\udf39]|\ud804[\udf3d-\udf3f]|\ud804[\udf41-\udf44]|𑍇|𑍈|\ud804[\udf4b-\udf4d]|𑍐|𑍗|\ud804[\udf5d-\udf63]|\ud805[\udc80-\udcb2]|𑒹|\ud805[\udcbb-\udcbe]|𑓁|\ud805[\udcc4-\udcc7]|\ud805[\udcd0-\udcd9]|\ud805[\udd80-\uddb1]|\ud805[\uddb8-\uddbb]|𑖾|\ud805[\uddc1-\udddb]|\ud805[\ude00-\ude32]|𑘻|𑘼|𑘾|\ud805[\ude41-\ude44]|\ud805[\ude50-\ude59]|\ud805[\ude80-\udeaa]|𑚬|𑚮|𑚯|𑚶|\ud805[\udec0-\udec9]|\ud805[\udf00-\udf19]|𑜠|𑜡|𑜦|\ud805[\udf30-\udf3f]|\ud806[\udca0-\udcf2]|𑣿|\ud806[\udec0-\udef8]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e]|\ud809[\udc70-\udc74]|\ud809[\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38]|\ud81a[\ude40-\ude5e]|\ud81a[\ude60-\ude69]|𖩮|𖩯|\ud81a[\uded0-\udeed]|𖫵|\ud81a[\udf00-\udf2f]|\ud81a[\udf37-\udf45]|\ud81a[\udf50-\udf59]|\ud81a[\udf5b-\udf61]|\ud81a[\udf63-\udf77]|\ud81a[\udf7d-\udf8f]|\ud81b[\udf00-\udf44]|\ud81b[\udf50-\udf7e]|\ud81b[\udf93-\udf9f]|𛀀|𛀁|\ud82f[\udc00-\udc6a]|\ud82f[\udc70-\udc7c]|\ud82f[\udc80-\udc88]|\ud82f[\udc90-\udc99]|𛲜|𛲟|\ud834[\udc00-\udcf5]|\ud834[\udd00-\udd26]|\ud834[\udd29-\udd66]|\ud834[\udd6a-\udd72]|𝆃|𝆄|\ud834[\udd8c-\udda9]|\ud834[\uddae-\udde8]|\ud834[\udf60-\udf71]|\ud835[\udc00-\udc54]|\ud835[\udc56-\udc9c]|𝒞|𝒟|𝒢|𝒥|𝒦|\ud835[\udca9-\udcac]|\ud835[\udcae-\udcb9]|𝒻|\ud835[\udcbd-\udcc3]|\ud835[\udcc5-\udd05]|\ud835[\udd07-\udd0a]|\ud835[\udd0d-\udd14]|\ud835[\udd16-\udd1c]|\ud835[\udd1e-\udd39]|\ud835[\udd3b-\udd3e]|\ud835[\udd40-\udd44]|𝕆|\ud835[\udd4a-\udd50]|\ud835[\udd52-\udea5]|\ud835[\udea8-\udeda]|\ud835[\udedc-\udf14]|\ud835[\udf16-\udf4e]|\ud835[\udf50-\udf88]|\ud835[\udf8a-\udfc2]|\ud835[\udfc4-\udfcb]|\ud836[\udc00-\uddff]|\ud836[\ude37-\ude3a]|\ud836[\ude6d-\ude74]|\ud836[\ude76-\ude83]|\ud836[\ude85-\ude8b]|\ud83c[\udd10-\udd2e]|\ud83c[\udd30-\udd69]|\ud83c[\udd70-\udd9a]|\ud83c[\udde6-\ude02]|\ud83c[\ude10-\ude3a]|\ud83c[\ude40-\ude48]|🉐|🉑|[\ud840-\ud868][\udc00-\udfff]|\ud869[\udc00-\uded6]|\ud869[\udf00-\udfff]|[\ud86a-\ud86c][\udc00-\udfff]|\ud86d[\udc00-\udf34]|\ud86d[\udf40-\udfff]|\ud86e[\udc00-\udc1d]|\ud86e[\udc20-\udfff]|[\ud86f-\ud872][\udc00-\udfff]|\ud873[\udc00-\udea1]|\ud87e[\udc00-\ude1d]|[\udb80-\udbbe][\udc00-\udfff]|\udbbf[\udc00-\udffd]|[\udbc0-\udbfe][\udc00-\udfff]|\udbff[\udc00-\udffd])|([֐־׀׃׆׈-׿߀-ߪߴߵߺ-ࠕࠚࠤࠨ࠮-ࡘ࡜-࢟‏יִײַ-ﬨשׁ-ﭏ؈؋؍؛-ي٭-ٯٱ-ەۥۦۮۯۺ-ܐܒ-ܯ݋-ޥޱ-޿ࢠ-࣢ﭐ-ﴽ﵀-﷏ﷰ-﷼﷾﷿ﹰ-﻾]|\ud802[\udc00-\udd1e]|\ud802[\udd20-\ude00]|𐨄|\ud802[\ude07-\ude0b]|\ud802[\ude10-\ude37]|\ud802[\ude3b-\ude3e]|\ud802[\ude40-\udee4]|\ud802[\udee7-\udf38]|\ud802[\udf40-\udfff]|\ud803[\udc00-\ude5f]|\ud803[\ude7f-\udfff]|\ud83a[\udc00-\udccf]|\ud83a[\udcd7-\udfff]|\ud83b[\udc00-\uddff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\ude00-\udeef]|\ud83b[\udef2-\udeff]))");function s(e){let t,n,r,a,i,o,u,c,d,s,l,f,p,h,m,g,v,b,E=0;function y(e){return()=>{for(let t=0;t<e.length;t++){let n=e[t]();if(null!==n)return n}return null}}function A(e){let t=E,n=[];for(let r=0;r<e.length;r++){let a=e[r]();if(null===a)return E=t,null;n.push(a)}return n}function T(e,t){return()=>{let n=E,r=[],a=t();for(;null!==a;)r.push(a),a=t();return r.length<e?(E=n,null):r}}function I(t){let n=t.length;return()=>{let r=null;return e.slice(E,E+n)===t&&(r=t,E+=n),r}}function L(t){return()=>{let n=e.slice(E).match(t);return null===n?null:(E+=n[0].length,n[0])}}function S(){let e=A([r,a]);return null===e?null:e[1]}function w(){let e=A([i,o]);return null===e?null:["REPLACE",parseInt(e[1],10)-1]}var C,k;function _(){let e=A([t,T(0,v)]);if(null===e)return null;let n=e[1];return n.length>1?["CONCAT"].concat(n):n[0]}function N(){let e=A([p,n,w]);return null===e?null:[e[0],e[2]]}function O(){let e=A([p,n,v]);return null===e?null:[e[0],e[2]]}function D(){let e=A([h,f,m]);return null===e?null:e[1]}if(t=I("|"),n=I(":"),r=I("\\"),a=L(/^./),i=I("$"),o=L(/^\d+/),u=L(/^[^{}[\]$\\]/),c=L(/^[^{}[\]$\\|]/),y([S,d=L(/^[^{}[\]$\s]/)]),s=y([S,c]),l=y([S,u]),C=L(/^[ !"$&'()*,.\/0-9;=?@A-Z^_`a-z~\x80-\xFF+-]+/),k=function(e){return e.toString()},p=()=>{let e=C();return null===e?null:k(e)},f=y([function(){let e=A([y([N,O]),T(0,_)]);return null===e?null:e[0].concat(e[1])},function(){let e=A([p,T(0,_)]);return null===e?null:[e[0]].concat(e[1])}]),h=I("{{"),m=I("}}"),g=y([D,w,function(){let e=T(1,l)();return null===e?null:e.join("")}]),v=y([D,w,function(){let e=T(1,s)();return null===e?null:e.join("")}]),null===(b=function(){let e=T(0,g)();return null===e?null:["CONCAT"].concat(e)}())||E!==e.length)throw new Error("Parse error at position "+E.toString()+" in input: "+e);return b}class l{constructor(e){this.locale=e,this.emitter=new class{constructor(e){this.locale=e,this.language=new(c[e]||c.default)(e)}emit(e,t){let n,r,a;switch(typeof e){case"string":case"number":n=e;break;case"object":if(r=e.slice(1).map(e=>this.emit(e,t)),"function"!=typeof this[a=e[0].toLowerCase()])throw new Error('unknown operation "'+a+'"');n=this[a](r,t);break;case"undefined":n="";break;default:throw new Error("unexpected type in AST: "+typeof e)}return n}concat(e){let t="";return e.forEach(e=>{t+=e}),t}replace(e,t){let n=parseInt(e[0],10);return n<t.length?t[n]:"$"+(n+1)}plural(e){let t=parseFloat(this.language.convertNumber(e[0],10)),n=e.slice(1);return n.length?this.language.convertPlural(t,n):""}gender(e){let t=e[0],n=e.slice(1);return this.language.gender(t,n)}grammar(e){let t=e[0],n=e[1];return n&&t&&this.language.convertGrammar(n,t)}bidi(e){var t,n=(t=e[0].match(d))?void 0===t[2]?"ltr":"rtl":null;return"ltr"===n?"‪"+e[0]+"‬":"rtl"===n?"‫"+e[0]+"‬":e[0]}}(this.locale)}parse(e,t){if(e.includes("{{")){let n=new s(e);return this.emitter.emit(n,t)}return this.simpleParse(e,t)}simpleParse(e,t){return e.replace(/\$(\d+)/g,(e,n)=>{let r=parseInt(n,10)-1;return void 0!==t[r]?t[r]:"$"+n})}}class f{constructor(e){this.sourceMap=new Map}load(e,t){if("object"!=typeof e)throw Error("Invalid message source. Must be an object");if(t){if(!/^[a-zA-Z0-9-]+$/.test(t))throw Error(`Invalid locale ${t}`);for(let n in e)if(0!==n.indexOf("@")){if("object"==typeof e[n])return this.load(e);if("string"!=typeof e[n])throw Error(`Invalid message for message ${n} in ${t} locale.`);break}this.sourceMap.has(t)?this.sourceMap.set(t,Object.assign(this.sourceMap.get(t),e)):this.sourceMap.set(t,e)}else for(t in e)this.load(e[t],t)}getMessage(e,t){let n=this.sourceMap.get(t);return n?n[e]:null}hasLocale(e){return this.sourceMap.has(e)}}var p=n(3);n.d(t,"default",(function(){return h}));class h{constructor(e,t){t=t||{},this.locale=e,this.parser=new l(this.locale),this.messageStore=new f,t.messages&&this.load(t.messages,this.locale),this.finalFallback=t.finalFallback||"en"}load(e,t){return this.messageStore.load(e,t||this.locale)}i18n(e,...t){return this.parser.parse(this.getMessage(e),t)}setLocale(e){this.locale=e,this.parser=new l(this.locale)}getFallbackLocales(){return[...p[this.locale]||[],this.finalFallback]}getMessage(e){let t=this.locale,n=0;const r=this.getFallbackLocales(this.locale);for(;t;){let a=t.split("-"),i=a.length;do{let t=a.slice(0,i).join("-"),n=this.messageStore.getMessage(e,t);if(n)return n;i--}while(i);t=r[n],n++}return e}}}]).default},,,,,,,function(e,t,n){"use strict";n.r(t);var r,a,i=n(33),o=n.n(i),u=n(16),c=n(21),d=n(9),s=n(22),l=function(e,t){if(void 0===t||t<0)return"";try{var n=e.i18n("page-last-edited",t);return n&&n.includes("undefined")&&(n=""),n}catch(e){return""}},f={MenuItemType:d.a.MenuItemType,add:function(e){var t=e.title,n=e.menu,a=n.items,i=n.editedDaysAgo,f=e.readMore,p=f.itemCount,h=f.baseURL;!1===u.a.isContainerAttached(document)&&document.getElementById("pcs").appendChild(u.a.containerFragment(document));var m=function(e){if(d.a.setHeading(e.i18n("article-about-title"),"pcs-footer-container-menu-heading",document),a.forEach((function(t){var n="",a="";switch(t){case d.a.MenuItemType.lastEdited:n=e.i18n("page-edit-history"),a=l(e,i);break;case d.a.MenuItemType.pageIssues:n=e.i18n("page-issues"),a=e.i18n("page-issues-subtitle");break;case d.a.MenuItemType.disambiguation:n=e.i18n("page-similar-titles");break;case d.a.MenuItemType.coordinate:n=e.i18n("page-location");break;case d.a.MenuItemType.talkPage:n=e.i18n("page-talk-page"),a=e.i18n("page-talk-page-subtitle")}d.a.maybeAddItem(n,a,t,"pcs-footer-container-menu-items",(function(e){r&&r.footerItemSelected(t,e)}),document)})),p&&p>0){s.a.fetchAndAdd(t,e.i18n("article-read-more-title"),p,"pcs-footer-container-readmore","pcs-footer-container-readmore-pages",h,(function(e){r&&r.titlesRetrieved(e)}),document)}c.a.add(document,e.i18n("license-footer-text"),e.i18n("license-footer-name"),"pcs-footer-container-legal",e.i18n("view-in-browser-footer-link"),(function(){r&&r.viewInBrowser()}))},g=new XMLHttpRequest,v=window.location.href||"",b=v.indexOf("/page/mobile-html/"),E=b>=0?v.slice(0,b):"",y="".concat(E,"/data/i18n/pcs");g.open("GET",y,!0);var A={i18n:function(e){return e}};g.onload=function(){var e;try{var t=JSON.parse(g.responseText),n=t&&t.locale||"en",r=document.head.querySelector('meta[property="pcs:locale"]');if(r){var a=r.getAttribute("content");a&&(n=a)}(e=new o.a(n)).load(t.messages)}catch(t){e=A}m(e)},g.onerror=function(){m(A)},g.send()},_connectHandlers:function(e){r=e},_getPageLastEditedString:l},p=n(3),h=n.n(p),m=n(2),g=n.n(m),v=n(8),b=n(10),E=n(5),y=n(14),A=n(6),T={InitialSetup:"setup",FinalSetup:"final_setup",LinkClicked:"link",ImageClicked:"image",ReferenceClicked:"reference",BackLink:"back_link",EditSection:"edit_section",AddTitleDescription:"add_title_description",PronunciationClicked:"pronunciation",ScrollToAnchor:"scroll_to_anchor",FooterItemSelected:"footer_item",SaveOtherPage:"save_other_page",ReadMoreTitlesRetrieved:"read_more_titles_retrieved",ViewInBrowser:"view_in_browser"},I=function e(t,n){g()(this,e),this.action=t,this.data=n},L=0,S=1,w=2,C=3,k=4,_=5,N=function(){function e(t,n,r){g()(this,e),this.target=t,this.href=n,this.pageLinkTitle=r}return h()(e,[{key:"type",value:function(){return y.a.isCitation(this.href,this.pageLinkTitle)?k:y.a.isBackLink(this.href,this.pageLinkTitle)?_:"IMG"===this.target.tagName&&(this.target.classList.contains(E.a.CLASSES.IMAGE_LOADED_CLASS)||this.target.classList.contains(E.a.CLASSES.IMAGE_LOADING_CLASS))&&(this.target.closest("figure")||this.target.closest("figure-inline"))?w:"SPAN"===this.target.tagName&&this.target.classList.contains(E.a.CLASSES.PLACEHOLDER_CLASS)&&(this.target.closest("figure")||this.target.closest("figure-inline"))?C:this.href?S:L}}]),e}(),O=function(e){a&&a(e)},D=function(e){return e&&e.replace(/^\.\/.+:/g,"./File:")},x=function(e){switch(e.type()){case S:t=e.target.closest("a"),"#"===(n=e.href)[0]&&v.a.expandCollapsedTableIfItContainsElement(document.getElementById(n.substring(1))),O(new I(T.LinkClicked,{href:n,text:t.innerText,title:t.title,source:t.getAttribute("data-pcs-source")}));break;case w:!function(e,t){var n=D(t);O(new I(T.ImageClicked,{href:n,src:e.getAttribute("src"),"data-file-width":e.getAttribute("data-file-width"),"data-file-height":e.getAttribute("data-file-height")}))}(e.target,e.href);break;case C:!function(e,t){var n=e.parentElement,r=D(t);O(new I(T.ImageClicked,{href:r,src:n.getAttribute("data-src"),"data-file-width":n.getAttribute("data-data-file-width"),"data-file-height":n.getAttribute("data-data-file-height")}))}(e.target,e.href);break;case k:!function(e,t){var n=y.a.collectNearbyReferences(document,e);n.href=t,O(new I(T.ReferenceClicked,n))}(e.target,e.href);break;case _:!function(e,t){var n=y.a.collectReferencesForBackLink(document,e,t);O(new I(T.BackLink,n))}(e.target,e.href);break;default:return!1}var t,n;return!0},P=function(e,t){O(new I(T.FooterItemSelected,{itemType:e,payload:t}))},R=function(e){O(new I(T.SaveOtherPage,{title:e}))},B=function(e){O(new I(T.ReadMoreTitlesRetrieved,{titles:e}))},H=function(){O(new I(T.ViewInBrowser))},M={Actions:T,getSelectionInfo:function(e){var t=(e||window).getSelection(),n=t.toString(),r=t.anchorNode;return{text:n,section:A.a.getSectionIDOfElement(r),isTitleDescription:r&&r.parentElement&&"pcs-edit-section-title-description"===r.parentElement.id||!1}},setInteractionHandler:function(e){a=e,f._connectHandlers({footerItemSelected:P,saveOtherPage:R,titlesRetrieved:B,viewInBrowser:H}),document.addEventListener("click",(function(e){e.preventDefault(),function(e){var t=e.target;if(t){var n=t.closest("A");if(n)if("edit_section"!==n.getAttribute("data-action"))if("add_title_description"!==n.getAttribute("data-action"))if("title_pronunciation"!==n.getAttribute("data-action")){var r=n.getAttribute("href");if(r){var a,i=document.head.querySelector('link[rel="dc:isVersionOf"]');if(i){var o=i.href;if(o){var u=o.split("/");""===(a=u.pop())&&(a=u.pop)}}x(new N(t,r,a))}}else{var c=n.getAttribute(b.a.DATA_ATTRIBUTE.PRONUNCIATION_URL)||void 0;O(new I(T.PronunciationClicked,{url:c}))}else O(new I(T.AddTitleDescription));else{var d=n.getAttribute("data-id")||void 0,s={sectionId:d};if(d&&"0"===d){var l=document.getElementById(b.a.IDS.TITLE_DESCRIPTION);s.descriptionSource=l&&l.getAttribute(b.a.DATA_ATTRIBUTE.DESCRIPTION_SOURCE)||void 0,s.wikidataEntityID=l&&l.getAttribute(b.a.DATA_ATTRIBUTE.WIKIDATA_ENTITY_ID)||void 0}O(new I(T.EditSection,s))}}}(e)}),!1)},initialSetupComplete:function(){O(new I(T.InitialSetup))},finalSetupComplete:function(){O(new I(T.FinalSetup))},scrollToAnchor:function(e,t){O(new I(T.ScrollToAnchor,{anchor:e,rect:t,href:"#".concat(e)}))}},z=n(17),F=n(18),q=function(e){var t=e.pathname.split("v1/page/mobile-html/"),n="".concat(e.protocol,"//").concat(e.host).concat(t[0],"v1"),r=t[1];f.add({version:"2",title:r,menu:{items:[f.MenuItemType.languages,f.MenuItemType.lastEdited,f.MenuItemType.pageIssues,f.MenuItemType.disambiguation,f.MenuItemType.talkPage],fragment:"pcs-menu",editedDaysAgo:3},readMore:{itemCount:3,baseURL:n,fragment:"pcs-read-more"}})},$=n(19),j=n(23),G=n(7),U=n(12),W=0,K={setScrollTop:function(e){W=e},scrollWithDecorOffset:function(e){window.scrollTo(0,e.parentNode.offsetTop-W)},testing:{getScrollTop:function(){return W}}},X=n(13),V=n(0),Y=/[^0-9]+$/,J=function(e){e instanceof Function&&(window&&window.requestAnimationFrame?window.requestAnimationFrame((function(){setTimeout((function(){e()}),1)})):e())},Z=function(e,t){var n,r=e||{};if(U.a.setVersion(document,r.version),void 0!==r.platform&&U.a.setPlatform(document,U.a.CLASS_PREFIX+r.platform),void 0!==r.theme&&X.a.setTheme(document,X.a.CLASS_PREFIX+r.theme),void 0!==r.dimImages&&$.a.dimImages(document,r.dimImages),void 0!==r.margins||void 0!==r.leadImageHeight){var a=r.margins||{};if(void 0!==r.leadImageHeight)if(n||(n=ne()),ae(n).source)if(a.top){var i=parseFloat(a.top,10),o=parseFloat(r.leadImageHeight,10),u=a.top.match(Y)||"";a.top=i+o+u}else a.top=r.leadImageHeight;F.a.setMargins(document.body,a)}if(void 0!==r.maxWidth&&Q(r.maxWidth),void 0!==r.userGroups){n||(n=ne());var c=te(n),d=!1;if(c.edit){d=!0;for(var s=0;s<r.userGroups.length;s++){if(r.userGroups[s]===c.edit){d=!1;break}}}ee(!0,d)}if(void 0===r.setupTableEventHandling||r.setupTableEventHandling){var l=!0!==r.areTablesInitiallyExpanded;v.a.setupEventHandling(window,document,l,K.scrollWithDecorOffset)}if(void 0!==r.scrollTop&&K.setScrollTop(r.scrollTop),void 0!==r.textSizeAdjustmentPercentage&&z.a.setPercentage(document.body,r.textSizeAdjustmentPercentage),void 0===r.loadImages||!0===r.loadImages){var f=new j.a(window,2);f.collectExistingPlaceholders(document.body),f.loadPlaceholders()}r.footer&&q(new URL(document.location)),J(t)},Q=function(e){document&&document.body&&(document.body.style.maxWidth=V.b.escape(e))},ee=function(e,t){b.a.setEditButtons(document,e,t)},te=function(e){var t={},n="mw:pageProtection:".length;return e.forEach((function(e){var r=e.getAttribute("property");r&&r.startsWith("mw:pageProtection:")&&(t[r.substring(n)]=e.getAttribute("content"))})),t},ne=function(){return document.head.querySelectorAll("meta")},re=function(){if(document)for(var e=".".concat("pcs-element-highlight"),t=document.querySelector(e);t;)t.classList.remove("pcs-element-highlight"),t=document.querySelector(e)},ae=function(e){for(var t={},n=0;n<e.length;n++){var r=e[n],a=r.getAttribute("property");if(a&&"mw:leadImage"===a){t.source=r.getAttribute("content");var i=r.getAttribute("data-file-width");i&&(t.width=parseInt(i,10));var o=r.getAttribute("data-file-height");o&&(t.height=parseInt(o,10));break}}return t},ie={onBodyStart:function(){if(document){if("undefined"!=typeof pcsClient&&pcsClient.getSetupSettings){var e=pcsClient.getSetupSettings();document.pcsSetupSettings=JSON.parse(e)}"undefined"!=typeof pcsClient&&pcsClient.onReceiveMessage&&(document.pcsActionHandler=function(e){pcsClient.onReceiveMessage(JSON.stringify(e))}),document.pcsActionHandler?M.setInteractionHandler(document.pcsActionHandler):M.setInteractionHandler((function(e){return console.log(e)}));var t=function(){M.initialSetupComplete()};if(document.pcsSetupSettings){var n={margins:document.pcsSetupSettings.margins,maxWidth:document.pcsSetupSettings.maxWidth,textSizeAdjustmentPercentage:document.pcsSetupSettings.textSizeAdjustmentPercentage,leadImageHeight:document.pcsSetupSettings.leadImageHeight,userGroups:document.pcsSetupSettings.userGroups,theme:document.pcsSetupSettings.theme,platform:document.pcsSetupSettings.platform,loadImages:!1,setupTableEventHandling:!1};Z(n,t)}else{var r={loadImages:!1,setupTableEventHandling:!1,maxWidth:"100ex",margins:{top:"2em",right:"auto",bottom:"0",left:"auto"}};if(document.location&&document.location.search){var a=new URLSearchParams(document.location.search);a.get("theme")&&(r.theme=a.get("theme"))}Z(r,t)}}},onBodyEnd:function(){if(document){var e=100;b.a.setARIAEditButtons(document);var t=function(){M.finalSetupComplete()};if(document.pcsSetupSettings){var n=document.pcsSetupSettings;delete n.theme,delete n.margins,delete n.maxWidth,delete n.userGroups,delete n.leadImageHeight,delete n.platform,delete n.textSizeAdjustmentPercentage,n.setupTableEventHandling=!0,Z(n,t),e=document.pcsSetupSettings.remainingTimeout||e}else{var r=function(){if(document.location&&document.location.search){var e=new URLSearchParams(document.location.search);return"true"===e.get("footer")||null!==e.get("demo")}return!1}();Z({setupTableEventHandling:!0,areTablesInitiallyExpanded:!0,footer:r},t)}setTimeout((function(){for(var e=document.querySelectorAll("section"),t=1;t<e.length;t++)e[t].style.display=""}),e)}},setup:Z,setTheme:function(e){X.a.setTheme(document,e)},setDimImages:function(e){$.a.dimImages(document,e)},setMargins:function(e){F.a.setMargins(document.body,e)},setMaxWidth:Q,setScrollTop:function(e){K.setScrollTop(e)},setTextSizeAdjustmentPercentage:function(e){z.a.setPercentage(document.body,e)},setEditButtons:ee,getLeadImage:function(){return ae(ne())},getProtection:function(){return te(ne())},getRevision:function(){var e=document.documentElement.getAttribute("about");if(e)return e.substring(e.lastIndexOf("/")+1)},getTableOfContents:function(){var e=document.querySelectorAll("section"),t=[],n=new Array(10).fill(0),r=0;return[].forEach.call(e,(function(e){var a=parseInt(e.getAttribute("data-mw-section-id"),10);if(!(!a||isNaN(a)||a<1)){var i=e.querySelector("h1,h2,h3,h4,h5,h6");if(i){var o=parseInt(i.tagName.charAt(1),10)-1;o<r&&n.fill(0,o),r=o,n[o-1]++,t.push({level:o,id:a,number:n.slice(0,o).map((function(e){return e.toString()})).join("."),anchor:i.getAttribute("id"),title:i.innerHTML.trim()})}}})),t},prepareForScrollToAnchor:function(e,t){if(document){var n=document.getElementById(e);n&&(A.a.expandCollapsedSectionIfItContainsElement(document,n),v.a.expandCollapsedTableIfItContainsElement(n),t&&t.highlight&&(re(),n.classList.add("pcs-element-highlight")),J((function(){var t=G.a.getBoundingClientRectAsPlainObject(n);M.scrollToAnchor(e,t)})))}},removeHighlightsFromHighlightedElements:re,waitForNextPaint:J,testing:{getScroller:function(){return K}}},oe=U.a.CLASS,ue={ANDROID:oe.ANDROID,IOS:oe.IOS},ce={getOffsets:A.a.getSectionOffsets,setHidden:function(e,t){document&&A.a.setHidden(document,e,t)}},de=X.a.THEME,se={DEFAULT:de.DEFAULT,DARK:de.DARK,SEPIA:de.SEPIA,BLACK:de.BLACK},le={Footer:f,InteractionHandling:M,Platforms:ue,Page:ie,Scroller:K,Sections:ce,Themes:se};t.default={c1:le}}]).default}));