var PCSHTMLConverter=function(e){var t={};function i(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=e,i.c=t,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=128)}({128:function(e,t,i){var n=i(129),r=i(149),a=i(150),o=i(151),s=new a("en");function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=t.locale||t.domain.split(".")[0]||"en";s.setLocale(i);var r=new n(e,t,n.OutputMode.contentAndReferences,s);return r.workSync(),r.finalizeSync(),t.mw&&r.addMediaWikiMetadata(t.mw),r.doc.documentElement.outerHTML}function c(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=new DOMParser,n=i.parseFromString(e,"text/html");return l(n,t)}function u(e,t,i){var n=(new DOMParser).parseFromString('<html><head><meta charset="utf-8"><title></title></head><body></body></html>',"text/html"),a={domain:t,baseURI:i,mw:p(e)},o=e.mobileview;return a.mobileview=o,"<!DOCTYPE html>"+l(r.convertToParsoidDocument(n,o,a),a)}function d(e,t,i,n){function r(e){return'<section data-mw-section-id="'+e.id+'"><h'+e.toclevel+" id="+e.anchor+">"+e.line+"</h"+e.toclevel+">"+e.text+"</section>"}var a;try{var o=t.sections.reduce((function(e,t){return e+"\n"+r(t)}),r(e.sections[0])),s={source:e.image&&e.image.urls?e.image.urls[640]:"",width:640,height:640};if(a=c(o,{domain:i,baseURI:n,mw:{pageid:e.id,ns:e.ns,displaytitle:e.displaytitle,originalimage:s,protection:[],description:e.description,description_source:e.description_source}}),conversionClient)return void conversionClient.onReceiveHtml(a)}catch(e){if(console.log(e),conversionClient)return void conversionClient.onError(e.message)}return a}function p(e){var t=Object.entries(e.mobileview.protection||{}),i=e.mobileview.imageURL,n=null==i?null:{source:i,width:null,height:null};return{pageid:e.mobileview.id,ns:e.mobileview.ns,displaytitle:e.mobileview.displaytitle,normalizedtitle:e.mobileview.normalizedtitle,contentmodel:"wikitext",touched:e.mobileview.lastmodified,lastrevid:e.mobileview.revision,description:e.mobileview.description,description_source:e.mobileview.descriptionsource,originalimage:n,protection:t.map((function(e){return{type:e[0],level:e[1][0],expiry:"infinity"}})),restrictiontypes:t.map((function(e){return e[0]}))}}s.load(o),e.exports={convertParsoidHTMLToMobileHTML:c,convertMobileSectionsJSONToMobileHTML:d,convertMobileViewJSONToMobileHTML:u,testMobileView:function(){var e,t,i;return regeneratorRuntime.async((function(n){for(;;)switch(n.prev=n.next){case 0:return"https://zh.wikipedia.org/w/api.php?action=mobileview&format=json&page=中國&sections=all&prop=text%7Csections%7Clanguagecount%7Cthumb%7Cimage%7Cid%7Crevision%7Cdescription%7Cnamespace%7Cnormalizedtitle%7Cdisplaytitle%7Cprotection%7Ceditable&sectionprop=toclevel%7Cline%7Canchor&noheadings=1&thumbwidth=1024&origin=*",n.next=3,regeneratorRuntime.awrap(fetch("https://zh.wikipedia.org/w/api.php?action=mobileview&format=json&page=中國&sections=all&prop=text%7Csections%7Clanguagecount%7Cthumb%7Cimage%7Cid%7Crevision%7Cdescription%7Cnamespace%7Cnormalizedtitle%7Cdisplaytitle%7Cprotection%7Ceditable&sectionprop=toclevel%7Cline%7Canchor&noheadings=1&thumbwidth=1024&origin=*"));case 3:return e=n.sent,n.next=6,regeneratorRuntime.awrap(e.json());case 6:return t=n.sent,"en.wikipedia.org","http://localhost:6927/en.wikipedia.org/v1/",i=u(t,"en.wikipedia.org","http://localhost:6927/en.wikipedia.org/v1/"),n.abrupt("return",i);case 11:case"end":return n.stop()}}))},testMobileSections:function(){var e,t,i,n,r,a,o,s;return regeneratorRuntime.async((function(l){for(;;)switch(l.prev=l.next){case 0:return"https://en.wikipedia.org/api/rest_v1/page/mobile-sections-lead/Dog",e="https://en.wikipedia.org/api/rest_v1/page/mobile-sections-remaining/Dog",t="en.wikipedia.org",i="http://localhost:6927/en.wikipedia.org/v1/",l.next=6,regeneratorRuntime.awrap(fetch("https://en.wikipedia.org/api/rest_v1/page/mobile-sections-lead/Dog"));case 6:return n=l.sent,l.next=9,regeneratorRuntime.awrap(fetch(e));case 9:return r=l.sent,l.next=12,regeneratorRuntime.awrap(n.json());case 12:return a=l.sent,l.next=15,regeneratorRuntime.awrap(r.json());case 15:return o=l.sent,s=d(a,o,t,i),l.abrupt("return",s);case 18:case"end":return l.stop()}}))}}},129:function(e,t,i){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function s(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function l(e,t,i){return t&&s(e.prototype,t),i&&s(e,i),e}function c(e,t){return(c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var u=i(130),d=i(134),p=i(136),f=i(62),h=i(63),g=h.EditTransform,m=h.WidenImage,v=h.LazyLoadTransform,b=h.CollapseTable,y=h.LeadIntroductionTransform,_=h.SectionUtilities,k=h.ReferenceCollection,w=i(137),x=i(138),C=i(146),E=i(147),A=i(148),S=function(e){function t(e,i){var n,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.OutputMode.contentAndReferences,l=arguments.length>3?arguments[3]:void 0;return r(this,t),(n=a(this,o(t).call(this,e))).prepareDoc(e),n.nodesToRemove=[],n.lazyLoadableImages=[],n.redLinks=[],n.infoboxes=[],n.references=[],n.headers={},n.sections={},n.currentSectionId="0",n.referenceSections={},n.referenceAnchors={},n.metadata=i||{},n.metadata.pronunciation=E.parsePronunciation(e),n.metadata.linkTitle=f.getParsoidLinkTitle(e),n.metadata.plainTitle=f.getParsoidPlainTitle(e),n.outputMode=s,n.localizer=l,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&c(e,t)}(t,e),l(t,[{key:"prepareElement",value:function(e){var t=e.getAttribute("id"),i=e.tagName,n=e.getAttribute("class");if(this.isRemovableElement(e,i,t,n))this.markForRemoval(e);else{if(this.isTopLevelSection(i,e))this.checkForReferenceSection(),this.currentSectionId=e.getAttribute("data-mw-section-id"),this.sections[this.currentSectionId]=e;else if(this.isHeader(i))this.headers[this.currentSectionId]||(this.headers[this.currentSectionId]=e);else{switch(i){case"A":this.prepareAnchor(e,n);break;case"LINK":this.makeSchemeless(e,"href");break;case"SCRIPT":case"SOURCE":this.makeSchemeless(e,"src");break;case"IMG":this.prepareImage(e);break;case"OL":this.prepareOrderedList(e,n);break;case"LI":this.prepareListItem(e,t);break;case"SPAN":this.prepareSpan(e,n);break;case"DIV":this.prepareDiv(e,n);break;case"TABLE":this.widenImageExcludedNode=e,this.prepareTable(e,n)}!this.widenImageExcludedNode&&w.widenImageExclusionClassRegex.test(n)&&(this.widenImageExcludedNode=e),this.checkElementForThemeExclusion(e,n)}var r=w.attributesToRemoveFromElements[i];if(r){var a=!0,o=!1,s=void 0;try{for(var l,c=r[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){var u=l.value;e.removeAttribute(u)}}catch(e){o=!0,s=e}finally{try{a||null==c.return||c.return()}finally{if(o)throw s}}}t&&w.mwidRegex.test(t)&&e.removeAttribute("id")}}},{key:"finalizeStep",value:function(){var e;if(this.checkForReferenceSection(),e=this.nodesToRemove.pop()){var t=e.parentNode;return t&&t.removeChild(e),!0}if(e=this.infoboxes.pop())return this.prepareInfobox(e),!0;if(e=this.redLinks.pop())return this.prepareRedLink(e,this.doc),!0;if(e=this.lazyLoadableImages.pop())return v.convertImageToPlaceholder(this.doc,e),!0;this.sectionIds||(this.sectionIds=Object.keys(this.sections));var i=this.sectionIds.pop();if(i)return this.prepareSection(i),!0;if(e=this.references.pop())return this.prepareReference(e),!0;var n=this.doc.createElement("div");n.setAttribute("id","pcs"),n.classList.add("mw-parser-output");for(var r=this.doc.body,a=0,o=Array.from(r.children);a<o.length;a++){var s=o[a];n.appendChild(s)}return r.appendChild(n),r.classList.add("skin-minerva"),x.addCssLinks(this.doc,this.metadata),x.addMetaViewport(this.doc),x.addPageLibJs(this.doc,this.metadata),x.avoidFaviconRequest(this.doc),this.localizer&&this.localizer.locale&&x.addLocale(this.doc,this.localizer.locale),!1}}],[{key:"OutputMode",get:function(){return{contentAndReferences:0,content:1,references:2}}}]),l(t,[{key:"addMediaWikiMetadata",value:function(e){this.metadata.mw=e,x.addMetaTags(this.doc,this.metadata),this.outputMode!==t.OutputMode.references&&C(this.doc,this.metadata,this.i18n("description-add-link-title"))}},{key:"process",value:function(e){for(;this.ancestor&&this.ancestor!==e.parentNode;)this.ancestor===this.themeExcludedNode&&(this.themeExcludedNode=void 0),this.ancestor===this.currentInfobox&&(this.currentInfobox=void 0),this.ancestor===this.widenImageExcludedNode&&(this.widenImageExcludedNode=void 0),this.ancestor=this.ancestor.parentNode;e.nodeType===p.ELEMENT_NODE?this.prepareElement(e):e.nodeType===p.COMMENT_NODE&&this.markForRemoval(e),this.ancestor=e}},{key:"markForRemoval",value:function(e){this.nodesToRemove.push(e)}},{key:"isHeader",value:function(e){return w.headerTagRegex.test(e)}},{key:"isIndicatorOfAReferenceSection",value:function(e,t){for(var i=e.parentElement;i;){if("TD"===i.tagName)return!1;if("SECTION"===i.tagName)break;i=i.parentElement}return t&&t.includes(w.referenceListClassName)}},{key:"isTopLevelSection",value:function(e,t){return"SECTION"===e&&"BODY"===t.parentElement.tagName}},{key:"checkForReferenceSection",value:function(){(this.hasReferenceSections||this.foundIndicatorOfAReferenceSection)&&(this.firstReferenceSectionId||(this.firstReferenceSectionId=this.currentSectionId),this.referenceSections[this.currentSectionId]=this.sections[this.currentSectionId]),this.foundIndicatorOfAReferenceSection=!1}},{key:"copyAttribute",value:function(e,t,i){var n=e.getAttribute(i);null!==n&&t.setAttribute(i,n)}},{key:"checkElementForThemeExclusion",value:function(e,t){if(this.themeExcludedNode)e.classList.add("notheme");else{if(!(t&&t.length>0&&w.themeExclusionClassRegex.test(t))){var i=e.getAttribute("style");return i&&w.inlineBackgroundStyleRegex.test(i)?(e.classList.add("notheme"),void(this.themeExcludedNode=e)):void 0}this.themeExcludedNode=e}}},{key:"prepareReference",value:function(e){if(e.textElement){for(var t=e.element.firstChild;t;){var i=t;t=t.nextSibling,e.element.removeChild(i)}var n=this.doc.createElement("div");n.classList.add(k.CLASS.REF);var r=this.doc.createElement("div");r.classList.add(k.CLASS.BACK_LINK_CONTAINER),n.appendChild(r);var a=this.doc.createElement("a");a.id="back_link_".concat(e.id);var o=this.referenceAnchors[e.id];if(o){var s=o.getAttribute("data-mw-group");if(s&&w.refGroupsGeneratedByCSS.has(s))a.setAttribute("data-mw-group",s),a.setAttribute("style",o.getAttribute("style"));else{var l=o.textContent;a.textContent=l?A.truncateLinkText(l):"↑"}}else a.textContent="↑";a.classList.add(k.CLASS.BACK_LINK_ANCHOR),a.setAttribute("href","./".concat(this.metadata.linkTitle).concat(k.BACK_LINK_FRAGMENT_PREFIX).concat(e.id)),a.setAttribute(k.BACK_LINK_ATTRIBUTE,JSON.stringify(e.backLinks)),r.appendChild(a);var c=this.doc.createElement("div");c.classList.add(k.CLASS.BODY),n.appendChild(c),c.appendChild(e.textElement),e.element.appendChild(n)}}},{key:"prepareSection",value:function(e){this.outputMode===t.OutputMode.references?this.prepareSectionForReferenceOutput(e):this.prepareSectionForCompleteOrContentOutput(e)}},{key:"prepareSectionForCompleteOrContentOutput",value:function(e){var t=this.sections[e];e<=0?(this.moveLeadParagraphUp(e,t),this.addLeadSectionButton(e,t)):this.prepareRemainingSection(e,t)}},{key:"moveLeadParagraphUp",value:function(e,t){for(var i,n=t.firstElementChild;n&&n.classList&&n.classList.contains("hatnote");)i=t.firstChild,n=n.nextElementSibling;y.moveLeadIntroductionUp(this.doc,t,i)}},{key:"addLeadSectionButton",value:function(e,t){var i=this.prepareEditButton(this.doc,e);t.insertBefore(i,t.firstChild)}},{key:"prepareRemainingSection",value:function(e,i){var n=this.outputMode===t.OutputMode.contentAndReferences&&this.referenceSections[e],r=this.headers[e];this.prepareSectionHeader(r,i,e,n,this.doc)}},{key:"prepareSectionForReferenceOutput",value:function(e){var t=this.sections[e];if(this.referenceSections[e]){var i=this.headers[e];this.prepareSectionHeader(i,t,e,!1,this.doc)}else t.parentNode.removeChild(t)}},{key:"prepareDoc",value:function(e){e.body.classList.add("content"),g.setEditButtons(e,!1,!1)}},{key:"prepareRedLink",value:function(e,t){var i=t.createElement("span");i.innerHTML=e.innerHTML,i.setAttribute("class",e.getAttribute("class")),e.parentNode.replaceChild(i,e)}},{key:"prepareSectionHeader",value:function(e,t,i,n,r){if(e){i===this.firstReferenceSectionId&&t&&_.createFoldHR(this.doc,t);var a=g.newEditSectionWrapper(r,i,e);n&&_.prepareForHiding(r,i,t,a,e,this.i18n("article-section-expand"),this.i18n("article-section-collapse")),e.parentNode===t?t.insertBefore(a,e):t.firstChild&&t.insertBefore(a,t.firstChild),g.appendEditSectionHeader(a,e);var o=this.prepareEditButton(r,i);a.appendChild(o)}}},{key:"prepareEditButton",value:function(e,t){var i=this.metadata.linkTitle?"/w/index.php?title=".concat(this.metadata.linkTitle,"&action=edit&section=").concat(t):"",n=g.newEditSectionLink(e,t,i);return g.newEditSectionButton(e,t,n,this.i18n("article-edit-button"),this.i18n("article-edit-protected-button"))}},{key:"isRemovableSpan",value:function(e,t){return!e.firstChild||(!!w.forbiddenSpanClassRegex.test(t)||!!w.bracketSpanRegex.test(e.text))}},{key:"isRemovableDiv",value:function(e,t){return w.forbiddenDivClassRegex.test(t)}},{key:"isRemovableLink",value:function(e){return"dc:isVersionOf"!==e.getAttribute("rel")}},{key:"isRemovableElement",value:function(e,t,i,n){if(w.forbiddenElementIDRegex.test(i))return!0;if(w.forbiddenElementClassSubstringRegex.test(n))return!0;if(w.forbiddenElementClassRegex.test(n))return!0;switch(t){case"DIV":return this.isRemovableDiv(e,n);case"SPAN":return this.isRemovableSpan(e,n);case"LINK":return this.isRemovableLink(e);default:return!1}}},{key:"makeSchemeless",value:function(e,t,i){var n=i||e.getAttribute(t);if(n){var r=n.replace(w.httpsRegex,"//");e.setAttribute(t,r)}}},{key:"isGalleryImage",value:function(e){return e.width>=64}},{key:"prepareImage",value:function(e){if(this.isGalleryImage(e)){if(!this.widenImageExcludedNode&&!e.hasAttribute("usemap"))try{d.scaleElementIfNecessary(e),m.widenImage(e)}catch(e){}this.lazyLoadableImages.push(e)}}},{key:"prepareAnchor",value:function(e,t){w.newClassRegex.test(t)&&this.redLinks.push(e);var i=e.getAttribute("rel");"nofollow"!==i&&"mw:ExtLink"!==i&&e.removeAttribute("rel");var n=e.getAttribute("href");if(n){var r=n.indexOf("#");if(r>=0){var a=n.slice(r+1);a.startsWith(w.citeNoteIdPrefix)?this.referenceAnchors[a]||(this.referenceAnchors[a]=e):this.currentReference&&a.startsWith(w.citeRefIdPrefix)&&this.currentReference.backLinks.push(n)}}this.makeSchemeless(e,"href",n)}},{key:"prepareOrderedList",value:function(e,t){this.isIndicatorOfAReferenceSection(e,t)&&(this.foundIndicatorOfAReferenceSection=!0)}},{key:"prepareListItem",value:function(e,t){if(t&&t.startsWith(w.citeNoteIdPrefix)){var i=new A(e,t);this.currentReference=i,this.references.push(i)}}},{key:"prepareSpan",value:function(e,t){this.currentReference&&t&&w.referenceClassRegex.test(t)&&(this.currentReference.textElement=e)}},{key:"prepareInfobox",value:function(e){var t=e.element,i=e.isInfoBox,n=this.metadata.plainTitle,r=i?this.i18n("info-box-title"):this.i18n("table-title-other"),a=this.i18n("info-box-close-text"),o=i?b.CLASS.TABLE_INFOBOX:b.CLASS.TABLE_OTHER,s=b.getTableHeaderTextArray(this.doc,t,n),l=this.i18n("table-collapse"),c=this.i18n("table-expand");(s.length||i)&&b.prepareTable(t,this.doc,n,r,o,s,a,l,c)}},{key:"markInfobox",value:function(e,t,i){if(!this.currentInfobox){var n=w.infoboxClassRegex.test(t);if((!i||n)&&!w.infoboxClassExclusionRegex.test(t)){var r;try{r="none"===e.style.display}catch(e){r=!0}r||(this.currentInfobox=e,this.infoboxes.push({element:e,isInfoBox:n}))}}}},{key:"prepareDiv",value:function(e,t){this.markInfobox(e,t,!0)}},{key:"prepareTable",value:function(e,t){this.markInfobox(e,t,!1)}},{key:"i18n",value:function(e){return this.localizer?this.localizer.i18n(e):e}},{key:"hasReferenceSections",get:function(){return 0!==Object.keys(this.referenceSections).length}}],[{key:"promise",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,a=new t(e,i,n,r);return a.promise}}]),t}(u);e.exports=S},130:function(e,t,i){(function(t){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var r=i(132),a=i(133),o=function(){function e(t,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.doc=t,this.treeWalker=t.createTreeWalker(i||t),this.processingTime=0}var i,o,s;return i=e,(o=[{key:"process",value:function(e){}},{key:"finalizeStep",value:function(){}},{key:"workFor",value:function(e){for(var t,i=!0,n=Date.now();t=this.treeWalker.nextNode();)if(this.process(t),-1!==e&&Date.now()-n>=e){i=!1;break}var r=Date.now();return this.processingTime+=r-n,i}},{key:"workSync",value:function(){this.workFor(-1)}},{key:"finalizeFor",value:function(e){for(var t=!0,i=Date.now();this.finalizeStep();)if(-1!==e&&Date.now()-i>=e){t=!1;break}var n=Date.now();return this.processingTime+=n-i,t}},{key:"finalizeSync",value:function(){this.finalizeFor(-1)}},{key:"_doWorkInChunks",value:function(e,i){var n=this;try{e()?t(i):t((function(){return n._doWorkInChunks(e,i)}))}catch(e){t((function(){return i(e)}))}}},{key:"promise",get:function(){var e=this,t=a.MAX_MS_PER_TICK;return new r((function(i,n){e._doWorkInChunks((function(){return e.workFor(t)}),(function(r){if(r)return n(r);e._doWorkInChunks((function(){return e.finalizeFor(t)}),(function(t){if(t)return n(t);i(e)}))}))}))}}])&&n(i.prototype,o),s&&n(i,s),e}();e.exports=o}).call(this,i(93).setImmediate)},131:function(e,t,i){(function(e,t){!function(e,i){"use strict";if(!e.setImmediate){var n,r,a,o,s,l=1,c={},u=!1,d=e.document,p=Object.getPrototypeOf&&Object.getPrototypeOf(e);p=p&&p.setTimeout?p:e,"[object process]"==={}.toString.call(e.process)?n=function(e){t.nextTick((function(){h(e)}))}:!function(){if(e.postMessage&&!e.importScripts){var t=!0,i=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=i,t}}()?e.MessageChannel?((a=new MessageChannel).port1.onmessage=function(e){h(e.data)},n=function(e){a.port2.postMessage(e)}):d&&"onreadystatechange"in d.createElement("script")?(r=d.documentElement,n=function(e){var t=d.createElement("script");t.onreadystatechange=function(){h(e),t.onreadystatechange=null,r.removeChild(t),t=null},r.appendChild(t)}):n=function(e){setTimeout(h,0,e)}:(o="setImmediate$"+Math.random()+"$",s=function(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(o)&&h(+t.data.slice(o.length))},e.addEventListener?e.addEventListener("message",s,!1):e.attachEvent("onmessage",s),n=function(t){e.postMessage(o+t,"*")}),p.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),i=0;i<t.length;i++)t[i]=arguments[i+1];var r={callback:e,args:t};return c[l]=r,n(l),l++},p.clearImmediate=f}function f(e){delete c[e]}function h(e){if(u)setTimeout(h,0,e);else{var t=c[e];if(t){u=!0;try{!function(e){var t=e.callback,i=e.args;switch(i.length){case 0:t();break;case 1:t(i[0]);break;case 2:t(i[0],i[1]);break;case 3:t(i[0],i[1],i[2]);break;default:t.apply(void 0,i)}}(t)}finally{f(e),u=!1}}}}}("undefined"==typeof self?void 0===e?this:e:self)}).call(this,i(48),i(61))},132:function(e,t,i){(function(t,i,n){var r;r=function(){var e,r,a;return function e(t,i,n){function r(o,s){if(!i[o]){if(!t[o]){var l="function"==typeof _dereq_&&_dereq_;if(!s&&l)return l(o,!0);if(a)return a(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var u=i[o]={exports:{}};t[o][0].call(u.exports,(function(e){var i=t[o][1][e];return r(i||e)}),u,u.exports,e,t,i,n)}return i[o].exports}for(var a="function"==typeof _dereq_&&_dereq_,o=0;o<n.length;o++)r(n[o]);return r}({1:[function(e,t,i){"use strict";t.exports=function(e){var t=e._SomePromiseArray;function i(e){var i=new t(e),n=i.promise();return i.setHowMany(1),i.setUnwrap(),i.init(),n}e.any=function(e){return i(e)},e.prototype.any=function(){return i(this)}}},{}],2:[function(e,i,n){"use strict";var r;try{throw new Error}catch(e){r=e}var a=e("./schedule"),o=e("./queue");function s(){this._customScheduler=!1,this._isTickUsed=!1,this._lateQueue=new o(16),this._normalQueue=new o(16),this._haveDrainedQueues=!1;var e=this;this.drainQueues=function(){e._drainQueues()},this._schedule=a}function l(e){for(;e.length()>0;)c(e)}function c(e){var t=e.shift();if("function"!=typeof t)t._settlePromises();else{var i=e.shift(),n=e.shift();t.call(i,n)}}s.prototype.setScheduler=function(e){var t=this._schedule;return this._schedule=e,this._customScheduler=!0,t},s.prototype.hasCustomScheduler=function(){return this._customScheduler},s.prototype.haveItemsQueued=function(){return this._isTickUsed||this._haveDrainedQueues},s.prototype.fatalError=function(e,i){i?(t.stderr.write("Fatal "+(e instanceof Error?e.stack:e)+"\n"),t.exit(2)):this.throwLater(e)},s.prototype.throwLater=function(e,t){if(1===arguments.length&&(t=e,e=function(){throw t}),"undefined"!=typeof setTimeout)setTimeout((function(){e(t)}),0);else try{this._schedule((function(){e(t)}))}catch(e){throw new Error("No async scheduler available\n\n    See http://goo.gl/MqrFmX\n")}},s.prototype.invokeLater=function(e,t,i){this._lateQueue.push(e,t,i),this._queueTick()},s.prototype.invoke=function(e,t,i){this._normalQueue.push(e,t,i),this._queueTick()},s.prototype.settlePromises=function(e){this._normalQueue._pushOne(e),this._queueTick()},s.prototype._drainQueues=function(){l(this._normalQueue),this._reset(),this._haveDrainedQueues=!0,l(this._lateQueue)},s.prototype._queueTick=function(){this._isTickUsed||(this._isTickUsed=!0,this._schedule(this.drainQueues))},s.prototype._reset=function(){this._isTickUsed=!1},i.exports=s,i.exports.firstLineError=r},{"./queue":26,"./schedule":29}],3:[function(e,t,i){"use strict";t.exports=function(e,t,i,n){var r=!1,a=function(e,t){this._reject(t)},o=function(e,t){t.promiseRejectionQueued=!0,t.bindingPromise._then(a,a,null,this,e)},s=function(e,t){0==(50397184&this._bitField)&&this._resolveCallback(t.target)},l=function(e,t){t.promiseRejectionQueued||this._reject(e)};e.prototype.bind=function(a){r||(r=!0,e.prototype._propagateFrom=n.propagateFromFunction(),e.prototype._boundValue=n.boundValueFunction());var c=i(a),u=new e(t);u._propagateFrom(this,1);var d=this._target();if(u._setBoundTo(c),c instanceof e){var p={promiseRejectionQueued:!1,promise:u,target:d,bindingPromise:c};d._then(t,o,void 0,u,p),c._then(s,l,void 0,u,p),u._setOnCancel(c)}else u._resolveCallback(d);return u},e.prototype._setBoundTo=function(e){void 0!==e?(this._bitField=2097152|this._bitField,this._boundTo=e):this._bitField=-2097153&this._bitField},e.prototype._isBound=function(){return 2097152==(2097152&this._bitField)},e.bind=function(t,i){return e.resolve(i).bind(t)}}},{}],4:[function(e,t,i){"use strict";var n;"undefined"!=typeof Promise&&(n=Promise);var r=e("./promise")();r.noConflict=function(){try{Promise===r&&(Promise=n)}catch(e){}return r},t.exports=r},{"./promise":22}],5:[function(e,t,i){"use strict";var n=Object.create;if(n){var r=n(null),a=n(null);r[" size"]=a[" size"]=0}t.exports=function(t){var i=e("./util"),n=i.canEvaluate;function r(e){return function(e,n){var r;if(null!=e&&(r=e[n]),"function"!=typeof r){var a="Object "+i.classString(e)+" has no method '"+i.toString(n)+"'";throw new t.TypeError(a)}return r}(e,this.pop()).apply(e,this)}function a(e){return e[this]}function o(e){var t=+this;return t<0&&(t=Math.max(0,t+e.length)),e[t]}i.isIdentifier,t.prototype.call=function(e){var t=[].slice.call(arguments,1);return t.push(e),this._then(r,void 0,void 0,t,void 0)},t.prototype.get=function(e){var t;if("number"==typeof e)t=o;else if(n){var i=(void 0)(e);t=null!==i?i:a}else t=a;return this._then(t,void 0,void 0,e,void 0)}}},{"./util":36}],6:[function(e,t,i){"use strict";t.exports=function(t,i,n,r){var a=e("./util"),o=a.tryCatch,s=a.errorObj,l=t._async;t.prototype.break=t.prototype.cancel=function(){if(!r.cancellation())return this._warn("cancellation is disabled");for(var e=this,t=e;e._isCancellable();){if(!e._cancelBy(t)){t._isFollowing()?t._followee().cancel():t._cancelBranched();break}var i=e._cancellationParent;if(null==i||!i._isCancellable()){e._isFollowing()?e._followee().cancel():e._cancelBranched();break}e._isFollowing()&&e._followee().cancel(),e._setWillBeCancelled(),t=e,e=i}},t.prototype._branchHasCancelled=function(){this._branchesRemainingToCancel--},t.prototype._enoughBranchesHaveCancelled=function(){return void 0===this._branchesRemainingToCancel||this._branchesRemainingToCancel<=0},t.prototype._cancelBy=function(e){return e===this?(this._branchesRemainingToCancel=0,this._invokeOnCancel(),!0):(this._branchHasCancelled(),!!this._enoughBranchesHaveCancelled()&&(this._invokeOnCancel(),!0))},t.prototype._cancelBranched=function(){this._enoughBranchesHaveCancelled()&&this._cancel()},t.prototype._cancel=function(){this._isCancellable()&&(this._setCancelled(),l.invoke(this._cancelPromises,this,void 0))},t.prototype._cancelPromises=function(){this._length()>0&&this._settlePromises()},t.prototype._unsetOnCancel=function(){this._onCancelField=void 0},t.prototype._isCancellable=function(){return this.isPending()&&!this._isCancelled()},t.prototype.isCancellable=function(){return this.isPending()&&!this.isCancelled()},t.prototype._doInvokeOnCancel=function(e,t){if(a.isArray(e))for(var i=0;i<e.length;++i)this._doInvokeOnCancel(e[i],t);else if(void 0!==e)if("function"==typeof e){if(!t){var n=o(e).call(this._boundValue());n===s&&(this._attachExtraTrace(n.e),l.throwLater(n.e))}}else e._resultCancelled(this)},t.prototype._invokeOnCancel=function(){var e=this._onCancel();this._unsetOnCancel(),l.invoke(this._doInvokeOnCancel,this,e)},t.prototype._invokeInternalOnCancel=function(){this._isCancellable()&&(this._doInvokeOnCancel(this._onCancel(),!0),this._unsetOnCancel())},t.prototype._resultCancelled=function(){this.cancel()}}},{"./util":36}],7:[function(e,t,i){"use strict";t.exports=function(t){var i=e("./util"),n=e("./es5").keys,r=i.tryCatch,a=i.errorObj;return function(e,o,s){return function(l){var c=s._boundValue();e:for(var u=0;u<e.length;++u){var d=e[u];if(d===Error||null!=d&&d.prototype instanceof Error){if(l instanceof d)return r(o).call(c,l)}else if("function"==typeof d){var p=r(d).call(c,l);if(p===a)return p;if(p)return r(o).call(c,l)}else if(i.isObject(l)){for(var f=n(d),h=0;h<f.length;++h){var g=f[h];if(d[g]!=l[g])continue e}return r(o).call(c,l)}}return t}}}},{"./es5":13,"./util":36}],8:[function(e,t,i){"use strict";t.exports=function(e){var t=!1,i=[];function n(){this._trace=new n.CapturedTrace(r())}function r(){var e=i.length-1;if(e>=0)return i[e]}return e.prototype._promiseCreated=function(){},e.prototype._pushContext=function(){},e.prototype._popContext=function(){return null},e._peekContext=e.prototype._peekContext=function(){},n.prototype._pushContext=function(){void 0!==this._trace&&(this._trace._promiseCreated=null,i.push(this._trace))},n.prototype._popContext=function(){if(void 0!==this._trace){var e=i.pop(),t=e._promiseCreated;return e._promiseCreated=null,t}return null},n.CapturedTrace=null,n.create=function(){if(t)return new n},n.deactivateLongStackTraces=function(){},n.activateLongStackTraces=function(){var i=e.prototype._pushContext,a=e.prototype._popContext,o=e._peekContext,s=e.prototype._peekContext,l=e.prototype._promiseCreated;n.deactivateLongStackTraces=function(){e.prototype._pushContext=i,e.prototype._popContext=a,e._peekContext=o,e.prototype._peekContext=s,e.prototype._promiseCreated=l,t=!1},t=!0,e.prototype._pushContext=n.prototype._pushContext,e.prototype._popContext=n.prototype._popContext,e._peekContext=e.prototype._peekContext=r,e.prototype._promiseCreated=function(){var e=this._peekContext();e&&null==e._promiseCreated&&(e._promiseCreated=this)}},n}},{}],9:[function(e,i,n){"use strict";i.exports=function(i,n,r,a){var o,s,l,c,u=i._async,d=e("./errors").Warning,p=e("./util"),f=e("./es5"),h=p.canAttachTrace,g=/[\\\/]bluebird[\\\/]js[\\\/](release|debug|instrumented)/,m=/\((?:timers\.js):\d+:\d+\)/,v=/[\/<\(](.+?):(\d+):(\d+)\)?\s*$/,b=null,y=null,_=!1,k=!(0==p.env("BLUEBIRD_DEBUG")),w=!(0==p.env("BLUEBIRD_WARNINGS")||!k&&!p.env("BLUEBIRD_WARNINGS")),x=!(0==p.env("BLUEBIRD_LONG_STACK_TRACES")||!k&&!p.env("BLUEBIRD_LONG_STACK_TRACES")),C=0!=p.env("BLUEBIRD_W_FORGOTTEN_RETURN")&&(w||!!p.env("BLUEBIRD_W_FORGOTTEN_RETURN"));!function(){var e=[];function t(){for(var t=0;t<e.length;++t)e[t]._notifyUnhandledRejection();n()}function n(){e.length=0}c=function(i){e.push(i),setTimeout(t,1)},f.defineProperty(i,"_unhandledRejectionCheck",{value:t}),f.defineProperty(i,"_unhandledRejectionClear",{value:n})}(),i.prototype.suppressUnhandledRejections=function(){var e=this._target();e._bitField=-1048577&e._bitField|524288},i.prototype._ensurePossibleRejectionHandled=function(){0==(524288&this._bitField)&&(this._setRejectionIsUnhandled(),c(this))},i.prototype._notifyUnhandledRejectionIsHandled=function(){W("rejectionHandled",o,void 0,this)},i.prototype._setReturnedNonUndefined=function(){this._bitField=268435456|this._bitField},i.prototype._returnedNonUndefined=function(){return 0!=(268435456&this._bitField)},i.prototype._notifyUnhandledRejection=function(){if(this._isRejectionUnhandled()){var e=this._settledValue();this._setUnhandledRejectionIsNotified(),W("unhandledRejection",s,e,this)}},i.prototype._setUnhandledRejectionIsNotified=function(){this._bitField=262144|this._bitField},i.prototype._unsetUnhandledRejectionIsNotified=function(){this._bitField=-262145&this._bitField},i.prototype._isUnhandledRejectionNotified=function(){return(262144&this._bitField)>0},i.prototype._setRejectionIsUnhandled=function(){this._bitField=1048576|this._bitField},i.prototype._unsetRejectionIsUnhandled=function(){this._bitField=-1048577&this._bitField,this._isUnhandledRejectionNotified()&&(this._unsetUnhandledRejectionIsNotified(),this._notifyUnhandledRejectionIsHandled())},i.prototype._isRejectionUnhandled=function(){return(1048576&this._bitField)>0},i.prototype._warn=function(e,t,i){return V(e,t,i||this)},i.onPossiblyUnhandledRejection=function(e){var t=i._getContext();s=p.contextBind(t,e)},i.onUnhandledRejectionHandled=function(e){var t=i._getContext();o=p.contextBind(t,e)};var E=function(){};i.longStackTraces=function(){if(u.haveItemsQueued()&&!ie.longStackTraces)throw new Error("cannot enable long stack traces after promises have been created\n\n    See http://goo.gl/MqrFmX\n");if(!ie.longStackTraces&&X()){var e=i.prototype._captureStackTrace,t=i.prototype._attachExtraTrace,r=i.prototype._dereferenceTrace;ie.longStackTraces=!0,E=function(){if(u.haveItemsQueued()&&!ie.longStackTraces)throw new Error("cannot enable long stack traces after promises have been created\n\n    See http://goo.gl/MqrFmX\n");i.prototype._captureStackTrace=e,i.prototype._attachExtraTrace=t,i.prototype._dereferenceTrace=r,n.deactivateLongStackTraces(),ie.longStackTraces=!1},i.prototype._captureStackTrace=U,i.prototype._attachExtraTrace=z,i.prototype._dereferenceTrace=H,n.activateLongStackTraces()}},i.hasLongStackTraces=function(){return ie.longStackTraces&&X()};var A={unhandledrejection:{before:function(){var e=p.global.onunhandledrejection;return p.global.onunhandledrejection=null,e},after:function(e){p.global.onunhandledrejection=e}},rejectionhandled:{before:function(){var e=p.global.onrejectionhandled;return p.global.onrejectionhandled=null,e},after:function(e){p.global.onrejectionhandled=e}}},S=function(){var e=function(e,t){if(!e)return!p.global.dispatchEvent(t);var i;try{return i=e.before(),!p.global.dispatchEvent(t)}finally{e.after(i)}};try{if("function"==typeof CustomEvent){var t=new CustomEvent("CustomEvent");return p.global.dispatchEvent(t),function(t,i){t=t.toLowerCase();var n=new CustomEvent(t,{detail:i,cancelable:!0});return f.defineProperty(n,"promise",{value:i.promise}),f.defineProperty(n,"reason",{value:i.reason}),e(A[t],n)}}return"function"==typeof Event?(t=new Event("CustomEvent"),p.global.dispatchEvent(t),function(t,i){t=t.toLowerCase();var n=new Event(t,{cancelable:!0});return n.detail=i,f.defineProperty(n,"promise",{value:i.promise}),f.defineProperty(n,"reason",{value:i.reason}),e(A[t],n)}):((t=document.createEvent("CustomEvent")).initCustomEvent("testingtheevent",!1,!0,{}),p.global.dispatchEvent(t),function(t,i){t=t.toLowerCase();var n=document.createEvent("CustomEvent");return n.initCustomEvent(t,!1,!0,i),e(A[t],n)})}catch(e){}return function(){return!1}}(),L=p.isNode?function(){return t.emit.apply(t,arguments)}:p.global?function(e){var t="on"+e.toLowerCase(),i=p.global[t];return!!i&&(i.apply(p.global,[].slice.call(arguments,1)),!0)}:function(){return!1};function T(e,t){return{promise:t}}var R={promiseCreated:T,promiseFulfilled:T,promiseRejected:T,promiseResolved:T,promiseCancelled:T,promiseChained:function(e,t,i){return{promise:t,child:i}},warning:function(e,t){return{warning:t}},unhandledRejection:function(e,t,i){return{reason:t,promise:i}},rejectionHandled:T},j=function(e){var t=!1;try{t=L.apply(null,arguments)}catch(e){u.throwLater(e),t=!0}var i=!1;try{i=S(e,R[e].apply(null,arguments))}catch(e){u.throwLater(e),i=!0}return i||t};function P(){return!1}function I(e,t,i){var n=this;try{e(t,i,(function(e){if("function"!=typeof e)throw new TypeError("onCancel must be a function, got: "+p.toString(e));n._attachCancellationCallback(e)}))}catch(e){return e}}function O(e){if(!this._isCancellable())return this;var t=this._onCancel();void 0!==t?p.isArray(t)?t.push(e):this._setOnCancel([t,e]):this._setOnCancel(e)}function $(){return this._onCancelField}function N(e){this._onCancelField=e}function F(){this._cancellationParent=void 0,this._onCancelField=void 0}function D(e,t){if(0!=(1&t)){this._cancellationParent=e;var i=e._branchesRemainingToCancel;void 0===i&&(i=0),e._branchesRemainingToCancel=i+1}0!=(2&t)&&e._isBound()&&this._setBoundTo(e._boundTo)}i.config=function(e){if("longStackTraces"in(e=Object(e))&&(e.longStackTraces?i.longStackTraces():!e.longStackTraces&&i.hasLongStackTraces()&&E()),"warnings"in e){var t=e.warnings;ie.warnings=!!t,C=ie.warnings,p.isObject(t)&&"wForgottenReturn"in t&&(C=!!t.wForgottenReturn)}if("cancellation"in e&&e.cancellation&&!ie.cancellation){if(u.haveItemsQueued())throw new Error("cannot enable cancellation after promises are in use");i.prototype._clearCancellationData=F,i.prototype._propagateFrom=D,i.prototype._onCancel=$,i.prototype._setOnCancel=N,i.prototype._attachCancellationCallback=O,i.prototype._execute=I,B=D,ie.cancellation=!0}if("monitoring"in e&&(e.monitoring&&!ie.monitoring?(ie.monitoring=!0,i.prototype._fireEvent=j):!e.monitoring&&ie.monitoring&&(ie.monitoring=!1,i.prototype._fireEvent=P)),"asyncHooks"in e&&p.nodeSupportsAsyncResource){var n=ie.asyncHooks,o=!!e.asyncHooks;n!==o&&(ie.asyncHooks=o,o?r():a())}return i},i.prototype._fireEvent=P,i.prototype._execute=function(e,t,i){try{e(t,i)}catch(e){return e}},i.prototype._onCancel=function(){},i.prototype._setOnCancel=function(e){},i.prototype._attachCancellationCallback=function(e){},i.prototype._captureStackTrace=function(){},i.prototype._attachExtraTrace=function(){},i.prototype._dereferenceTrace=function(){},i.prototype._clearCancellationData=function(){},i.prototype._propagateFrom=function(e,t){};var B=function(e,t){0!=(2&t)&&e._isBound()&&this._setBoundTo(e._boundTo)};function M(){var e=this._boundTo;return void 0!==e&&e instanceof i?e.isFulfilled()?e.value():void 0:e}function U(){this._trace=new ee(this._peekContext())}function z(e,t){if(h(e)){var i=this._trace;if(void 0!==i&&t&&(i=i._parent),void 0!==i)i.attachExtraTrace(e);else if(!e.__stackCleaned__){var n=Y(e);p.notEnumerableProp(e,"stack",n.message+"\n"+n.stack.join("\n")),p.notEnumerableProp(e,"__stackCleaned__",!0)}}}function H(){this._trace=void 0}function V(e,t,n){if(ie.warnings){var r,a=new d(e);if(t)n._attachExtraTrace(a);else if(ie.longStackTraces&&(r=i._peekContext()))r.attachExtraTrace(a);else{var o=Y(a);a.stack=o.message+"\n"+o.stack.join("\n")}j("warning",a)||G(a,"",!0)}}function q(e){for(var t=[],i=0;i<e.length;++i){var n=e[i],r="    (No stack trace)"===n||b.test(n),a=r&&Z(n);r&&!a&&(_&&" "!==n.charAt(0)&&(n="    "+n),t.push(n))}return t}function Y(e){var t=e.stack,i=e.toString();return t="string"==typeof t&&t.length>0?function(e){for(var t=e.stack.replace(/\s+$/g,"").split("\n"),i=0;i<t.length;++i){var n=t[i];if("    (No stack trace)"===n||b.test(n))break}return i>0&&"SyntaxError"!=e.name&&(t=t.slice(i)),t}(e):["    (No stack trace)"],{message:i,stack:"SyntaxError"==e.name?t:q(t)}}function G(e,t,i){if("undefined"!=typeof console){var n;if(p.isObject(e)){var r=e.stack;n=t+y(r,e)}else n=t+String(e);"function"==typeof l?l(n,i):"function"!=typeof console.log&&"object"!=typeof console.log||console.log(n)}}function W(e,t,i,n){var r=!1;try{"function"==typeof t&&(r=!0,"rejectionHandled"===e?t(n):t(i,n))}catch(e){u.throwLater(e)}"unhandledRejection"===e?j(e,i,n)||r||G(i,"Unhandled rejection "):j(e,n)}function K(e){var t;if("function"==typeof e)t="[function "+(e.name||"anonymous")+"]";else{if(t=e&&"function"==typeof e.toString?e.toString():p.toString(e),/\[object [a-zA-Z0-9$_]+\]/.test(t))try{t=JSON.stringify(e)}catch(e){}0===t.length&&(t="(empty array)")}return"(<"+function(e){return e.length<41?e:e.substr(0,38)+"..."}(t)+">, no stack trace)"}function X(){return"function"==typeof te}var Z=function(){return!1},Q=/[\/<\(]([^:\/]+):(\d+):(?:\d+)\)?\s*$/;function J(e){var t=e.match(Q);if(t)return{fileName:t[1],line:parseInt(t[2],10)}}function ee(e){this._parent=e,this._promisesCreated=0;var t=this._length=1+(void 0===e?0:e._length);te(this,ee),t>32&&this.uncycle()}p.inherits(ee,Error),n.CapturedTrace=ee,ee.prototype.uncycle=function(){var e=this._length;if(!(e<2)){for(var t=[],i={},n=0,r=this;void 0!==r;++n)t.push(r),r=r._parent;for(n=(e=this._length=n)-1;n>=0;--n){var a=t[n].stack;void 0===i[a]&&(i[a]=n)}for(n=0;n<e;++n){var o=i[t[n].stack];if(void 0!==o&&o!==n){o>0&&(t[o-1]._parent=void 0,t[o-1]._length=1),t[n]._parent=void 0,t[n]._length=1;var s=n>0?t[n-1]:this;o<e-1?(s._parent=t[o+1],s._parent.uncycle(),s._length=s._parent._length+1):(s._parent=void 0,s._length=1);for(var l=s._length+1,c=n-2;c>=0;--c)t[c]._length=l,l++;return}}}},ee.prototype.attachExtraTrace=function(e){if(!e.__stackCleaned__){this.uncycle();for(var t=Y(e),i=t.message,n=[t.stack],r=this;void 0!==r;)n.push(q(r.stack.split("\n"))),r=r._parent;!function(e){for(var t=e[0],i=1;i<e.length;++i){for(var n=e[i],r=t.length-1,a=t[r],o=-1,s=n.length-1;s>=0;--s)if(n[s]===a){o=s;break}for(s=o;s>=0;--s){var l=n[s];if(t[r]!==l)break;t.pop(),r--}t=n}}(n),function(e){for(var t=0;t<e.length;++t)(0===e[t].length||t+1<e.length&&e[t][0]===e[t+1][0])&&(e.splice(t,1),t--)}(n),p.notEnumerableProp(e,"stack",function(e,t){for(var i=0;i<t.length-1;++i)t[i].push("From previous event:"),t[i]=t[i].join("\n");return i<t.length&&(t[i]=t[i].join("\n")),e+"\n"+t.join("\n")}(i,n)),p.notEnumerableProp(e,"__stackCleaned__",!0)}};var te=function(){var e=/^\s*at\s*/,t=function(e,t){return"string"==typeof e?e:void 0!==t.name&&void 0!==t.message?t.toString():K(t)};if("number"==typeof Error.stackTraceLimit&&"function"==typeof Error.captureStackTrace){Error.stackTraceLimit+=6,b=e,y=t;var i=Error.captureStackTrace;return Z=function(e){return g.test(e)},function(e,t){Error.stackTraceLimit+=6,i(e,t),Error.stackTraceLimit-=6}}var n,r=new Error;if("string"==typeof r.stack&&r.stack.split("\n")[0].indexOf("stackDetection@")>=0)return b=/@/,y=t,_=!0,function(e){e.stack=(new Error).stack};try{throw new Error}catch(e){n="stack"in e}return"stack"in r||!n||"number"!=typeof Error.stackTraceLimit?(y=function(e,t){return"string"==typeof e?e:"object"!=typeof t&&"function"!=typeof t||void 0===t.name||void 0===t.message?K(t):t.toString()},null):(b=e,y=t,function(e){Error.stackTraceLimit+=6;try{throw new Error}catch(t){e.stack=t.stack}Error.stackTraceLimit-=6})}();"undefined"!=typeof console&&void 0!==console.warn&&(l=function(e){console.warn(e)},p.isNode&&t.stderr.isTTY?l=function(e,t){var i=t?"[33m":"[31m";console.warn(i+e+"[0m\n")}:p.isNode||"string"!=typeof(new Error).stack||(l=function(e,t){console.warn("%c"+e,t?"color: darkorange":"color: red")}));var ie={warnings:w,longStackTraces:!1,cancellation:!1,monitoring:!1,asyncHooks:!1};return x&&i.longStackTraces(),{asyncHooks:function(){return ie.asyncHooks},longStackTraces:function(){return ie.longStackTraces},warnings:function(){return ie.warnings},cancellation:function(){return ie.cancellation},monitoring:function(){return ie.monitoring},propagateFromFunction:function(){return B},boundValueFunction:function(){return M},checkForgottenReturns:function(e,t,i,n,r){if(void 0===e&&null!==t&&C){if(void 0!==r&&r._returnedNonUndefined())return;if(0==(65535&n._bitField))return;i&&(i+=" ");var a="",o="";if(t._trace){for(var s=t._trace.stack.split("\n"),l=q(s),c=l.length-1;c>=0;--c){var u=l[c];if(!m.test(u)){var d=u.match(v);d&&(a="at "+d[1]+":"+d[2]+":"+d[3]+" ");break}}if(l.length>0){var p=l[0];for(c=0;c<s.length;++c)if(s[c]===p){c>0&&(o="\n"+s[c-1]);break}}}var f="a promise was created in a "+i+"handler "+a+"but was not returned from it, see http://goo.gl/rRqMUw"+o;n._warn(f,!0,t)}},setBounds:function(e,t){if(X()){for(var i,n,r=(e.stack||"").split("\n"),a=(t.stack||"").split("\n"),o=-1,s=-1,l=0;l<r.length;++l)if(c=J(r[l])){i=c.fileName,o=c.line;break}for(l=0;l<a.length;++l){var c;if(c=J(a[l])){n=c.fileName,s=c.line;break}}o<0||s<0||!i||!n||i!==n||o>=s||(Z=function(e){if(g.test(e))return!0;var t=J(e);return!!(t&&t.fileName===i&&o<=t.line&&t.line<=s)})}},warn:V,deprecated:function(e,t){var i=e+" is deprecated and will be removed in a future version.";return t&&(i+=" Use "+t+" instead."),V(i)},CapturedTrace:ee,fireDomEvent:S,fireGlobalEvent:L}}},{"./errors":12,"./es5":13,"./util":36}],10:[function(e,t,i){"use strict";t.exports=function(e){function t(){return this.value}function i(){throw this.reason}e.prototype.return=e.prototype.thenReturn=function(i){return i instanceof e&&i.suppressUnhandledRejections(),this._then(t,void 0,void 0,{value:i},void 0)},e.prototype.throw=e.prototype.thenThrow=function(e){return this._then(i,void 0,void 0,{reason:e},void 0)},e.prototype.catchThrow=function(e){if(arguments.length<=1)return this._then(void 0,i,void 0,{reason:e},void 0);var t=arguments[1],n=function(){throw t};return this.caught(e,n)},e.prototype.catchReturn=function(i){if(arguments.length<=1)return i instanceof e&&i.suppressUnhandledRejections(),this._then(void 0,t,void 0,{value:i},void 0);var n=arguments[1];n instanceof e&&n.suppressUnhandledRejections();var r=function(){return n};return this.caught(i,r)}}},{}],11:[function(e,t,i){"use strict";t.exports=function(e,t){var i=e.reduce,n=e.all;function r(){return n(this)}e.prototype.each=function(e){return i(this,e,t,0)._then(r,void 0,void 0,this,void 0)},e.prototype.mapSeries=function(e){return i(this,e,t,t)},e.each=function(e,n){return i(e,n,t,0)._then(r,void 0,void 0,e,void 0)},e.mapSeries=function(e,n){return i(e,n,t,t)}}},{}],12:[function(e,t,i){"use strict";var n,r,a=e("./es5"),o=a.freeze,s=e("./util"),l=s.inherits,c=s.notEnumerableProp;function u(e,t){function i(n){if(!(this instanceof i))return new i(n);c(this,"message","string"==typeof n?n:t),c(this,"name",e),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):Error.call(this)}return l(i,Error),i}var d=u("Warning","warning"),p=u("CancellationError","cancellation error"),f=u("TimeoutError","timeout error"),h=u("AggregateError","aggregate error");try{n=TypeError,r=RangeError}catch(e){n=u("TypeError","type error"),r=u("RangeError","range error")}for(var g="join pop push shift unshift slice filter forEach some every map indexOf lastIndexOf reduce reduceRight sort reverse".split(" "),m=0;m<g.length;++m)"function"==typeof Array.prototype[g[m]]&&(h.prototype[g[m]]=Array.prototype[g[m]]);a.defineProperty(h.prototype,"length",{value:0,configurable:!1,writable:!0,enumerable:!0}),h.prototype.isOperational=!0;var v=0;function b(e){if(!(this instanceof b))return new b(e);c(this,"name","OperationalError"),c(this,"message",e),this.cause=e,this.isOperational=!0,e instanceof Error?(c(this,"message",e.message),c(this,"stack",e.stack)):Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}h.prototype.toString=function(){var e=Array(4*v+1).join(" "),t="\n"+e+"AggregateError of:\n";v++,e=Array(4*v+1).join(" ");for(var i=0;i<this.length;++i){for(var n=this[i]===this?"[Circular AggregateError]":this[i]+"",r=n.split("\n"),a=0;a<r.length;++a)r[a]=e+r[a];t+=(n=r.join("\n"))+"\n"}return v--,t},l(b,Error);var y=Error.__BluebirdErrorTypes__;y||(y=o({CancellationError:p,TimeoutError:f,OperationalError:b,RejectionError:b,AggregateError:h}),a.defineProperty(Error,"__BluebirdErrorTypes__",{value:y,writable:!1,enumerable:!1,configurable:!1})),t.exports={Error:Error,TypeError:n,RangeError:r,CancellationError:y.CancellationError,OperationalError:y.OperationalError,TimeoutError:y.TimeoutError,AggregateError:y.AggregateError,Warning:d}},{"./es5":13,"./util":36}],13:[function(e,t,i){var n=function(){"use strict";return void 0===this}();if(n)t.exports={freeze:Object.freeze,defineProperty:Object.defineProperty,getDescriptor:Object.getOwnPropertyDescriptor,keys:Object.keys,names:Object.getOwnPropertyNames,getPrototypeOf:Object.getPrototypeOf,isArray:Array.isArray,isES5:n,propertyIsWritable:function(e,t){var i=Object.getOwnPropertyDescriptor(e,t);return!(i&&!i.writable&&!i.set)}};else{var r={}.hasOwnProperty,a={}.toString,o={}.constructor.prototype,s=function(e){var t=[];for(var i in e)r.call(e,i)&&t.push(i);return t};t.exports={isArray:function(e){try{return"[object Array]"===a.call(e)}catch(e){return!1}},keys:s,names:s,defineProperty:function(e,t,i){return e[t]=i.value,e},getDescriptor:function(e,t){return{value:e[t]}},freeze:function(e){return e},getPrototypeOf:function(e){try{return Object(e).constructor.prototype}catch(e){return o}},isES5:n,propertyIsWritable:function(){return!0}}}},{}],14:[function(e,t,i){"use strict";t.exports=function(e,t){var i=e.map;e.prototype.filter=function(e,n){return i(this,e,n,t)},e.filter=function(e,n,r){return i(e,n,r,t)}}},{}],15:[function(e,t,i){"use strict";t.exports=function(t,i,n){var r=e("./util"),a=t.CancellationError,o=r.errorObj,s=e("./catch_filter")(n);function l(e,t,i){this.promise=e,this.type=t,this.handler=i,this.called=!1,this.cancelPromise=null}function c(e){this.finallyHandler=e}function u(e,t){return null!=e.cancelPromise&&(arguments.length>1?e.cancelPromise._reject(t):e.cancelPromise._cancel(),e.cancelPromise=null,!0)}function d(){return f.call(this,this.promise._target()._settledValue())}function p(e){if(!u(this,e))return o.e=e,o}function f(e){var r=this.promise,s=this.handler;if(!this.called){this.called=!0;var l=this.isFinallyHandler()?s.call(r._boundValue()):s.call(r._boundValue(),e);if(l===n)return l;if(void 0!==l){r._setReturnedNonUndefined();var f=i(l,r);if(f instanceof t){if(null!=this.cancelPromise){if(f._isCancelled()){var h=new a("late cancellation observer");return r._attachExtraTrace(h),o.e=h,o}f.isPending()&&f._attachCancellationCallback(new c(this))}return f._then(d,p,void 0,this,void 0)}}}return r.isRejected()?(u(this),o.e=e,o):(u(this),e)}return l.prototype.isFinallyHandler=function(){return 0===this.type},c.prototype._resultCancelled=function(){u(this.finallyHandler)},t.prototype._passThrough=function(e,t,i,n){return"function"!=typeof e?this.then():this._then(i,n,void 0,new l(this,t,e),void 0)},t.prototype.lastly=t.prototype.finally=function(e){return this._passThrough(e,0,f,f)},t.prototype.tap=function(e){return this._passThrough(e,1,f)},t.prototype.tapCatch=function(e){var i=arguments.length;if(1===i)return this._passThrough(e,1,void 0,f);var n,a=new Array(i-1),o=0;for(n=0;n<i-1;++n){var l=arguments[n];if(!r.isObject(l))return t.reject(new TypeError("tapCatch statement predicate: expecting an object but got "+r.classString(l)));a[o++]=l}a.length=o;var c=arguments[n];return this._passThrough(s(a,c,this),1,void 0,f)},l}},{"./catch_filter":7,"./util":36}],16:[function(e,t,i){"use strict";t.exports=function(t,i,n,r,a,o){var s=e("./errors").TypeError,l=e("./util"),c=l.errorObj,u=l.tryCatch,d=[];function p(e,i,r,a){if(o.cancellation()){var s=new t(n),l=this._finallyPromise=new t(n);this._promise=s.lastly((function(){return l})),s._captureStackTrace(),s._setOnCancel(this)}else(this._promise=new t(n))._captureStackTrace();this._stack=a,this._generatorFunction=e,this._receiver=i,this._generator=void 0,this._yieldHandlers="function"==typeof r?[r].concat(d):d,this._yieldedPromise=null,this._cancellationPhase=!1}l.inherits(p,a),p.prototype._isResolved=function(){return null===this._promise},p.prototype._cleanup=function(){this._promise=this._generator=null,o.cancellation()&&null!==this._finallyPromise&&(this._finallyPromise._fulfill(),this._finallyPromise=null)},p.prototype._promiseCancelled=function(){if(!this._isResolved()){var e;if(void 0!==this._generator.return)this._promise._pushContext(),e=u(this._generator.return).call(this._generator,void 0),this._promise._popContext();else{var i=new t.CancellationError("generator .return() sentinel");t.coroutine.returnSentinel=i,this._promise._attachExtraTrace(i),this._promise._pushContext(),e=u(this._generator.throw).call(this._generator,i),this._promise._popContext()}this._cancellationPhase=!0,this._yieldedPromise=null,this._continue(e)}},p.prototype._promiseFulfilled=function(e){this._yieldedPromise=null,this._promise._pushContext();var t=u(this._generator.next).call(this._generator,e);this._promise._popContext(),this._continue(t)},p.prototype._promiseRejected=function(e){this._yieldedPromise=null,this._promise._attachExtraTrace(e),this._promise._pushContext();var t=u(this._generator.throw).call(this._generator,e);this._promise._popContext(),this._continue(t)},p.prototype._resultCancelled=function(){if(this._yieldedPromise instanceof t){var e=this._yieldedPromise;this._yieldedPromise=null,e.cancel()}},p.prototype.promise=function(){return this._promise},p.prototype._run=function(){this._generator=this._generatorFunction.call(this._receiver),this._receiver=this._generatorFunction=void 0,this._promiseFulfilled(void 0)},p.prototype._continue=function(e){var i=this._promise;if(e===c)return this._cleanup(),this._cancellationPhase?i.cancel():i._rejectCallback(e.e,!1);var n=e.value;if(!0===e.done)return this._cleanup(),this._cancellationPhase?i.cancel():i._resolveCallback(n);var a=r(n,this._promise);if(a instanceof t||null!==(a=function(e,i,n){for(var a=0;a<i.length;++a){n._pushContext();var o=u(i[a])(e);if(n._popContext(),o===c){n._pushContext();var s=t.reject(c.e);return n._popContext(),s}var l=r(o,n);if(l instanceof t)return l}return null}(a,this._yieldHandlers,this._promise))){var o=(a=a._target())._bitField;0==(50397184&o)?(this._yieldedPromise=a,a._proxy(this,null)):0!=(33554432&o)?t._async.invoke(this._promiseFulfilled,this,a._value()):0!=(16777216&o)?t._async.invoke(this._promiseRejected,this,a._reason()):this._promiseCancelled()}else this._promiseRejected(new s("A value %s was yielded that could not be treated as a promise\n\n    See http://goo.gl/MqrFmX\n\n".replace("%s",String(n))+"From coroutine:\n"+this._stack.split("\n").slice(1,-7).join("\n")))},t.coroutine=function(e,t){if("function"!=typeof e)throw new s("generatorFunction must be a function\n\n    See http://goo.gl/MqrFmX\n");var i=Object(t).yieldHandler,n=p,r=(new Error).stack;return function(){var t=e.apply(this,arguments),a=new n(void 0,void 0,i,r),o=a.promise();return a._generator=t,a._promiseFulfilled(void 0),o}},t.coroutine.addYieldHandler=function(e){if("function"!=typeof e)throw new s("expecting a function but got "+l.classString(e));d.push(e)},t.spawn=function(e){if(o.deprecated("Promise.spawn()","Promise.coroutine()"),"function"!=typeof e)return i("generatorFunction must be a function\n\n    See http://goo.gl/MqrFmX\n");var n=new p(e,this),r=n.promise();return n._run(t.spawn),r}}},{"./errors":12,"./util":36}],17:[function(e,t,i){"use strict";t.exports=function(t,i,n,r,a){var o=e("./util");o.canEvaluate,o.tryCatch,o.errorObj,t.join=function(){var e,t=arguments.length-1;t>0&&"function"==typeof arguments[t]&&(e=arguments[t]);var n=[].slice.call(arguments);e&&n.pop();var r=new i(n).promise();return void 0!==e?r.spread(e):r}}},{"./util":36}],18:[function(e,t,i){"use strict";t.exports=function(t,i,n,r,a,o){var s=e("./util"),l=s.tryCatch,c=s.errorObj,u=t._async;function d(e,i,n,r){this.constructor$(e),this._promise._captureStackTrace();var o=t._getContext();if(this._callback=s.contextBind(o,i),this._preservedValues=r===a?new Array(this.length()):null,this._limit=n,this._inFlight=0,this._queue=[],u.invoke(this._asyncInit,this,void 0),s.isArray(e))for(var l=0;l<e.length;++l){var c=e[l];c instanceof t&&c.suppressUnhandledRejections()}}function p(e,i,r,a){if("function"!=typeof i)return n("expecting a function but got "+s.classString(i));var o=0;if(void 0!==r){if("object"!=typeof r||null===r)return t.reject(new TypeError("options argument must be an object but it is "+s.classString(r)));if("number"!=typeof r.concurrency)return t.reject(new TypeError("'concurrency' must be a number but it is "+s.classString(r.concurrency)));o=r.concurrency}return new d(e,i,o="number"==typeof o&&isFinite(o)&&o>=1?o:0,a).promise()}s.inherits(d,i),d.prototype._asyncInit=function(){this._init$(void 0,-2)},d.prototype._init=function(){},d.prototype._promiseFulfilled=function(e,i){var n=this._values,a=this.length(),s=this._preservedValues,u=this._limit;if(i<0){if(n[i=-1*i-1]=e,u>=1&&(this._inFlight--,this._drainQueue(),this._isResolved()))return!0}else{if(u>=1&&this._inFlight>=u)return n[i]=e,this._queue.push(i),!1;null!==s&&(s[i]=e);var d=this._promise,p=this._callback,f=d._boundValue();d._pushContext();var h=l(p).call(f,e,i,a),g=d._popContext();if(o.checkForgottenReturns(h,g,null!==s?"Promise.filter":"Promise.map",d),h===c)return this._reject(h.e),!0;var m=r(h,this._promise);if(m instanceof t){var v=(m=m._target())._bitField;if(0==(50397184&v))return u>=1&&this._inFlight++,n[i]=m,m._proxy(this,-1*(i+1)),!1;if(0==(33554432&v))return 0!=(16777216&v)?(this._reject(m._reason()),!0):(this._cancel(),!0);h=m._value()}n[i]=h}return++this._totalResolved>=a&&(null!==s?this._filter(n,s):this._resolve(n),!0)},d.prototype._drainQueue=function(){for(var e=this._queue,t=this._limit,i=this._values;e.length>0&&this._inFlight<t;){if(this._isResolved())return;var n=e.pop();this._promiseFulfilled(i[n],n)}},d.prototype._filter=function(e,t){for(var i=t.length,n=new Array(i),r=0,a=0;a<i;++a)e[a]&&(n[r++]=t[a]);n.length=r,this._resolve(n)},d.prototype.preservedValues=function(){return this._preservedValues},t.prototype.map=function(e,t){return p(this,e,t,null)},t.map=function(e,t,i,n){return p(e,t,i,n)}}},{"./util":36}],19:[function(e,t,i){"use strict";t.exports=function(t,i,n,r,a){var o=e("./util"),s=o.tryCatch;t.method=function(e){if("function"!=typeof e)throw new t.TypeError("expecting a function but got "+o.classString(e));return function(){var n=new t(i);n._captureStackTrace(),n._pushContext();var r=s(e).apply(this,arguments),o=n._popContext();return a.checkForgottenReturns(r,o,"Promise.method",n),n._resolveFromSyncValue(r),n}},t.attempt=t.try=function(e){if("function"!=typeof e)return r("expecting a function but got "+o.classString(e));var n,l=new t(i);if(l._captureStackTrace(),l._pushContext(),arguments.length>1){a.deprecated("calling Promise.try with more than 1 argument");var c=arguments[1],u=arguments[2];n=o.isArray(c)?s(e).apply(u,c):s(e).call(u,c)}else n=s(e)();var d=l._popContext();return a.checkForgottenReturns(n,d,"Promise.try",l),l._resolveFromSyncValue(n),l},t.prototype._resolveFromSyncValue=function(e){e===o.errorObj?this._rejectCallback(e.e,!1):this._resolveCallback(e,!0)}}},{"./util":36}],20:[function(e,t,i){"use strict";var n=e("./util"),r=n.maybeWrapAsError,a=e("./errors").OperationalError,o=e("./es5"),s=/^(?:name|message|stack|cause)$/;function l(e){var t;if(function(e){return e instanceof Error&&o.getPrototypeOf(e)===Error.prototype}(e)){(t=new a(e)).name=e.name,t.message=e.message,t.stack=e.stack;for(var i=o.keys(e),r=0;r<i.length;++r){var l=i[r];s.test(l)||(t[l]=e[l])}return t}return n.markAsOriginatingFromRejection(e),e}t.exports=function(e,t){return function(i,n){if(null!==e){if(i){var a=l(r(i));e._attachExtraTrace(a),e._reject(a)}else if(t){var o=[].slice.call(arguments,1);e._fulfill(o)}else e._fulfill(n);e=null}}}},{"./errors":12,"./es5":13,"./util":36}],21:[function(e,t,i){"use strict";t.exports=function(t){var i=e("./util"),n=t._async,r=i.tryCatch,a=i.errorObj;function o(e,t){if(!i.isArray(e))return s.call(this,e,t);var o=r(t).apply(this._boundValue(),[null].concat(e));o===a&&n.throwLater(o.e)}function s(e,t){var i=this._boundValue(),o=void 0===e?r(t).call(i,null):r(t).call(i,null,e);o===a&&n.throwLater(o.e)}function l(e,t){if(!e){var i=new Error(e+"");i.cause=e,e=i}var o=r(t).call(this._boundValue(),e);o===a&&n.throwLater(o.e)}t.prototype.asCallback=t.prototype.nodeify=function(e,t){if("function"==typeof e){var i=s;void 0!==t&&Object(t).spread&&(i=o),this._then(i,l,void 0,this,e)}return this}}},{"./util":36}],22:[function(e,i,n){"use strict";i.exports=function(){var n=function(){return new b("circular promise resolution chain\n\n    See http://goo.gl/MqrFmX\n")},r=function(){return new I.PromiseInspection(this._target())},a=function(e){return I.reject(new b(e))};function o(){}var s={},l=e("./util");l.setReflectHandler(r);var c=function(){var e=t.domain;return void 0===e?null:e},u=function(){return{domain:c(),async:null}},d=l.isNode&&l.nodeSupportsAsyncResource?e("async_hooks").AsyncResource:null,p=function(){return{domain:c(),async:new d("Bluebird::Promise")}},f=l.isNode?u:function(){return null};l.notEnumerableProp(I,"_getContext",f);var h=e("./es5"),g=e("./async"),m=new g;h.defineProperty(I,"_async",{value:m});var v=e("./errors"),b=I.TypeError=v.TypeError;I.RangeError=v.RangeError;var y=I.CancellationError=v.CancellationError;I.TimeoutError=v.TimeoutError,I.OperationalError=v.OperationalError,I.RejectionError=v.OperationalError,I.AggregateError=v.AggregateError;var _=function(){},k={},w={},x=e("./thenables")(I,_),C=e("./promise_array")(I,_,x,a,o),E=e("./context")(I),A=E.create,S=e("./debuggability")(I,E,(function(){f=p,l.notEnumerableProp(I,"_getContext",p)}),(function(){f=u,l.notEnumerableProp(I,"_getContext",u)})),L=(S.CapturedTrace,e("./finally")(I,x,w)),T=e("./catch_filter")(w),R=e("./nodeback"),j=l.errorObj,P=l.tryCatch;function I(e){e!==_&&function(e,t){if(null==e||e.constructor!==I)throw new b("the promise constructor cannot be invoked directly\n\n    See http://goo.gl/MqrFmX\n");if("function"!=typeof t)throw new b("expecting a function but got "+l.classString(t))}(this,e),this._bitField=0,this._fulfillmentHandler0=void 0,this._rejectionHandler0=void 0,this._promise0=void 0,this._receiver0=void 0,this._resolveFromExecutor(e),this._promiseCreated(),this._fireEvent("promiseCreated",this)}function O(e){this.promise._resolveCallback(e)}function $(e){this.promise._rejectCallback(e,!1)}function N(e){var t=new I(_);t._fulfillmentHandler0=e,t._rejectionHandler0=e,t._promise0=e,t._receiver0=e}return I.prototype.toString=function(){return"[object Promise]"},I.prototype.caught=I.prototype.catch=function(e){var t=arguments.length;if(t>1){var i,n=new Array(t-1),r=0;for(i=0;i<t-1;++i){var o=arguments[i];if(!l.isObject(o))return a("Catch statement predicate: expecting an object but got "+l.classString(o));n[r++]=o}if(n.length=r,"function"!=typeof(e=arguments[i]))throw new b("The last argument to .catch() must be a function, got "+l.toString(e));return this.then(void 0,T(n,e,this))}return this.then(void 0,e)},I.prototype.reflect=function(){return this._then(r,r,void 0,this,void 0)},I.prototype.then=function(e,t){if(S.warnings()&&arguments.length>0&&"function"!=typeof e&&"function"!=typeof t){var i=".then() only accepts functions but was passed: "+l.classString(e);arguments.length>1&&(i+=", "+l.classString(t)),this._warn(i)}return this._then(e,t,void 0,void 0,void 0)},I.prototype.done=function(e,t){this._then(e,t,void 0,void 0,void 0)._setIsFinal()},I.prototype.spread=function(e){return"function"!=typeof e?a("expecting a function but got "+l.classString(e)):this.all()._then(e,void 0,void 0,k,void 0)},I.prototype.toJSON=function(){var e={isFulfilled:!1,isRejected:!1,fulfillmentValue:void 0,rejectionReason:void 0};return this.isFulfilled()?(e.fulfillmentValue=this.value(),e.isFulfilled=!0):this.isRejected()&&(e.rejectionReason=this.reason(),e.isRejected=!0),e},I.prototype.all=function(){return arguments.length>0&&this._warn(".all() was passed arguments but it does not take any"),new C(this).promise()},I.prototype.error=function(e){return this.caught(l.originatesFromRejection,e)},I.getNewLibraryCopy=i.exports,I.is=function(e){return e instanceof I},I.fromNode=I.fromCallback=function(e){var t=new I(_);t._captureStackTrace();var i=arguments.length>1&&!!Object(arguments[1]).multiArgs,n=P(e)(R(t,i));return n===j&&t._rejectCallback(n.e,!0),t._isFateSealed()||t._setAsyncGuaranteed(),t},I.all=function(e){return new C(e).promise()},I.cast=function(e){var t=x(e);return t instanceof I||((t=new I(_))._captureStackTrace(),t._setFulfilled(),t._rejectionHandler0=e),t},I.resolve=I.fulfilled=I.cast,I.reject=I.rejected=function(e){var t=new I(_);return t._captureStackTrace(),t._rejectCallback(e,!0),t},I.setScheduler=function(e){if("function"!=typeof e)throw new b("expecting a function but got "+l.classString(e));return m.setScheduler(e)},I.prototype._then=function(e,t,i,n,r){var a=void 0!==r,o=a?r:new I(_),s=this._target(),c=s._bitField;a||(o._propagateFrom(this,3),o._captureStackTrace(),void 0===n&&0!=(2097152&this._bitField)&&(n=0!=(50397184&c)?this._boundValue():s===this?void 0:this._boundTo),this._fireEvent("promiseChained",this,o));var u=f();if(0!=(50397184&c)){var d,p,h=s._settlePromiseCtx;0!=(33554432&c)?(p=s._rejectionHandler0,d=e):0!=(16777216&c)?(p=s._fulfillmentHandler0,d=t,s._unsetRejectionIsUnhandled()):(h=s._settlePromiseLateCancellationObserver,p=new y("late cancellation observer"),s._attachExtraTrace(p),d=t),m.invoke(h,s,{handler:l.contextBind(u,d),promise:o,receiver:n,value:p})}else s._addCallbacks(e,t,o,n,u);return o},I.prototype._length=function(){return 65535&this._bitField},I.prototype._isFateSealed=function(){return 0!=(117506048&this._bitField)},I.prototype._isFollowing=function(){return 67108864==(67108864&this._bitField)},I.prototype._setLength=function(e){this._bitField=-65536&this._bitField|65535&e},I.prototype._setFulfilled=function(){this._bitField=33554432|this._bitField,this._fireEvent("promiseFulfilled",this)},I.prototype._setRejected=function(){this._bitField=16777216|this._bitField,this._fireEvent("promiseRejected",this)},I.prototype._setFollowing=function(){this._bitField=67108864|this._bitField,this._fireEvent("promiseResolved",this)},I.prototype._setIsFinal=function(){this._bitField=4194304|this._bitField},I.prototype._isFinal=function(){return(4194304&this._bitField)>0},I.prototype._unsetCancelled=function(){this._bitField=-65537&this._bitField},I.prototype._setCancelled=function(){this._bitField=65536|this._bitField,this._fireEvent("promiseCancelled",this)},I.prototype._setWillBeCancelled=function(){this._bitField=8388608|this._bitField},I.prototype._setAsyncGuaranteed=function(){if(!m.hasCustomScheduler()){var e=this._bitField;this._bitField=e|(536870912&e)>>2^134217728}},I.prototype._setNoAsyncGuarantee=function(){this._bitField=-134217729&(536870912|this._bitField)},I.prototype._receiverAt=function(e){var t=0===e?this._receiver0:this[4*e-4+3];if(t!==s)return void 0===t&&this._isBound()?this._boundValue():t},I.prototype._promiseAt=function(e){return this[4*e-4+2]},I.prototype._fulfillmentHandlerAt=function(e){return this[4*e-4+0]},I.prototype._rejectionHandlerAt=function(e){return this[4*e-4+1]},I.prototype._boundValue=function(){},I.prototype._migrateCallback0=function(e){e._bitField;var t=e._fulfillmentHandler0,i=e._rejectionHandler0,n=e._promise0,r=e._receiverAt(0);void 0===r&&(r=s),this._addCallbacks(t,i,n,r,null)},I.prototype._migrateCallbackAt=function(e,t){var i=e._fulfillmentHandlerAt(t),n=e._rejectionHandlerAt(t),r=e._promiseAt(t),a=e._receiverAt(t);void 0===a&&(a=s),this._addCallbacks(i,n,r,a,null)},I.prototype._addCallbacks=function(e,t,i,n,r){var a=this._length();if(a>=65531&&(a=0,this._setLength(0)),0===a)this._promise0=i,this._receiver0=n,"function"==typeof e&&(this._fulfillmentHandler0=l.contextBind(r,e)),"function"==typeof t&&(this._rejectionHandler0=l.contextBind(r,t));else{var o=4*a-4;this[o+2]=i,this[o+3]=n,"function"==typeof e&&(this[o+0]=l.contextBind(r,e)),"function"==typeof t&&(this[o+1]=l.contextBind(r,t))}return this._setLength(a+1),a},I.prototype._proxy=function(e,t){this._addCallbacks(void 0,void 0,t,e,null)},I.prototype._resolveCallback=function(e,t){if(0==(117506048&this._bitField)){if(e===this)return this._rejectCallback(n(),!1);var i=x(e,this);if(!(i instanceof I))return this._fulfill(e);t&&this._propagateFrom(i,2);var r=i._target();if(r!==this){var a=r._bitField;if(0==(50397184&a)){var o=this._length();o>0&&r._migrateCallback0(this);for(var s=1;s<o;++s)r._migrateCallbackAt(this,s);this._setFollowing(),this._setLength(0),this._setFollowee(i)}else if(0!=(33554432&a))this._fulfill(r._value());else if(0!=(16777216&a))this._reject(r._reason());else{var l=new y("late cancellation observer");r._attachExtraTrace(l),this._reject(l)}}else this._reject(n())}},I.prototype._rejectCallback=function(e,t,i){var n=l.ensureErrorObject(e),r=n===e;if(!r&&!i&&S.warnings()){var a="a promise was rejected with a non-error: "+l.classString(e);this._warn(a,!0)}this._attachExtraTrace(n,!!t&&r),this._reject(e)},I.prototype._resolveFromExecutor=function(e){if(e!==_){var t=this;this._captureStackTrace(),this._pushContext();var i=!0,n=this._execute(e,(function(e){t._resolveCallback(e)}),(function(e){t._rejectCallback(e,i)}));i=!1,this._popContext(),void 0!==n&&t._rejectCallback(n,!0)}},I.prototype._settlePromiseFromHandler=function(e,t,i,n){var r=n._bitField;if(0==(65536&r)){var a;n._pushContext(),t===k?i&&"number"==typeof i.length?a=P(e).apply(this._boundValue(),i):(a=j).e=new b("cannot .spread() a non-array: "+l.classString(i)):a=P(e).call(t,i);var o=n._popContext();0==(65536&(r=n._bitField))&&(a===w?n._reject(i):a===j?n._rejectCallback(a.e,!1):(S.checkForgottenReturns(a,o,"",n,this),n._resolveCallback(a)))}},I.prototype._target=function(){for(var e=this;e._isFollowing();)e=e._followee();return e},I.prototype._followee=function(){return this._rejectionHandler0},I.prototype._setFollowee=function(e){this._rejectionHandler0=e},I.prototype._settlePromise=function(e,t,i,n){var a=e instanceof I,s=this._bitField,l=0!=(134217728&s);0!=(65536&s)?(a&&e._invokeInternalOnCancel(),i instanceof L&&i.isFinallyHandler()?(i.cancelPromise=e,P(t).call(i,n)===j&&e._reject(j.e)):t===r?e._fulfill(r.call(i)):i instanceof o?i._promiseCancelled(e):a||e instanceof C?e._cancel():i.cancel()):"function"==typeof t?a?(l&&e._setAsyncGuaranteed(),this._settlePromiseFromHandler(t,i,n,e)):t.call(i,n,e):i instanceof o?i._isResolved()||(0!=(33554432&s)?i._promiseFulfilled(n,e):i._promiseRejected(n,e)):a&&(l&&e._setAsyncGuaranteed(),0!=(33554432&s)?e._fulfill(n):e._reject(n))},I.prototype._settlePromiseLateCancellationObserver=function(e){var t=e.handler,i=e.promise,n=e.receiver,r=e.value;"function"==typeof t?i instanceof I?this._settlePromiseFromHandler(t,n,r,i):t.call(n,r,i):i instanceof I&&i._reject(r)},I.prototype._settlePromiseCtx=function(e){this._settlePromise(e.promise,e.handler,e.receiver,e.value)},I.prototype._settlePromise0=function(e,t,i){var n=this._promise0,r=this._receiverAt(0);this._promise0=void 0,this._receiver0=void 0,this._settlePromise(n,e,r,t)},I.prototype._clearCallbackDataAtIndex=function(e){var t=4*e-4;this[t+2]=this[t+3]=this[t+0]=this[t+1]=void 0},I.prototype._fulfill=function(e){var t=this._bitField;if(!((117506048&t)>>>16)){if(e===this){var i=n();return this._attachExtraTrace(i),this._reject(i)}this._setFulfilled(),this._rejectionHandler0=e,(65535&t)>0&&(0!=(134217728&t)?this._settlePromises():m.settlePromises(this),this._dereferenceTrace())}},I.prototype._reject=function(e){var t=this._bitField;if(!((117506048&t)>>>16)){if(this._setRejected(),this._fulfillmentHandler0=e,this._isFinal())return m.fatalError(e,l.isNode);(65535&t)>0?m.settlePromises(this):this._ensurePossibleRejectionHandled()}},I.prototype._fulfillPromises=function(e,t){for(var i=1;i<e;i++){var n=this._fulfillmentHandlerAt(i),r=this._promiseAt(i),a=this._receiverAt(i);this._clearCallbackDataAtIndex(i),this._settlePromise(r,n,a,t)}},I.prototype._rejectPromises=function(e,t){for(var i=1;i<e;i++){var n=this._rejectionHandlerAt(i),r=this._promiseAt(i),a=this._receiverAt(i);this._clearCallbackDataAtIndex(i),this._settlePromise(r,n,a,t)}},I.prototype._settlePromises=function(){var e=this._bitField,t=65535&e;if(t>0){if(0!=(16842752&e)){var i=this._fulfillmentHandler0;this._settlePromise0(this._rejectionHandler0,i,e),this._rejectPromises(t,i)}else{var n=this._rejectionHandler0;this._settlePromise0(this._fulfillmentHandler0,n,e),this._fulfillPromises(t,n)}this._setLength(0)}this._clearCancellationData()},I.prototype._settledValue=function(){var e=this._bitField;return 0!=(33554432&e)?this._rejectionHandler0:0!=(16777216&e)?this._fulfillmentHandler0:void 0},"undefined"!=typeof Symbol&&Symbol.toStringTag&&h.defineProperty(I.prototype,Symbol.toStringTag,{get:function(){return"Object"}}),I.defer=I.pending=function(){return S.deprecated("Promise.defer","new Promise"),{promise:new I(_),resolve:O,reject:$}},l.notEnumerableProp(I,"_makeSelfResolutionError",n),e("./method")(I,_,x,a,S),e("./bind")(I,_,x,S),e("./cancel")(I,C,a,S),e("./direct_resolve")(I),e("./synchronous_inspection")(I),e("./join")(I,C,x,_,m),I.Promise=I,I.version="3.7.2",e("./call_get.js")(I),e("./generators.js")(I,a,_,x,o,S),e("./map.js")(I,C,a,x,_,S),e("./nodeify.js")(I),e("./promisify.js")(I,_),e("./props.js")(I,C,x,a),e("./race.js")(I,_,x,a),e("./reduce.js")(I,C,a,x,_,S),e("./settle.js")(I,C,S),e("./some.js")(I,C,a),e("./timers.js")(I,_,S),e("./using.js")(I,a,x,A,_,S),e("./any.js")(I),e("./each.js")(I,_),e("./filter.js")(I,_),l.toFastProperties(I),l.toFastProperties(I.prototype),N({a:1}),N({b:2}),N({c:3}),N(1),N((function(){})),N(void 0),N(!1),N(new I(_)),S.setBounds(g.firstLineError,l.lastLineError),I}},{"./any.js":1,"./async":2,"./bind":3,"./call_get.js":5,"./cancel":6,"./catch_filter":7,"./context":8,"./debuggability":9,"./direct_resolve":10,"./each.js":11,"./errors":12,"./es5":13,"./filter.js":14,"./finally":15,"./generators.js":16,"./join":17,"./map.js":18,"./method":19,"./nodeback":20,"./nodeify.js":21,"./promise_array":23,"./promisify.js":24,"./props.js":25,"./race.js":27,"./reduce.js":28,"./settle.js":30,"./some.js":31,"./synchronous_inspection":32,"./thenables":33,"./timers.js":34,"./using.js":35,"./util":36,async_hooks:void 0}],23:[function(e,t,i){"use strict";t.exports=function(t,i,n,r,a){var o=e("./util");function s(e){var n=this._promise=new t(i);e instanceof t&&(n._propagateFrom(e,3),e.suppressUnhandledRejections()),n._setOnCancel(this),this._values=e,this._length=0,this._totalResolved=0,this._init(void 0,-2)}return o.isArray,o.inherits(s,a),s.prototype.length=function(){return this._length},s.prototype.promise=function(){return this._promise},s.prototype._init=function e(i,a){var s=n(this._values,this._promise);if(s instanceof t){var l=(s=s._target())._bitField;if(this._values=s,0==(50397184&l))return this._promise._setAsyncGuaranteed(),s._then(e,this._reject,void 0,this,a);if(0==(33554432&l))return 0!=(16777216&l)?this._reject(s._reason()):this._cancel();s=s._value()}if(null!==(s=o.asArray(s)))0!==s.length?this._iterate(s):-5===a?this._resolveEmptyArray():this._resolve(function(e){switch(e){case-2:return[];case-3:return{};case-6:return new Map}}(a));else{var c=r("expecting an array or an iterable object but got "+o.classString(s)).reason();this._promise._rejectCallback(c,!1)}},s.prototype._iterate=function(e){var i=this.getActualLength(e.length);this._length=i,this._values=this.shouldCopyValues()?new Array(i):this._values;for(var r=this._promise,a=!1,o=null,s=0;s<i;++s){var l=n(e[s],r);o=l instanceof t?(l=l._target())._bitField:null,a?null!==o&&l.suppressUnhandledRejections():null!==o?0==(50397184&o)?(l._proxy(this,s),this._values[s]=l):a=0!=(33554432&o)?this._promiseFulfilled(l._value(),s):0!=(16777216&o)?this._promiseRejected(l._reason(),s):this._promiseCancelled(s):a=this._promiseFulfilled(l,s)}a||r._setAsyncGuaranteed()},s.prototype._isResolved=function(){return null===this._values},s.prototype._resolve=function(e){this._values=null,this._promise._fulfill(e)},s.prototype._cancel=function(){!this._isResolved()&&this._promise._isCancellable()&&(this._values=null,this._promise._cancel())},s.prototype._reject=function(e){this._values=null,this._promise._rejectCallback(e,!1)},s.prototype._promiseFulfilled=function(e,t){return this._values[t]=e,++this._totalResolved>=this._length&&(this._resolve(this._values),!0)},s.prototype._promiseCancelled=function(){return this._cancel(),!0},s.prototype._promiseRejected=function(e){return this._totalResolved++,this._reject(e),!0},s.prototype._resultCancelled=function(){if(!this._isResolved()){var e=this._values;if(this._cancel(),e instanceof t)e.cancel();else for(var i=0;i<e.length;++i)e[i]instanceof t&&e[i].cancel()}},s.prototype.shouldCopyValues=function(){return!0},s.prototype.getActualLength=function(e){return e},s}},{"./util":36}],24:[function(e,t,i){"use strict";t.exports=function(t,i){var n={},r=e("./util"),a=e("./nodeback"),o=r.withAppended,s=r.maybeWrapAsError,l=r.canEvaluate,c=e("./errors").TypeError,u={__isPromisified__:!0},d=new RegExp("^(?:"+["arity","length","name","arguments","caller","callee","prototype","__isPromisified__"].join("|")+")$"),p=function(e){return r.isIdentifier(e)&&"_"!==e.charAt(0)&&"constructor"!==e};function f(e){return!d.test(e)}function h(e){try{return!0===e.__isPromisified__}catch(e){return!1}}function g(e,t,i){var n=r.getDataPropertyOrDefault(e,t+i,u);return!!n&&h(n)}function m(e,t,i,n){for(var a=r.inheritedDataKeys(e),o=[],s=0;s<a.length;++s){var l=a[s],u=e[l],d=n===p||p(l);"function"!=typeof u||h(u)||g(e,l,t)||!n(l,u,e,d)||o.push(l,u)}return function(e,t,i){for(var n=0;n<e.length;n+=2){var r=e[n];if(i.test(r))for(var a=r.replace(i,""),o=0;o<e.length;o+=2)if(e[o]===a)throw new c("Cannot promisify an API that has normal methods with '%s'-suffix\n\n    See http://goo.gl/MqrFmX\n".replace("%s",t))}}(o,t,i),o}var v=l?void 0:function(e,l,c,u,d,p){var f=function(){return this}(),h=e;function g(){var r=l;l===n&&(r=this);var c=new t(i);c._captureStackTrace();var u="string"==typeof h&&this!==f?this[h]:e,d=a(c,p);try{u.apply(r,o(arguments,d))}catch(e){c._rejectCallback(s(e),!0,!0)}return c._isFateSealed()||c._setAsyncGuaranteed(),c}return"string"==typeof h&&(e=u),r.notEnumerableProp(g,"__isPromisified__",!0),g};function b(e,t,i,a,o){for(var s=new RegExp(t.replace(/([$])/,"\\$")+"$"),l=m(e,t,s,i),c=0,u=l.length;c<u;c+=2){var d=l[c],p=l[c+1],f=d+t;if(a===v)e[f]=v(d,n,d,p,t,o);else{var h=a(p,(function(){return v(d,n,d,p,t,o)}));r.notEnumerableProp(h,"__isPromisified__",!0),e[f]=h}}return r.toFastProperties(e),e}t.promisify=function(e,t){if("function"!=typeof e)throw new c("expecting a function but got "+r.classString(e));if(h(e))return e;var i=function(e,t,i){return v(e,t,void 0,e,null,i)}(e,void 0===(t=Object(t)).context?n:t.context,!!t.multiArgs);return r.copyDescriptors(e,i,f),i},t.promisifyAll=function(e,t){if("function"!=typeof e&&"object"!=typeof e)throw new c("the target of promisifyAll must be an object or a function\n\n    See http://goo.gl/MqrFmX\n");var i=!!(t=Object(t)).multiArgs,n=t.suffix;"string"!=typeof n&&(n="Async");var a=t.filter;"function"!=typeof a&&(a=p);var o=t.promisifier;if("function"!=typeof o&&(o=v),!r.isIdentifier(n))throw new RangeError("suffix must be a valid identifier\n\n    See http://goo.gl/MqrFmX\n");for(var s=r.inheritedDataKeys(e),l=0;l<s.length;++l){var u=e[s[l]];"constructor"!==s[l]&&r.isClass(u)&&(b(u.prototype,n,a,o,i),b(u,n,a,o,i))}return b(e,n,a,o,i)}}},{"./errors":12,"./nodeback":20,"./util":36}],25:[function(e,t,i){"use strict";t.exports=function(t,i,n,r){var a,o=e("./util"),s=o.isObject,l=e("./es5");"function"==typeof Map&&(a=Map);var c=function(){var e=0,t=0;function i(i,n){this[e]=i,this[e+t]=n,e++}return function(n){t=n.size,e=0;var r=new Array(2*n.size);return n.forEach(i,r),r}}();function u(e){var t,i=!1;if(void 0!==a&&e instanceof a)t=c(e),i=!0;else{var n=l.keys(e),r=n.length;t=new Array(2*r);for(var o=0;o<r;++o){var s=n[o];t[o]=e[s],t[o+r]=s}}this.constructor$(t),this._isMap=i,this._init$(void 0,i?-6:-3)}function d(e){var i,a=n(e);return s(a)?(i=a instanceof t?a._then(t.props,void 0,void 0,void 0,void 0):new u(a).promise(),a instanceof t&&i._propagateFrom(a,2),i):r("cannot await properties of a non-object\n\n    See http://goo.gl/MqrFmX\n")}o.inherits(u,i),u.prototype._init=function(){},u.prototype._promiseFulfilled=function(e,t){if(this._values[t]=e,++this._totalResolved>=this._length){var i;if(this._isMap)i=function(e){for(var t=new a,i=e.length/2|0,n=0;n<i;++n){var r=e[i+n],o=e[n];t.set(r,o)}return t}(this._values);else{i={};for(var n=this.length(),r=0,o=this.length();r<o;++r)i[this._values[r+n]]=this._values[r]}return this._resolve(i),!0}return!1},u.prototype.shouldCopyValues=function(){return!1},u.prototype.getActualLength=function(e){return e>>1},t.prototype.props=function(){return d(this)},t.props=function(e){return d(e)}}},{"./es5":13,"./util":36}],26:[function(e,t,i){"use strict";function n(e){this._capacity=e,this._length=0,this._front=0}n.prototype._willBeOverCapacity=function(e){return this._capacity<e},n.prototype._pushOne=function(e){var t=this.length();this._checkCapacity(t+1),this[this._front+t&this._capacity-1]=e,this._length=t+1},n.prototype.push=function(e,t,i){var n=this.length()+3;if(this._willBeOverCapacity(n))return this._pushOne(e),this._pushOne(t),void this._pushOne(i);var r=this._front+n-3;this._checkCapacity(n);var a=this._capacity-1;this[r+0&a]=e,this[r+1&a]=t,this[r+2&a]=i,this._length=n},n.prototype.shift=function(){var e=this._front,t=this[e];return this[e]=void 0,this._front=e+1&this._capacity-1,this._length--,t},n.prototype.length=function(){return this._length},n.prototype._checkCapacity=function(e){this._capacity<e&&this._resizeTo(this._capacity<<1)},n.prototype._resizeTo=function(e){var t=this._capacity;this._capacity=e,function(e,t,i,n,r){for(var a=0;a<r;++a)i[a+n]=e[a+t],e[a+t]=void 0}(this,0,this,t,this._front+this._length&t-1)},t.exports=n},{}],27:[function(e,t,i){"use strict";t.exports=function(t,i,n,r){var a=e("./util");function o(e,s){var l,c=n(e);if(c instanceof t)return(l=c).then((function(e){return o(e,l)}));if(null===(e=a.asArray(e)))return r("expecting an array or an iterable object but got "+a.classString(e));var u=new t(i);void 0!==s&&u._propagateFrom(s,3);for(var d=u._fulfill,p=u._reject,f=0,h=e.length;f<h;++f){var g=e[f];(void 0!==g||f in e)&&t.cast(g)._then(d,p,void 0,u,null)}return u}t.race=function(e){return o(e,void 0)},t.prototype.race=function(){return o(this,void 0)}}},{"./util":36}],28:[function(e,t,i){"use strict";t.exports=function(t,i,n,r,a,o){var s=e("./util"),l=s.tryCatch;function c(e,i,n,r){this.constructor$(e);var o=t._getContext();this._fn=s.contextBind(o,i),void 0!==n&&(n=t.resolve(n))._attachCancellationCallback(this),this._initialValue=n,this._currentCancellable=null,this._eachValues=r===a?Array(this._length):0===r?null:void 0,this._promise._captureStackTrace(),this._init$(void 0,-5)}function u(e,t){this.isFulfilled()?t._resolve(e):t._reject(e)}function d(e,t,i,r){return"function"!=typeof t?n("expecting a function but got "+s.classString(t)):new c(e,t,i,r).promise()}function p(e){this.accum=e,this.array._gotAccum(e);var i=r(this.value,this.array._promise);return i instanceof t?(this.array._currentCancellable=i,i._then(f,void 0,void 0,this,void 0)):f.call(this,i)}function f(e){var i,n=this.array,r=n._promise,a=l(n._fn);r._pushContext(),(i=void 0!==n._eachValues?a.call(r._boundValue(),e,this.index,this.length):a.call(r._boundValue(),this.accum,e,this.index,this.length))instanceof t&&(n._currentCancellable=i);var s=r._popContext();return o.checkForgottenReturns(i,s,void 0!==n._eachValues?"Promise.each":"Promise.reduce",r),i}s.inherits(c,i),c.prototype._gotAccum=function(e){void 0!==this._eachValues&&null!==this._eachValues&&e!==a&&this._eachValues.push(e)},c.prototype._eachComplete=function(e){return null!==this._eachValues&&this._eachValues.push(e),this._eachValues},c.prototype._init=function(){},c.prototype._resolveEmptyArray=function(){this._resolve(void 0!==this._eachValues?this._eachValues:this._initialValue)},c.prototype.shouldCopyValues=function(){return!1},c.prototype._resolve=function(e){this._promise._resolveCallback(e),this._values=null},c.prototype._resultCancelled=function(e){if(e===this._initialValue)return this._cancel();this._isResolved()||(this._resultCancelled$(),this._currentCancellable instanceof t&&this._currentCancellable.cancel(),this._initialValue instanceof t&&this._initialValue.cancel())},c.prototype._iterate=function(e){var i,n;this._values=e;var r=e.length;void 0!==this._initialValue?(i=this._initialValue,n=0):(i=t.resolve(e[0]),n=1),this._currentCancellable=i;for(var a=n;a<r;++a){var o=e[a];o instanceof t&&o.suppressUnhandledRejections()}if(!i.isRejected())for(;n<r;++n){var s={accum:null,value:e[n],index:n,length:r,array:this};i=i._then(p,void 0,void 0,s,void 0),0==(127&n)&&i._setNoAsyncGuarantee()}void 0!==this._eachValues&&(i=i._then(this._eachComplete,void 0,void 0,this,void 0)),i._then(u,u,void 0,i,this)},t.prototype.reduce=function(e,t){return d(this,e,t,null)},t.reduce=function(e,t,i,n){return d(e,t,i,n)}}},{"./util":36}],29:[function(e,r,a){"use strict";var o,s,l,c,u,d=e("./util"),p=d.getNativePromise();if(d.isNode&&"undefined"==typeof MutationObserver){var f=i.setImmediate,h=t.nextTick;o=d.isRecentNode?function(e){f.call(i,e)}:function(e){h.call(t,e)}}else if("function"==typeof p&&"function"==typeof p.resolve){var g=p.resolve();o=function(e){g.then(e)}}else o="undefined"!=typeof MutationObserver&&("undefined"==typeof window||!window.navigator||!window.navigator.standalone&&!window.cordova)&&"classList"in document.documentElement?(s=document.createElement("div"),l={attributes:!0},c=!1,u=document.createElement("div"),new MutationObserver((function(){s.classList.toggle("foo"),c=!1})).observe(u,l),function(e){var t=new MutationObserver((function(){t.disconnect(),e()}));t.observe(s,l),c||(c=!0,u.classList.toggle("foo"))}):void 0!==n?function(e){n(e)}:"undefined"!=typeof setTimeout?function(e){setTimeout(e,0)}:function(){throw new Error("No async scheduler available\n\n    See http://goo.gl/MqrFmX\n")};r.exports=o},{"./util":36}],30:[function(e,t,i){"use strict";t.exports=function(t,i,n){var r=t.PromiseInspection;function a(e){this.constructor$(e)}e("./util").inherits(a,i),a.prototype._promiseResolved=function(e,t){return this._values[e]=t,++this._totalResolved>=this._length&&(this._resolve(this._values),!0)},a.prototype._promiseFulfilled=function(e,t){var i=new r;return i._bitField=33554432,i._settledValueField=e,this._promiseResolved(t,i)},a.prototype._promiseRejected=function(e,t){var i=new r;return i._bitField=16777216,i._settledValueField=e,this._promiseResolved(t,i)},t.settle=function(e){return n.deprecated(".settle()",".reflect()"),new a(e).promise()},t.allSettled=function(e){return new a(e).promise()},t.prototype.settle=function(){return t.settle(this)}}},{"./util":36}],31:[function(e,t,i){"use strict";t.exports=function(t,i,n){var r=e("./util"),a=e("./errors").RangeError,o=e("./errors").AggregateError,s=r.isArray,l={};function c(e){this.constructor$(e),this._howMany=0,this._unwrap=!1,this._initialized=!1}function u(e,t){if((0|t)!==t||t<0)return n("expecting a positive integer\n\n    See http://goo.gl/MqrFmX\n");var i=new c(e),r=i.promise();return i.setHowMany(t),i.init(),r}r.inherits(c,i),c.prototype._init=function(){if(this._initialized)if(0!==this._howMany){this._init$(void 0,-5);var e=s(this._values);!this._isResolved()&&e&&this._howMany>this._canPossiblyFulfill()&&this._reject(this._getRangeError(this.length()))}else this._resolve([])},c.prototype.init=function(){this._initialized=!0,this._init()},c.prototype.setUnwrap=function(){this._unwrap=!0},c.prototype.howMany=function(){return this._howMany},c.prototype.setHowMany=function(e){this._howMany=e},c.prototype._promiseFulfilled=function(e){return this._addFulfilled(e),this._fulfilled()===this.howMany()&&(this._values.length=this.howMany(),1===this.howMany()&&this._unwrap?this._resolve(this._values[0]):this._resolve(this._values),!0)},c.prototype._promiseRejected=function(e){return this._addRejected(e),this._checkOutcome()},c.prototype._promiseCancelled=function(){return this._values instanceof t||null==this._values?this._cancel():(this._addRejected(l),this._checkOutcome())},c.prototype._checkOutcome=function(){if(this.howMany()>this._canPossiblyFulfill()){for(var e=new o,t=this.length();t<this._values.length;++t)this._values[t]!==l&&e.push(this._values[t]);return e.length>0?this._reject(e):this._cancel(),!0}return!1},c.prototype._fulfilled=function(){return this._totalResolved},c.prototype._rejected=function(){return this._values.length-this.length()},c.prototype._addRejected=function(e){this._values.push(e)},c.prototype._addFulfilled=function(e){this._values[this._totalResolved++]=e},c.prototype._canPossiblyFulfill=function(){return this.length()-this._rejected()},c.prototype._getRangeError=function(e){var t="Input array must contain at least "+this._howMany+" items but contains only "+e+" items";return new a(t)},c.prototype._resolveEmptyArray=function(){this._reject(this._getRangeError(0))},t.some=function(e,t){return u(e,t)},t.prototype.some=function(e){return u(this,e)},t._SomePromiseArray=c}},{"./errors":12,"./util":36}],32:[function(e,t,i){"use strict";t.exports=function(e){function t(e){void 0!==e?(e=e._target(),this._bitField=e._bitField,this._settledValueField=e._isFateSealed()?e._settledValue():void 0):(this._bitField=0,this._settledValueField=void 0)}t.prototype._settledValue=function(){return this._settledValueField};var i=t.prototype.value=function(){if(!this.isFulfilled())throw new TypeError("cannot get fulfillment value of a non-fulfilled promise\n\n    See http://goo.gl/MqrFmX\n");return this._settledValue()},n=t.prototype.error=t.prototype.reason=function(){if(!this.isRejected())throw new TypeError("cannot get rejection reason of a non-rejected promise\n\n    See http://goo.gl/MqrFmX\n");return this._settledValue()},r=t.prototype.isFulfilled=function(){return 0!=(33554432&this._bitField)},a=t.prototype.isRejected=function(){return 0!=(16777216&this._bitField)},o=t.prototype.isPending=function(){return 0==(50397184&this._bitField)},s=t.prototype.isResolved=function(){return 0!=(50331648&this._bitField)};t.prototype.isCancelled=function(){return 0!=(8454144&this._bitField)},e.prototype.__isCancelled=function(){return 65536==(65536&this._bitField)},e.prototype._isCancelled=function(){return this._target().__isCancelled()},e.prototype.isCancelled=function(){return 0!=(8454144&this._target()._bitField)},e.prototype.isPending=function(){return o.call(this._target())},e.prototype.isRejected=function(){return a.call(this._target())},e.prototype.isFulfilled=function(){return r.call(this._target())},e.prototype.isResolved=function(){return s.call(this._target())},e.prototype.value=function(){return i.call(this._target())},e.prototype.reason=function(){var e=this._target();return e._unsetRejectionIsUnhandled(),n.call(e)},e.prototype._value=function(){return this._settledValue()},e.prototype._reason=function(){return this._unsetRejectionIsUnhandled(),this._settledValue()},e.PromiseInspection=t}},{}],33:[function(e,t,i){"use strict";t.exports=function(t,i){var n=e("./util"),r=n.errorObj,a=n.isObject,o={}.hasOwnProperty;return function(e,s){if(a(e)){if(e instanceof t)return e;var l=function(e){try{return function(e){return e.then}(e)}catch(e){return r.e=e,r}}(e);if(l===r){s&&s._pushContext();var c=t.reject(l.e);return s&&s._popContext(),c}if("function"==typeof l)return function(e){try{return o.call(e,"_promise0")}catch(e){return!1}}(e)?(c=new t(i),e._then(c._fulfill,c._reject,void 0,c,null),c):function(e,a,o){var s=new t(i),l=s;o&&o._pushContext(),s._captureStackTrace(),o&&o._popContext();var c=n.tryCatch(a).call(e,(function(e){s&&(s._resolveCallback(e),s=null)}),(function(e){s&&(s._rejectCallback(e,!1,!0),s=null)}));return s&&c===r&&(s._rejectCallback(c.e,!0,!0),s=null),l}(e,l,s)}return e}}},{"./util":36}],34:[function(e,t,i){"use strict";t.exports=function(t,i,n){var r=e("./util"),a=t.TimeoutError;function o(e){this.handle=e}o.prototype._resultCancelled=function(){clearTimeout(this.handle)};var s=function(e){return l(+this).thenReturn(e)},l=t.delay=function(e,r){var a,l;return void 0!==r?(a=t.resolve(r)._then(s,null,null,e,void 0),n.cancellation()&&r instanceof t&&a._setOnCancel(r)):(a=new t(i),l=setTimeout((function(){a._fulfill()}),+e),n.cancellation()&&a._setOnCancel(new o(l)),a._captureStackTrace()),a._setAsyncGuaranteed(),a};function c(e){return clearTimeout(this.handle),e}function u(e){throw clearTimeout(this.handle),e}t.prototype.delay=function(e){return l(e,this)},t.prototype.timeout=function(e,t){var i,s;e=+e;var l=new o(setTimeout((function(){i.isPending()&&function(e,t,i){var n;n="string"!=typeof t?t instanceof Error?t:new a("operation timed out"):new a(t),r.markAsOriginatingFromRejection(n),e._attachExtraTrace(n),e._reject(n),null!=i&&i.cancel()}(i,t,s)}),e));return n.cancellation()?(s=this.then(),(i=s._then(c,u,void 0,l,void 0))._setOnCancel(l)):i=this._then(c,u,void 0,l,void 0),i}}},{"./util":36}],35:[function(e,t,i){"use strict";t.exports=function(t,i,n,r,a,o){var s=e("./util"),l=e("./errors").TypeError,c=e("./util").inherits,u=s.errorObj,d=s.tryCatch,p={};function f(e){setTimeout((function(){throw e}),0)}function h(e,i){var r=0,o=e.length,s=new t(a);return function a(){if(r>=o)return s._fulfill();var l=function(e){var t=n(e);return t!==e&&"function"==typeof e._isDisposable&&"function"==typeof e._getDisposer&&e._isDisposable()&&t._setDisposable(e._getDisposer()),t}(e[r++]);if(l instanceof t&&l._isDisposable()){try{l=n(l._getDisposer().tryDispose(i),e.promise)}catch(e){return f(e)}if(l instanceof t)return l._then(a,f,null,null,null)}a()}(),s}function g(e,t,i){this._data=e,this._promise=t,this._context=i}function m(e,t,i){this.constructor$(e,t,i)}function v(e){return g.isDisposer(e)?(this.resources[this.index]._setDisposable(e),e.promise()):e}function b(e){this.length=e,this.promise=null,this[e-1]=null}g.prototype.data=function(){return this._data},g.prototype.promise=function(){return this._promise},g.prototype.resource=function(){return this.promise().isFulfilled()?this.promise().value():p},g.prototype.tryDispose=function(e){var t=this.resource(),i=this._context;void 0!==i&&i._pushContext();var n=t!==p?this.doDispose(t,e):null;return void 0!==i&&i._popContext(),this._promise._unsetDisposable(),this._data=null,n},g.isDisposer=function(e){return null!=e&&"function"==typeof e.resource&&"function"==typeof e.tryDispose},c(m,g),m.prototype.doDispose=function(e,t){return this.data().call(e,e,t)},b.prototype._resultCancelled=function(){for(var e=this.length,i=0;i<e;++i){var n=this[i];n instanceof t&&n.cancel()}},t.using=function(){var e=arguments.length;if(e<2)return i("you must pass at least 2 arguments to Promise.using");var r,a=arguments[e-1];if("function"!=typeof a)return i("expecting a function but got "+s.classString(a));var l=!0;2===e&&Array.isArray(arguments[0])?(e=(r=arguments[0]).length,l=!1):(r=arguments,e--);for(var c=new b(e),p=0;p<e;++p){var f=r[p];if(g.isDisposer(f)){var m=f;(f=f.promise())._setDisposable(m)}else{var y=n(f);y instanceof t&&(f=y._then(v,null,null,{resources:c,index:p},void 0))}c[p]=f}var _=new Array(c.length);for(p=0;p<_.length;++p)_[p]=t.resolve(c[p]).reflect();var k=t.all(_).then((function(e){for(var t=0;t<e.length;++t){var i=e[t];if(i.isRejected())return u.e=i.error(),u;if(!i.isFulfilled())return void k.cancel();e[t]=i.value()}w._pushContext(),a=d(a);var n=l?a.apply(void 0,e):a(e),r=w._popContext();return o.checkForgottenReturns(n,r,"Promise.using",w),n})),w=k.lastly((function(){var e=new t.PromiseInspection(k);return h(c,e)}));return c.promise=w,w._setOnCancel(c),w},t.prototype._setDisposable=function(e){this._bitField=131072|this._bitField,this._disposer=e},t.prototype._isDisposable=function(){return(131072&this._bitField)>0},t.prototype._getDisposer=function(){return this._disposer},t.prototype._unsetDisposable=function(){this._bitField=-131073&this._bitField,this._disposer=void 0},t.prototype.disposer=function(e){if("function"==typeof e)return new m(e,this,r());throw new l}}},{"./errors":12,"./util":36}],36:[function(e,n,r){"use strict";var a=e("./es5"),o="undefined"==typeof navigator,s={e:{}},l,c="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==i?i:void 0!==this?this:null;function u(){try{var e=l;return l=null,e.apply(this,arguments)}catch(e){return s.e=e,s}}function d(e){return l=e,u}var p=function(e,t){var i={}.hasOwnProperty;function n(){for(var n in this.constructor=e,this.constructor$=t,t.prototype)i.call(t.prototype,n)&&"$"!==n.charAt(n.length-1)&&(this[n+"$"]=t.prototype[n])}return n.prototype=t.prototype,e.prototype=new n,e.prototype};function f(e){return null==e||!0===e||!1===e||"string"==typeof e||"number"==typeof e}function h(e){return"function"==typeof e||"object"==typeof e&&null!==e}function g(e){return f(e)?new Error(S(e)):e}function m(e,t){var i,n=e.length,r=new Array(n+1);for(i=0;i<n;++i)r[i]=e[i];return r[i]=t,r}function v(e,t,i){if(!a.isES5)return{}.hasOwnProperty.call(e,t)?e[t]:void 0;var n=Object.getOwnPropertyDescriptor(e,t);return null!=n?null==n.get&&null==n.set?n.value:i:void 0}function b(e,t,i){if(f(e))return e;var n={value:i,configurable:!0,enumerable:!1,writable:!0};return a.defineProperty(e,t,n),e}function y(e){throw e}var _=function(){var e=[Array.prototype,Object.prototype,Function.prototype],t=function(t){for(var i=0;i<e.length;++i)if(e[i]===t)return!0;return!1};if(a.isES5){var i=Object.getOwnPropertyNames;return function(e){for(var n=[],r=Object.create(null);null!=e&&!t(e);){var o;try{o=i(e)}catch(e){return n}for(var s=0;s<o.length;++s){var l=o[s];if(!r[l]){r[l]=!0;var c=Object.getOwnPropertyDescriptor(e,l);null!=c&&null==c.get&&null==c.set&&n.push(l)}}e=a.getPrototypeOf(e)}return n}}var n={}.hasOwnProperty;return function(i){if(t(i))return[];var r=[];e:for(var a in i)if(n.call(i,a))r.push(a);else{for(var o=0;o<e.length;++o)if(n.call(e[o],a))continue e;r.push(a)}return r}}(),k=/this\s*\.\s*\S+\s*=/;function w(e){try{if("function"==typeof e){var t=a.names(e.prototype),i=a.isES5&&t.length>1,n=t.length>0&&!(1===t.length&&"constructor"===t[0]),r=k.test(e+"")&&a.names(e).length>0;if(i||n||r)return!0}return!1}catch(e){return!1}}function x(e){function t(){}t.prototype=e;var i=new t;function n(){return typeof i.foo}return n(),n(),e}var C=/^[a-z$_][a-z$_0-9]*$/i;function E(e){return C.test(e)}function A(e,t,i){for(var n=new Array(e),r=0;r<e;++r)n[r]=t+r+i;return n}function S(e){try{return e+""}catch(e){return"[no string representation]"}}function L(e){return e instanceof Error||null!==e&&"object"==typeof e&&"string"==typeof e.message&&"string"==typeof e.name}function T(e){try{b(e,"isOperational",!0)}catch(e){}}function R(e){return null!=e&&(e instanceof Error.__BluebirdErrorTypes__.OperationalError||!0===e.isOperational)}function j(e){return L(e)&&a.propertyIsWritable(e,"stack")}var P="stack"in new Error?function(e){return j(e)?e:new Error(S(e))}:function(e){if(j(e))return e;try{throw new Error(S(e))}catch(e){return e}};function I(e){return{}.toString.call(e)}function O(e,t,i){for(var n=a.names(e),r=0;r<n.length;++r){var o=n[r];if(i(o))try{a.defineProperty(t,o,a.getDescriptor(e,o))}catch(e){}}}var $=function(e){return a.isArray(e)?e:null};if("undefined"!=typeof Symbol&&Symbol.iterator){var N="function"==typeof Array.from?function(e){return Array.from(e)}:function(e){for(var t,i=[],n=e[Symbol.iterator]();!(t=n.next()).done;)i.push(t.value);return i};$=function(e){return a.isArray(e)?e:null!=e&&"function"==typeof e[Symbol.iterator]?N(e):null}}var F=void 0!==t&&"[object process]"===I(t).toLowerCase(),D=void 0!==t&&void 0!==t.env,B;function M(e){return D?t.env[e]:void 0}function U(){if("function"==typeof Promise)try{if("[object Promise]"===I(new Promise((function(){}))))return Promise}catch(e){}}function z(e,t){if(null===e||"function"!=typeof t||t===B)return t;null!==e.domain&&(t=e.domain.bind(t));var i=e.async;if(null!==i){var n=t;t=function(){var e=new Array(2).concat([].slice.call(arguments));return e[0]=n,e[1]=this,i.runInAsyncScope.apply(i,e)}}return t}var H={setReflectHandler:function(e){B=e},isClass:w,isIdentifier:E,inheritedDataKeys:_,getDataPropertyOrDefault:v,thrower:y,isArray:a.isArray,asArray:$,notEnumerableProp:b,isPrimitive:f,isObject:h,isError:L,canEvaluate:o,errorObj:s,tryCatch:d,inherits:p,withAppended:m,maybeWrapAsError:g,toFastProperties:x,filledRange:A,toString:S,canAttachTrace:j,ensureErrorObject:P,originatesFromRejection:R,markAsOriginatingFromRejection:T,classString:I,copyDescriptors:O,isNode:F,hasEnvVariables:D,env:M,global:c,getNativePromise:U,contextBind:z},V;H.isRecentNode=H.isNode&&(t.versions&&t.versions.node?V=t.versions.node.split(".").map(Number):t.version&&(V=t.version.split(".").map(Number)),0===V[0]&&V[1]>10||V[0]>0),H.nodeSupportsAsyncResource=H.isNode&&function(){var t=!1;try{t="function"==typeof e("async_hooks").AsyncResource.prototype.runInAsyncScope}catch(e){t=!1}return t}(),H.isNode&&H.toFastProperties(t);try{throw new Error}catch(e){H.lastLineError=e}n.exports=H},{"./es5":13,async_hooks:void 0}]},{},[4])(4)},e.exports=r(),"undefined"!=typeof window&&null!==window?window.P=window.Promise:"undefined"!=typeof self&&null!==self&&(self.P=self.Promise)}).call(this,i(61),i(48),i(93).setImmediate)},133:function(e,t,i){(function(t){var i=0;t.env.MAX_MS_PER_TICK&&(i=parseInt(t.env.MAX_MS_PER_TICK)),(!i||i.isNan||i<=0)&&(i=100),e.exports={MAX_MS_PER_TICK:i}}).call(this,i(61))},134:function(e,t,i){var n=i(135),r=/\/thumb\//,a=/(\d+)px-[^/]+$/,o=[n.LEAD_IMAGE_M,n.LEAD_IMAGE_S],s=function(e,t,i){if(e.match(r)){var n=a.exec(e);if(n)if((i||n[1])>t){var o=n[0].replace(n[1],t);return e.replace(a,o)}}},l=function(e){var t=e.getAttribute("width"),i=e.getAttribute("height");return t<48||i<48},c=function(e){return!!e.closest(".metadata,.noviewer")},u=function(e){return!!e.closest(".gallerybox")},d=function(e,t,i){var n=e.split(",").map((function(e){return e.trim()})),r=[];if(n.forEach((function(e){var n=e.split(" "),a=n[0],o=n[1],l=o.substring(0,o.toLowerCase().indexOf("x")),c=i*l;if(c<t){var u=s(a,c,t);u&&r.push("".concat(u," ").concat(o))}})),r.length)return r.join(", ")};e.exports={isTooSmall:l,isDisallowed:c,isFromGallery:u,scaleURL:s,scaleElementIfNecessary:function(e){if(!(l(e)||c(e)||u(e)))for(var t=e.getAttribute("src"),i=e.getAttribute("srcset"),n=e.getAttribute("width"),r=e.getAttribute("height"),a=e.getAttribute("data-file-width"),p=0;p<o.length;p++){var f=o[p];if(!(f>=a)){var h=s(t,f,a);if(h){if(e.setAttribute("src",h),e.setAttribute("height",Math.round(r*f/n)),e.setAttribute("width",f),i){var g=d(i,a,f);g?e.setAttribute("srcSet",g):e.removeAttribute("srcSet")}break}}}}}},135:function(e,t){var i={CARD_THUMB_LIST_ITEM_SIZE:320,CARD_THUMB_FEATURE_SIZE:640,LEAD_IMAGE_S:320,LEAD_IMAGE_M:640,LEAD_IMAGE_L:800,LEAD_IMAGE_XL:1024};e.exports=i},136:function(e,t,i){"use strict";e.exports={ELEMENT_NODE:1,TEXT_NODE:3,PROCESSING_INSTRUCTION_NODE:7,COMMENT_NODE:8,DOCUMENT_NODE:9,DOCUMENT_TYPE_NODE:10,DOCUMENT_FRAGMENT_NODE:11}},137:function(e,t){var i={attributesToRemoveFromElements:{A:["about","data-mw","typeof"],ABBR:["title"],B:["about","data-mw","typeof"],BLOCKQUOTE:["about","data-mw","typeof"],BR:["about","data-mw","typeof"],CITE:["about","data-mw","typeof"],CODE:["about","data-mw","typeof"],DIV:["data-mw","typeof"],FIGURE:["typeof"],"FIGURE-INLINE":["about","data-file-type","data-mw","itemscope","itemtype","lang","rel","title","typeof"],I:["about","data-mw","typeof"],IMG:["about"],LI:["about"],LINK:["data-mw","typeof"],OL:["about","data-mw","typeof"],P:["data-mw","typeof"],SPAN:["about","data-file-type","data-mw","itemscope","itemtype","lang","rel","title","typeof"],STYLE:["about","data-mw"],SUP:["about","data-mw","rel","typeof"],TABLE:["about","data-mw","typeof"],UL:["about","data-mw","typeof"]}};i.refGroupsGeneratedByCSS=new Set(["decimal","lower-alpha","upper-alpha","lower-greek","lower-roman","upper-roman"]),i.forbiddenElementClassRegex=/(?:^|\s)(?:(?:geo-nondefault)|(?:geo-multi-punct)|(?:hide-when-compact))(?:\s|$)/,i.forbiddenElementClassSubstringRegex=/(?:(?:nomobile)|(?:navbox))/,i.forbiddenDivClassRegex=/(?:^|\s)(?:(?:infobox)|(?:magnify))(?:\s|$)/,i.forbiddenSpanClassRegex=/(?:^|\s)(?:Z3988)(?:\s|$)/,i.forbiddenElementIDRegex=/^coordinates$/,i.mwidRegex=/^mw[\w-]{2,3}$/,i.httpsRegex=/^https:\/\//,i.headerTagRegex=/^H[0-9]$/,i.bracketSpanRegex=/^(\[|\])$/,i.themeExclusionClassRegex=/(?:^|\s)(?:notheme)(?:\s|$)/,i.inlineBackgroundStyleRegex=/(?:^|\s|;)background(?:-color)?:\s*(?!(?:transparent)|(?:none)|(?:inherit)|(?:unset)|(?:#?$)|(?:#?;))/,i.infoboxClassRegex=/(?:^|\s)infobox(?:_v3)?(?:\s|$)/,i.infoboxClassExclusionRegex=/(?:^|\s)(?:(?:metadata)|(?:mbox-small))(?:\s|$)/i,i.newClassRegex=/(?:^|\s)new(?:\s|$)/,i.referenceListClassName="mw-references",i.citeNoteIdPrefix="cite_note-",i.citeNoteIdPrefixLength=i.citeNoteIdPrefix.length,i.citeRefIdPrefix="cite_ref-",i.referenceClassRegex=/(?:^|\s)(?:mw-)?reference-text(?:\s|$)+/,i.widenImageExclusionClassRegex=/(?:^|\s)(?:(?:tsingle)|(?:noresize)|(?:noviewer))(?:\s|$)/,e.exports=i},138:function(e,t,i){"use strict";var n=i(139),r=i(145),a=i(62);function o(e,t){var i=e.createElement("link");return i.setAttribute("rel","stylesheet"),i.setAttribute("href",t),i}e.exports={addCssLinks:function(e,t){var i=e.head,s=t.domain;if(!s){var l=a.getHttpsBaseUri(e);l&&(s=n.parse(l).hostname)}if(i){if(i.appendChild(o(e,"".concat(t.baseURI,"data/css/mobile/base"))),s){var c=r.getExternalRestApiUri(s);i.appendChild(o(e,"".concat(c,"data/css/mobile/site")))}i.appendChild(o(e,"".concat(t.baseURI,"data/css/mobile/pcs")))}},addMetaViewport:function(e){var t=e.head;t&&t.appendChild(function(e){var t=e.createElement("meta");return t.setAttribute("name","viewport"),t.setAttribute("content","width=device-width, user-scalable=no, initial-scale=1, shrink-to-fit=no"),t}(e))},addPageLibJs:function(e,t){var i=e.head;i&&i.appendChild(function(e,t){var i=e.createElement("script");return i.setAttribute("src","".concat(t.baseURI,"data/javascript/mobile/pcs")),i}(e,t));var n=e.getElementById("pcs");if(n){var r=n.querySelectorAll("section");if(r.length>0){var a=e.createElement("script");a.innerHTML="pcs.c1.Page.onBodyStart();",n.insertBefore(a,r[0]);for(var o=1;o<r.length;o++)r[o].style.display="none"}var s=e.createElement("script");s.setAttribute("defer","true"),s.innerHTML="pcs.c1.Page.onBodyEnd();",n.appendChild(s)}},addMetaTags:function(e,t){var i=e.head;if(i){t.mw.protection.forEach((function(t){var n=e.createElement("meta");n.setAttribute("property","mw:pageProtection:".concat(t.type)),n.setAttribute("content",t.level),i.appendChild(n)}));var n=t.mw.originalimage;if(n){var r=e.createElement("meta");r.setAttribute("property","mw:leadImage"),r.setAttribute("content",n.source),r.setAttribute("data-file-width",n.width),r.setAttribute("data-file-height",n.height),i.appendChild(r)}}},avoidFaviconRequest:function(e){var t=e.head;if(t){var i=e.createElement("link");i.setAttribute("rel","icon"),i.setAttribute("href","data:,"),t.appendChild(i)}},addLocale:function(e,t){if(t){var i=e.head;if(i){var n=e.createElement("meta");n.setAttribute("property","pcs:locale"),n.setAttribute("content",t),i.appendChild(n)}}}}},139:function(e,t,i){"use strict";var n=i(140),r=i(141);function a(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}t.parse=y,t.resolve=function(e,t){return y(e,!1,!0).resolve(t)},t.resolveObject=function(e,t){return e?y(e,!1,!0).resolveObject(t):t},t.format=function(e){r.isString(e)&&(e=y(e));return e instanceof a?e.format():a.prototype.format.call(e)},t.Url=a;var o=/^([a-z0-9.+-]+:)/i,s=/:[0-9]*$/,l=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,c=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),u=["'"].concat(c),d=["%","/","?",";","#"].concat(u),p=["/","?","#"],f=/^[+a-z0-9A-Z_-]{0,63}$/,h=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,g={javascript:!0,"javascript:":!0},m={javascript:!0,"javascript:":!0},v={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},b=i(142);function y(e,t,i){if(e&&r.isObject(e)&&e instanceof a)return e;var n=new a;return n.parse(e,t,i),n}a.prototype.parse=function(e,t,i){if(!r.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var a=e.indexOf("?"),s=-1!==a&&a<e.indexOf("#")?"?":"#",c=e.split(s);c[0]=c[0].replace(/\\/g,"/");var y=e=c.join(s);if(y=y.trim(),!i&&1===e.split("#").length){var _=l.exec(y);if(_)return this.path=y,this.href=y,this.pathname=_[1],_[2]?(this.search=_[2],this.query=t?b.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var k=o.exec(y);if(k){var w=(k=k[0]).toLowerCase();this.protocol=w,y=y.substr(k.length)}if(i||k||y.match(/^\/\/[^@\/]+@[^@\/]+/)){var x="//"===y.substr(0,2);!x||k&&m[k]||(y=y.substr(2),this.slashes=!0)}if(!m[k]&&(x||k&&!v[k])){for(var C,E,A=-1,S=0;S<p.length;S++){-1!==(L=y.indexOf(p[S]))&&(-1===A||L<A)&&(A=L)}-1!==(E=-1===A?y.lastIndexOf("@"):y.lastIndexOf("@",A))&&(C=y.slice(0,E),y=y.slice(E+1),this.auth=decodeURIComponent(C)),A=-1;for(S=0;S<d.length;S++){var L;-1!==(L=y.indexOf(d[S]))&&(-1===A||L<A)&&(A=L)}-1===A&&(A=y.length),this.host=y.slice(0,A),y=y.slice(A),this.parseHost(),this.hostname=this.hostname||"";var T="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!T)for(var R=this.hostname.split(/\./),j=(S=0,R.length);S<j;S++){var P=R[S];if(P&&!P.match(f)){for(var I="",O=0,$=P.length;O<$;O++)P.charCodeAt(O)>127?I+="x":I+=P[O];if(!I.match(f)){var N=R.slice(0,S),F=R.slice(S+1),D=P.match(h);D&&(N.push(D[1]),F.unshift(D[2])),F.length&&(y="/"+F.join(".")+y),this.hostname=N.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),T||(this.hostname=n.toASCII(this.hostname));var B=this.port?":"+this.port:"",M=this.hostname||"";this.host=M+B,this.href+=this.host,T&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==y[0]&&(y="/"+y))}if(!g[w])for(S=0,j=u.length;S<j;S++){var U=u[S];if(-1!==y.indexOf(U)){var z=encodeURIComponent(U);z===U&&(z=escape(U)),y=y.split(U).join(z)}}var H=y.indexOf("#");-1!==H&&(this.hash=y.substr(H),y=y.slice(0,H));var V=y.indexOf("?");if(-1!==V?(this.search=y.substr(V),this.query=y.substr(V+1),t&&(this.query=b.parse(this.query)),y=y.slice(0,V)):t&&(this.search="",this.query={}),y&&(this.pathname=y),v[w]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){B=this.pathname||"";var q=this.search||"";this.path=B+q}return this.href=this.format(),this},a.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",i=this.pathname||"",n=this.hash||"",a=!1,o="";this.host?a=e+this.host:this.hostname&&(a=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(a+=":"+this.port)),this.query&&r.isObject(this.query)&&Object.keys(this.query).length&&(o=b.stringify(this.query));var s=this.search||o&&"?"+o||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||v[t])&&!1!==a?(a="//"+(a||""),i&&"/"!==i.charAt(0)&&(i="/"+i)):a||(a=""),n&&"#"!==n.charAt(0)&&(n="#"+n),s&&"?"!==s.charAt(0)&&(s="?"+s),t+a+(i=i.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})))+(s=s.replace("#","%23"))+n},a.prototype.resolve=function(e){return this.resolveObject(y(e,!1,!0)).format()},a.prototype.resolveObject=function(e){if(r.isString(e)){var t=new a;t.parse(e,!1,!0),e=t}for(var i=new a,n=Object.keys(this),o=0;o<n.length;o++){var s=n[o];i[s]=this[s]}if(i.hash=e.hash,""===e.href)return i.href=i.format(),i;if(e.slashes&&!e.protocol){for(var l=Object.keys(e),c=0;c<l.length;c++){var u=l[c];"protocol"!==u&&(i[u]=e[u])}return v[i.protocol]&&i.hostname&&!i.pathname&&(i.path=i.pathname="/"),i.href=i.format(),i}if(e.protocol&&e.protocol!==i.protocol){if(!v[e.protocol]){for(var d=Object.keys(e),p=0;p<d.length;p++){var f=d[p];i[f]=e[f]}return i.href=i.format(),i}if(i.protocol=e.protocol,e.host||m[e.protocol])i.pathname=e.pathname;else{for(var h=(e.pathname||"").split("/");h.length&&!(e.host=h.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==h[0]&&h.unshift(""),h.length<2&&h.unshift(""),i.pathname=h.join("/")}if(i.search=e.search,i.query=e.query,i.host=e.host||"",i.auth=e.auth,i.hostname=e.hostname||e.host,i.port=e.port,i.pathname||i.search){var g=i.pathname||"",b=i.search||"";i.path=g+b}return i.slashes=i.slashes||e.slashes,i.href=i.format(),i}var y=i.pathname&&"/"===i.pathname.charAt(0),_=e.host||e.pathname&&"/"===e.pathname.charAt(0),k=_||y||i.host&&e.pathname,w=k,x=i.pathname&&i.pathname.split("/")||[],C=(h=e.pathname&&e.pathname.split("/")||[],i.protocol&&!v[i.protocol]);if(C&&(i.hostname="",i.port=null,i.host&&(""===x[0]?x[0]=i.host:x.unshift(i.host)),i.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===h[0]?h[0]=e.host:h.unshift(e.host)),e.host=null),k=k&&(""===h[0]||""===x[0])),_)i.host=e.host||""===e.host?e.host:i.host,i.hostname=e.hostname||""===e.hostname?e.hostname:i.hostname,i.search=e.search,i.query=e.query,x=h;else if(h.length)x||(x=[]),x.pop(),x=x.concat(h),i.search=e.search,i.query=e.query;else if(!r.isNullOrUndefined(e.search)){if(C)i.hostname=i.host=x.shift(),(T=!!(i.host&&i.host.indexOf("@")>0)&&i.host.split("@"))&&(i.auth=T.shift(),i.host=i.hostname=T.shift());return i.search=e.search,i.query=e.query,r.isNull(i.pathname)&&r.isNull(i.search)||(i.path=(i.pathname?i.pathname:"")+(i.search?i.search:"")),i.href=i.format(),i}if(!x.length)return i.pathname=null,i.search?i.path="/"+i.search:i.path=null,i.href=i.format(),i;for(var E=x.slice(-1)[0],A=(i.host||e.host||x.length>1)&&("."===E||".."===E)||""===E,S=0,L=x.length;L>=0;L--)"."===(E=x[L])?x.splice(L,1):".."===E?(x.splice(L,1),S++):S&&(x.splice(L,1),S--);if(!k&&!w)for(;S--;S)x.unshift("..");!k||""===x[0]||x[0]&&"/"===x[0].charAt(0)||x.unshift(""),A&&"/"!==x.join("/").substr(-1)&&x.push("");var T,R=""===x[0]||x[0]&&"/"===x[0].charAt(0);C&&(i.hostname=i.host=R?"":x.length?x.shift():"",(T=!!(i.host&&i.host.indexOf("@")>0)&&i.host.split("@"))&&(i.auth=T.shift(),i.host=i.hostname=T.shift()));return(k=k||i.host&&x.length)&&!R&&x.unshift(""),x.length?i.pathname=x.join("/"):(i.pathname=null,i.path=null),r.isNull(i.pathname)&&r.isNull(i.search)||(i.path=(i.pathname?i.pathname:"")+(i.search?i.search:"")),i.auth=e.auth||i.auth,i.slashes=i.slashes||e.slashes,i.href=i.format(),i},a.prototype.parseHost=function(){var e=this.host,t=s.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},140:function(e,t,i){(function(e,n){var r;/*! https://mths.be/punycode v1.4.1 by @mathias */!function(a){t&&t.nodeType,e&&e.nodeType;var o="object"==typeof n&&n;o.global!==o&&o.window!==o&&o.self;var s,l=2147483647,c=/^xn--/,u=/[^\x20-\x7E]/,d=/[\x2E\u3002\uFF0E\uFF61]/g,p={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},f=Math.floor,h=String.fromCharCode;function g(e){throw new RangeError(p[e])}function m(e,t){for(var i=e.length,n=[];i--;)n[i]=t(e[i]);return n}function v(e,t){var i=e.split("@"),n="";return i.length>1&&(n=i[0]+"@",e=i[1]),n+m((e=e.replace(d,".")).split("."),t).join(".")}function b(e){for(var t,i,n=[],r=0,a=e.length;r<a;)(t=e.charCodeAt(r++))>=55296&&t<=56319&&r<a?56320==(64512&(i=e.charCodeAt(r++)))?n.push(((1023&t)<<10)+(1023&i)+65536):(n.push(t),r--):n.push(t);return n}function y(e){return m(e,(function(e){var t="";return e>65535&&(t+=h((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=h(e)})).join("")}function _(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function k(e,t,i){var n=0;for(e=i?f(e/700):e>>1,e+=f(e/t);e>455;n+=36)e=f(e/35);return f(n+36*e/(e+38))}function w(e){var t,i,n,r,a,o,s,c,u,d,p,h=[],m=e.length,v=0,b=128,_=72;for((i=e.lastIndexOf("-"))<0&&(i=0),n=0;n<i;++n)e.charCodeAt(n)>=128&&g("not-basic"),h.push(e.charCodeAt(n));for(r=i>0?i+1:0;r<m;){for(a=v,o=1,s=36;r>=m&&g("invalid-input"),((c=(p=e.charCodeAt(r++))-48<10?p-22:p-65<26?p-65:p-97<26?p-97:36)>=36||c>f((l-v)/o))&&g("overflow"),v+=c*o,!(c<(u=s<=_?1:s>=_+26?26:s-_));s+=36)o>f(l/(d=36-u))&&g("overflow"),o*=d;_=k(v-a,t=h.length+1,0==a),f(v/t)>l-b&&g("overflow"),b+=f(v/t),v%=t,h.splice(v++,0,b)}return y(h)}function x(e){var t,i,n,r,a,o,s,c,u,d,p,m,v,y,w,x=[];for(m=(e=b(e)).length,t=128,i=0,a=72,o=0;o<m;++o)(p=e[o])<128&&x.push(h(p));for(n=r=x.length,r&&x.push("-");n<m;){for(s=l,o=0;o<m;++o)(p=e[o])>=t&&p<s&&(s=p);for(s-t>f((l-i)/(v=n+1))&&g("overflow"),i+=(s-t)*v,t=s,o=0;o<m;++o)if((p=e[o])<t&&++i>l&&g("overflow"),p==t){for(c=i,u=36;!(c<(d=u<=a?1:u>=a+26?26:u-a));u+=36)w=c-d,y=36-d,x.push(h(_(d+w%y,0))),c=f(w/y);x.push(h(_(c,0))),a=k(i,v,n==r),i=0,++n}++i,++t}return x.join("")}s={version:"1.4.1",ucs2:{decode:b,encode:y},decode:w,encode:x,toASCII:function(e){return v(e,(function(e){return u.test(e)?"xn--"+x(e):e}))},toUnicode:function(e){return v(e,(function(e){return c.test(e)?w(e.slice(4).toLowerCase()):e}))}},void 0===(r=function(){return s}.call(t,i,t,e))||(e.exports=r)}()}).call(this,i(94)(e),i(48))},141:function(e,t,i){"use strict";e.exports={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},142:function(e,t,i){"use strict";t.decode=t.parse=i(143),t.encode=t.stringify=i(144)},143:function(e,t,i){"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,t,i,a){t=t||"&",i=i||"=";var o={};if("string"!=typeof e||0===e.length)return o;var s=/\+/g;e=e.split(t);var l=1e3;a&&"number"==typeof a.maxKeys&&(l=a.maxKeys);var c=e.length;l>0&&c>l&&(c=l);for(var u=0;u<c;++u){var d,p,f,h,g=e[u].replace(s,"%20"),m=g.indexOf(i);m>=0?(d=g.substr(0,m),p=g.substr(m+1)):(d=g,p=""),f=decodeURIComponent(d),h=decodeURIComponent(p),n(o,f)?r(o[f])?o[f].push(h):o[f]=[o[f],h]:o[f]=h}return o};var r=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},144:function(e,t,i){"use strict";var n=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,t,i,s){return t=t||"&",i=i||"=",null===e&&(e=void 0),"object"==typeof e?a(o(e),(function(o){var s=encodeURIComponent(n(o))+i;return r(e[o])?a(e[o],(function(e){return s+encodeURIComponent(n(e))})).join(t):s+encodeURIComponent(n(e[o]))})).join(t):s?encodeURIComponent(n(s))+i+encodeURIComponent(n(e)):""};var r=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function a(e,t){if(e.map)return e.map(t);for(var i=[],n=0;n<e.length;n++)i.push(t(e[n],n));return i}var o=Object.keys||function(e){var t=[];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.push(i);return t}},145:function(e,t){e.exports={getExternalRestApiUri:function(e){return"//".concat(e,"/api/rest_v1/")}}},146:function(e,t,i){"use strict";var n=i(63).EditTransform;i(62);e.exports=function(e,t,i){var r=e.getElementById("pcs")||e.body,a=e.createElement("header"),o=r&&r.querySelector("section");o?r.insertBefore(a,o):r.appendChild(a);var s=t.pronunciation&&t.pronunciation.url,l=t.mw&&t.mw.pageprops&&t.mw.pageprops.wikibase_item,c=n.newPageHeader(e,t.mw.displaytitle,t.mw.description,t.mw.description_source,l,i,l&&(void 0===t.mw.description||"central"===t.mw.description_source),s);a.appendChild(c)}},147:function(e,t,i){"use strict";var n=i(64).PronunciationParentSelector,r=i(64).PronunciationSelector,a=i(64).SpokenWikipediaId;e.exports={parsePronunciation:function(e){var t=e.querySelector(n);if(t){var i=t.parentNode.querySelector(r),a=i&&i.getAttribute("href");return a&&{url:a}}},parseSpokenWikipedia:function(e){var t,i=e.querySelector("div".concat(a));if(i){var n,r=i.getAttribute("data-mw"),o=r&&JSON.parse(r),s=o&&o.parts[0],l=s&&s.template,c=l&&l.target;if(c&&c.wt)if("Spoken Wikipedia"===c.wt)t={files:[n="File:".concat(l.params[1].wt)]};else if(/Spoken Wikipedia-([2-5])/.exec(c.wt)){var u=Object.keys(l.params).length;t={files:[]};for(var d=2;d<=u;d++)n="File:".concat(l.params[d].wt),t.files.push(n)}}return t}}},148:function(e,t){function i(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var n=/[\[\]\s]/,r=function(){function e(t,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.element=t,this.id=i,this.backLinks=[]}var t,r,a;return t=e,a=[{key:"truncateLinkText",value:function(e){if(!e||e.length<6)return e;var t=e,i=t.split(n).filter((function(e){return""!==e})),r=i[i.length-1],a=i.length>1,o=a?2:3,s=r.length-o;return s>0&&(r=Array(s+2).join(".")+r.slice(s+1)),t=a?"[".concat(i[0].slice(0,1).toUpperCase()," ").concat(r,"]"):"[".concat(r,"]")}}],(r=null)&&i(t.prototype,r),a&&i(t,a),e}();e.exports=r},149:function(e,t,i){var n=i(63).HTMLUtilities;function r(e,t,i){var n=e.createElement("meta");return n.setAttribute("property",t),n.setAttribute("content",i),n}function a(e){Array.from(e.querySelectorAll("a.image")).forEach((function(t){var i=e.createElement("figure");i.className="mw-default-size",i.innerHTML=t.outerHTML,t.parentNode.replaceChild(i,t)}))}function o(e,t){var i=e.createElement("section");return i.setAttribute("data-mw-section-id",t.id),0===t.id?(i.setAttribute("id","content-block-0"),i.innerHTML=t.text):i.innerHTML="".concat(function(e,t){var i=t.toclevel+1;return"<h".concat(i,' id="').concat(n.escape(t.anchor),'">').concat(t.line,"</h").concat(i,">")}(0,t)).concat(t.text),i}function s(e,t){for(var i=e.querySelectorAll(t)||[],n=arguments.length,r=new Array(n>2?n-2:0),a=2;a<n;a++)r[a-2]=arguments[a];for(var o=function(e){var t=i[e];r.forEach((function(e){var i=t.getAttribute(e);i&&(i=i.replace(/^\/wiki\//,"./"),t.setAttribute(e,i))}))},s=0;s<i.length;s++)o(s)}e.exports={convertToParsoidDocument:function(e,t,i){!function(e,t,i){var n=i&&i.mw&&i.mw.normalizedtitle;t&&e.documentElement.setAttribute("about","http://".concat(i.domain,"/wiki/Special:Redirect/revision/").concat(t.revision)),e.createElement("meta").setAttribute("charset","utf-8");var a=e.head;if(a){a.appendChild(r(e,"mw:pageNamespace",t.ns)),a.appendChild(r(e,"mw:pageId",t.id)),a.appendChild(r(e,"dc:modified",t.lastmodified));var o=e.createElement("link");o.setAttribute("rel","dc:isVersionOf"),o.setAttribute("href","//".concat(i.domain,"/wiki/").concat(encodeURIComponent(n))),a.appendChild(o),a.querySelector("title").innerHTML=t.displaytitle;var s=e.createElement("base");s.setAttribute("href","//".concat(i.domain,"/wiki/")),a.appendChild(s)}}(e,t,i);var n=e.body;n.setAttribute("dir","ltr");var l=!0,c=!1,u=void 0;try{for(var d,p=t.sections[Symbol.iterator]();!(l=(d=p.next()).done);l=!0){var f=d.value;n.appendChild(o(e,f))}}catch(e){c=!0,u=e}finally{try{l||null==p.return||p.return()}finally{if(c)throw u}}return s(n,"a","href"),a(e),function(e){var t=1e3,i=e.querySelectorAll("ol.references"),n=!0,r=!1,a=void 0;try{for(var o,s=i[Symbol.iterator]();!(n=(o=s.next()).done);n=!0){var l=o.value;l.classList.add("mw-references");var c=e.createElement("DIV");c.classList.add("mw-references-wrap"),c.setAttribute("typeof","mw:Extension/references"),c.setAttribute("about","#mwt".concat(t++)),l.parentNode.replaceChild(c,l),c.appendChild(l)}}catch(e){r=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(r)throw a}}}(e),e},testing:{buildSection:o,rewriteWikiLinks:s,wrapImagesInFigureElements:a}}},150:function(e,t,i){e.exports=function(e){var t={};function i(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=e,i.c=t,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=2)}([function(e){e.exports=JSON.parse('{"ar":"٠١٢٣٤٥٦٧٨٩","fa":"۰۱۲۳۴۵۶۷۸۹","ml":"൦൧൨൩൪൫൬൭൮൯","kn":"೦೧೨೩೪೫೬೭೮೯","lo":"໐໑໒໓໔໕໖໗໘໙","or":"୦୧୨୩୪୫୬୭୮୯","kh":"០១២៣៤៥៦៧៨៩","nqo":"߀߁߂߃߄߅߆߇߈߉","pa":"੦੧੨੩੪੫੬੭੮੯","gu":"૦૧૨૩૪૫૬૭૮૯","hi":"०१२३४५६७८९","my":"၀၁၂၃၄၅၆၇၈၉","ta":"௦௧௨௩௪௫௬௭௮௯","te":"౦౧౨౩౪౫౬౭౮౯","th":"๐๑๒๓๔๕๖๗๘๙","bo":"༠༡༢༣༤༥༦༧༨༩"}')},function(e){e.exports=JSON.parse('{"ab":["ru"],"ace":["id"],"aln":["sq"],"als":["gsw","de"],"an":["es"],"anp":["hi"],"arn":["es"],"arz":["ar"],"av":["ru"],"ay":["es"],"ba":["ru"],"bar":["de"],"bat-smg":["sgs","lt"],"bcc":["fa"],"be-x-old":["be-tarask"],"bh":["bho"],"bjn":["id"],"bm":["fr"],"bpy":["bn"],"bqi":["fa"],"bug":["id"],"cbk-zam":["es"],"ce":["ru"],"crh":["crh-latn"],"crh-cyrl":["ru"],"csb":["pl"],"cv":["ru"],"de-at":["de"],"de-ch":["de"],"de-formal":["de"],"dsb":["de"],"dtp":["ms"],"egl":["it"],"eml":["it"],"ff":["fr"],"fit":["fi"],"fiu-vro":["vro","et"],"frc":["fr"],"frp":["fr"],"frr":["de"],"fur":["it"],"gag":["tr"],"gan":["gan-hant","zh-hant","zh-hans"],"gan-hans":["zh-hans"],"gan-hant":["zh-hant","zh-hans"],"gl":["pt"],"glk":["fa"],"gn":["es"],"gsw":["de"],"hif":["hif-latn"],"hsb":["de"],"ht":["fr"],"ii":["zh-cn","zh-hans"],"inh":["ru"],"iu":["ike-cans"],"jut":["da"],"jv":["id"],"kaa":["kk-latn","kk-cyrl"],"kbd":["kbd-cyrl"],"khw":["ur"],"kiu":["tr"],"kk":["kk-cyrl"],"kk-arab":["kk-cyrl"],"kk-latn":["kk-cyrl"],"kk-cn":["kk-arab","kk-cyrl"],"kk-kz":["kk-cyrl"],"kk-tr":["kk-latn","kk-cyrl"],"kl":["da"],"ko-kp":["ko"],"koi":["ru"],"krc":["ru"],"ks":["ks-arab"],"ksh":["de"],"ku":["ku-latn"],"ku-arab":["ckb"],"kv":["ru"],"lad":["es"],"lb":["de"],"lbe":["ru"],"lez":["ru"],"li":["nl"],"lij":["it"],"liv":["et"],"lmo":["it"],"ln":["fr"],"ltg":["lv"],"lzz":["tr"],"mai":["hi"],"map-bms":["jv","id"],"mg":["fr"],"mhr":["ru"],"min":["id"],"mo":["ro"],"mrj":["ru"],"mwl":["pt"],"myv":["ru"],"mzn":["fa"],"nah":["es"],"nap":["it"],"nds":["de"],"nds-nl":["nl"],"nl-informal":["nl"],"no":["nb"],"os":["ru"],"pcd":["fr"],"pdc":["de"],"pdt":["de"],"pfl":["de"],"pms":["it"],"pt":["pt-br"],"pt-br":["pt"],"qu":["es"],"qug":["qu","es"],"rgn":["it"],"rmy":["ro"],"roa-rup":["rup"],"rue":["uk","ru"],"ruq":["ruq-latn","ro"],"ruq-cyrl":["mk"],"ruq-latn":["ro"],"sa":["hi"],"sah":["ru"],"scn":["it"],"sg":["fr"],"sgs":["lt"],"sli":["de"],"sr":["sr-ec"],"srn":["nl"],"stq":["de"],"su":["id"],"szl":["pl"],"tcy":["kn"],"tg":["tg-cyrl"],"tt":["tt-cyrl","ru"],"tt-cyrl":["ru"],"ty":["fr"],"udm":["ru"],"ug":["ug-arab"],"uk":["ru"],"vec":["it"],"vep":["et"],"vls":["nl"],"vmf":["de"],"vot":["fi"],"vro":["et"],"wa":["fr"],"wo":["fr"],"wuu":["zh-hans"],"xal":["ru"],"xmf":["ka"],"yi":["he"],"za":["zh-hans"],"zea":["nl"],"zh":["zh-hans"],"zh-classical":["lzh"],"zh-cn":["zh-hans"],"zh-hant":["zh-hans"],"zh-hk":["zh-hant","zh-hans"],"zh-min-nan":["nan"],"zh-mo":["zh-hk","zh-hant","zh-hans"],"zh-my":["zh-sg","zh-hans"],"zh-sg":["zh-hans"],"zh-tw":["zh-hant","zh-hans"],"zh-yue":["yue"]}')},function(e,t,i){"use strict";i.r(t);var n=i(0);class r{constructor(e){this.locale=e}convertPlural(e,t){var i=new RegExp("\\d+=","i");if(!t||0===t.length)return"";for(let n=0;n<t.length;n++){let r=t[n];if(i.test(r)){if(parseInt(r.slice(0,r.indexOf("=")),10)===e)return r.slice(r.indexOf("=")+1);t[n]=void 0}}t=t.filter(e=>!!e);let n=this.getPluralForm(e,this.locale);return n=Math.min(n,t.length-1),t[n]}getPluralForm(e,t){const i=new Intl.PluralRules(t),n=i.resolvedOptions().pluralCategories,r=i.select(e);return["zero","one","two","few","many","other"].filter(e=>n.includes(e)).indexOf(r)}convertNumber(e,t){let i=this.digitTransformTable(this.locale),n=String(e),r="";if(!i)return e;if(t){if(parseFloat(e,10)===e)return e;let t=[];for(let e in i)t[i[e]]=e;i=t}for(let e=0;e<n.length;e++)i[n[e]]?r+=i[n[e]]:r+=n[e];return t?parseFloat(r,10):r}convertGrammar(e,t){return e}gender(e,t){if(!t||0===t.length)return"";for(;t.length<2;)t.push(t[t.length-1]);return"male"===e?t[0]:"female"===e?t[1]:3===t.length?t[2]:t[0]}digitTransformTable(e){return!!n[e]&&n[e].split("")}}var a={bs:class extends r{convertGrammar(e,t){switch(t){case"instrumental":e="s "+e;break;case"lokativ":e="o "+e}return e}},default:r,dsb:class extends r{convertGrammar(e,t){switch(t){case"instrumental":e="z "+e;break;case"lokatiw":e="wo "+e}return e}},fi:class extends r{convertGrammar(e,t){let i=e.match(/[aou][^äöy]*$/i),n=e;switch(e.match(/wiki$/i)&&(i=!1),e.match(/[bcdfghjklmnpqrstvwxz]$/i)&&(e+="i"),t){case"genitive":e+="n";break;case"elative":e+=i?"sta":"stä";break;case"partitive":e+=i?"a":"ä";break;case"illative":e+=e.slice(-1)+"n";break;case"inessive":e+=i?"ssa":"ssä";break;default:e=n}return e}},ga:class extends r{convertGrammar(e,t){if("ainmlae"===t)switch(e){case"an Domhnach":e="Dé Domhnaigh";break;case"an Luan":e="Dé Luain";break;case"an Mháirt":e="Dé Mháirt";break;case"an Chéadaoin":e="Dé Chéadaoin";break;case"an Déardaoin":e="Déardaoin";break;case"an Aoine":e="Dé hAoine";break;case"an Satharn":e="Dé Sathairn"}return e}},he:class extends r{convertGrammar(e,t){switch(t){case"prefixed":case"תחילית":"ו"===e.slice(0,1)&&"וו"!==e.slice(0,2)&&(e="ו"+e),"ה"===e.slice(0,1)&&(e=e.slice(1)),(e.slice(0,1)<"א"||e.slice(0,1)>"ת")&&(e="־"+e)}return e}},hsb:class extends r{convertGrammar(e,t){switch(t){case"instrumental":e="z "+e;break;case"lokatiw":e="wo "+e}return e}},hu:class extends r{convertGrammar(e,t){switch(t){case"rol":e+="ról";break;case"ba":e+="ba";break;case"k":e+="k"}return e}},hy:class extends r{convertGrammar(e,t){return"genitive"===t&&("ա"===e.slice(-1)?e=e.slice(0,-1)+"այի":"ո"===e.slice(-1)?e=e.slice(0,-1)+"ոյի":"գիրք"===e.slice(-4)?e=e.slice(0,-4)+"գրքի":e+="ի"),e}},la:class extends r{convertGrammar(e,t){switch(t){case"genitive":e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/u[ms]$/i,"i")).replace(/ommunia$/i,"ommunium")).replace(/a$/i,"ae")).replace(/libri$/i,"librorum")).replace(/nuntii$/i,"nuntiorum")).replace(/tio$/i,"tionis")).replace(/ns$/i,"ntis")).replace(/as$/i,"atis")).replace(/es$/i,"ei");break;case"accusative":e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/u[ms]$/i,"um")).replace(/ommunia$/i,"am")).replace(/a$/i,"ommunia")).replace(/libri$/i,"libros")).replace(/nuntii$/i,"nuntios")).replace(/tio$/i,"tionem")).replace(/ns$/i,"ntem")).replace(/as$/i,"atem")).replace(/es$/i,"em");break;case"ablative":e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/u[ms]$/i,"o")).replace(/ommunia$/i,"ommunibus")).replace(/a$/i,"a")).replace(/libri$/i,"libris")).replace(/nuntii$/i,"nuntiis")).replace(/tio$/i,"tione")).replace(/ns$/i,"nte")).replace(/as$/i,"ate")).replace(/es$/i,"e")}return e}},os:class extends r{convertGrammar(e,t){var i,n,r,a;switch(i="мæ",n="",r="",a="",e.match(/тæ$/i)?(e=e.slice(0,-1),i="æм"):e.match(/[аæеёиоыэюя]$/i)?n="й":e.match(/у$/i)?e.slice(-2,-1).match(/[аæеёиоыэюя]$/i)||(n="й"):e.match(/[бвгджзйклмнопрстфхцчшщьъ]$/i)||(r="-"),t){case"genitive":a=r+n+"ы";break;case"dative":a=r+n+"æн";break;case"allative":a=r+i;break;case"ablative":a="й"===n?r+n+"æ":r+n+"æй";break;case"superessive":a=r+n+"ыл";break;case"equative":a=r+n+"ау";break;case"comitative":a=r+"имæ"}return e+a}},ru:class extends r{convertGrammar(e,t){return"genitive"===t&&("ь"===e.slice(-1)?e=e.slice(0,-1)+"я":"ия"===e.slice(-2)?e=e.slice(0,-2)+"ии":"ка"===e.slice(-2)?e=e.slice(0,-2)+"ки":"ти"===e.slice(-2)?e=e.slice(0,-2)+"тей":"ды"===e.slice(-2)?e=e.slice(0,-2)+"дов":"ник"===e.slice(-3)&&(e=e.slice(0,-3)+"ника")),e}},sl:class extends r{convertGrammar(e,t){switch(t){case"mestnik":e="o "+e;break;case"orodnik":e="z "+e}return e}},uk:class extends r{convertGrammar(e,t){switch(t){case"genitive":"ь"===e.slice(-1)?e=e.slice(0,-1)+"я":"ія"===e.slice(-2)?e=e.slice(0,-2)+"ії":"ка"===e.slice(-2)?e=e.slice(0,-2)+"ки":"ти"===e.slice(-2)?e=e.slice(0,-2)+"тей":"ды"===e.slice(-2)?e=e.slice(0,-2)+"дов":"ник"===e.slice(-3)&&(e=e.slice(0,-3)+"ника");break;case"accusative":"ія"===e.slice(-2)&&(e=e.slice(0,-2)+"ію")}return e}}};const o=new RegExp("(?:([A-Za-zªµºÀ-ÖØ-öø-ʸʻ-ˁːˑˠ-ˤˮͰ-ͳͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-҂Ҋ-ԯԱ-Ֆՙ-՟ա-և։ः-हऻऽ-ीॉ-ौॎ-ॐक़-ॡ।-ঀংঃঅ-ঌএঐও-নপ-রলশ-হঽ-ীেৈোৌৎৗড়ঢ়য়-ৡ০-ৱ৴-৺ਃਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਾ-ੀਖ਼-ੜਫ਼੦-੯ੲ-ੴઃઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽ-ીૉોૌૐૠૡ૦-૰ૹଂଃଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽାୀେୈୋୌୗଡ଼ଢ଼ୟ-ୡ୦-୷ஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹாிுூெ-ைொ-ௌௐௗ௦-௲ఁ-ఃఅ-ఌఎ-ఐఒ-నప-హఽు-ౄౘ-ౚౠౡ౦-౯౿ಂಃಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽ-ೄೆ-ೈೊೋೕೖೞೠೡ೦-೯ೱೲംഃഅ-ഌഎ-ഐഒ-ഺഽ-ീെ-ൈൊ-ൌൎൗൟ-ൡ൦-൵൹-ൿංඃඅ-ඖක-නඳ-රලව-ෆා-ෑෘ-ෟ෦-෯ෲ-෴ก-ะาำเ-ๆ๏-๛ກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ະາຳຽເ-ໄໆ໐-໙ໜ-ໟༀ-༗༚-༴༶༸༾-ཇཉ-ཬཿ྅ྈ-ྌ྾-࿅࿇-࿌࿎-࿚က-ာေးျြဿ-ၗၚ-ၝၡ-ၰၵ-ႁႃႄႇ-ႌႎ-ႜ႞-ჅჇჍა-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚ፠-፼ᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙿᚁ-ᚚᚠ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱ᜵᜶ᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳាើ-ៅះៈ។-៚ៜ០-៩᠐-᠙ᠠ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᤣ-ᤦᤩ-ᤫᤰᤱᤳ-ᤸ᥆-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉ᧐-᧚ᨀ-ᨖᨙᨚ᨞-ᩕᩗᩡᩣᩤᩭ-ᩲ᪀-᪉᪐-᪙᪠-᪭ᬄ-ᬳᬵᬻᬽ-ᭁᭃ-ᭋ᭐-᭪᭴-᭼ᮂ-ᮡᮦᮧ᮪ᮮ-ᯥᯧᯪ-ᯬᯮ᯲᯳᯼-ᰫᰴᰵ᰻-᱉ᱍ-᱿᳀-᳇᳓᳡ᳩ-ᳬᳮ-ᳳᳵᳶᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼ‎ⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎ⅏Ⅰ-ↈ⌶-⍺⎕⒜-ⓩ⚬⠀-⣿Ⰰ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯ⵰ⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〮〯〱-〵〸-〼ぁ-ゖゝ-ゟァ-ヺー-ヿㄅ-ㄭㄱ-ㆎ㆐-ㆺㇰ-㈜㈠-㉏㉠-㉻㉿-㊰㋀-㋋㋐-㋾㌀-㍶㍻-㏝㏠-㏾㐀-䶵一-鿕ꀀ-ꒌꓐ-ꘌꘐ-ꘫꙀ-ꙮꚀ-ꚝꚠ-ꛯ꛲-꛷Ꜣ-ꞇ꞉-ꞭꞰ-ꞷꟷ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠤꠧ꠰-꠷ꡀ-ꡳꢀ-ꣃ꣎-꣙ꣲ-ꣽ꤀-ꤥ꤮-ꥆꥒ꥓꥟-ꥼꦃ-ꦲꦴꦵꦺꦻꦽ-꧍ꧏ-꧙꧞-ꧤꧦ-ꧾꨀ-ꨨꨯꨰꨳꨴꩀ-ꩂꩄ-ꩋꩍ꩐-꩙꩜-ꩻꩽ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫫꫮ-ꫵꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭥꭰ-ꯤꯦꯧꯩ-꯬꯰-꯹가-힣ힰ-ퟆퟋ-ퟻ-舘並-龎ﬀ-ﬆﬓ-ﬗＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ]|\ud800[\udc00-\udc0b]|\ud800[\udc0d-\udc26]|\ud800[\udc28-\udc3a]|𐀼|𐀽|\ud800[\udc3f-\udc4d]|\ud800[\udc50-\udc5d]|\ud800[\udc80-\udcfa]|𐄀|𐄂|\ud800[\udd07-\udd33]|\ud800[\udd37-\udd3f]|\ud800[\uddd0-\uddfc]|\ud800[\ude80-\ude9c]|\ud800[\udea0-\uded0]|\ud800[\udf00-\udf23]|\ud800[\udf30-\udf4a]|\ud800[\udf50-\udf75]|\ud800[\udf80-\udf9d]|\ud800[\udf9f-\udfc3]|\ud800[\udfc8-\udfd5]|\ud801[\udc00-\udc9d]|\ud801[\udca0-\udca9]|\ud801[\udd00-\udd27]|\ud801[\udd30-\udd63]|𐕯|\ud801[\ude00-\udf36]|\ud801[\udf40-\udf55]|\ud801[\udf60-\udf67]|𑀀|\ud804[\udc02-\udc37]|\ud804[\udc47-\udc4d]|\ud804[\udc66-\udc6f]|\ud804[\udc82-\udcb2]|𑂷|𑂸|\ud804[\udcbb-\udcc1]|\ud804[\udcd0-\udce8]|\ud804[\udcf0-\udcf9]|\ud804[\udd03-\udd26]|𑄬|\ud804[\udd36-\udd43]|\ud804[\udd50-\udd72]|\ud804[\udd74-\udd76]|\ud804[\udd82-\uddb5]|\ud804[\uddbf-\uddc9]|𑇍|\ud804[\uddd0-\udddf]|\ud804[\udde1-\uddf4]|\ud804[\ude00-\ude11]|\ud804[\ude13-\ude2e]|𑈲|𑈳|𑈵|\ud804[\ude38-\ude3d]|\ud804[\ude80-\ude86]|𑊈|\ud804[\ude8a-\ude8d]|\ud804[\ude8f-\ude9d]|\ud804[\ude9f-\udea9]|\ud804[\udeb0-\udede]|\ud804[\udee0-\udee2]|\ud804[\udef0-\udef9]|𑌂|𑌃|\ud804[\udf05-\udf0c]|𑌏|𑌐|\ud804[\udf13-\udf28]|\ud804[\udf2a-\udf30]|𑌲|𑌳|\ud804[\udf35-\udf39]|\ud804[\udf3d-\udf3f]|\ud804[\udf41-\udf44]|𑍇|𑍈|\ud804[\udf4b-\udf4d]|𑍐|𑍗|\ud804[\udf5d-\udf63]|\ud805[\udc80-\udcb2]|𑒹|\ud805[\udcbb-\udcbe]|𑓁|\ud805[\udcc4-\udcc7]|\ud805[\udcd0-\udcd9]|\ud805[\udd80-\uddb1]|\ud805[\uddb8-\uddbb]|𑖾|\ud805[\uddc1-\udddb]|\ud805[\ude00-\ude32]|𑘻|𑘼|𑘾|\ud805[\ude41-\ude44]|\ud805[\ude50-\ude59]|\ud805[\ude80-\udeaa]|𑚬|𑚮|𑚯|𑚶|\ud805[\udec0-\udec9]|\ud805[\udf00-\udf19]|𑜠|𑜡|𑜦|\ud805[\udf30-\udf3f]|\ud806[\udca0-\udcf2]|𑣿|\ud806[\udec0-\udef8]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e]|\ud809[\udc70-\udc74]|\ud809[\udc80-\udd43]|\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38]|\ud81a[\ude40-\ude5e]|\ud81a[\ude60-\ude69]|𖩮|𖩯|\ud81a[\uded0-\udeed]|𖫵|\ud81a[\udf00-\udf2f]|\ud81a[\udf37-\udf45]|\ud81a[\udf50-\udf59]|\ud81a[\udf5b-\udf61]|\ud81a[\udf63-\udf77]|\ud81a[\udf7d-\udf8f]|\ud81b[\udf00-\udf44]|\ud81b[\udf50-\udf7e]|\ud81b[\udf93-\udf9f]|𛀀|𛀁|\ud82f[\udc00-\udc6a]|\ud82f[\udc70-\udc7c]|\ud82f[\udc80-\udc88]|\ud82f[\udc90-\udc99]|𛲜|𛲟|\ud834[\udc00-\udcf5]|\ud834[\udd00-\udd26]|\ud834[\udd29-\udd66]|\ud834[\udd6a-\udd72]|𝆃|𝆄|\ud834[\udd8c-\udda9]|\ud834[\uddae-\udde8]|\ud834[\udf60-\udf71]|\ud835[\udc00-\udc54]|\ud835[\udc56-\udc9c]|𝒞|𝒟|𝒢|𝒥|𝒦|\ud835[\udca9-\udcac]|\ud835[\udcae-\udcb9]|𝒻|\ud835[\udcbd-\udcc3]|\ud835[\udcc5-\udd05]|\ud835[\udd07-\udd0a]|\ud835[\udd0d-\udd14]|\ud835[\udd16-\udd1c]|\ud835[\udd1e-\udd39]|\ud835[\udd3b-\udd3e]|\ud835[\udd40-\udd44]|𝕆|\ud835[\udd4a-\udd50]|\ud835[\udd52-\udea5]|\ud835[\udea8-\udeda]|\ud835[\udedc-\udf14]|\ud835[\udf16-\udf4e]|\ud835[\udf50-\udf88]|\ud835[\udf8a-\udfc2]|\ud835[\udfc4-\udfcb]|\ud836[\udc00-\uddff]|\ud836[\ude37-\ude3a]|\ud836[\ude6d-\ude74]|\ud836[\ude76-\ude83]|\ud836[\ude85-\ude8b]|\ud83c[\udd10-\udd2e]|\ud83c[\udd30-\udd69]|\ud83c[\udd70-\udd9a]|\ud83c[\udde6-\ude02]|\ud83c[\ude10-\ude3a]|\ud83c[\ude40-\ude48]|🉐|🉑|[\ud840-\ud868][\udc00-\udfff]|\ud869[\udc00-\uded6]|\ud869[\udf00-\udfff]|[\ud86a-\ud86c][\udc00-\udfff]|\ud86d[\udc00-\udf34]|\ud86d[\udf40-\udfff]|\ud86e[\udc00-\udc1d]|\ud86e[\udc20-\udfff]|[\ud86f-\ud872][\udc00-\udfff]|\ud873[\udc00-\udea1]|\ud87e[\udc00-\ude1d]|[\udb80-\udbbe][\udc00-\udfff]|\udbbf[\udc00-\udffd]|[\udbc0-\udbfe][\udc00-\udfff]|\udbff[\udc00-\udffd])|([֐־׀׃׆׈-׿߀-ߪߴߵߺ-ࠕࠚࠤࠨ࠮-ࡘ࡜-࢟‏יִײַ-ﬨשׁ-ﭏ؈؋؍؛-ي٭-ٯٱ-ەۥۦۮۯۺ-ܐܒ-ܯ݋-ޥޱ-޿ࢠ-࣢ﭐ-ﴽ﵀-﷏ﷰ-﷼﷾﷿ﹰ-﻾]|\ud802[\udc00-\udd1e]|\ud802[\udd20-\ude00]|𐨄|\ud802[\ude07-\ude0b]|\ud802[\ude10-\ude37]|\ud802[\ude3b-\ude3e]|\ud802[\ude40-\udee4]|\ud802[\udee7-\udf38]|\ud802[\udf40-\udfff]|\ud803[\udc00-\ude5f]|\ud803[\ude7f-\udfff]|\ud83a[\udc00-\udccf]|\ud83a[\udcd7-\udfff]|\ud83b[\udc00-\uddff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\udf00-\udfff]|\ud83b[\ude00-\udeef]|\ud83b[\udef2-\udeff]))");function s(e){let t,i,n,r,a,o,s,l,c,u,d,p,f,h,g,m,v,b,y=0;function _(e){return()=>{for(let t=0;t<e.length;t++){let i=e[t]();if(null!==i)return i}return null}}function k(e){let t=y,i=[];for(let n=0;n<e.length;n++){let r=e[n]();if(null===r)return y=t,null;i.push(r)}return i}function w(e,t){return()=>{let i=y,n=[],r=t();for(;null!==r;)n.push(r),r=t();return n.length<e?(y=i,null):n}}function x(t){let i=t.length;return()=>{let n=null;return e.slice(y,y+i)===t&&(n=t,y+=i),n}}function C(t){return()=>{let i=e.slice(y).match(t);return null===i?null:(y+=i[0].length,i[0])}}function E(){let e=k([n,r]);return null===e?null:e[1]}function A(){let e=k([a,o]);return null===e?null:["REPLACE",parseInt(e[1],10)-1]}var S,L;function T(){let e=k([t,w(0,v)]);if(null===e)return null;let i=e[1];return i.length>1?["CONCAT"].concat(i):i[0]}function R(){let e=k([f,i,A]);return null===e?null:[e[0],e[2]]}function j(){let e=k([f,i,v]);return null===e?null:[e[0],e[2]]}function P(){let e=k([h,p,g]);return null===e?null:e[1]}if(t=x("|"),i=x(":"),n=x("\\"),r=C(/^./),a=x("$"),o=C(/^\d+/),s=C(/^[^{}[\]$\\]/),l=C(/^[^{}[\]$\\|]/),c=C(/^[^{}[\]$\s]/),_([E,c]),u=_([E,l]),d=_([E,s]),S=C(/^[ !"$&'()*,./0-9;=?@A-Z^_`a-z~\x80-\xFF+-]+/),L=function(e){return e.toString()},f=()=>{let e=S();return null===e?null:L(e)},p=_([function(){let e=k([_([R,j]),w(0,T)]);return null===e?null:e[0].concat(e[1])},function(){let e=k([f,w(0,T)]);return null===e?null:[e[0]].concat(e[1])}]),h=x("{{"),g=x("}}"),m=_([P,A,function(){let e=w(1,d)();return null===e?null:e.join("")}]),v=_([P,A,function(){let e=w(1,u)();return null===e?null:e.join("")}]),b=function(){let e=w(0,m)();return null===e?null:["CONCAT"].concat(e)}(),null===b||y!==e.length)throw new Error("Parse error at position "+y.toString()+" in input: "+e);return b}class l{constructor(e){this.locale=e,this.emitter=new class{constructor(e){this.locale=e,this.language=new(a[e]||a.default)(e)}emit(e,t){let i,n,r;switch(typeof e){case"string":case"number":i=e;break;case"object":if(n=e.slice(1).map(e=>this.emit(e,t)),r=e[0].toLowerCase(),"function"!=typeof this[r])throw new Error('unknown operation "'+r+'"');i=this[r](n,t);break;case"undefined":i="";break;default:throw new Error("unexpected type in AST: "+typeof e)}return i}concat(e){let t="";return e.forEach(e=>{t+=e}),t}replace(e,t){let i=parseInt(e[0],10);return i<t.length?t[i]:"$"+(i+1)}plural(e){let t=parseFloat(this.language.convertNumber(e[0],10)),i=e.slice(1);return i.length?this.language.convertPlural(t,i):""}gender(e){let t=e[0],i=e.slice(1);return this.language.gender(t,i)}grammar(e){let t=e[0],i=e[1];return i&&t&&this.language.convertGrammar(i,t)}bidi(e){var t=function(e){var t=e.match(o);return t?void 0===t[2]?"ltr":"rtl":null}(e[0]);return"ltr"===t?"‪"+e[0]+"‬":"rtl"===t?"‫"+e[0]+"‬":e[0]}}(this.locale)}parse(e,t){if(e.includes("{{")){let i=new s(e);return this.emitter.emit(i,t)}return this.simpleParse(e,t)}simpleParse(e,t){return e.replace(/\$(\d+)/g,(e,i)=>{let n=parseInt(i,10)-1;return void 0!==t[n]?t[n]:"$"+i})}}class c{constructor(e){this.sourceMap=new Map}load(e,t){if("object"!=typeof e)throw Error("Invalid message source. Must be an object");if(t){if(!/^[a-zA-Z0-9-]+$/.test(t))throw Error(`Invalid locale ${t}`);for(let i in e)if(0!==i.indexOf("@")){if("object"==typeof e[i])return this.load(e);if("string"!=typeof e[i])throw Error(`Invalid message for message ${i} in ${t} locale.`);break}this.sourceMap.has(t)?this.sourceMap.set(t,Object.assign(this.sourceMap.get(t),e)):this.sourceMap.set(t,e)}else for(t in e)this.load(e[t],t)}getMessage(e,t){let i=this.sourceMap.get(t);return i?i[e]:null}hasLocale(e){return this.sourceMap.has(e)}}var u=i(1);i.d(t,"default",(function(){return d}));class d{constructor(e,t){t=t||{},this.locale=e,this.parser=new l(this.locale),this.messageStore=new c,t.messages&&this.load(t.messages,this.locale),this.finalFallback=t.finalFallback||"en"}load(e,t){return this.messageStore.load(e,t||this.locale)}i18n(e,...t){return this.parser.parse(this.getMessage(e),t)}setLocale(e){this.locale=e,this.parser=new l(this.locale)}getFallbackLocales(){return[...u[this.locale]||[],this.finalFallback]}getMessage(e){let t=this.locale,i=0;const n=this.getFallbackLocales(this.locale);for(;t;){let r=t.split("-"),a=r.length;do{let t=r.slice(0,a).join("-"),i=this.messageStore.getMessage(e,t);if(i)return i;a--}while(a);t=n[i],i++}return e}}}]).default},151:function(e){e.exports=JSON.parse('{"skr-arab":{"description-add-link-title":"عنوان دی تفصیل لکھو","info-box-close-text":"بند کرو","license-footer-name":" کریئیٹیو کامنز انتساب / یکساں-شراکت اجازت نامہ 3.0","article-read-more-title":"ٻیا پڑھو","table-title-other":"ٻیاں معلومات"},"uk":{"description-add-link-title":"Додати опис назви","table-title-other":"Більше інформації","article-read-more-title":"Детальніше","page-edit-history":"Повна історія редагування","info-box-close-text":"Закрити","page-last-edited":"{{PLURAL:$1|0=Відредаговано сьогодні|1=Відредаговано вчора|few=Відредаговано $1 дні назад|many=Відредаговано $1 днів назад}}","info-box-title":"Табличка основної інформації","page-talk-page":"Сторінка обговорення","page-similar-titles":"Подібні сторінки","page-location":"Переглянути на карті","view-in-browser-footer-link":"Переглянути статтю в браузері","article-about-title":"Про цю статтю","license-footer-name":"CC BY-SA 3.0","page-issues":"Проблеми сторінки","license-footer-text":"Вміст доступний на умовах $1, якщо не вказано інше.","page-read-in-other-languages":"Доступно в {{PLURAL:$1|іншій мові|$1 інших мовах}}"},"ta":{"article-read-more-title":"மேலும் படிக்க","table-title-other":"மேலதிகத் தகவல்கள்","page-edit-history":"முழு தொகுப்பு வரலாறு","info-box-close-text":"மூடு","page-last-edited":"{{PLURAL:$1|0=இன்று தொகுக்கப்பட்டது|1=நேற்று தொகுக்கப்பட்டது|$1 நாட்களுக்கு முன் தொகுக்கப்பட்டது}}","info-box-title":"விரைவு காரணி","page-talk-page":"பேச்சுப் பக்கத்தைக் காண்க","page-location":"நிலப்படத்தில் காட்டுக","page-similar-titles":"ஒத்த பக்கங்கள்","article-about-title":"இந்தக் கட்டுரையைப் பற்றி","license-footer-name":"CC BY-SA 3.0","page-issues":"பக்க சிக்கல்கள்","license-footer-text":"வேறுவகையாகக் குறிப்பிடப்பட்டிருந்தாலன்றி இவ்வுள்ளடக்கம் $1 இல் கீழ் கிடைக்கும்.","page-read-in-other-languages":"{{PLURAL:$1|$1 மற்ற மொழியில்|$1 மற்ற மொழிகளில்}} கிடைக்கத்தக்கது"},"fi":{"description-add-link-title":"Lisää otsikon kuvaus","table-title-other":"Lisätietoja","article-read-more-title":"Lue lisää","page-edit-history":"Täysi muokkaushistoria","info-box-close-text":"Sulje","page-last-edited":"{{PLURAL:$1|0=Muokattu tänään|1=Muokattu eilen|Muokattu $1 päivää sitten}}","info-box-title":"Pikafaktoja","page-talk-page":"Tarkastele keskustelusivua","page-similar-titles":"Samankaltaiset sivut","page-location":"Näytä kartalla","view-in-browser-footer-link":"Lue artikkeli selaimessa","article-about-title":"Tietoja tästä artikkelista","license-footer-name":"CC BY-SA 3.0","page-issues":"Sivun ongelmat","license-footer-text":"Sisältö on käytettävissä $1 -lisenssin ehtojen mukaisesti ellei toisin mainita.","page-read-in-other-languages":"Saatavilla {{PLURAL:$1|$1 muulla kielellä|$1 muulla kielellä}}"},"ku-latn":{"article-read-more-title":"Zêdetir bixwine","page-last-edited":"Berî $1 hate guherandin","table-title-other":"Zêdetir agahî","info-box-close-text":"Bigre","article-about-title":"Di derbarê vê gotarê de","license-footer-name":"CC BY-SA 3.0","page-issues":"Pirsgirêkên rûpelê","page-similar-titles":"Peyamên wek hev"},"ru":{"page-read-in-other-languages":"Доступно на {{PLURAL:$1|другом языке|$1 других языках}}","article-read-more-title":"Подробнее","table-title-other":"Подробнее","page-edit-history":"Полная история редактирования","info-box-close-text":"Закрыть","page-last-edited-unknown":"Отредактировано какое-то время назад","page-last-edited":"{{PLURAL:$1|0=Изменено сегодня|1=Изменено вчера|few=Изменено $1 дня назад|many=Изменено $1 дней назад}}","info-box-title":"Краткие факты","page-location":"Посмотреть на карте","page-similar-titles":"Похожие страницы","view-in-browser-footer-link":"Просмотреть статью в браузере","page-talk-page":"Страница обсуждения","article-about-title":"Об этой статье","page-issues":"Вопросы по странице","license-footer-name":"CC BY-SA 3.0","license-footer-text":"Содержимое доступно по лицензии $1","description-add-link-title":"Добавление краткого описания"},"id":{"description-add-link-title":"Tambah deskripsi judul","table-title-other":"Informasi lebih lanjut","article-read-more-title":"Baca lebih lanjut","page-edit-history":"Semua riwayat suntingan","info-box-close-text":"Tutup","page-last-edited":"{{PLURAL:$1|0=Disunting hari ini|1=Disunting kemarin|Disunting $1 hari yang lalu}}","info-box-title":"Fakta Singkat","page-talk-page":"Lihat halaman pembicaraan","page-similar-titles":"Halaman serupa","page-location":"Lihat di peta","view-in-browser-footer-link":"Lihat artikel di peramban","article-about-title":"Mengenai artikel ini","license-footer-name":"CC BY-SA 3.0","page-issues":"Masalah halaman","license-footer-text":"Konten tersedia di bawah $1 kecuali dinyatakan lain.","page-read-in-other-languages":"Tersedia dalam {{PLURAL:$1|$1 bahasa lain|$1 bahasa lain}}"},"pa":{"article-read-more-title":"ਹੋਰ ਪੜ੍ਹੋ","table-title-other":"ਹੋਰ ਜਾਣਕਾਰੀ","page-edit-history":"ਪੂਰਾ ਸੋਧ ਇਤੇਹਾਸ","info-box-close-text":"ਬੰਦ ਕਰੋ","page-last-edited":"$1 ਪਹਿਲਾੰ ਸੋਧਿਆ ਗਿਆ","info-box-title":"ਵਿਸ਼ੇਸ਼ ਤੱਥ","page-similar-titles":"ੲਿੱਕੋ ਜਿਹੇ ਪੰਨੇ","article-about-title":"ਇਸ ਲੇਖ ਬਾਰੇ","license-footer-name":"CC BY-SA 3.0","page-issues":"ਸਫ਼ੇ ਸੰਬੰਧੀ ਮੁੱਦੇ","license-footer-text":"ਇਹ ਸਮੱਗਰੀ $1 ਹੇਠ ਮੌਜੂਦ ਹੈ। ਅਜਿਹਾ ਨਾ ਹੋਣ ਉੱਤੇ ਵਿਸ਼ੇਸ਼ ਤੌਰ ਉੱਤੇ ਦੱਸਿਆ ਜਾਵੇਗਾ।","page-read-in-other-languages":"$1 ਹੋਰ ਭਾਸ਼ਾਵਾੰ ਵਿੱਚ ਉਪਲਬਧ"},"cs":{"description-add-link-title":"Přidat popis článku","table-title-other":"Další informace","article-read-more-title":"Číst dál","page-edit-history":"Celá historie editací","info-box-close-text":"Zavřít","page-last-edited-unknown":"Editováno před nějakou dobou","page-last-edited":"{{PLURAL:$1|0=Editováno dnes|1=Editováno včera|2=Editováno předevčírem|Editováno před $1 dny}}","info-box-title":"Stručná fakta","page-location":"Zobrazit na mapě","page-similar-titles":"Podobné stránky","view-in-browser-footer-link":"Zobrazit článek v prohlížeči","page-talk-page":"Zobrazit diskusní stránku","article-about-title":"O tomto článku","page-issues":"Nedostatky stránky","license-footer-name":"CC BY-SA 3.0","license-footer-text":"Obsah je dostupný pod $1, pokud není uvedeno jinak.","page-read-in-other-languages":"Dostupné v {{PLURAL:$1|$1 dalším jazyce|$1 dalších jazycích}}"},"te":{"article-read-more-title":"మరింత చదవండి","table-title-other":"మరింత సమాచారం","page-last-edited":"$1 రోజుల క్రితం మార్చబడింది","article-about-title":"ఈ వ్యాసం గురించి","info-box-close-text":"మూసివేయి","page-read-in-other-languages":"$1 భాషల్లో చదవండి","info-box-title":"త్వరిత వాస్తవాలు","page-edit-history":"పూర్తి మార్పుల చరితం","page-issues":"పేజి సమస్యలు","page-similar-titles":"పోలిన పేజీలు"},"tcy":{"article-read-more-title":"ನನಲಾ ಓದುಲೇ","table-title-other":"ಜಾಸ್ತಿ ಮಾಹಿತಿ","info-box-close-text":"ಮುಚ್ಚಿಲೆ","page-last-edited":"$1 ದಿನ ದುಂದು ಸೇರ್ಸಾಯಿನಾ","page-talk-page":"ಚರ್ಚೆ ಪುಟೊನು ತೂಲೆ","info-box-title":"ಇತ್ತೆತ ವಿಸಯೊ.","page-similar-titles":"ಇಂಚಿನವೇ ಪುಟಕುಲು","article-about-title":"ಈ ಲೇಕನೋತ ಬಗ್ಗೆ","license-footer-name":"CC BY-SA 3.0","page-issues":"ಪುಟೊತ್ತಾ ಸಮಸ್ಯೆ","license-footer-text":"ಪ್ರತ್ಯೇಕವಾದ್ ಉಲ್ಲೇಕ ಮಲ್ಪಂದೆ ಇತ್ತ್ಂಡ, ವಿಸಯ \\"$1\\" ದಡಿಟ್ ಲಭ್ಯ ಉಂಡು.","page-read-in-other-languages":"$1 ಬಾಸೆಲೆಡ್ ಉಂಡು"},"cnh":{"article-read-more-title":"Tamdeuh in relnak","table-title-other":"Konglam tamdeuh","info-box-close-text":"Chuak","info-box-title":"Cinkenawk tete","license-footer-text":"$1 tangah chungum pawl zoh khawh a si.","page-similar-titles":"Cahmai khatmi pawl","license-footer-name":"CC BY-SA 3.0","page-issues":"Cahmai nambar"},"ur":{"description-add-link-title":"عنوان کی وضاحت درج کریں","article-read-more-title":"مزید پڑھیں","table-title-other":"دیگر معلومات","page-edit-history":"مکمل ترمیمی تاریخچہ","info-box-close-text":"بند کریں","page-last-edited":"$1 دن پہلے ترمیم کیا گیا.","info-box-title":"اجمالی معلومات","page-talk-page":"تبادلۂ خیال صفحہ دیکھیں","page-similar-titles":"یکساں صفحات","article-about-title":"اس مضمون کے بارے میں","license-footer-name":" کریئیٹیو کامنز انتساب / یکساں-شراکت اجازت نامہ 3.0","page-issues":"تبصرہ تحریر","license-footer-text":"تمام مواد $1 کے تحت میسر ہے، جب تک کوئی دوسری وجہ نا ہو۔","page-read-in-other-languages":"$1 دیگر زبانوں ميں دستیاب"},"fo":{"info-box-title":"Skjót fakta","table-title-other":"Meira kunning","info-box-close-text":"Lat aftur","page-similar-titles":"Líknandi síður","article-read-more-title":"Les meira","article-about-title":"Um hesa greinina"},"mk":{"description-add-link-title":"Додај опис на насловот","table-title-other":"Повеќе информации","article-read-more-title":"Прочитајте повеќе","page-edit-history":"Цела историја на уредувања","info-box-close-text":"Затвори","page-last-edited":"{{PLURAL:$1|0=Изменето денес|1=Изменето вчера|Изменето пред $1 дена}}","page-last-edited-unknown":"Уредено пред некое време","info-box-title":"Кратки факти","page-similar-titles":"Слични страници","page-talk-page":"Разговорна страница","page-location":"Погл. на карта","view-in-browser-footer-link":"Отвори во прелистувач","article-about-title":"Уреди ја статијава","license-footer-name":"CC-BY-SA 3.0","page-issues":"Проблеми со страницата","license-footer-text":"Содржината е достапна под условите на $1 освен ако не е поинаку наведено.","page-read-in-other-languages":"Достапно на {{PLURAL:$1|уште $1 јазик|уште $1 јазици}}"},"th":{"description-add-link-title":"เพิ่มคำอธิบายชื่อเรื่อง","table-title-other":"ข้อมูลเพิ่มเติม","article-read-more-title":"อ่านเพิ่มเติม","page-edit-history":"ประวัติการแก้ไขแบบเต็ม","info-box-close-text":"ปิด","page-last-edited":"{{PLURAL:$1|0=แก้ไขเมื่อวันนี้|1=แก้ไขเมื่อวาน|แก้ไขเมื่อ $1 วันที่ผ่านมา}}","info-box-title":"ข้อมูลเบื้องต้น","view-in-browser-footer-link":"ดูบทความในเบราว์เซอร์","page-location":"ดูบนแผนที่","page-talk-page":"ดูหน้าคุย","page-similar-titles":"หน้าที่คล้ายกัน","article-about-title":"เกี่ยวกับบทความนี้","license-footer-name":"CC BY-SA 3.0","page-issues":"ปัญหาในหน้า","license-footer-text":"เนื้อหาอนุญาตให้เผยแพร่ภายใต้ $1 เว้นแต่ระบุไว้เป็นอย่างอื่น","page-read-in-other-languages":"มีใน {{PLURAL:$1|$1 ภาษาอื่น}}"},"ml":{"info-box-title":"വസ്തുതകൾ","description-add-link-title":"തലക്കെട്ട് വിവരണം ചേർക്കുക","table-title-other":"കൂടുതൽ വിവരങ്ങൾ","info-box-close-text":"അടയ്ക്കുക","article-read-more-title":"കൂടുതൽ വായിക്കുക","article-about-title":"ഈ ലേഖനത്തെക്കുറിച്ച്"},"ab":{"article-about-title":"Ари астатиа иазкны","info-box-close-text":"Иарктәуп","page-similar-titles":"Еиҧшу адаҟьақәа","license-footer-name":"CC BY-SA 3.0","page-issues":"Адаҟьазы азҵаарақәа"},"lb":{"table-title-other":"Méi Informatiounen","article-read-more-title":"Liest méi","page-edit-history":"All Versioune vun der Säit","info-box-close-text":"Zoumaachen","page-last-edited":"{{PLURAL:$1|0=Haut today|1=Gëschter|Viru(n) $1 Deeg}} geännert","page-last-edited-unknown":"Virun enger gewësser Zäit geännert","info-box-title":"Séier Fakten","page-location":"Op enger Kaart weisen","page-talk-page":"Diskussiounssäit weisen","page-similar-titles":"Ähnlech Säiten","view-in-browser-footer-link":"Artikel am Browser kucken","article-about-title":"Iwwer dësen Artikel","license-footer-name":"CC-BY-SA 3.0","page-issues":"Problemer mat der Säit","license-footer-text":"Den Inhalt ass ënner der $1 disponibel wann et net anescht uginn ass.","page-read-in-other-languages":"Disponibel a(n) $1 anere Sproochen"},"sr-el":{"description-add-link-title":"Dodaj opis naslova","table-title-other":"Više informacija","article-read-more-title":"Pročitajte više","page-edit-history":"Celokupna istorija izmena","info-box-close-text":"Zatvori","page-last-edited":"{{PLURAL:$1|0=Uređeno danas|1=Uređeno juče|Uređeno pre $1 dana}}","info-box-title":"Kratke činjenice","view-in-browser-footer-link":"Prikaži članak u pregledaču","page-location":"Prikaži na mapi","page-talk-page":"Prikaži stranicu za razgovor","page-similar-titles":"Slične stranice","article-about-title":"O ovom članku","license-footer-name":"CC BY-SA 3.0","page-issues":"Problemi sa stranicom","license-footer-text":"Sadržaj je dostupan pod licencom $1, osim ako je drugačije navedeno.","page-read-in-other-languages":"Dostupno na još $1 jezika"},"bn":{"table-title-other":"আরও তথ্য","article-read-more-title":"আরো পড়ুন","page-edit-history":"সম্পূর্ণ সম্পাদনা ইতিহাস","info-box-close-text":"বন্ধ","page-last-edited":"$1 দিন আগে সম্পাদিত","info-box-title":"দ্রুত তথ্য","page-talk-page":"আলাপ পাতা দেখুন","page-similar-titles":"অনুরূপ পাতাগুলি","page-location":"একটি মানচিত্রে দেখুন","view-in-browser-footer-link":"ব্রাউজারে নিবন্ধ দেখুন","article-about-title":"এই নিবন্ধ সম্পর্কে","license-footer-name":"সিসি বাই-এসএ ৩.০","page-issues":"পাতার সমস্যা","license-footer-text":"বিষয়বস্তু $1-এর আওতায় প্রকাশিত যদি না অন্য কিছু নির্ধারিত থাকে।","page-read-in-other-languages":"আরো $1টি ভাষায় উপলব্ধ"},"fr":{"page-read-in-other-languages":"Disponible dans $1 autre{{PLURAL:$1||s}} langue{{PLURAL:$1||s}}","article-read-more-title":"En savoir plus","table-title-other":"Plus d’informations","page-edit-history":"Historique complet des modifications","info-box-close-text":"Fermer","page-last-edited-unknown":"Modifié il y a quelque temps","page-last-edited":"Modifié {{PLURAL:$1|0=aujourd’hui|1=hier|il y a $1 jours}}","info-box-title":"Faits en bref","page-location":"Afficher sur une carte","page-similar-titles":"Pages similaires","view-in-browser-footer-link":"Afficher l’article dans le navigateur","page-talk-page":"Afficher la page de discussion","article-about-title":"À propos de cet article","page-issues":"Problèmes de page","license-footer-name":"CC BY-SA 3.0","license-footer-text":"Le contenu est disponible sous $1 sauf spécification contraire.","description-add-link-title":"Ajouter la description du titre"},"cy":{"info-box-title":"Ffeithiau sydyn","article-about-title":"Ynglŷn â\'r erthygl hon","info-box-close-text":"Cau","article-read-more-title":"Darllen rhagor","table-title-other":"Rhagor o wybodaeth"},"sa":{"article-read-more-title":"अधिकं पठ्यताम्","table-title-other":"अधिकसूचना","info-box-close-text":"पिदधातु","info-box-title":"त्वरिततथ्यानि","license-footer-text":"$1 अन्तर्गतम् अन्तर्विषयः उपलब्धः अस्ति","page-similar-titles":"समानपृष्ठानि","license-footer-name":"CC BY-SA 3.0","page-issues":"पृष्ठस्य समस्याः"},"jv":{"description-add-link-title":"Wuwuh wedharaning sesirah","article-read-more-title":"Waca candhaké","table-title-other":"Katerangan liyané","page-edit-history":"Sajarah besutan wutuh","info-box-close-text":"Tutup","page-last-edited":"{{PLURAL:$1|0=Kabesut dina iki|1=Kabesut wingi|Kabesut $1 dina kapungkur}}","info-box-title":"Sunyata Rikat","view-in-browser-footer-link":"Deleng artikel ing pangluron","page-location":"Deleng ing petha","page-similar-titles":"Kaca sairib","page-talk-page":"Deleng kaca parembugan","article-about-title":"Ngenani artikel iki","page-issues":"Masalah kaca","license-footer-name":"CC BY-SA 3.0","license-footer-text":"Isi cumepak kanthi pangayoman $1, kajaba ana katerangan liyané.","page-read-in-other-languages":"Cumawis ing {{PLURAL:$1|$1 basa liyané}}"},"tl":{"info-box-title":"Madaliang kabatiran","info-box-close-text":"Isara","article-read-more-title":"Magbasa pa","table-title-other":"Higit pang kabatiran (impormasyon)"},"diq":{"info-box-title":"Pratik zanage","table-title-other":"Dehana vêşi melumat","info-box-close-text":"Racın","article-read-more-title":"Vêşi bıwane","article-about-title":"Heqa na wesiqa"},"af":{"article-read-more-title":"Lees meer","table-title-other":"Meer inligting","info-box-close-text":"Sluit","page-last-edited":"$1 dae gelede gewysig","info-box-title":"Kitsfeite","page-location":"Bekyk op ’n kaart","page-similar-titles":"Soortgelyke bladsye","article-about-title":"Oor hierdie artikel","license-footer-name":"CC BY-SA 3.0","page-issues":"Bladprobleme","license-footer-text":"Inhoud is beskikbaar onder $1-lisensie tensy anders vermeld.","page-read-in-other-languages":"Beskikbaar in nog $1 tale"},"zh-hant":{"description-add-link-title":"增加標題描述","table-title-other":"更多資訊","article-read-more-title":"延伸閱讀","page-edit-history":"完整編輯歷史","info-box-close-text":"關閉","page-last-edited":"{{PLURAL:$1|0=今日編輯|1=昨日編輯|$1 日前編輯}}","page-last-edited-unknown":"在之前編輯過","info-box-title":"快速預覽","page-similar-titles":"相似的頁面","page-talk-page":"檢視對話頁","view-in-browser-footer-link":"在瀏覽器裡檢視條目","page-location":"在地圖上檢視","article-about-title":"關於此條目","license-footer-name":"CC BY-SA 3.0","page-issues":"頁面問題","license-footer-text":"除非另有註明，否則頁面內容均以 $1 條款授權","page-read-in-other-languages":"在 {{PLURAL:$1|$1 種其它語言}}中可用"},"br":{"description-add-link-title":"Ouzhpennañ deskvrivadur an titl","article-read-more-title":"Lenn muioc\'h","table-title-other":"Muioc\'h a ditouroù","page-edit-history":"Istor klok ar c\'hemmoù","info-box-close-text":"Serriñ","page-last-edited":"Kemmet $1 deiz zo","info-box-title":"Fedoù berr-ha-berr","page-talk-page":"Gwelet ar bajenn gaozeal","page-location":"Diskwel war ur gartenn","page-similar-titles":"Pajennoù damheñvel","article-about-title":"Diwar-benn ar pennad-mañ","license-footer-name":"CC-BY-SA 3.0","page-issues":"Kudennoù war ar bajenn","license-footer-text":"An endalc\'had a c\'haller kavout dindan $1, nemet e vefe spisaet ar c\'hontrol.","page-read-in-other-languages":"A c\'haller kaout e $1 yezh all"},"sd":{"description-add-link-title":"عنوان جي تشريح وجھو","table-title-other":"وڌيڪ معلومات","article-read-more-title":"وڌيڪ پڙهو","page-edit-history":"پوري سنوار جي سوانح","info-box-close-text":"بند ڪريو","page-last-edited":"{{PLURAL:$1|0=اڄ سنواريل|1=ڪالھ سنواريل|$1 ڏينھن اڳ سنواريل}}","info-box-title":"ترت ڄاڻ","view-in-browser-footer-link":"مضمون کي جھانگوءَ ۾ ڏسو","page-location":"نقشي تي ڏسو","page-talk-page":"بحث صفحو ڏسو","page-similar-titles":"ان جهڙا صفحا","article-about-title":"هن مضمون بابت","license-footer-name":"CC BY-SA 3.0","page-issues":"صفحي جا مسئلا","license-footer-text":"مواد $1 ھيٺ موجود آهي نہ تہ ٻي صورت واضع ڪيل آھي.","page-read-in-other-languages":"{{PLURAL:$1|$1 ٻي ٻولي|$1 ٻين ٻولين}} ۾ موجود"},"mai":{"article-read-more-title":"आर बेसी पढू","table-title-other":"आधिक जानकारी","info-box-close-text":"बंद करू","license-footer-name":"CC BY-SA 3.0","license-footer-text":"$१ के तहत सामग्री उपलब्ध अछि","page-similar-titles":"एकरंग पन्ना","page-issues":"पन्नाके मुद्दासभ","info-box-title":"त्वरित तथ्य"},"da":{"description-add-link-title":"Tilføj titelbeskrivelse","table-title-other":"Flere oplysninger","article-read-more-title":"Læs mere","page-edit-history":"Fuld redigeringshistorik","info-box-close-text":"Luk","page-last-edited":"{{PLURAL:$1|0=Redigeret i dag|1=Redigeret i går|Redigeret for $1 dage siden}}","info-box-title":"Hurtige fakta","page-talk-page":"Vis diskussionsside","page-similar-titles":"Lignende sider","page-location":"Vis på et kort","view-in-browser-footer-link":"Vis artikel i browser","article-about-title":"Om denne artikel","license-footer-name":"CC BY-SA 3.0","page-issues":"Problemer med siden","license-footer-text":"Indholdet er udgivet under $1 medmindre andet er angivet.","page-read-in-other-languages":"Tilgængelig på {{PLURAL:$1|$1 andet sprog|$1 andre sprog}}"},"xmf":{"info-box-title":"მერკე ფაქტეფი","article-about-title":"თე სტატიაშ გეშა","info-box-close-text":"კილუა","article-read-more-title":"კილიშკილშა კითხირი","table-title-other":"კილიშკილშა"},"bs":{"article-read-more-title":"Također pogledajte","table-title-other":"Više informacija","page-edit-history":"Cjelokupna historija izmjena","info-box-close-text":"Zatvori","page-last-edited":"Izmijenjeno prije $1 dana","info-box-title":"Kratke činjenice","page-location":"Pogledaj na karti","page-similar-titles":"Slične stranice","article-about-title":"O ovom članku","license-footer-name":"CC BY-SA 3.0","page-issues":"Problemi sa sadržajem","license-footer-text":"Sadržaj je dostupan pod $1 osim ako je drukčije navedeno.","page-read-in-other-languages":"Dostupno na još $1 jezika"},"ms":{"description-add-link-title":"Tambah huraian tajuk","table-title-other":"Maklumat lanjut","article-read-more-title":"Selanjutnya","page-edit-history":"Semua sejarah penyuntingan","info-box-close-text":"Tutup","page-last-edited":"{{PLURAL:$1|0=Disunting hari ini|1=Disunting semalam|Disunting $1 hari lalu}}","info-box-title":"Fakta Segera","view-in-browser-footer-link":"Lihat rencana dalam penyemak imbas","page-location":"Paparkan di peta","page-talk-page":"Lihat laman perbincangan","page-similar-titles":"Laman-laman serupa","article-about-title":"Mengenai rencana ini","license-footer-name":"CC BY-SA 3.0","page-issues":"Isu-isu laman","license-footer-text":"Kandungan disediakan di bawah $1 melainkan dinyatakan sebaliknya.","page-read-in-other-languages":"Tersedia dalam {{PLURAL:$1|$1 bahasa lain|$1 bahasa lain}}"},"pl":{"description-add-link-title":"Dodaj opis tytułu","table-title-other":"Więcej informacji","article-read-more-title":"Czytaj więcej","page-edit-history":"Pełna historia edycji","info-box-close-text":"Zamknij","page-last-edited":"{{PLURAL:$1|0=Edytowane dzisiaj|1=Edytowane wczoraj|Edytowane $1 dni temu}}","info-box-title":"Szybkie fakty","view-in-browser-footer-link":"Zobacz artykuł w przeglądarce","page-location":"Zobacz na mapie","page-talk-page":"Zobacz stronę dyskusji","page-similar-titles":"Podobne strony","article-about-title":"O tym artykule","license-footer-name":"CC BY-SA 3.0","page-issues":"Problemy ze stroną","license-footer-text":"Treść udostępniana na licencji $1, jeśli nie podano inaczej.","page-read-in-other-languages":"Dostępne w {{PLURAL:$1|$1 innym języku|$1 innych językach}}"},"el":{"article-read-more-title":"Διαβάστε περισσότερα","table-title-other":"Περισσότερες πληροφορίες","page-edit-history":"Πλήρες ιστορικό επεξεργασίας","info-box-close-text":"Κλείσιμο","page-last-edited":"Επεξεργάστηκε $1 ημέρες πριν","info-box-title":"Γρήγορες Πληροφορίες","view-in-browser-footer-link":"Προβολή άρθρου στο φυλλομετρητή","page-location":"Προβολή σε έναν χάρτη","page-similar-titles":"Παρόμοιες σελίδες","page-talk-page":"Προβολή σελίδας συζήτησης","license-footer-name":"CC BY-SA 3.0","article-about-title":"Σχετικά με αυτό το άρθρο","page-issues":"Ζητήματα με τη σελίδα","license-footer-text":"Το περιεχόμενο είναι διαθέσιμο σύμφωνα με την $1 εκτός αν αναφέρεται διαφορετικά.","page-read-in-other-languages":"Διαθέσιμο σε $1 άλλες γλώσσες"},"sco":{"article-about-title":"Aboot this airticle","article-read-more-title":"Read mair"},"he":{"description-add-link-title":"הוספת תיאור כותרת","table-title-other":"מידע נוסף","article-read-more-title":"לקרוא עוד","page-edit-history":"היסטוריית עריכה מלאה","info-box-close-text":"סגירה","page-last-edited":"{{PLURAL:$1|0=נערך היום|נערך אתמול|נערך שלשום|נערך לפני $1 ימים}}","page-last-edited-unknown":"נערך לפני זמן־מה","info-box-title":"עובדות מהירות","page-location":"הצגה על מפה","page-talk-page":"הצגת דף השיחה","view-in-browser-footer-link":"הצגת הערך בדפדפן","page-similar-titles":"דפים דומים","article-about-title":"מידע על הערך הזה","license-footer-name":"CC-BY-SA 3.0","page-issues":"בעיות בדף","license-footer-text":"התוכן זמין לפי תנאי $1 אלא אם נאמר אחרת.","page-read-in-other-languages":"זמין ב{{PLURAL:$1|עוד שפה אחת|־$1 שפות נוספות}}"},"mr":{"article-read-more-title":"अधिक वाचन","table-title-other":"अधिक माहिती","page-edit-history":"संपूर्ण संपादन इतिहास","info-box-close-text":"बंद करा","page-last-edited":"$1 दिवसांआधी संपादित केले","info-box-title":"जलद तथ्य","page-similar-titles":"सारखी पाने","article-about-title":"या लेखाबद्दल","page-issues":"पानाची समस्या","license-footer-name":"CC BY-SA 3.0","license-footer-text":"तसे नोंदविलेले नसेल तर, यातील आशय हा $1 अंतर्गत उपलब्ध आहे","page-read-in-other-languages":"$1 इतर भाषेत उपलब्ध"},"oc":{"article-read-more-title":"Ne saber mai","page-last-edited":"Modificat i a $1 jorns","table-title-other":"Mai d\'informacions","article-about-title":"A prepaus d\'aqueste article","info-box-close-text":"Tampar","page-read-in-other-languages":"Disponible dins $1 autras lengas","info-box-title":"Faits en brèu","page-edit-history":"Istoric complet de las modificacions","page-issues":"Problèmas de pagina","page-similar-titles":"Paginas similaras","license-footer-name":"CC BY-SA 3.0"},"be-tarask":{"article-read-more-title":"Даведацца болей","table-title-other":"Болей зьвестак","page-edit-history":"Уся гісторыя рэдагаваньняў","info-box-close-text":"Закрыць","page-last-edited":"Рэдагаваны $1 дзён таму","info-box-title":"Хуткія факты","page-similar-titles":"Падобныя старонкі","article-about-title":"Пра гэты артыкул","license-footer-name":"CC BY-SA 3.0","page-issues":"Хібы старонкі","license-footer-text":"Зьмест даступны на ўмовах $1, калі не пазначанае іншае.","page-read-in-other-languages":"Чытаць на $1 іншых мовах"},"krc":{"info-box-close-text":"Джаб","info-box-title":"Къысха фактла"},"en":{"description-add-link-title":"Add title description","table-title-other":"More information","article-read-more-title":"Read more","page-edit-history":"Full edit history","info-box-close-text":"Close","page-last-edited":"{{PLURAL:$1|0=Edited today|1=Edited yesterday|Edited $1 days ago}}","page-last-edited-unknown":"Edited some time ago","view-in-browser-footer-link":"View article in browser","info-box-title":"Quick Facts","page-talk-page":"View talk page","page-similar-titles":"Similar pages","page-location":"View on a map","article-about-title":"About this article","license-footer-name":"CC BY-SA 3.0","page-issues":"Page issues","license-footer-text":"Content is available under $1 unless otherwise noted.","page-read-in-other-languages":"Available in {{PLURAL:$1|$1 other language|$1 other languages}}"},"mnw":{"description-add-link-title":"စုတ် က္ဍိုပ်လိက် မဗမံက်ထ္ၜး","table-title-other":"ဆက်ကဵု ပရိုင်တင်ဂၞင်","article-read-more-title":"ဆက် ဗှ်","page-edit-history":"ဗဵု လၟေင် မပလေဝ်ကၠုင်လဝ်","info-box-close-text":"ကၟာတ်","page-last-edited":"ပလေဝ်ဒါန်လဝ် နူကဵု $1 တ္ၚဲမတုဲကၠုင်","info-box-title":"တိတ်ထောအ်","view-in-browser-footer-link":"ဗဵု လိက်ပရေင် ပ္ဍဲ browser","page-location":"ဗဵု ပ္ဍဲ ဗီုတိ","page-talk-page":"ဗဵု မုက်လိက် ဓရီုကျာ","page-similar-titles":"မုက်လိက် မတုပ်သၟဟ်","article-about-title":"ပရူ လိက်ပရေင် ဏအ်","license-footer-name":"CC BY-SA 3.0","page-issues":"မုက်လိက် ပတိတ်တြး","license-footer-text":"လိက်ဂှ် မံက် ပ္ဍဲ $1 ယဝ်ရတင်ယောင်ယာ မွဲမွဲ ဟွံမွဲမ္ဂး၊၊","page-read-in-other-languages":"ဟွံမွဲဏီ ပ္ဍဲ $1 ဘာသာတၞဟ်"},"tr":{"description-add-link-title":"Başlık tanımı ekle","table-title-other":"Daha fazla bilgi","article-read-more-title":"Devamını oku","page-edit-history":"Bütün düzenleme geçmişi","info-box-close-text":"Kapat","page-last-edited":"{{PLURAL:$1|0=Bugün düzenlendi|1=Dün düzenlendi|$1 gün önce düzenlendi}}","page-last-edited-unknown":"Bir süre önce düzenlendi","view-in-browser-footer-link":"Maddeyi tarayıcıda görüntüle","info-box-title":"Pratik Bilgiler","page-talk-page":"Tartışma sayfasını görüntüle","page-location":"Haritada görüntüle","page-similar-titles":"Benzer sayfalar","article-about-title":"Bu madde hakkında","license-footer-name":"CC BY-SA 3.0","page-issues":"Sayfa sorunları","license-footer-text":"İçerik, aksi belirtilmedikçe $1 lisansı altında kullanılabilir","page-read-in-other-languages":"{{PLURAL:$1|$1 farklı dilde|$1 farklı dilde}} mevcut"},"eo":{"description-add-link-title":"Aldoni titolan priskribon","table-title-other":"Pliaj informoj","article-read-more-title":"Legi plu","page-edit-history":"Plena redakto-historion","info-box-close-text":"Fermi","page-last-edited":"Redaktita {{PLURAL:$1|0=hodiaŭ|1=hieraŭ|antaŭ $1 tagoj}}","info-box-title":"Rapidaj faktoj","page-talk-page":"Montri diskutpaĝon","page-similar-titles":"Similaj paĝoj","page-location":"Montri sur mapo","view-in-browser-footer-link":"Vidi la artikolon en retumilo","article-about-title":"Pri ĉi tiu artikolo","license-footer-name":"CC BY-SA 3.0","page-issues":"Problemoj pri paĝo","license-footer-text":"La enhavo estas disponebla laŭ $1, se ne estas alia indiko.","page-read-in-other-languages":"Havebla en {{PLURAL:$1|$1 alia lingvo|$1 aliaj lingvoj}}"},"ka":{"article-read-more-title":"წაიკითხეთ მეტი","table-title-other":"მეტი ინფორმაცია","page-edit-history":"მთლიანი რედაქტირების ისტორია","info-box-close-text":"დახურვა","page-last-edited":"რედაქტირებულია $1 დღის წინ","info-box-title":"სწრაფი ფაქტები","page-similar-titles":"მსგავსი გვერდები","article-about-title":"ამ სტატიის შესახებ","license-footer-name":"CC BY-SA 3.0","page-issues":"გვერდის საკითხები","license-footer-text":"შიგთავსი ხელმისაწვდომია $1 ლიცენზიით.","page-read-in-other-languages":"ხელმისაწვდომია $1-სა და სხვა ენებზე"},"de":{"description-add-link-title":"Titelbeschreibung hinzufügen","table-title-other":"Weitere Informationen","article-read-more-title":"Mehr lesen","page-edit-history":"Vollständige Versionsgeschichte","info-box-close-text":"Schließen","page-last-edited":"{{PLURAL:$1|0=Heute|1=Gestern|Vor $1 Tagen}} bearbeitet","info-box-title":"Schnelle Fakten","view-in-browser-footer-link":"Artikel im Browser ansehen","page-location":"Auf einer Karte ansehen","page-talk-page":"Diskussionsseite ansehen","page-similar-titles":"Ähnliche Seiten","article-about-title":"Über diesen Artikel","license-footer-name":"CC-BY-SA 3.0","page-issues":"Seitenprobleme","license-footer-text":"Der Inhalt ist verfügbar unter $1, sofern nicht anders angegeben.","page-read-in-other-languages":"In {{PLURAL:$1|einer weiteren Sprache|$1 weiteren Sprachen}} verfügbar"},"is":{"description-add-link-title":"Bæta við lýsingu á titli","table-title-other":"Nánari upplýsingar","article-read-more-title":"Lesa meira","page-edit-history":"Öll breytingaskráin","info-box-close-text":"Loka","page-last-edited":"{{PLURAL:$1|0=Breytt í dag|1=Breytt í gær|Breytt fyrir $1 dögum síðan}}","info-box-title":"Staðreyndir strax","page-talk-page":"Skoða spjallsíðu","page-similar-titles":"Áþekkar síður","page-location":"Skoða á korti","view-in-browser-footer-link":"Skoða grein í vafra","article-about-title":"Um þessa grein","license-footer-name":"CC BY-SA 3.0","page-issues":"Vandamál varðandi síðu","license-footer-text":"Efni má nota samkvæmt $1 nema annað komi fram.","page-read-in-other-languages":"Aðgengilegt á {{PLURAL:$1|$1 öðru tungumáli|$1 öðrum tungumálum}}"},"hi":{"table-title-other":"अधिक जानकारी","article-read-more-title":"और पढ़ें","page-edit-history":"पूर्ण संपादन इतिहास","info-box-close-text":"बंद करें","page-last-edited":"$1 दिन पहले सम्पादित किया गया","info-box-title":"सामान्य तथ्य","view-in-browser-footer-link":"ब्राउज़र में लेख देखें","page-location":"नक्शे पर देखें","page-talk-page":"वार्ता पृष्ठ देखें","page-similar-titles":"समान पृष्ठ","article-about-title":"इस लेख के बारे में","license-footer-name":"CC BY-SA 3.0","page-issues":"पेज समस्याएं","license-footer-text":"जब तक बताया न गया हो, सामग्री $1 के अंतर्गत उपलब्ध है","page-read-in-other-languages":"$1 अन्य भाषाओं में उपलब्ध"},"it":{"description-add-link-title":"Aggiungi una descrizione del titolo","table-title-other":"Ulteriori informazioni","article-read-more-title":"Leggi altro","page-edit-history":"Cronologia delle modifiche completa","info-box-close-text":"Chiudi","page-last-edited-unknown":"Modificato un po\' di tempo fa","page-last-edited":"Modificata {{PLURAL:$1|0=oggi|1=ieri|$1 giorni fa}}","info-box-title":"Fatti in breve","page-location":"Visualizza sulla mappa","view-in-browser-footer-link":"Vedi la voce nel browser","page-similar-titles":"Pagine simili","article-about-title":"Su questa voce","license-footer-name":"CC BY-SA 3.0","page-issues":"Problemi alla pagina","license-footer-text":"Il contenuto è disponibile in base alla licenza $1, se non diversamente specificato.","page-read-in-other-languages":"Disponibile in {{PLURAL:$1|un\'altra lingua|altre $1 lingue}}"},"kab":{"table-title-other":"Ugar n telɣut","article-read-more-title":"Ɣer ugar","page-edit-history":"Amazray ummid n usnifel","info-box-close-text":"Mdel","page-last-edited":"Yettwaẓreg $1 n wussan aya","info-box-title":"Axeddim s tewzel","view-in-browser-footer-link":"Sken amagrad deg iminig","page-location":"Wali ɣef tkarḍa","page-talk-page":"Wali asebter n usqerdec","page-similar-titles":"Isebtar icuban","article-about-title":"Ɣef umagrad-agi","license-footer-name":"CC BY-SA 3.0","page-issues":"Uguren n usebter","license-footer-text":"Agbur yella ddaw $1 ḥaca ma abdar anemgal.","page-read-in-other-languages":"Yella deg $1 n tutlayin-nniḍen"},"qqq":{"description-add-link-title":"Text for link for adding a title description","table-title-other":"The title of non-info box tables - in collapsed and expanded form {{Identical|More information}}","article-read-more-title":"The text that is displayed before the read more section at the bottom of an article {{Identical|Read more}}","page-edit-history":"Label for button used to show an article\'s complete edit history","info-box-close-text":"The text for telling users they can tap the bottom of the info box to close it {{Identical|Close}}","page-last-edited-unknown":"Shown on the item for showing the article history when it\'s unclear how many days ago the article was edited.","page-last-edited":"Relative days since an article was last edited. 0 = today, singular = yesterday. $1 will be replaced with the number of days ago.","info-box-title":"The title of infoboxes – in collapsed and expanded form","page-location":"Label for button used to show an article on the map","page-similar-titles":"Label for button that shows a list of similar titles (disambiguation) for the current page","view-in-browser-footer-link":"Link to view article in browser","page-talk-page":"Label for button linking out to an article\'s talk page","article-about-title":"The text that is displayed before the \'about\' section at the bottom of an article","page-issues":"Label for the button that shows the \\"Page issues\\" dialog, where information about the imperfections of the current page is provided (by displaying the warning/cleanup templates). {{Identical|Page issue}}","license-footer-name":"License short name; usually leave untranslated as CC-BY-SA 3.0 {{Identical|CC BY-SA}}","license-footer-text":"Marker at page end for who last modified the page when anonymous. $1 is a relative date such as \'2 months ago\' or \'today\'.","page-read-in-other-languages":"Label for button showing number of languages an article is available in. $1 will be replaced with the number of languages"},"my":{"page-read-in-other-languages":"{{PLURAL:$1|$1 အခြားဘာသာစကား|$1 အခြားဘာသာစကားများ}}တွင် ရရှိနိုင်သည်","article-read-more-title":"ဆက်ဖတ်ပါ","table-title-other":"နောက်ထပ် အချက်အလက်များ","page-edit-history":"တည်းဖြတ်မှုမှတ်တမ်း အပြည့်အစုံ","info-box-close-text":"ပိတ်ရန်","page-last-edited-unknown":"အချို့သောအချိန်တုန်းက တည်းဖြတ်ခဲ့သည်","page-last-edited":"{{PLURAL:$1|0=ယနေ့တည်းဖြတ်ခဲ့သည်|1=မနေ့ကတည်းဖြတ်ခဲ့သည်|$1 ရက်အကြာက တည်းဖြတ်ခဲ့သည်}}","info-box-title":"အချက်တိုများ","page-location":"မြေပုံပေါ်တွင် ကြည့်ရန်","page-similar-titles":"ဆင်တူစာမျက်နှာများ","view-in-browser-footer-link":"ဆောင်းပါးကို ဘရောက်ဆာတွင် ကြည့်ရန်","page-talk-page":"ဆွေးနွေးချက်စာမျက်နှာကို ကြည့်ရန်","article-about-title":"ဤဆောင်းပါးအကြောင်း","page-issues":"စာမျက်နှာ ကိစ္စရပ်များ","license-footer-name":"CC BY-SA 3.0","license-footer-text":"အကြောင်းအရာများကို အခြားမှတ်ချက်မရှိပါက $1 အောက်တွင် ရရှိနိုင်ပါသည်။","description-add-link-title":"ဖော်ပြချက်အတို ပေါင်းထည့်ရန်"},"sk":{"description-add-link-title":"Pridať popis článku","table-title-other":"Viac informácií","article-read-more-title":"Ďalšie informácie","page-edit-history":"Úplná história úprav","info-box-close-text":"Zavrieť","page-last-edited-unknown":"Upravené pred nejakým časom","page-last-edited":"{{PLURAL:$1|0=Upravené dnes|1=Upravené včera|2=Upravené predvčerom|Upravené pred $1 dňami}}","info-box-title":"Rýchle fakty","page-location":"Zobraziť na mape","page-similar-titles":"Podobné stránky","view-in-browser-footer-link":"Zobraziť článok v prehliadači","page-talk-page":"Zobraziť diskusiu","article-about-title":"O tomto článku","page-issues":"Problémy stránky","license-footer-name":"CC BY-SA 3.0","license-footer-text":"Pokiaľ nie je uvedené inak, je obsah dostupný v rámci licencie $1.","page-read-in-other-languages":"Dostupné v {{PLURAL:$1|$1 ďalšom jazyku|$1 dalších jazykoch}}"},"ga":{"description-add-link-title":"Cuir sainchuntas teidil leis","article-read-more-title":"Léigh a thuilleadh","table-title-other":"Tuilleadh eolais","page-edit-history":"Stair iomlán eagarthóireachta","info-box-close-text":"Dún","page-last-edited":"Curtha in eagar $1 lá ó shin","info-box-title":"Fíricí Gasta","view-in-browser-footer-link":"Féach ar alt i mbrabhsálaí","page-location":"Féach ar léarscáil","page-similar-titles":"Leathanaigh den chineál céanna","page-talk-page":"Féach ar leathanach plé","article-about-title":"Maidir leis an alt seo","page-issues":"Ceisteanna le reitiu","license-footer-name":"CC BY-SA 3.0","license-footer-text":"Inneachar ar fáil faoi $1 mura luaitear a mhalairt.","page-read-in-other-languages":"Ar fáil i {{PLURAL:$1|dteanga amháin eile|$1 teanga eile}}"},"es":{"page-read-in-other-languages":"\\nDisponible en {{PLURAL:$1|$1 idioma más|otros $1 idiomas}}","article-read-more-title":"Seguir leyendo","table-title-other":"Más información","page-edit-history":"Historial de edición completo","info-box-close-text":"Cerrar","page-last-edited-unknown":"Editado hace algún tiempo","page-last-edited":"\\n\\n{{PLURAL:$1|0=Editado hoy|1=Editado ayer|Editado hace $1 días}}","info-box-title":"Datos rápidos","page-location":"Ver en mapa","page-similar-titles":"Páginas similares","view-in-browser-footer-link":"Ver artículo en el navegador","page-talk-page":"Ver página de discusión","article-about-title":"Acerca de este artículo","page-issues":"Problemas de la página","license-footer-name":"CC-BY-SA 3.0","license-footer-text":"El contenido está disponible bajo la licencia $1, salvo que se indique lo contrario.","description-add-link-title":"Añade descripción de título"},"ps":{"article-read-more-title":"نور لوستل","table-title-other":"نور مالومات","page-edit-history":"د سمون بشپړ پېښليک","info-box-close-text":"تړل","page-last-edited":"$1 ورځې مخکې سم شوی","info-box-title":"چټک مالومات","page-similar-titles":"ورته مخونه","article-about-title":"د دې ليکنې په اړه","license-footer-name":"CC BY-SA 3.0","page-issues":"د مخ ستونزې","license-footer-text":"دا مېنځپانگه د $1 سره سم ستاسې لاسرسي کې ده، خو چې مخالف يې وييل شوی وي.","page-read-in-other-languages":"په $1 ژبو کې شته"},"sl":{"article-read-more-title":"Več o tem","info-box-close-text":"Zapri","article-about-title":"O tem članku","description-add-link-title":"Dodaj opis naslova","page-talk-page":"Poglej pogovorno stran","page-location":"Poglej na karti","page-similar-titles":"Podobne strani","license-footer-name":"CC BY-SA 3.0"},"ckb":{"table-title-other":"زانیاریی زیاتر","article-read-more-title":"زیاتر بخوێنەوە","page-edit-history":"مێژووی دەستکاری تەواو","info-box-close-text":"دایخە","page-last-edited":"$1 لەمەوپێش دەستکاری کراوە","info-box-title":"زانیاریی خێرا","page-talk-page":"پیشاندانی پەڕەی وتووێژ","page-similar-titles":"پەڕە لێکچووەکان","page-location":"بینین لەسەر نەخشە","view-in-browser-footer-link":"پیشاندانی وتار لە بڕاوسەرەکەدا","article-about-title":"دەربارەی ئەم وتارە","license-footer-name":"CC BY-SA 3.0","page-issues":"گرفتەکانی پەڕە","license-footer-text":"ناوەڕۆک بەردەستە لەژێر $1 مەگەر بە پێچەوانەوە وترابێت.","page-read-in-other-languages":"بە $1 زمان بەردەستە"},"pt":{"description-add-link-title":"Adicionar título de descrição","table-title-other":"Mais informação","article-read-more-title":"Ler mais","page-edit-history":"Historial de edições completo","info-box-close-text":"Fechar","page-last-edited":"{{PLURAL:$1|0=Editado hoje|1=Editado ontem|Editado há $1 dias}}","info-box-title":"Factos rápidos","page-talk-page":"Ver página de discussão","page-similar-titles":"Páginas semelhantes","page-location":"Ver num mapa","view-in-browser-footer-link":"Ver artigo no browser","article-about-title":"Sobre este artigo","license-footer-name":"CC BY-SA 3.0","page-issues":"Problemas da página","license-footer-text":"Conteúdo disponibilizado nos termos da $1, salvo indicação em contrário.","page-read-in-other-languages":"Disponível {{PLURAL:$1|noutra língua|em $1 outras línguas}}"},"eu":{"table-title-other":"Informazio gehiago","article-read-more-title":"Irakurri gehiago","page-edit-history":"Aldaketa guztien historia","info-box-close-text":"Itxi","page-last-edited":"Orain dela $1 egun aldatu da","info-box-title":"Datu azkarrak","page-talk-page":"Elkarrizketa orrialdea ikusi","page-similar-titles":"Antzeko orriak","page-location":"Ikusi mapan","view-in-browser-footer-link":"Artikulua arakatzailean ikusi","article-about-title":"Artikulu honi buruz","license-footer-name":"CC BY-SA 3.0","page-issues":"Orriaren arazoak","license-footer-text":"Eduki guztia $1(r)en babespean dago, ez bada kontrakoa esaten.","page-read-in-other-languages":"Irakurgarri beste $1 hizkuntzatan"},"ar":{"description-add-link-title":"إضافة وصف للعنوان","table-title-other":"مزيد من المعلومات","article-read-more-title":"قراءة المزيد","page-edit-history":"تاريخ التعديل الكامل","info-box-close-text":"إغلاق","page-last-edited":"عُدِّلت {{PLURAL:$1|0=اليوم|1=أمس|قبل $1 أيام}}","info-box-title":"معلومات سريعة","page-talk-page":"اعرض صفحة النقاش","page-similar-titles":"صفحات مشابهة","page-location":"شاهده على خريطة","view-in-browser-footer-link":"اعرض المقالة في المتصفح","article-about-title":"حول المقال","license-footer-name":"CC BY-SA 3.0","page-issues":"مشاكل في الصفحة","license-footer-text":"المحتوى متاح بموجب رخصة $1 إلا في حالة ذكر خلاف ذلك.","page-read-in-other-languages":"متاحة {{PLURAL:$1|بـ$1 لغة أخرى|بـ$1 لغات أخرى}}"},"ca":{"page-read-in-other-languages":"Disponible en {{PLURAL:$1|$1 altra llengua|$1 altres llengües}}","article-read-more-title":"Més informació","table-title-other":"Més informació","page-edit-history":"Historial complet","info-box-close-text":"Tanca","page-last-edited-unknown":"S\'ha modificat fa un temps","page-last-edited":"{{PLURAL:$1|0=S\'ha modificat avui|1=S\'ha modificat ahir|S\'ha modificat fa $1 dies}}","info-box-title":"Dades ràpides","page-location":"Mostra en un mapa","page-similar-titles":"Pàgines similars","view-in-browser-footer-link":"Mostra l\'article al navegador","page-talk-page":"Mostra la pàgina de discussió","article-about-title":"Sobre aquest article","page-issues":"Problemes de la pàgina","license-footer-name":"CC-BY-SA 3.0","license-footer-text":"Contingut disponible sota $1 a menys que s\'indiqui el contrari.","description-add-link-title":"Afegeix una descripció del títol"},"as":{"article-read-more-title":"আৰু পঢ়ক","table-title-other":"অধিক তথ্য","article-about-title":"এই প্ৰবন্ধ সম্পৰ্কে","info-box-close-text":"বন্ধ কৰক","info-box-title":"ক্ষিপ্ৰ তথ্য","license-footer-name":"CC BY-SA 3.0","page-issues":"পৃষ্ঠা সমস্যা","page-similar-titles":"একে ধৰণৰ পৃষ্ঠা","license-footer-text":"সমল $1ৰ আওতাত উপলব্ধ"},"pt-br":{"description-add-link-title":"Adicionar descrição do título","table-title-other":"Mais informações","article-read-more-title":"Leia mais","page-edit-history":"Histórico de edição completo","info-box-close-text":"Fechar","page-last-edited":"{{PLURAL:$1|0=Editado hoje|1=Editado ontem|Editado $1 dia atrás}}","page-last-edited-unknown":"Editado há algum tempo","info-box-title":"Fatos rápidos","page-location":"Ver no mapa","page-talk-page":"Ver página de discussão","view-in-browser-footer-link":"Ver artigo no navegador","page-similar-titles":"Páginas similares","article-about-title":"Sobre este artigo","license-footer-name":"CC BY-SA 3.0","page-issues":"Problemas da página","license-footer-text":"Conteúdo disponibilizado nos termos da $1, salvo indicação contrária.","page-read-in-other-languages":"Disponível em {PLURAL:$1|$1 outro idioma|$1 outros idiomas}}"},"zh-hans":{"description-add-link-title":"添加标题描述","table-title-other":"更多信息","article-read-more-title":"阅读更多","page-edit-history":"完整编辑历史","info-box-close-text":"关闭","page-last-edited":"{{PLURAL:$1|0=编辑于今天|1=编辑于昨天|编辑于 $1 天前}}","info-box-title":"快速预览","page-talk-page":"查看讨论页","page-similar-titles":"相似页面","page-location":"在地图上查看","view-in-browser-footer-link":"在浏览器查看条目","article-about-title":"关于此条目","license-footer-name":"CC BY-SA 3.0","page-issues":"页面问题","license-footer-text":"除非另有说明，内容基于$1授权。","page-read-in-other-languages":"在$1种其他语言中可用"},"nb":{"description-add-link-title":"Legg til tittelbeskrivelse","table-title-other":"Mer informasjon","article-read-more-title":"Les mer","page-edit-history":"Fullstendig redigeringshistorikk","info-box-close-text":"Lukk","page-last-edited":"{{PLURAL:$1|0=Redigert i dag|1=Redigert i går|Redigert for $1 dager siden}}","info-box-title":"Kjappe fakta","view-in-browser-footer-link":"Vis artikkelen i nettleseren","page-location":"Vis på kart","page-talk-page":"Vis diskusjonsside","page-similar-titles":"Lignende sider","article-about-title":"Om artikkelen","license-footer-name":"CC BY-SA 3.0","page-issues":"Merknader for siden","license-footer-text":"Innholdet er tilgjengelig under $1 med mindre annet er angitt.","page-read-in-other-languages":"Tilgjengelig på {{PLURAL:$1|$1 annet språk|$1 andre språk}}"},"vi":{"table-title-other":"Thêm thông tin","article-read-more-title":"Đọc tiếp","page-edit-history":"Toàn bộ lịch sử sửa đổi","info-box-close-text":"Đóng","page-last-edited":"Sửa đổi lần cuối $1 ngày trước","info-box-title":"Thông tin Nhanh","page-talk-page":"Xem trang thảo luận","page-similar-titles":"Trang tương tự","page-location":"Xem trên bản đồ","view-in-browser-footer-link":"Xem bài trong trình duyệt","article-about-title":"Thông tin về bài viết","license-footer-name":"CC BY-SA 3.0","page-issues":"Vấn đề trang","license-footer-text":"Nội dung được phát hành theo $1, trừ khi có ghi chú khác.","page-read-in-other-languages":"Có sẵn trong $1 ngôn ngữ khác"},"lt":{"table-title-other":"Daugiau informacijos","article-read-more-title":"Skaityti daugiau","page-edit-history":"Pilna keitimų istorija","info-box-close-text":"Uždaryti","page-last-edited":"{{PLURAL:$1|0=Redaguota šiandien|1=Redaguota vakar|Redaguota prieš $1 dienas}}","info-box-title":"Trumpi faktai","view-in-browser-footer-link":"Rodyti straipsnį naršyklėje","page-location":"Žiūrėti žemėlapyje","page-talk-page":"Žiūrėti aptarimo puslapį","page-similar-titles":"Panašūs puslapiai","article-about-title":"Apie šį straipsnį","license-footer-name":"CC BY-SA 3.0","page-issues":"Puslapio klausimai","license-footer-text":"Turinys pateikiamas pagal $1, jei nėra nurodyta kitaip.","page-read-in-other-languages":"Prieinama {{PLURAL:$1|$1 kita kalba|$1 kitų kalbų}}"},"om":{"info-box-close-text":"Cufi","article-read-more-title":"Dabalata dubbisi","page-similar-titles":"Fuula walfakkaatu"},"sr-ec":{"description-add-link-title":"Додај опис наслова","table-title-other":"Више информација","article-read-more-title":"Прочитајте више","page-edit-history":"Целокупна историја измена","info-box-close-text":"Затвори","page-last-edited-unknown":"Уређено пре неког времена","page-last-edited":"{{PLURAL:$1|0=Уређено данас|1=Уређено јуче|Уређено пре $1 дана}}","info-box-title":"Кратке чињенице","page-location":"Прикажи на мапи","page-similar-titles":"Сличне странице","view-in-browser-footer-link":"Прикажи чланак у прегледачу","page-talk-page":"Прикажи страницу за разговор","article-about-title":"О овом чланку","page-issues":"Проблеми са страницом","license-footer-name":"CC BY-SA 3.0","license-footer-text":"Садржај је доступан под лиценцом $1, осим ако је другачије наведено.","page-read-in-other-languages":"Доступно на још $1 језика"},"azb":{"table-title-other":"آرتیق بیلگیلر","info-box-close-text":"باغلا","article-read-more-title":"داها چوْخ اوْخویون","article-about-title":"بۇ مقاله‌یه گؤره"},"sq":{"article-read-more-title":"Lexo më shumë","table-title-other":"Më shumë informacion","info-box-close-text":"Mbylle","article-about-title":"Rreth këtij artikulli","info-box-title":"Fakte të shpejta","license-footer-name":"CC BY-SA 3.0","page-similar-titles":"Faqe të ngjashme"},"hr":{"article-read-more-title":"Pročitajte više","table-title-other":"Više informacija","page-last-edited-unknown":"Uređeno prije nekog vremena","info-box-close-text":"Zatvori","article-about-title":"O ovom članku","info-box-title":"Kratke činjenice","license-footer-name":"CC BY-SA 3.0"},"hsb":{"info-box-title":"Zakładne fakty","info-box-close-text":"Začinić","article-read-more-title":"Wjace čitać","table-title-other":"Dalše informacije"},"ja":{"description-add-link-title":"記事の説明を追加","article-read-more-title":"さらに読む","table-title-other":"さらに見る","page-edit-history":"すべての編集履歴","info-box-close-text":"閉じる","page-last-edited":"{{PLURAL:$1|0=今日編集|1=昨日編集|$1日前に編集}}","info-box-title":"概要","view-in-browser-footer-link":"ブラウザで記事を表示する","page-location":"地図で見る","page-similar-titles":"類似したページ","page-talk-page":"トークページを見る","article-about-title":"この記事について","page-issues":"ページの問題点","license-footer-name":"クリエイティブ・コモンズ 表示-継承 3.0","license-footer-text":"特に記載がない限り、コンテンツは$1のライセンスで利用できます。","page-read-in-other-languages":"他に{{PLURAL:$1|$1 言語}}で利用可能"},"lv":{"article-read-more-title":"Lasīt vairāk","table-title-other":"Vairāk informācijas","article-about-title":"Par šo rakstu","info-box-close-text":"Aizvērt","info-box-title":"Ātrie fakti","page-talk-page":"Skatīt diskusiju lapu","page-similar-titles":"Līdzīgas lapas","page-edit-history":"Pilna labojumu vēsture","license-footer-name":"CC-BY-SA 3.0"},"ce":{"info-box-close-text":"ДӀачӀагӀа","license-footer-name":"CC-BY-SA 3.0"},"ne":{"article-read-more-title":"थप पढ्ने","table-title-other":"थप जानकारी","page-edit-history":"पूर्ण सम्पादन इतिहास","info-box-close-text":"बन्द गर्नुहोस्","page-last-edited-unknown":"केही समय अगाडी सम्पादन गरिएकाे","page-last-edited":"$1 दिन पहिले सम्पादन गरिएको","info-box-title":"छरितो तथ्यहरू","page-location":"कुनै नक्सामा हेर्नुहोस्","page-similar-titles":"मिल्दोजुल्दो पृष्ठहरू","view-in-browser-footer-link":"यस पृष्ठ ब्राउजरमा खोल्नुहोस्","page-talk-page":"वार्तालाप पृष्ठ हेर्नुहोस्","article-about-title":"यस लेखको बारेमा।","page-issues":"पृष्ठका समस्याहरू","license-footer-name":"CC BY-SA 3.0","description-add-link-title":"शीर्षकमा विवरण थप्नुहोस्","page-read-in-other-languages":" $1 अरु भाषाहरूमा उपलब्ध"},"km":{"description-add-link-title":"បន្ថែមចំណារពន្យល់អំពីចំណងជើង","article-read-more-title":"អានបន្ថែម","table-title-other":"ព័ត៌មានបន្ថែម","page-edit-history":"ប្រវត្តិការកែប្រែទាំងអស់","info-box-close-text":"បិទ","page-last-edited":"បានកែប្រែ $1 ថ្ងៃមុន","info-box-title":"ព័ត៌មានសង្ខេប","view-in-browser-footer-link":"មើលអត្ថបទក្នុងកម្មវិធីរុករក","page-location":"មើលនៅលើផែនទី","page-similar-titles":"ទំព័រស្រដៀង","page-talk-page":"មើលទំព័រពិភាក្សា","article-about-title":"អំពីអត្ថបទនេះ","page-issues":"បញ្ហាក្នុងទំព័រ","license-footer-name":"CC BY-SA 3.0","license-footer-text":"ខ្លឹមសារអត្ថបទនេះផ្ដល់ជូនក្រោមអាជ្ញាប័ណ្ណ $1 លើកលែងមានការសម្គាល់ផ្សេងពីនេះ។","page-read-in-other-languages":"អាចប្រើបានជា$1ភាសាផ្សេងទៀត"},"ksh":{"article-read-more-title":"Mih lässe","table-title-other":"Mih Aanjahbe","page-edit-history":"De kumplätte Verjangeheijt","info-box-close-text":"Zohmaache","page-last-edited":"Vör $1 Dähsch verändert","info-box-title":"Koot jesaat","page-similar-titles":"Ähnlejje Sigge","article-about-title":"Övver heh dä Atikkel","page-issues":"Problehme met heh dä Sigg","license-footer-name":"CC BY-SA 3.0","license-footer-text":"Wann nit angersch aanjejovve es dä Ennhalld ze hann onger $1","page-read-in-other-languages":"En $1 annder Schprohche ze han"},"ast":{"description-add-link-title":"Amestar una descripción del títulu","table-title-other":"Más información","article-read-more-title":"Lleer más","page-edit-history":"Historial d\'edición completu","info-box-close-text":"Zarrar","page-last-edited":"{{PLURAL:$1|0=Editáu güei|1=Editáu ayeri|Editáu hai $1 díes}}","info-box-title":"Datos rápidos","page-talk-page":"Ver la páxina d\'alderique","page-similar-titles":"Páxines asemeyaes","page-location":"Ver nun mapa","view-in-browser-footer-link":"Ver artículu nel navegador","article-about-title":"Tocante a esti artículu","license-footer-name":"CC-BY-SA 3.0","page-issues":"Problemes de la páxina","license-footer-text":"El conteníu ta disponible baxo llicencia $1 si nun s\'indica otra cosa.","page-read-in-other-languages":"Disponible\'n {{PLURAL:$1|$1 idioma más|$1 idiomes más}}"},"hu":{"description-add-link-title":"Adj meg egy címleírást","table-title-other":"További információk","article-read-more-title":"További információ","page-edit-history":"Teljes laptörténet","info-box-close-text":"Bezárás","page-last-edited":"Szerkesztve {{PLURAL:$1|0=ma|1=tegnap|$1 napja}}","info-box-title":"Gyors adatok","view-in-browser-footer-link":"Szócikk olvasása böngészőben","page-location":"Megtekintés térképen","page-talk-page":"Vitalap megtekintése","page-similar-titles":"Hasonló lapok","article-about-title":"Erről a szócikkről","license-footer-name":"CC BY-SA 3.0","page-issues":"A lap problémái","license-footer-text":"A tartalom további jelölés hiányában a(z) $1 feltételei szerint használható fel.","page-read-in-other-languages":"Elérhető $1 további nyelven"},"su":{"article-read-more-title":"Baca leuwih jero","table-title-other":"Informasi leuwih jero","page-edit-history":"Sakumna jujutan édit","info-box-close-text":"Tutup","page-last-edited":"Diédit $1 poé katukang","info-box-title":"Fakta Singget","view-in-browser-footer-link":"Tempo artikel di panyungsi","page-location":"Tempo dina péta","page-similar-titles":"Kaca nu sarua","page-talk-page":"Tempo kaca obrolan","license-footer-name":"CC BY-SA 3.0","article-about-title":"Ngeunaan ieu artikel","page-issues":"Masalah kaca","license-footer-text":"Eusi nu nyangkaruk ditangtayungan ku $1 iwal lamun disebutkeun béda.","page-read-in-other-languages":"Nyangkaruk dina $1 basa séjén"},"fa":{"description-add-link-title":"افزودن توضیحات عنوان","article-read-more-title":"بیشتر بخوانید","table-title-other":"اطلاعات بیشتر","page-edit-history":"تاریخچهٔ کامل ویرایش","info-box-close-text":"بستن","page-last-edited":"{{PLURAL:$1|0=ویرایش‌شده امروز|1=ویرایش‌شده دیروز|ویرایش‌شده $1 روز پیش}}","info-box-title":"اطلاعات اجمالی","view-in-browser-footer-link":"مقاله را در مرورگر ببینید","page-location":"دیدن در نقشه","page-similar-titles":"صفحات مشابه","page-talk-page":"نمایش صفحه بحث","article-about-title":"دربارهٔ این مقاله","page-issues":"موضوعات صفحه","license-footer-name":"CC BY-SA 3.0","license-footer-text":"محتوا تحت $1 در دسترس است، مگر اینکه خلافش ذکر شده‌باشد.","page-read-in-other-languages":"موجود به {{PLURAL:$1|$1 زیان دیگر|$1 زبان دیگر}}"},"ko":{"description-add-link-title":"제목 설명 추가","table-title-other":"자세한 정보","article-read-more-title":"더 읽어보기","page-edit-history":"전체 편집 내역","info-box-close-text":"닫기","page-last-edited-unknown":"조금 전에 편집됨","page-last-edited":"{{PLURAL:$1|0=오늘 마지막으로 편집|1=어제 마지막으로 편집|$1일 전에 마지막으로 편집}}","info-box-title":"간략 정보","page-location":"지도에서 보기","page-similar-titles":"비슷한 문서","view-in-browser-footer-link":"브라우저에서 문서 보기","page-talk-page":"토론 문서 보기","article-about-title":"이 문서의 정보","page-issues":"문서 이슈","license-footer-name":"CC BY-SA 3.0","license-footer-text":"특별한 언급이 없으면 내용은 $1에 따라 이용할 수 있습니다.","page-read-in-other-languages":"{{PLURAL:$1|$1개의 다른 언어}}로 읽어보기"},"sv":{"description-add-link-title":"Lägg till titelbeskrivning","table-title-other":"Mer information","article-read-more-title":"Läs mer","page-edit-history":"Full redigeringshistorik","info-box-close-text":"Stäng","page-last-edited":"Redigerades {{PLURAL:$1|0=idag|1=igår|för $1 dygn sedan}}","page-last-edited-unknown":"Redigerades för ett tid sedan","info-box-title":"Snabbfakta","page-location":"Visa på en karta","page-talk-page":"Visa diskussionssidan","page-similar-titles":"Liknande sidor","view-in-browser-footer-link":"Visa artikeln i webbläsaren","article-about-title":"Om den här artikeln","license-footer-name":"CC BY-SA 3.0","page-issues":"Sidproblem","license-footer-text":"Innehållet är tillgängligt under $1 om ingenting annat anges.","page-read-in-other-languages":"Tillgänglig på {{PLURAL:$1|$1 annat|$1 andra}} språk"},"gl":{"description-add-link-title":"Engadir unha descrición de título","table-title-other":"Máis información","article-read-more-title":"Ler máis","page-edit-history":"Historial de edición completo","info-box-close-text":"Pechar","page-last-edited":"{{PLURAL:$1|0=Editado hoxe|1=Editado onte|Editado hai $1 días}}","info-box-title":"Datos rápidos","page-talk-page":"Ver a páxina de conversa","page-similar-titles":"Páxinas similares","page-location":"Ver nun mapa","view-in-browser-footer-link":"Ver artigo no navegador","article-about-title":"Acerca deste artigo","license-footer-name":"CC BY-SA 3.0","page-issues":"Problemas na páxina","license-footer-text":"O contido está dispoñible baixo a $1, salvo que se indique o contrario.","page-read-in-other-languages":"Dispoñible {{PLURAL:$1|noutra lingua|noutras $1 linguas}}"},"vec":{"info-box-title":"Info velòsi","info-box-close-text":"Sara","license-footer-name":"CC BY-SA 3.0","article-read-more-title":"Lèxi de pi","table-title-other":"Pì informasion"},"sah":{"page-read-in-other-languages":"Атын $1 тылынан ааҕыахха сөп","article-read-more-title":"Бу туһунан сиһилии","table-title-other":"Сиһилии","page-edit-history":"Уларытыы толору устуоруйата","info-box-close-text":"Сап","page-last-edited":"$1 күн ынараа өттүгэр уларыйбыт","info-box-title":"Кылгас чахчылар","page-location":"Каартаҕа көр","page-similar-titles":"Майгынныыр сирэйдэр","license-footer-name":"CC BY-SA 3.0","article-about-title":"Бу ыстатыйа туһунан","page-issues":"Сирэйгэ ыйытыылар","license-footer-text":"Иһинээҕитэ $1 лиссиэнсийэнэн тарҕанар","description-add-link-title":"Аатын быһаар"},"yi":{"article-read-more-title":"לייענט ווײטער","table-title-other":"נאך אינפארמאציע","info-box-close-text":"פארמאכן","article-about-title":"וועגן דעם ארטיקל","info-box-title":"גיכע פאקטן","license-footer-name":"CC BY-SA 3.0","page-similar-titles":"ענלעכע בלעטער","page-issues":"בלאט־פֿראגעס"},"sw":{"info-box-close-text":"Funga","article-read-more-title":"Soma zaidi","license-footer-name":"CC BY-SA 3.0","table-title-other":"Maelezo zaidi"},"hy":{"article-read-more-title":"Կարդալ ավելին","table-title-other":"Մանրամասն տեղեկատվություն","info-box-close-text":"Փակել","article-about-title":"Այս հոդվածի մասին","info-box-title":"Համառոտ փաստեր","license-footer-text":"Բովանդակությունը հասանելի է","page-issues":"Էջի խնդիրներ","page-similar-titles":"Նմանատիպ էջեր","license-footer-name":"CC BY-SA 3.0"},"ba":{"article-read-more-title":"Тулыраҡ уҡырға","table-title-other":"Тулыраҡ","page-edit-history":"Мөхәррирләүҙең тулы тарихы","info-box-close-text":"Ябырға","page-last-edited":"$1 көн элек мөхәррирләнгән","info-box-title":"Ҡыҫҡа факттар","page-similar-titles":"Оҡшаш биттәр","article-about-title":"Был мәҡәлә тураһында","license-footer-name":"CC-BY-SA 3.0","page-issues":"Бит буйынса һорауҙар","license-footer-text":"$1 лицензияһына ярашлы, эстәлеге менән һәр кем файҙалана ала (башҡаһы күрһәтелмәһә)","page-read-in-other-languages":"$1 телдәргә аңлайышлы"},"shn":{"description-add-link-title":"ထႅမ်သႂ်ႇ ၶေႃႈသပ်းလႅင်းႁူဝ်ၶေႃႈ","table-title-other":"လွၼ်ႉၶၢဝ်ႇ ၼမ်လိူဝ်","article-read-more-title":"သိုပ်ႇလူ","page-edit-history":"ပိုၼ်းမႄးထတ်း တဵမ်ထူၼ်ႈ","info-box-close-text":"ႁပ်း","page-last-edited":"{{PLURAL:$1|0=ဢၼ်မႄးထတ်းမႃး မိူဝ်ႈၼႆႉ|1=ဢၼ်မႄးထတ်းမႃး မိူဝ်ႈဝႃး|ဢၼ်မႄးထတ်း မိူဝ်ႈပူၼ်ႉမႃး $1 ဝၼ်း}}","info-box-title":"ၶေႃႈၶၢဝ်ႇ တူၺ်းဝႆး","page-talk-page":"တူၺ်းၼႃႈလိၵ်ႈတႃႇဢုပ်ႇ","page-similar-titles":"ၼႃႈလိၵ်ႈ မိူၼ်ၵၼ်","page-location":"တူၺ်းၵႃႈတီႈၼိူဝ် ႁၢင်ႈၽႅၼ်ႇလိၼ်","view-in-browser-footer-link":"တူၺ်းပွင်ႈၵႂၢမ်း တီႈၼႂ်း တူဝ်ပိုတ်ႇႁႃ","article-about-title":"လွင်ႈပွင်ႈလိၵ်ႈဢၼ်ၼႆႉ","license-footer-name":"CC BY-SA 3.0","page-issues":"ၶေႃႈၶၼ် ၼႃႈလိၵ်ႈ","license-footer-text":"ၸိူဝ်းၶဝ်ႈပႃးဝႆႉၼႆႉ မၼ်းတေၸၢင်ႈၸႂ်ႉလႆႈ ၵႃႈတီႈ ဝႂ် $1","page-read-in-other-languages":"တူၺ်းလႆႈ ၵႃႈတီႈ {{PLURAL:$1|ၽႃႇသႃႇၵႂၢမ်း $1 ဢၼ်|ၽႃႇသႃႇၵႂၢမ်းတၢင်ႇၸိူဝ်း  $1 ဢၼ်}}"},"ro":{"article-read-more-title":"Citiți mai multe","table-title-other":"Mai multe informații","page-edit-history":"Istoric complet al modificărilor","info-box-close-text":"Închide","page-last-edited":"Modificat acum $1 zi(le)","info-box-title":"Informații pe scurt","page-similar-titles":"Pagini similare","article-about-title":"Despre acest articol","page-issues":"Problemele paginii","license-footer-name":"CC BY-SA 3.0","license-footer-text":"Conținutul este disponibil sub $1, exceptând cazurile în care se specifică altfel.","page-read-in-other-languages":"Disponibil în alte $1 limbi"},"nl":{"description-add-link-title":"Titelbeschrijving toevoegen","table-title-other":"Meer informatie","article-read-more-title":"Meer lezen","page-edit-history":"Volledige bewerkingsgeschiedenis","info-box-close-text":"Sluiten","page-last-edited-unknown":"Enige tijd geleden bewerkt","page-last-edited":"{{PLURAL:$1|0=Vandaag bewerkt|1=Gisteren bewerkt|$1 dagen geleden bewerkt}}","info-box-title":"Snelle feiten","page-location":"Op een kaart bekijken","page-similar-titles":"Vergelijkbare pagina\'s","view-in-browser-footer-link":"Artikel in browser bekijken","page-talk-page":"Overlegpagina bekijken","article-about-title":"Over deze pagina","page-issues":"Paginaproblemen","license-footer-name":"CC BY-SA 3.0","license-footer-text":"De inhoud is beschikbaar onder de $1 tenzij anders aangegeven.","page-read-in-other-languages":"Beschikbaar in {{PLURAL:$1|$1 andere taal|$1 andere talen}}"}}')},48:function(e,t){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(e){"object"==typeof window&&(i=window)}e.exports=i},61:function(e,t){var i,n,r=e.exports={};function a(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function s(e){if(i===setTimeout)return setTimeout(e,0);if((i===a||!i)&&setTimeout)return i=setTimeout,setTimeout(e,0);try{return i(e,0)}catch(t){try{return i.call(null,e,0)}catch(t){return i.call(this,e,0)}}}!function(){try{i="function"==typeof setTimeout?setTimeout:a}catch(e){i=a}try{n="function"==typeof clearTimeout?clearTimeout:o}catch(e){n=o}}();var l,c=[],u=!1,d=-1;function p(){u&&l&&(u=!1,l.length?c=l.concat(c):d=-1,c.length&&f())}function f(){if(!u){var e=s(p);u=!0;for(var t=c.length;t;){for(l=c,c=[];++d<t;)l&&l[d].run();d=-1,t=c.length}l=null,u=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===o||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function g(){}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var i=1;i<arguments.length;i++)t[i-1]=arguments[i];c.push(new h(e,t)),1!==c.length||u||s(f)},h.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=g,r.addListener=g,r.once=g,r.off=g,r.removeListener=g,r.removeAllListeners=g,r.emit=g,r.prependListener=g,r.prependOnceListener=g,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}},62:function(e,t,i){"use strict";var n={isRTL:function(e){var t=e.closest("[dir]");return!!t&&"rtl"===t.getAttribute("dir")},getBaseUri:function(e){var t=e.head.querySelector("base");return t&&t.getAttribute("href")},getHttpsBaseUri:function(e){var t=n.getBaseUri(e);return t&&t.startsWith("http")?t:"https:".concat(t)},getParsoidPlainTitle:function(e){var t=e.head.querySelector("title");return t&&t.textContent},getParsoidLinkTitle:function(e){var t=e.head.querySelector('link[rel="dc:isVersionOf"]');if(t){var i=t.getAttribute("href"),r=n.getBaseUri(e),a=i.replace(r,"");try{return decodeURIComponent(a)}catch(e){throw e.message="".concat(e.message,": ").concat(a),e}}}};e.exports=n},63:function(e,t,i){(function(e){var i,n,r,a;function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}a=function(){return function(e){var t={};function i(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=e,i.c=t,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==o(e)&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=41)}([function(e,t,i){"use strict";i.d(t,"a",(function(){return n}));var n={LABEL:"aria-label",LABELED_BY:"aria-labelledby"},r=function(e){switch(e){case"'":return"&#039;";case'"':return"&quot;";case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";default:return""}};t.b={escape:function(e){return e&&e.replace(/['"<>&]/g,r)}}},function(e,t,i){"use strict";i.r(t);var n="undefined"!=typeof window&&window.CustomEvent||function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{bubbles:!1,cancelable:!1,detail:void 0},i=document.createEvent("CustomEvent");return i.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),i};t.default={matchesSelector:function(e,t){return e.matches?e.matches(t):e.matchesSelector?e.matchesSelector(t):!!e.webkitMatchesSelector&&e.webkitMatchesSelector(t)},querySelectorAll:function(e,t){return Array.prototype.slice.call(e.querySelectorAll(t))},CustomEvent:n}},function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t){function i(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}e.exports=function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}},function(e,t,i){"use strict";i.r(t);var n=i(1),r=function(e,t){var i;for(i=e.parentElement;i&&!n.default.matchesSelector(i,t);i=i.parentElement);return i};t.default={findClosestAncestor:r,isNestedInTable:function(e){return Boolean(r(e,"table"))},closestInlineStyle:function(e,t,i){for(var n=e;n;n=n.parentElement){var r=void 0;try{r=n.style[t]}catch(e){continue}if(r){if(void 0===i)return n;if(i===r)return n}}},isVisible:function(e){return Boolean(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},copyAttributesToDataAttributes:function(e,t,i){i.filter((function(t){return e.hasAttribute(t)})).forEach((function(i){return t.setAttribute("data-".concat(i),e.getAttribute(i))}))},copyDataAttributesToAttributes:function(e,t,i){i.filter((function(t){return e.hasAttribute("data-".concat(t))})).forEach((function(i){return t.setAttribute(i,e.getAttribute("data-".concat(i)))}))}}},function(e,t,i){"use strict";i(30);var n=i(20).default,r=i(4).default,a=i(1).default,o=["class","style","src","srcset","width","height","alt","usemap","data-file-width","data-file-height","data-image-gallery"],s={px:50,ex:10,em:5},l=function(e,t){var i=e.createElement("span");t.hasAttribute("class")&&i.setAttribute("class",t.getAttribute("class")||""),i.classList.add("pcs-lazy-load-placeholder"),i.classList.add("pcs-lazy-load-placeholder-pending");var a=n.from(t);a.width&&i.style.setProperty("width","".concat(a.width)),r.copyAttributesToDataAttributes(t,i,o);var s=e.createElement("span");if(a.width&&a.height){var l=a.heightValue/a.widthValue;s.style.setProperty("padding-top","".concat(100*l,"%"))}return i.appendChild(s),t.parentNode&&t.parentNode.replaceChild(i,t),i},c=function(e){var t=n.from(e);if(!t.width||!t.height)return!0;var i=s[t.widthUnit]||1/0,r=s[t.heightUnit]||1/0;return t.widthValue>=i&&t.heightValue>=r};t.a={CLASSES:{PLACEHOLDER_CLASS:"pcs-lazy-load-placeholder",PLACEHOLDER_PENDING_CLASS:"pcs-lazy-load-placeholder-pending",PLACEHOLDER_LOADING_CLASS:"pcs-lazy-load-placeholder-loading",PLACEHOLDER_ERROR_CLASS:"pcs-lazy-load-placeholder-error",IMAGE_LOADING_CLASS:"pcs-lazy-load-image-loading",IMAGE_LOADED_CLASS:"pcs-lazy-load-image-loaded"},PLACEHOLDER_CLASS:"pcs-lazy-load-placeholder",isLazyLoadable:c,queryLazyLoadableImages:function(e){return a.querySelectorAll(e,"img").filter((function(e){return c(e)}))},convertImagesToPlaceholders:function(e,t){return t.map((function(t){return l(e,t)}))},convertImageToPlaceholder:l,loadPlaceholder:function(e,t){t.classList.add("pcs-lazy-load-placeholder-loading"),t.classList.remove("pcs-lazy-load-placeholder-pending");var i=e.createElement("img"),n=function(e){i.setAttribute("src",i.getAttribute("src")||""),e.stopPropagation(),e.preventDefault()};return i.addEventListener("load",(function(){t.removeEventListener("click",n),t.parentNode&&t.parentNode.replaceChild(i,t),i.classList.add("pcs-lazy-load-image-loaded"),i.classList.remove("pcs-lazy-load-image-loading")}),{once:!0}),i.addEventListener("error",(function(){t.classList.add("pcs-lazy-load-placeholder-error"),t.classList.remove("pcs-lazy-load-placeholder-loading"),t.addEventListener("click",n)}),{once:!0}),r.copyDataAttributesToAttributes(t,i,o),i.classList.add("pcs-lazy-load-image-loading"),i}}},function(e,t,i){"use strict";var n=i(0),r=i(1).default,a=function(e){return!!e&&!("SECTION"!==e.tagName||!e.getAttribute("data-mw-section-id"))},o="pcs-section-control",s="pcs-section-control-show",l="pcs-section-control-hide",c="pcs-section-hidden",u="pcs-section-hideable-header",d="pcs-section-content-",p="pcs-section-control-",f="pcs-section-aria-collapse",h="pcs-section-aria-expand",g=function(e){return p+e},m=function(e){return d+e},v=function(e,t){var i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=g(t),a=m(t),o=e.getElementById(r),u=e.getElementById(a);if(o&&u){i?(o.classList.remove(l),o.classList.add(s),u.classList.add(c),o.setAttribute(n.a.LABELED_BY,h)):(o.classList.remove(s),o.classList.add(l),u.classList.remove(c),o.setAttribute(n.a.LABELED_BY,f));var d=o.parentElement;d&&d.setAttribute("onclick","pcs.c1.Sections.setHidden('".concat(t,"', ").concat(!i,");"))}};t.a={createFoldHR:function(e,t){if(t.parentElement){var i=e.createElement("hr");i.classList.add("pcs-fold-hr"),t.parentElement.insertBefore(i,t)}},expandCollapsedSectionIfItContainsElement:function(e,t){var i=function(e){for(var t=e;t=t.parentElement;)if("SECTION"===t.tagName&&t.parentElement&&"pcs"===t.parentElement.id){var i=t.getAttribute("data-mw-section-id");if(i)return i}}(t);i&&v(e,i,!1)},getSectionIDOfElement:function(e){var t=function(e){for(var t=e;t;){if(a(t))return t;t=t.parentElement}return null}(e);return t&&t.getAttribute("data-mw-section-id")},getLeadParagraphText:function(e){var t=e.querySelector("#content-block-0>p");return t&&t.innerText||""},getSectionOffsets:function(e){return{sections:r.querySelectorAll(e,"section").reduce((function(e,t){var i=t.getAttribute("data-mw-section-id"),n=t&&t.firstElementChild&&t.firstElementChild.querySelector(".pcs-edit-section-title");return i&&parseInt(i)>=1&&e.push({heading:n&&n.innerHTML,id:parseInt(i),yOffset:t.offsetTop}),e}),[])}},prepareForHiding:function(e,t,i,r,a,l,d){var p=function(e,t){var i=e.createElement("span");return i.id=g(t),i.classList.add(o),i.classList.add(s),i}(e,t);if(null===e.getElementById(h)){var v=e.createElement("span");v.setAttribute("id",h),v.setAttribute(n.a.LABEL,l),p.appendChild(v)}if(null===e.getElementById(f)){var b=e.createElement("span");b.setAttribute("id",f),b.setAttribute(n.a.LABEL,d),p.appendChild(b)}p.setAttribute("role","button"),p.setAttribute(n.a.LABELED_BY,h),r&&p&&(r.appendChild(p),r.classList.add(u),r.setAttribute("onclick","pcs.c1.Sections.setHidden('".concat(t,"', false);")));for(var y=i.firstElementChild,_=e.createElement("div");y;){var k=y;y=y.nextElementSibling,k!==a&&(i.removeChild(k),_.appendChild(k))}_.id=m(t),_.classList.add(c),i.appendChild(_)},setHidden:v,getControlIdForSectionId:g,isMediaWikiSectionElement:a}},function(e,t,i){"use strict";var n={ELEMENT_NODE:1,TEXT_NODE:3};t.a={isNodeTypeElementOrText:function(e){return e.nodeType===n.ELEMENT_NODE||e.nodeType===n.TEXT_NODE},getBoundingClientRectAsPlainObject:function(e){var t=e.getBoundingClientRect();return{top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.width,height:t.height,x:t.x,y:t.y}},NODE_TYPE:n}},function(e,t,i){"use strict";i(29);var n=i(0),r={SECTION_HEADER:"pcs-edit-section-header",TITLE:"pcs-edit-section-title",LINK_CONTAINER:"pcs-edit-section-link-container",LINK:"pcs-edit-section-link",PROTECTION:{UNPROTECTED:"",PROTECTED:"page-protected",FORBIDDEN:"no-editing"}},a={TITLE_DESCRIPTION:"pcs-edit-section-title-description",ADD_TITLE_DESCRIPTION:"pcs-edit-section-add-title-description",DIVIDER:"pcs-edit-section-divider",PRONUNCIATION:"pcs-edit-section-title-pronunciation",ARIA_EDIT_PROTECTED:"pcs-edit-section-aria-protected",ARIA_EDIT_NORMAL:"pcs-edit-section-aria-normal"},o={SECTION_INDEX:"data-id",ACTION:"data-action",PRONUNCIATION_URL:"data-pronunciation-url",DESCRIPTION_SOURCE:"data-description-source",WIKIDATA_ENTITY_ID:"data-wikdata-entity-id"},s=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",s=e.createElement("a");return s.href=i,s.setAttribute(o.SECTION_INDEX,t),s.setAttribute(o.ACTION,"edit_section"),s.setAttribute(n.a.LABELED_BY,a.ARIA_EDIT_NORMAL),s.classList.add(r.LINK),s},l=function(e,t){var i=e.createElement("div");return i.classList.add(r.SECTION_HEADER),i.classList.add("v2"),i},c=function(e,t){t.classList.add(r.TITLE),e.appendChild(t)};t.a={appendEditSectionHeader:c,CLASS:r,IDS:a,DATA_ATTRIBUTE:o,setEditButtons:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=e.documentElement.classList;t?n.remove(r.PROTECTION.FORBIDDEN):n.add(r.PROTECTION.FORBIDDEN),i?n.add(r.PROTECTION.PROTECTED):n.remove(r.PROTECTION.PROTECTED)},setARIAEditButtons:function(e){e.documentElement.classList.contains(r.PROTECTION.PROTECTED)&&Array.from(e.getElementsByClassName(r.LINK)).forEach((function(e){return e.setAttribute(n.a.LABELED_BY,a.ARIA_EDIT_PROTECTED)}))},newEditSectionButton:function(e,t,i,o,l){var c=e.createElement("span");if(c.classList.add(r.LINK_CONTAINER),null===e.getElementById(a.ARIA_EDIT_NORMAL)&&o){var u=e.createElement("span");u.setAttribute("id",a.ARIA_EDIT_NORMAL),u.setAttribute(n.a.LABEL,o),c.appendChild(u)}if(null===e.getElementById(a.ARIA_EDIT_PROTECTED)&&l){var d=e.createElement("span");d.setAttribute("id",a.ARIA_EDIT_PROTECTED),d.setAttribute(n.a.LABEL,l),c.appendChild(d)}var p=i;return p||(p=s(e,t)),c.appendChild(p),c},newEditSectionWrapper:l,newEditSectionLink:s,newPageHeader:function(e,t,i,n,r,s,u,d){var p=e.createDocumentFragment(),f=function(e,t,i,n){var r=l(e),a=e.createElement("h".concat(1));return a.innerHTML=n||"",a.setAttribute(o.SECTION_INDEX,0),c(r,a),r}(e,0,0,t);if(d){var h=e.createElement("a");h.setAttribute(o.ACTION,"title_pronunciation"),h.setAttribute(o.PRONUNCIATION_URL,d),h.id=a.PRONUNCIATION,f.querySelector("h1").appendChild(h)}p.appendChild(f);var g=function(e,t,i,n,r,s){if(void 0!==t&&t.length>0){var l=e.createElement("p");return l.setAttribute(o.DESCRIPTION_SOURCE,i),l.setAttribute(o.WIKIDATA_ENTITY_ID,n),l.id=a.TITLE_DESCRIPTION,l.innerHTML=t,l}if(s){var c=e.createElement("a");c.href="#",c.setAttribute(o.ACTION,"add_title_description");var u=e.createElement("p");return u.id=a.ADD_TITLE_DESCRIPTION,u.innerHTML=r,c.appendChild(u),c}return null}(e,i,n,r,s,u);g&&p.appendChild(g);var m=e.createElement("hr");return m.id=a.DIVIDER,p.appendChild(m),p}}},function(e,t,i){"use strict";i(28);var n=i(4),r=i(7),a=i(1),o=i(6),s=i(0),l=r.a.NODE_TYPE,c={ICON:"pcs-collapse-table-icon",CONTAINER:"pcs-collapse-table-container",CONTENT:"pcs-collapse-table-content",COLLAPSED_CONTAINER:"pcs-collapse-table-collapsed-container",COLLAPSED:"pcs-collapse-table-collapsed",COLLAPSED_BOTTOM:"pcs-collapse-table-collapsed-bottom",COLLAPSE_TEXT:"pcs-collapse-table-collapse-text",EXPANDED:"pcs-collapse-table-expanded",TABLE_INFOBOX:"pcs-table-infobox",TABLE_OTHER:"pcs-table-other",TABLE:"pcs-collapse-table"},u="pcs-collapse-table-aria-collapse",d="pcs-collapse-table-aria-expand",p=function(e){return a.default.querySelectorAll(e,"a").length<3},f=function(e){return e&&e.replace(/[\s0-9]/g,"").length>0},h=function(e){var t=e.match(/\w+/);if(t)return t[0]},g=function(e,t){var i=h(t),n=h(e.textContent);return!(!i||!n)&&i.toLowerCase()===n.toLowerCase()},m=function(e){return e.trim().replace(/\s/g," ")},v=function(e,t){t.parentNode.replaceChild(e.createTextNode(" "),t)},b=function(e,t,i){if(!p(t))return null;var n=e.createDocumentFragment();n.appendChild(t.cloneNode(!0));var o=n.querySelector("th");a.default.querySelectorAll(o,".geo, .coordinates, sup.mw-ref, ol, ul, style, script").forEach((function(e){return e.remove()}));for(var s,c=o.lastChild;c;)i&&r.a.isNodeTypeElementOrText(c)&&g(c,i)?c.previousSibling?(c=c.previousSibling).nextSibling.remove():(c.remove(),c=void 0):(s=c).nodeType===l.ELEMENT_NODE&&"BR"===s.tagName?(v(e,c),c=c.previousSibling):c=c.previousSibling;var u=o.textContent;return f(u)?m(u):null},y=function(e,t,i){for(var n=[],r=e.createTreeWalker(t),a=r.nextNode();a;)if("TH"===a.tagName){var o=b(e,a,i);if(o&&-1===n.indexOf(o)&&(n.push(o),2===n.length))break;a=r.nextNode()}else a=r.nextNode();return n},_=function(e,t,i){var n=e.children[0],r=e.children[1],a=e.children[2],o=n.querySelector(".pcs-collapse-table-aria"),l="none"!==r.style.display;return l?(r.style.display="none",n.classList.remove(c.COLLAPSED),n.classList.remove(c.ICON),n.classList.add(c.EXPANDED),o&&o.setAttribute(s.a.LABELED_BY,d),a.style.display="none",t===a&&i&&i(e)):(r.style.display="block",n.classList.remove(c.EXPANDED),n.classList.add(c.COLLAPSED),n.classList.add(c.ICON),o&&o.setAttribute(s.a.LABELED_BY,u),a.style.display="block"),l},k=function(e){var t=this.parentNode;return _(t,this,e)},w=function(e){var t,i=["navbox","vertical-navbox","navbox-inner","metadata","mbox-small"].some((function(t){return e.classList.contains(t)}));try{t="none"===e.style.display}catch(e){t=!0}return!t&&!i},x=function(e){return e.classList.contains("infobox")||e.classList.contains("infobox_v3")},C=function(e,t){var i=e.createElement("div");return i.classList.add(c.COLLAPSED_CONTAINER),i.classList.add(c.EXPANDED),i.appendChild(t),i},E=function(e,t){var i=e.createElement("div");return i.classList.add(c.COLLAPSED_BOTTOM),i.classList.add(c.ICON),i.textContent=t||"",i},A=function(e,t,i,n,r,a){var o=e.createDocumentFragment(),l=e.createElement("strong");l.textContent=t,l.classList.add(i),o.appendChild(l);var p=e.createElement("span");p.classList.add(c.COLLAPSE_TEXT),n.length>0&&p.appendChild(e.createTextNode(": ".concat(n[0]))),n.length>1&&p.appendChild(e.createTextNode(", ".concat(n[1]))),n.length>0&&p.appendChild(e.createTextNode(" ...")),o.appendChild(p);var f=e.createElement("span");if(f.classList.add("pcs-collapse-table-aria"),f.setAttribute(s.a.LABELED_BY,d),f.setAttribute("role","button"),f.setAttribute("display","none"),f.appendChild(e.createTextNode("")),null===e.getElementById(d)){var h=e.createElement("span");h.setAttribute("id",d),h.setAttribute(s.a.LABEL,a),f.appendChild(h)}if(null===e.getElementById(u)){var g=e.createElement("span");g.setAttribute("id",u),g.setAttribute(s.a.LABEL,r),f.appendChild(g)}return o.appendChild(f),o},S=function(e,t,i,n,r,a,s,l,u){var d=A(t,n,r,a,l,u),p=t.createElement("div");p.className=c.CONTAINER,function(e,t){if(e&&t){var i=e,n=e.parentNode;if(n){for(var r=!1;n;){if(o.a.isMediaWikiSectionElement(n)){r=!0;break}i=n,n=n.parentNode}r||(i=e,n=e.parentNode),n.insertBefore(t,i),n.removeChild(i)}}}(e,p),e.classList.add(c.TABLE);var f=C(t,d);f.style.display="block";var h=E(t,s);h.style.display="none",p.appendChild(f);var g=t.createElement("div");g.className=c.CONTENT,g.appendChild(e),p.appendChild(g),p.appendChild(h),g.style.display="none"},L=function(e,t,i,r,a){for(var o=e.querySelectorAll("table, .infobox_v3"),s=0;s<o.length;++s){var l=o[s];if(!n.default.findClosestAncestor(l,".".concat(c.CONTAINER))&&w(l)){var u=x(l),d=y(e,l,t);(d.length||u)&&S(l,e,0,u?i:r,u?c.TABLE_INFOBOX:c.TABLE_OTHER,d,a)}}},T=function(e){a.default.querySelectorAll(e,".".concat(c.CONTAINER)).forEach((function(e){_(e)}))},R=function(e,t,i,n){var r=function(t){return e.dispatchEvent(new a.default.CustomEvent("section-toggled",{collapsed:t}))};a.default.querySelectorAll(t,".".concat(c.COLLAPSED_CONTAINER)).forEach((function(e){e.onclick=function(){var t=k.bind(e)();r(t)}})),a.default.querySelectorAll(t,".".concat(c.COLLAPSED_BOTTOM)).forEach((function(e){e.onclick=function(){var t=k.bind(e,n)();r(t)}})),i||T(t)},j=function(e,t,i,n,r,a,o,s,l){n||(L(t,i,a,o,s),R(e,t,r,l))};t.a={CLASS:c,SECTION_TOGGLED_EVENT_TYPE:"section-toggled",toggleCollapsedForAll:T,toggleCollapseClickCallback:k,collapseTables:function(e,t,i,n,r,a,o,s){j(e,t,i,n,!0,r,a,o,s)},getTableHeaderTextArray:y,adjustTables:j,prepareTables:L,prepareTable:S,setupEventHandling:R,expandCollapsedTableIfItContainsElement:function(e){if(e){var t='[class*="'.concat(c.CONTAINER,'"]'),i=n.default.findClosestAncestor(e,t);if(i){var r=i.firstElementChild;r&&r.classList.contains(c.EXPANDED)&&r.click()}}},test:{extractEligibleHeaderText:b,firstWordFromString:h,shouldTableBeCollapsed:w,isHeaderEligible:p,isHeaderTextEligible:f,isInfobox:x,newCollapsedHeaderDiv:C,newCollapsedFooterDiv:E,newCaptionFragment:A,isNodeTextContentSimilarToPageTitle:g,stringWithNormalizedWhitespace:m,replaceNodeWithBreakingSpaceTextNode:v,getTableHeaderTextArray:y}}},function(e,t,i){"use strict";var n=i(2),r=i.n(n),a=i(3),o=i.n(a),s=(i(26),i(11)),l=i(0),c={lastEdited:"lastEdited",pageIssues:"pageIssues",disambiguation:"disambiguation",coordinate:"coordinate",talkPage:"talkPage"},u=function(){function e(t,i,n,a){r()(this,e),this.title=t,this.subtitle=i,this.itemType=n,this.clickHandler=a,this.payload=[]}return o()(e,[{key:"iconClass",value:function(){switch(this.itemType){case c.lastEdited:return"pcs-footer-menu-icon-last-edited";case c.talkPage:return"pcs-footer-menu-icon-talk-page";case c.pageIssues:return"pcs-footer-menu-icon-page-issues";case c.disambiguation:return"pcs-footer-menu-icon-disambiguation";case c.coordinate:return"pcs-footer-menu-icon-coordinate";default:return""}}},{key:"payloadExtractor",value:function(){switch(this.itemType){case c.pageIssues:return s.a.collectPageIssues;case c.disambiguation:return s.a.collectHatnotes;default:return}}}]),e}();t.a={MenuItemType:c,setHeading:function(e,t,i){var n=i.getElementById(t);n.textContent=e,n.title=l.b.escape(e)},maybeAddItem:function(e,t,i,n,r,a){if(""!==e){var o=new u(e,t,i,r),s=o.payloadExtractor();s&&(o.payload=s(a),0===o.payload.length)||function(e,t,i){i.getElementById(t).appendChild(function(e,t){var i=t.createElement("div");i.className="pcs-footer-menu-item",i.role="menuitem";var n=t.createElement("a");if(n.addEventListener("click",(function(){e.clickHandler(e.payload)})),i.appendChild(n),e.title){var r=t.createElement("div");r.className="pcs-footer-menu-item-title",r.textContent=e.title,n.title=e.title,n.appendChild(r)}if(e.subtitle){var a=t.createElement("div");a.className="pcs-footer-menu-item-subtitle",a.textContent=e.subtitle,n.appendChild(a)}var o=e.iconClass();return o&&i.classList.add(o),t.createDocumentFragment().appendChild(i)}(e,i))}(o,n,a)}}}},function(e,t,i){"use strict";var n=i(1),r=function(e){return e?n.default.querySelectorAll(e,".mbox-text-span").map((function(e){return n.default.querySelectorAll(e,".hide-when-compact, .collapsed").forEach((function(e){return e.remove()})),e})):[]},a=function(e){var t=e.closest("section[data-mw-section-id]"),i=t&&t.querySelector("h1,h2,h3,h4,h5,h6");return{id:t&&parseInt(t.getAttribute("data-mw-section-id"),10),title:i&&i.innerHTML.trim(),anchor:i&&i.getAttribute("id")}};t.a={collectHatnotes:function(e){return e?n.default.querySelectorAll(e,"div.hatnote").map((function(e){var t=n.default.querySelectorAll(e,'div.hatnote a[href]:not([href=""]):not([redlink="1"])').map((function(e){return e.href}));return{html:e.innerHTML.trim(),links:t,section:a(e)}})):[]},collectPageIssues:function(e){return r(e).map((function(e){return{html:e.innerHTML.trim(),section:a(e)}}))},test:{collectPageIssueElements:r}}},function(e,t,i){"use strict";var n={ANDROID:"".concat("pcs-platform-","android"),IOS:"".concat("pcs-platform-","ios")};t.a={CLASS:n,CLASS_PREFIX:"pcs-platform-",classify:function(e){var t=e.document.documentElement;(function(e){return/android/i.test(e.navigator.userAgent)})(e)&&t.classList.add(n.ANDROID),function(e){return/ipad|iphone|ipod/i.test(e.navigator.userAgent)}(e)&&t.classList.add(n.IOS)},setPlatform:function(e,t){e&&e.documentElement&&e.documentElement.classList.add(t)},setVersion:function(e,t){if(e&&e.documentElement)for(var i=t||1,n=1;n<=2&&(e.documentElement.classList.add("pcs-v"+n),n!==i);n++);}}},function(e,t,i){"use strict";i(32);var n={DEFAULT:"".concat("pcs-theme-","default"),DARK:"".concat("pcs-theme-","dark"),SEPIA:"".concat("pcs-theme-","sepia"),BLACK:"".concat("pcs-theme-","black")},r=function(e,t){if(e)for(var i in e.classList.add(t),n)Object.prototype.hasOwnProperty.call(n,i)&&n[i]!==t&&e.classList.remove(n[i])};t.a={THEME:n,CLASS_PREFIX:"pcs-theme-",setTheme:function(e,t){var i=e.body;r(i,t);var n=e.getElementById("pcs");r(n,t)}}},function(e,t,i){"use strict";var n=i(2),r=i.n(n),a=i(4),o=i(7),s=i(1),l=function(e,t,i){var n=decodeURIComponent(e),r=decodeURIComponent(t);if(void 0!==i){var a=decodeURIComponent(i),o="./".concat(a);return 0===n.indexOf(o)&&e.indexOf(r)===o.length}return n.indexOf(r)>-1},c=function(e,t){return l(e,"#cite_note-",t)},u=function(e){return Boolean(e)&&e.nodeType===Node.TEXT_NODE&&Boolean(e.textContent.match(/^\s+$/))},d=function(e){var t=e.querySelector("a");return t&&c(t.hash)},p=function(e,t){var i=t.querySelector("A").getAttribute("href").split("#")[1];return e.getElementById(i)||e.getElementById(decodeURIComponent(i))},f=function(e,t){var i=p(e,t);if(!i)return"";var n=i.querySelector("span.mw-reference-text");return n?n.innerHTML.trim():""},h=function(e){return s.default.matchesSelector(e,".reference, .mw-ref")?e:a.default.findClosestAncestor(e,".reference, .mw-ref")},g=function e(t,i,n,a,o){r()(this,e),this.id=t,this.rect=i,this.text=n,this.html=a,this.href=o},m=function e(t,i){r()(this,e),this.href=t,this.text=i},v=function e(t,i){r()(this,e),this.selectedIndex=t,this.referencesGroup=i},b=function(e,t){var i=e;do{i=t(i)}while(u(i));return i},y=function(e,t,i){for(var n=e;(n=b(n,t))&&n.nodeType===Node.ELEMENT_NODE&&d(n);)i(n)},_=function(e){return e.previousSibling},k=function(e){return e.nextSibling},w=function(e){var t=[e];return y(e,_,(function(e){return t.unshift(e)})),y(e,k,(function(e){return t.push(e)})),t};t.a={collectNearbyReferences:function(e,t){return function(e,t){var i=w(t),n=i.indexOf(t),r=i.map((function(t){return function(e,t){return new g(h(t).id,o.a.getBoundingClientRectAsPlainObject(t),t.textContent,f(e,t),t.querySelector("A").getAttribute("href"))}(e,t)}));return new v(n,r)}(e,t.parentElement)},collectNearbyReferencesAsText:function(e,t){var i=t.parentElement,n=w(i),r=n.indexOf(i),a=n.map((function(e){return function(e,t){return new m(t.querySelector("A").getAttribute("href"),t.textContent)}(0,e)}));return new v(r,a)},collectReferencesForBackLink:function(e,t,i){var n=function(e){var t=e.getAttribute("pcs-back-links");return t?JSON.parse(t):[]}(t);if(!n||0===n.length)return{};for(var r,a=i.split("#pcs-ref-back-link-")[1],o=[],s=n[0],l=0;l<n.length;l++){var c=n[l].split("#")[1],u=e.getElementById(c);u&&(r||(r=u.textContent.trim()),o.push({id:c}))}return{referenceId:a,referenceText:r,backLinks:o,href:s}},isBackLink:function(e,t){return l(e,"#pcs-ref-back-link-",t)},isCitation:c,CLASS:{BACK_LINK_ANCHOR:"pcs-ref-back-link",BACK_LINK_CONTAINER:"pcs-ref-backlink-container",BODY:"pcs-ref-body",BODY_HEADER:"pcs-ref-body-header",BODY_CONTENT:"pcs-ref-body-content",REF:"pcs-ref"},BACK_LINK_FRAGMENT_PREFIX:"#pcs-ref-back-link-",BACK_LINK_ATTRIBUTE:"pcs-back-links",test:{adjacentNonWhitespaceNode:b,closestReferenceClassElement:h,collectAdjacentReferenceNodes:y,collectNearbyReferenceNodes:w,collectRefText:f,getRefTextContainer:p,hasCitationLink:d,isWhitespaceTextNode:u,nextSiblingGetter:k,prevSiblingGetter:_}}},function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));var n=i(2),r=i.n(n),a=i(3),o=i.n(a),s=function(){function e(t,i,n){r()(this,e),this._window=t,this._period=i,this._function=n,this._context=void 0,this._arguments=void 0,this._result=void 0,this._timeout=0,this._timestamp=0}return o()(e,null,[{key:"wrap",value:function(t,i,n){var r=new e(t,i,n),a=function(){return r.queue(this,arguments)};return a.result=function(){return r.result},a.pending=function(){return r.pending()},a.delay=function(){return r.delay()},a.cancel=function(){return r.cancel()},a.reset=function(){return r.reset()},a}}]),o()(e,[{key:"queue",value:function(e,t){var i=this;return this._context=e,this._arguments=t,this.pending()||(this._timeout=this._window.setTimeout((function(){i._timeout=0,i._timestamp=Date.now(),i._result=i._function.apply(i._context,i._arguments)}),this.delay())),this.result}},{key:"pending",value:function(){return Boolean(this._timeout)}},{key:"delay",value:function(){return this._timestamp?Math.max(0,this._period-(Date.now()-this._timestamp)):0}},{key:"cancel",value:function(){this._timeout&&this._window.clearTimeout(this._timeout),this._timeout=0}},{key:"reset",value:function(){this.cancel(),this._result=void 0,this._timestamp=0}},{key:"result",get:function(){return this._result}}]),e}()},function(e,t,i){"use strict";i(24),t.a={containerFragment:function(e){var t=e.createDocumentFragment(),i=e.createElement("section");i.id="pcs-footer-container-menu",i.className="pcs-footer-section",i.innerHTML="<h2 id='pcs-footer-container-menu-heading'></h2>\n   <div id='pcs-footer-container-menu-items'></div>",t.appendChild(i);var n=e.createElement("section");n.id="pcs-footer-container-readmore",n.className="pcs-footer-section",n.style.display="none",n.innerHTML="<h2 id='pcs-footer-container-readmore-heading'></h2>\n   <div id='pcs-footer-container-readmore-pages'></div>",t.appendChild(n);var r=e.createElement("section");return r.id="pcs-footer-container-legal",t.appendChild(r),t},isContainerAttached:function(e){return Boolean(e.querySelector("#pcs-footer-container"))}}},function(e,t,i){"use strict";t.a={setPercentage:function(e,t){if(t){var i=(Number(t.slice(0,-1))/100*.95*100).toString()+"%";e.style["font-size"]=i}}}},function(e,t,i){"use strict";t.a={setMargins:function(e,t){void 0!==t.top&&(e.style.marginTop=t.top),void 0!==t.right&&(e.style.marginRight=t.right),void 0!==t.bottom&&(e.style.marginBottom=t.bottom),void 0!==t.left&&(e.style.marginLeft=t.left)},setPadding:function(e,t){void 0!==t.top&&(e.style.paddingTop=t.top),void 0!==t.right&&(e.style.paddingRight=t.right),void 0!==t.bottom&&(e.style.paddingBottom=t.bottom),void 0!==t.left&&(e.style.paddingLeft=t.left)}}},function(e,t,i){"use strict";i(31);var n="pcs-dim-images",r=function(e,t){e.body.classList[t?"add":"remove"](n)},a=function(e){return e.body.classList.contains(n)};t.a={CLASS:n,dim:function(e,t){return r(e.document,t)},isDim:function(e){return a(e.document)},dimImages:r,areImagesDimmed:a}},function(e,t,i){"use strict";i.r(t),i.d(t,"default",(function(){return c}));var n=i(2),r=i.n(n),a=i(3),o=i.n(a),s=/(-?\d*\.?\d*)(\D+)?/,l=function(){function e(t,i){r()(this,e),this._value=Number(t),this._unit=i||"px"}return o()(e,null,[{key:"fromElement",value:function(t,i){return t.style.getPropertyValue(i)&&e.fromStyle(t.style.getPropertyValue(i))||t.hasAttribute(i)&&new e(t.getAttribute(i))||void 0}},{key:"fromStyle",value:function(t){var i=t.match(s)||[];return new e(i[1],i[2])}}]),o()(e,[{key:"toString",value:function(){return isNaN(this.value)?"":"".concat(this.value).concat(this.unit)}},{key:"value",get:function(){return this._value}},{key:"unit",get:function(){return this._unit}}]),e}(),c=function(){function e(t,i){r()(this,e),this._width=t,this._height=i}return o()(e,null,[{key:"from",value:function(t){return new e(l.fromElement(t,"width"),l.fromElement(t,"height"))}}]),o()(e,[{key:"width",get:function(){return this._width}},{key:"widthValue",get:function(){return this._width&&!isNaN(this._width.value)?this._width.value:NaN}},{key:"widthUnit",get:function(){return this._width&&this._width.unit||"px"}},{key:"height",get:function(){return this._height}},{key:"heightValue",get:function(){return this._height&&!isNaN(this._height.value)?this._height.value:NaN}},{key:"heightUnit",get:function(){return this._height&&this._height.unit||"px"}}]),e}()},function(e,t,i){"use strict";i(25);var n=i(0);t.a={add:function(e,t,i,r,a,o){var s=e.querySelector("#".concat(r));s.innerHTML="<div class='pcs-footer-legal-contents'>\n    <hr class='pcs-footer-legal-divider'>\n    <span class='pcs-footer-legal-license'>\n    ".concat(function(e,t){var i=e.split("$1");return"".concat(n.b.escape(i[0]),'<a class="external text" rel="mw:ExtLink" href="https://creativecommons.org/licenses/by-sa/3.0/">').concat(n.b.escape(t),"</a>").concat(n.b.escape(i[1]))}(t,i),"\n    <br>\n      <div class=\"pcs-footer-browser\">\n        <a class='pcs-footer-browser-link' href='N/A'>\n          ").concat(n.b.escape(a),"\n        </a>\n      </div>\n    </span>\n  </div>"),s.querySelector(".pcs-footer-browser-link").addEventListener("click",(function(){o()}))}}},function(e,t,i){"use strict";var n=i(2),r=i.n(n),a=(i(27),i(0)),o=function(e,t,i){var n=new RegExp("\\s?[".concat(t,"][^").concat(t).concat(i,"]+[").concat(i,"]"),"g"),r=0,a=e,o="";do{o=a,a=a.replace(n,""),r++}while(o!==a&&r<30);return a},s=function(e){var t=e;return t=o(t,"(",")"),o(t,"/","/")},l=function e(t,i,n,a,o){r()(this,e),this.title=t,this.displayTitle=i,this.thumbnail=n,this.description=a,this.extract=o},c=/^[a-z]+:/,u=function(e,t,i,n,r,a){var o=[],u=a.getElementById(i),p=a.getElementById(n);d(t,"pcs-footer-container-readmore-heading",a),e.forEach((function(e,t){var i=e.titles.normalized;o.push(i);var n=function(e,t,i){var n=i.createElement("a");n.id=t,n.className="pcs-footer-readmore-page";var r=!i.pcsSetupSettings||i.pcsSetupSettings.loadImages;if(e.thumbnail&&e.thumbnail.source&&r){var a=i.createElement("div");a.style.backgroundImage="url(".concat(e.thumbnail.source.replace(c,""),")"),a.classList.add("pcs-footer-readmore-page-image"),n.appendChild(a)}var o,l,u=i.createElement("div");if(u.classList.add("pcs-footer-readmore-page-container"),n.appendChild(u),n.setAttribute("title",e.title),n.setAttribute("data-pcs-source","read-more"),n.href="./".concat(encodeURI(e.title)),e.displayTitle?o=e.displayTitle:e.title&&(o=e.title),o){var d=i.createElement("div");d.id=t,d.className="pcs-footer-readmore-page-title",d.innerHTML=o.replace(/_/g," "),n.title=e.title.replace(/_/g," "),u.appendChild(d)}if(e.description&&(l=e.description),(!l||l.length<10)&&e.extract&&(l=s(e.extract)),l){var p=i.createElement("div");p.id=t,p.className="pcs-footer-readmore-page-description",p.innerHTML=l,u.appendChild(p)}return i.createDocumentFragment().appendChild(n)}(new l(i,e.titles.display,e.thumbnail,e.description,e.extract),t,a);p.appendChild(n)})),r(o),u.style.display="block"},d=function(e,t,i){var n=i.getElementById(t);n.textContent=e,n.title=a.b.escape(e)};t.a={fetchAndAdd:function(e,t,i,n,r,a,o,s){var l=new XMLHttpRequest;l.open("GET",function(e,t,i){return"".concat(i||"","/page/related/").concat(e)}(e,0,a),!0),l.onload=function(){var e;try{e=JSON.parse(l.responseText).pages}catch(e){}if(e&&e.length){var a;if(e.length>i){var c=Math.floor(Math.random()*Math.floor(e.length-i));a=e.slice(c,c+i)}else a=e;u(a,t,n,r,o,s)}},l.send()},setHeading:d,test:{cleanExtract:s,safelyRemoveEnclosures:o}}},function(e,t,i){"use strict";i.d(t,"a",(function(){return f}));var n=i(2),r=i.n(n),a=i(3),o=i.n(a),s=i(9),l=i(4),c=i(5),u=i(1),d=i(15),p=["scroll","resize",s.a.SECTION_TOGGLED_EVENT_TYPE],f=function(){function e(t,i){var n=this;r()(this,e),this._window=t,this._loadDistanceMultiplier=i,this._placeholders=[],this._registered=!1,this._throttledLoadPlaceholders=d.a.wrap(t,100,(function(){return n._loadPlaceholders()}))}return o()(e,[{key:"convertImagesToPlaceholders",value:function(e){var t=c.a.queryLazyLoadableImages(e),i=c.a.convertImagesToPlaceholders(this._window.document,t);this._placeholders=this._placeholders.concat(i),this._register()}},{key:"collectExistingPlaceholders",value:function(e){var t=u.default.querySelectorAll(e,".".concat(c.a.PLACEHOLDER_CLASS));this._placeholders=this._placeholders.concat(t),this._register()}},{key:"loadPlaceholders",value:function(){this._throttledLoadPlaceholders()}},{key:"deregister",value:function(){var e=this;this._registered&&(p.forEach((function(t){return e._window.removeEventListener(t,e._throttledLoadPlaceholders)})),this._throttledLoadPlaceholders.reset(),this._placeholders=[],this._registered=!1)}},{key:"_register",value:function(){var e=this;!this._registered&&this._placeholders.length&&(this._registered=!0,p.forEach((function(t){return e._window.addEventListener(t,e._throttledLoadPlaceholders)})))}},{key:"_loadPlaceholders",value:function(){var e=this;this._placeholders=this._placeholders.filter((function(t){var i=!0;return e._isPlaceholderEligibleToLoad(t)&&(c.a.loadPlaceholder(e._window.document,t),i=!1),i})),0===this._placeholders.length&&this.deregister()}},{key:"_isPlaceholderEligibleToLoad",value:function(e){return l.default.isVisible(e)&&this._isPlaceholderWithinLoadDistance(e)}},{key:"_isPlaceholderWithinLoadDistance",value:function(e){var t=e.getBoundingClientRect(),i=this._window.innerHeight*this._loadDistanceMultiplier;return!(t.top>i||t.bottom<-i)}}]),e}()},function(e,t,i){},function(e,t,i){},function(e,t,i){},function(e,t,i){},function(e,t,i){},function(e,t,i){},function(e,t,i){},function(e,t,i){},function(e,t,i){},,function(e,t,i){},function(e,t,i){},function(e,t,i){},function(e,t,i){},,,,function(e,t,i){"use strict";i.r(t);var n=i(13),r=i(17),a=i(18),o=i(9),s=i(11),l={FILTER:"pcs-compatibility-filter"},c={COMPATIBILITY:l,enableSupport:function(e){var t=e.documentElement;(function(e){return function(e,t,i){var n=e.createElement("span");return["webkitFilter","filter"].some((function(e){return n.style[e]="blur(0)",n.style.cssText}))}(e)})(e)||t.classList.add(l.FILTER)}},u=i(19),d=i(8),p=i(20),f=i(4),h=function(e){var t=e.querySelector('[id="coordinates"]'),i=t?t.textContent.length:0;return e.textContent.length-i>=50},g=function(e){var t=[],i=e;do{t.push(i),i=i.nextSibling}while(i&&(1!==i.nodeType||"P"!==i.tagName));return t},m=function(e,t){if(t)for(var i=t.firstElementChild;i;){if("P"===i.tagName&&h(i))return i;i=i.nextElementSibling}},v={moveLeadIntroductionUp:function(e,t,i){var n=m(0,t);if(n){var r=e.createDocumentFragment();g(n).forEach((function(e){return r.appendChild(e)}));var a=i?i.nextSibling:t.firstChild;t.insertBefore(r,a)}},test:{isParagraphEligible:h,extractLeadIntroductionNodes:g,getEligibleParagraph:m}},b=i(16),y=i(21),_=i(10),k=i(22),w=i(5),x=i(23),C=i(12),E=i(1),A=function(e,t){e.innerHTML=t.innerHTML,e.setAttribute("class",t.getAttribute("class"))},S=function(e){return E.default.querySelectorAll(e,"a.new")},L=function(e){return e.createElement("span")},T=function(e,t){return e.parentNode.replaceChild(t,e)},R={hideRedLinks:function(e){var t=L(e);S(e).forEach((function(e){var i=t.cloneNode(!1);A(i,e),T(e,i)}))},test:{configureRedLinkTemplate:A,redLinkAnchorsInDocument:S,newRedLinkTemplate:L,replaceAnchorWithSpan:T}},j=i(14),P=i(15),I=i(6),O=i(0),$=(i(34),function(e){for(var t=[],i=e;i.parentElement&&"SECTION"!==(i=i.parentElement).tagName;)t.push(i);return t}),N=function(e){$(e).forEach((function(e){return e.classList.add("pcs-widen-image-ancestor")}))},F={widenImage:function(e){N(e),e.classList.add("pcs-widen-image-override")},test:{ancestorsToWiden:$,widenAncestors:N}};i(35),i(36),i(37),t.default={AdjustTextSize:r.a,BodySpacingTransform:a.a,CollapseTable:o.a,CollectionUtilities:s.a,CompatibilityTransform:c,DimImagesTransform:u.a,EditTransform:d.a,HTMLUtilities:O.b,LeadIntroductionTransform:v,FooterContainer:b.a,FooterLegal:y.a,FooterMenu:_.a,FooterReadMore:k.a,LazyLoadTransform:w.a,LazyLoadTransformer:x.a,PlatformTransform:C.a,RedLinks:R,ReferenceCollection:j.a,SectionUtilities:I.a,ThemeTransform:n.a,WidenImage:F,test:{ElementGeometry:p.default,ElementUtilities:f.default,Polyfill:E.default,Throttle:P.a}}}]).default},"object"==o(t)&&"object"==o(e)?e.exports=a():(n=[],void 0===(r="function"==typeof(i=a)?i.apply(t,n):i)||(e.exports=r))}).call(this,i(94)(e))},64:function(e,t,i){"use strict";var n=['*[typeof^="mw:Image"]','*[typeof^="mw:Video"]','*[typeof^="mw:Audio"]',"img.".concat("mwe-math-fallback-image-inline")],r=n.filter((function(e){return e.includes("Video")}));e.exports={MediaSelectors:n,VideoSelectors:r,PronunciationParentSelector:"span.IPA",PronunciationSelector:'a[rel="mw:MediaLink"]',SpokenWikipediaId:"#section_SpokenWikipedia",MATHOID_IMG_CLASS:"mwe-math-fallback-image-inline"}},93:function(e,t,i){(function(e){var n=void 0!==e&&e||"undefined"!=typeof self&&self||window,r=Function.prototype.apply;function a(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new a(r.call(setTimeout,n,arguments),clearTimeout)},t.setInterval=function(){return new a(r.call(setInterval,n,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(n,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},i(131),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(this,i(48))},94:function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}}});