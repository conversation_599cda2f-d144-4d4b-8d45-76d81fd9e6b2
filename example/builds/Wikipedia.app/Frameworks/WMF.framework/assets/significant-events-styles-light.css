
div#significant-changes-container{
    background-color:#eaecf0;
    box-shadow:inset 0 -2px 1px rgba(0,0,0,0.02),inset 0 5px 10px rgba(0,0,0,0.1);
}
div#significant-changes-inner-container{
    background-color:#fff;
    box-shadow:1px 3px 5px rgba(0,0,0,0.1);
}
div#significant-changes-skeleton{
    box-shadow:1px 3px 5px rgba(0,0,0,0.1);
    background: #e9ebee;
    background: linear-gradient(to right, #e9ebee 0%, #f4f5f6 20%, #e9ebee 80%);
}
h4#significant-changes-header{
    color:#72777d;
}
ul#significant-changes-list li:before{
    background-color:#00af89;
}
ul#significant-changes-list li:after{
    background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' aria-hidden='true' viewBox='0 0 32 32' focusable='false'%3E%3Ccircle stroke='none' fill='%2300af89' cx='16' cy='16' r='10'%3E%3C/circle%3E%3C/svg%3E");
}
p.significant-changes-timestamp{
    color:#00af89
}
p.significant-changes-description{
    color:#202122
}
p.significant-changes-userInfo{
    color:#72777d
}
hr#significant-changes-hr{
    color:#72777d
}
p#significant-changes-read-more{
    color:#36c;
}
div#significant-changes-top-skeleton{
    background: #e9ebee;
    background: linear-gradient(to right, #e9ebee 0%, #f4f5f6 20%, #e9ebee 80%);
}
div#significant-changes-top-inner-container-new-changes {
    background-color: #00af89;
}
div#significant-changes-top-inner-container-last-updated {
    background-color: #72777d;
}

span#significant-changes-top-dot {
    background-color: #fff;
}
span#significant-changes-top-text-new-changes {
    color: #fff;
}
span#significant-changes-top-text-last-updated {
    color: #fff;
}
span#significant-changes-top-timestamp {
    color: #72777d;
}