div#significant-changes-container{
    box-shadow:inset 0 -2px 1px rgba(0,0,0,0.02),inset 0 5px 10px rgba(0,0,0,0.1);
    padding:1.25em
}
div#significant-changes-inner-container{
    padding:.80em 1.2em .1em;
    border-radius: 2pt;
}
div#significant-changes-skeleton{
    width: 100%;
    height: 20em;
    animation : shimmer 2s infinite;
    background-size: 100% 100%;
    border-radius: 2pt;
}
@keyframes shimmer {
  0% {
    background-position: -20em 0;
  }
  100% {
    background-position: 22em 0;
  }
}
h4#significant-changes-header{
    font-size:90%;
    font-weight: 600;
    padding-left: 0em;
}
ul#significant-changes-list{
    list-style-type:none;
    padding-left: 0em;
    padding-top: .8em;
    margin-left: -.3em;
}
ul#significant-changes-list li{
    position:relative;
    margin:0;
    padding-bottom:1em;
    padding-left:1.5em;
}
ul#significant-changes-list li:before{
    content:'';
    position:absolute;
    bottom:0;
    top:0;
    left:.45em;
    width:.07em;
}
ul#significant-changes-list li:after{
    content:'';
    position:absolute;
    left:0;
    top:.2em;
    height:1em;
    width:1em;
}
li#significant-changes-first-list{
    padding-top:.7em;
}
ul#significant-changes-list li#significant-changes-first-list:after{
    top:1em;
}

ul#significant-changes-list li#significant-changes-last-list{
    padding-bottom:0em;
}

ul#significant-changes-list li#significant-changes-last-list p#significant-changes-userInfo-last {
    padding-bottom:0px;
}

li#significant-changes-list li p{
    margin:0;
    padding:0
}
ul#significant-changes-list li p{
    margin:0;
    padding:0 0 5px
}
p.significant-changes-timestamp{
    font-size:85%;
    font-weight:600;
}
p.significant-changes-userInfo{
    font-size:80%;
}
hr#significant-changes-hr{
    margin-top: 1.5em;
}
p#significant-changes-read-more{
    text-align:center;
    margin:0;
    padding:.7em 0 0 0;
    font-weight:700;
    padding-bottom:.8em;
    font-size: .9em;
}
div#significant-changes-top-container {
    padding: .3em 0em .5em 0em;
}

div#significant-changes-top-skeleton {
    border-radius: 3pt;
    margin: .3em 0em .5em 0em;
    width: 8em;
    height: 1.6em;
    animation : shimmertop 2s infinite;
    background-size: 100% 100%;
}
@keyframes shimmertop {
  0% {
    background-position: -8em 0;
  }
  100% {
    background-position: 8em 0;
  }
}
div#significant-changes-top-inner-container-new-changes {
    border-radius: 3pt;
    display: inline-block;
    padding: 0em .5em .2em .5em;
}
div#significant-changes-top-inner-container-last-updated {
    border-radius: 3pt;
    display: inline-block;
    padding: 0em .5em .2em .5em;
}

span#significant-changes-top-dot {
    height: .5em;
    width: .5em;
    border-radius: 50%;
    display: inline-block;
}
span#significant-changes-top-text-new-changes {
    font-size: 85%;
    font-weight: 650;
    padding-left: .5em;
}
span#significant-changes-top-text-last-updated {
    font-size: 85%;
    font-weight: 650;
}
span#significant-changes-top-timestamp {
    font-size: 85%;
    padding-left: .5em;
}
