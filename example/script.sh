#!/bin/bash

# Script para ejecutar pruebas de <PERSON>wright con dependencia restaurada
# Uso: ./script.sh [android|ios]

# Verificar que se proporcione un argumento
if [ $# -eq 0 ]; then
    echo "Error: Debes especificar la plataforma (android o ios)"
    echo "Uso: ./script.sh android"
    echo "     ./script.sh ios"
    exit 1
fi

PLATFORM=$1

# Validar que la plataforma sea válida
if [ "$PLATFORM" != "android" ] && [ "$PLATFORM" != "ios" ]; then
    echo "Error: Plataforma no válida. Usa 'android' o 'ios'"
    exit 1
fi

echo "🔧 Preparando entorno para pruebas de <PERSON>wright en $PLATFORM..."

# Verificar si la dependencia appium-uiautomator2-driver existe en package.json
if ! grep -q '"appium-uiautomator2-driver"' package.json; then
    echo "⚠️  Dependencia appium-uiautomator2-driver no encontrada. Agregándola..."

    # Usar npm para agregar la dependencia específica
    npm install --save-dev appium-uiautomator2-driver@3.8.0

    if [ $? -eq 0 ]; then
        echo "✅ Dependencia appium-uiautomator2-driver@3.8.0 agregada exitosamente"
    else
        echo "❌ Error al agregar la dependencia"
        exit 1
    fi
else
    echo "✅ Dependencia appium-uiautomator2-driver ya existe en package.json"
fi

echo "🚀 Ejecutando pruebas de Appwright para $PLATFORM..."

# Ejecutar las pruebas de Appwright
npx appwright test --project $PLATFORM

# Capturar el código de salida
EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Pruebas completadas exitosamente"
else
    echo "❌ Las pruebas fallaron con código de salida: $EXIT_CODE"
fi

exit $EXIT_CODE